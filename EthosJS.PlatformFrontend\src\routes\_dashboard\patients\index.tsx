import { useEffect, useMemo, useRef, useState } from "react";
import {
  Box,
  Button,
  Chip,
  debounce,
  FormControl,
  InputAdornment,
  InputLabel,
  MenuItem,
  Select,
  Stack,
  TextField,
  Typography,
} from "@mui/material";
import { DataGridPro, GridColDef } from "@mui/x-data-grid-pro";
import { DatePicker } from "@mui/x-date-pickers-pro";
import { createFileRoute } from "@tanstack/react-router";
import PageContainer from "@components/page-container";
import {
  Add,
  ArrowUpward,
  People,
  Search,
  FilterList,
  FilterAlt,
} from "@mui/icons-material";
import dayjs, { Dayjs } from "dayjs";
import LoadingComponent from "@components/loading-component";
import { useQuery } from "@tanstack/react-query";
import { PatientCreate, PatientRead } from "@auth/scopes";
import { paginationQueryParams } from "@utils/query";
import { queryParams } from "./-utils/-definitions";
import { buildPatientQueryDto } from "./-utils/-query-helpers";
import { EthosWorkflowsApiPatientDto } from "@client/workflows";
import { postApiPatientSearchOptions } from "@client/workflows/@tanstack/react-query.gen";
import usePatientCreate from "@features/patient-create/hooks/use-patient-create";

// Helper function to get status chip color and style
const getStatusChipProps = (status: string) => {
  switch (status?.toLowerCase()) {
    case 'active':
      return { color: 'success' as const, variant: 'filled' as const };
    case 'draft':
      return { color: 'warning' as const, variant: 'filled' as const };
    case 'archived':
      return { color: 'default' as const, variant: 'outlined' as const };
    default:
      return { color: 'default' as const, variant: 'outlined' as const };
  }
};

const columns: GridColDef<EthosWorkflowsApiPatientDto>[] = [
  {
    field: "patientInformation",
    headerName: "Patient Information",
    width: 430.33,
    sortable: true,
    renderHeader: () => (
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
        Patient Information
        <ArrowUpward sx={{ fontSize: 16, opacity: 0.7 }} />
      </Box>
    ),
    renderCell: (params) => {
      const row = params.row;
      const firstName = row.names?.[0]?.firstName ?? "Unknown";
      const lastName = row.names?.[0]?.lastName ?? "Unknown";
      const fullName = `${firstName} ${lastName}`;
      const patientId = row.id ?? "N/A";
      const dateOfBirth = row.demographics?.dateOfBirth
        ? dayjs(row.demographics.dateOfBirth).format("MM/DD/YYYY")
        : "N/A";
      const gender = row.demographics?.gender ? `Gender: ${row.demographics.gender}` : "Gender: N/A";
      const maritalStatus = row.demographics?.maritalStatus ? `Marital: ${row.demographics.maritalStatus}` : "Marital: N/A";

      return (
        <Box sx={{ py: 1, height: "100%" }}>
          <Typography variant="body2" sx={{ fontWeight: 600, lineHeight: 1.2 }}>
            {fullName}
          </Typography>
          <Typography variant="caption" sx={{ color: 'text.secondary', lineHeight: 1.2 }}>
            {patientId} • {dateOfBirth}
          </Typography>
          <Typography variant="caption" sx={{ color: 'text.secondary', lineHeight: 1.2, display: 'block' }}>
            {gender} • {maritalStatus}
          </Typography>
        </Box>
      );
    },
  },
  {
    field: "contact",
    headerName: "Contact",
    width: 430.33,
    sortable: true,
    renderHeader: () => (
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
        Contact
        <ArrowUpward sx={{ fontSize: 16, opacity: 0.7 }} />
      </Box>
    ),
    renderCell: (params) => {
      const row = params.row;
      const phoneNumber = row.contactInformation?.phoneNumbers?.[0]?.value ?? "N/A";
      const email = row.contactInformation?.emails?.[0]?.value ?? "N/A";

      return (
        <Box sx={{ py: 1 }}>
          <Typography variant="body2" sx={{ lineHeight: 1.2 }}>
            {phoneNumber}
          </Typography>
          <Typography variant="caption" sx={{ color: 'text.secondary', lineHeight: 1.2 }}>
            {email}
          </Typography>
        </Box>
      );
    },
  },
  {
    field: "address",
    headerName: "Address",
    width: 430.33,
    sortable: true,
    renderHeader: () => (
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
        Address
        <ArrowUpward sx={{ fontSize: 16, opacity: 0.7 }} />
      </Box>
    ),
    renderCell: (params) => {
      const row = params.row;
      const address = row.contactInformation?.addresses?.[0]?.address;
      const line1 = address?.line1 ?? "N/A";
      const line2 = address?.line2 ? `, ${address.line2}` : "";
      const city = address?.city ?? "N/A";
      const postalCode = address?.postalCode ?? "N/A";
      const country = address?.country ?? "N/A";

      return (
        <Box sx={{ py: 1 }}>
          <Typography variant="body2" sx={{ lineHeight: 1.2 }}>
            {line1}
          </Typography>
          <Typography variant="caption" sx={{ color: 'text.secondary', lineHeight: 1.2 }}>
            {line2}{city}, {country} {postalCode}
          </Typography>
        </Box>
      );
    },
  },
  {
    field: "status",
    headerName: "Status",
    width: 120,
    sortable: true,
    renderHeader: () => (
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
        Status
        <ArrowUpward sx={{ fontSize: 16, opacity: 0.7 }} />
      </Box>
    ),
    renderCell: (_params) => {
      // For now, we'll use a placeholder status since it's not in the current data structure
      // This should be replaced with actual status field when available
      const status = "Active"; // This should come from the actual data
      const chipProps = getStatusChipProps(status);

      return (
        <Chip
          label={status}
          size="small"
          {...chipProps}
          sx={{
            minWidth: 70,
            fontSize: '0.75rem',
            fontWeight: 500,
          }}
        />
      );
    },
  },
];

export const Route = createFileRoute("/_dashboard/patients/")({
  component: PatientsPage,
  validateSearch: queryParams,
  loaderDeps: ({ search }) => ({ ...search }),
  loader: async ({ context: { queryClient }, deps }) => {
    return queryClient.fetchQuery(
      postApiPatientSearchOptions({
        scopes: [PatientCreate.value, PatientRead.value],
        responseType: "json",
        body: buildPatientQueryDto(Object.keys(deps) ? deps : undefined),
      })
    );
  },
  pendingComponent: () => <LoadingComponent />,
});

interface SearchParams {
  FirstName: string;
  LastName: string;
  Location: string;
  Status: string;
  DateOfBirth: Dayjs | null;
  StudyDate: Dayjs | null;
}

interface SearchDeps
  extends Partial<Omit<SearchParams, "DateOfBirth" | "StudyDate">> {
  DateOfBirth?: string;
  StudyDate?: string;
}

function PatientsPage() {
  const navigate = Route.useNavigate();

  const deps = Route.useLoaderDeps();
  const [userSearchValue, setUserearchValue] = useState("");
  const [searchValue, setSearchValue] = useState("");
  const debouncedSearch = useMemo(
    () =>
      debounce((value: string) => {
        setSearchValue(value);
      }, 300),
    []
  );

  const [searchParams, setSearchParams] = useState<SearchParams>({
    FirstName: "",
    LastName: "",
    Location: "",
    Status: "all",
    DateOfBirth: null,
    StudyDate: null,
  });

  const [paginationModel, setPaginationModel] = useState({
    pageSize: 100,
    page: 0,
  });

  const { data, isFetching } = useQuery(
    postApiPatientSearchOptions({
      scopes: [PatientCreate.value, PatientRead.value],
      responseType: "json",
      query: paginationQueryParams(paginationModel),
      body: buildPatientQueryDto(Object.keys(deps) ? deps : undefined),
    })
  );

  const { createNewPatient, isCreatingPatient } = usePatientCreate();

  const rowCountRef = useRef(data?.totalCount || 0);

  const rowCount = useMemo(() => {
    if (data?.totalCount !== undefined) {
      rowCountRef.current = data?.totalCount;
    }
    return rowCountRef.current;
  }, [data?.totalCount]);

  const statusOptions = [
    { label: "All Status", value: "all" },
    { label: "Active", value: "Active" },
    { label: "Archived", value: "Archived" },
    { label: "Draft", value: "Draft" },
  ];

  const debouncedNavigate = useMemo(
    () =>
      debounce((searchParams: SearchParams) => {
        const deps = Object.keys(searchParams).reduce((acc, key) => {
          const val = searchParams[key as keyof typeof searchParams];
          if (typeof val === "string") {
            acc[key as keyof typeof searchParams] =
              val === "" ? undefined : val;
          }
          if (val instanceof dayjs && val.isValid()) {
            acc[key as keyof typeof searchParams] = val.format("YYYY-MM-DD");
          }
          if (val === null) {
            acc[key as keyof typeof searchParams] = undefined;
          }
          return acc;
        }, {} as SearchDeps);
        navigate({
          search: (old) => ({
            ...old,
            ...deps,
          }),
        });
      }, 300),
    [navigate]
  );

  useEffect(() => {
    if (userSearchValue !== searchValue && searchValue) {
      debouncedSearch(userSearchValue);
    }
  }, [debouncedSearch, searchValue, userSearchValue]);

  useEffect(() => {
    if (
      Object.keys(searchParams).some(
        (key) =>
          searchParams[key as keyof typeof searchParams] !== "" ||
          searchParams[key as keyof typeof searchParams] !== null
      )
    ) {
      debouncedNavigate(searchParams);
    }
  }, [debouncedNavigate, searchParams]);

  const handleClickNewPatient = async () => {
    createNewPatient(
      {
        flowState: {
          progress: 0,
          status: "InProgress",
          lastUpdate: dayjs().format("MMM D, YYYY h:mm A"),
        },
        stepState: {
          BasicInformation: "InProgress",
          Contacts: "NotStarted",
          Addresses: "NotStarted",
          Insurances: "NotStarted",
          Guardians: "NotStarted",
          ClinicalConsiderations: "NotStarted",
        },
      },
      (data) => {
        navigate({
          to: "/patients/$patientId/patient-information",
          params: { patientId: data.entityId! },
        });
      }
    );
  };

  return (
    <PageContainer
      title="Patients"
      icon={People}
      actions={
        <Stack direction="row" alignItems="center" spacing={"16px"}>
          <TextField
            value={userSearchValue}
            onChange={(e) => setUserearchValue(e.target.value)}
            variant="outlined"
            placeholder="Search"
            size="small"
            sx={{ width: "300px" }}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <Search />
                </InputAdornment>
              ),
            }}
          />
          <FormControl size="small" sx={{ width: "180px" }}>
            <InputLabel id="status-label">Status</InputLabel>
            <Select
              labelId="status-label"
              id="status-select"
              value={searchParams.Status}
              onChange={(e) => {
                setSearchParams((prev) => ({
                  ...prev,
                  Status: e.target.value,
                }));
              }}
              label="Status"
            >
              {statusOptions.map((option) => (
                <MenuItem key={option.value} value={option.value}>
                  {option.label}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
          <FilterAlt style={{ height: "24px", width: "24px", color: "grey" }} />
          <Button
            variant="contained"
            color="primary"
            loading={isCreatingPatient}
            startIcon={<Add />}
            sx={{ height: "36px" }}
            onClick={handleClickNewPatient}
          >
            Create New Patient
          </Button>
        </Stack>
      }
    >
      {/* <Snackbar
				open={!!error}
				message={error?.message}
				autoHideDuration={5000}
				anchorOrigin={{ vertical: 'top', horizontal: 'right' }}
				onClose={handleSnackbarClose}>
				<Alert
					onClose={handleSnackbarClose}
					severity="error"
					variant="filled"
					sx={{ width: '100%' }}>
					<Typography variant="body1">{error?.message}</Typography>
				</Alert>
			</Snackbar> */}
      {/* <Stack
        direction="row"
        spacing={2}
        sx={{
          justifyContent: "flex-end",
          alignItems: "center",
          mb: 2,
        }}
      >
        <TextField
          label="Patient First Name"
          variant="outlined"
          value={searchParams.FirstName}
          onChange={(e) => {
            setSearchParams((prev) => {
              return { ...prev, FirstName: e.target.value };
            });
          }}
          size="small"
        />
        <TextField
          label="Patient Last Name"
          variant="outlined"
          size="small"
          value={searchParams.LastName}
          onChange={(e) => {
            setSearchParams((prev) => {
              return { ...prev, LastName: e.target.value };
            });
          }}
        />
        <TextField
          label="Location"
          variant="outlined"
          size="small"
          value={searchParams.Location}
          onChange={(e) => {
            setSearchParams((prev) => {
              return { ...prev, Location: e.target.value };
            });
          }}
        />
        <DatePicker
          label="Date of Birth"
          slotProps={{
            textField: {
              size: "small",
            },
            field: {
              clearable: true,
            },
          }}
          value={
            searchParams.DateOfBirth ? dayjs(searchParams.DateOfBirth) : null
          }
          onChange={(date) => {
            setSearchParams((prev) => {
              return { ...prev, DateOfBirth: date };
            });
          }}
        />
        <DatePicker
          label="Study Date"
          slotProps={{
            textField: {
              size: "small",
            },
            field: {
              clearable: true,
            },
          }}
          value={searchParams.StudyDate ? dayjs(searchParams.StudyDate) : null}
          onChange={(date) => {
            setSearchParams((prev) => {
              return { ...prev, StudyDate: date };
            });
          }}
        />
      </Stack> */}
      <Box
        sx={{
          minHeight: 400,
          maxHeight: "100%",
          width: "100%",
          display: "flex",
          flexDirection: "column",
          mb: 2,
        }}
      >
        <DataGridPro
          loading={isFetching}
          
          rows={data?.items ?? []}
          rowCount={rowCount}
          paginationMode="server"
          paginationModel={paginationModel}
          onPaginationModelChange={setPaginationModel}
          pageSizeOptions={[10, 25, 50, 100]}
          pagination
          columns={columns}
        />
      </Box>
    </PageContainer>
  );
}
