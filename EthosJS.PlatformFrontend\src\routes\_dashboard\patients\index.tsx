import { useEffect, useMemo, useRef, useState } from "react";
import {
  Box,
  Button,
  <PERSON>,
  debounce,
  FormControl,
  IconButton,
  InputAdornment,
  InputLabel,
  MenuItem,
  Select,
  Stack,
  TextField,
  Typography,
} from "@mui/material";
import {
  DataGridPro,
  GridColDef,
  GridFilterModel,
  useGridApiRef,
} from "@mui/x-data-grid-pro";
import { createFileRoute } from "@tanstack/react-router";
import PageContainer from "@components/page-container";
import {
  Add,
  Search,
  FilterAlt,
  PeopleOutline,
} from "@mui/icons-material";
import dayjs, { Dayjs } from "dayjs";
import LoadingComponent from "@components/loading-component";
import { useQuery } from "@tanstack/react-query";
import { PatientCreate, PatientRead } from "@auth/scopes";
import { paginationQueryParams } from "@utils/query";
import { queryParams } from "./-utils/-definitions";
import { buildPatientQueryDto } from "./-utils/-query-helpers";
import { EthosWorkflowsApiPatientDto } from "@client/workflows";
import { postApiPatientSearchOptions } from "@client/workflows/@tanstack/react-query.gen";
import usePatientCreate from "@features/patient-create/hooks/use-patient-create";
import StyledCardFooter from "@components/card-footer";

const getStatusChipProps = (status: string) => {
  switch (status?.toLowerCase()) {
    case "active":
      return { color: "success" as const, variant: "outlined" as const };
    case "draft":
      return { color: "warning" as const, variant: "outlined" as const };
    case "archived":
      return { color: "default" as const, variant: "filled" as const };
    default:
      return { color: "default" as const, variant: "outlined" as const };
  }
};

const columns: GridColDef<EthosWorkflowsApiPatientDto>[] = [
  {
    field: "patientInformation",
    headerName: "Patient Information",
    flex: 30.5,
    minWidth: 300,
    sortable: true,
    renderHeader: () => (
      <Box
        sx={{
          display: "flex",
          fontWeight: "medium",
          alignItems: "center",
          gap: 0.5,
          fontSize: "0.875rem",
          padding: "16px",
        }}
      >
        Patient Information
      </Box>
    ),
    renderCell: (params) => {
      const row = params.row;
      const firstName = row.names?.[0]?.firstName ?? "Walter";
      const lastName = row.names?.[0]?.lastName ?? "Bishop";
      const fullName = `${firstName} ${lastName}`;
      const patientId = "#34197564";
      const dateOfBirth = row.demographics?.dateOfBirth
        ? `DOB: ${dayjs(row.demographics.dateOfBirth).format("MM/DD/YYYY")}`
        : "DOB: 06/01/1969";
      const gender = row.demographics?.gender
        ? row.demographics.gender
        : "Male";
      const maritalStatus = row.demographics?.maritalStatus
        ? row.demographics.maritalStatus
        : "Single";

      return (
        <Box
          sx={{ height: "100%", fontSize: "0.875rem" }}
          color="black"
          padding={"16px"}
          display={"flex flex-col"}
          alignItems={"center"}
        >
          <Typography
            variant="body2"
            sx={{ fontWeight: "bold", lineHeight: 1.2 }}
          >
            {fullName}
          </Typography>
          <Typography
            variant="body2"
            sx={{ fontWeight: "regular", lineHeight: 1.2 }}
          >
            {patientId} • {dateOfBirth}
          </Typography>
          <Typography
            variant="body2"
            sx={{ lineHeight: 1.2, display: "block" }}
          >
            {gender} • {maritalStatus}
          </Typography>
        </Box>
      );
    },
  },
  {
    field: "contact",
    headerName: "Contact",
    flex: 30.5,
    minWidth: 300,
    renderHeader: () => (
      <Box
        sx={{
          display: "flex",
          fontWeight: "medium",
          alignItems: "center",
          gap: 0.5,
          fontSize: "0.875rem",
          padding: "16px",
        }}
      >
        Contact
      </Box>
    ),
    renderCell: (params) => {
      const row = params.row;
      const phoneNumber =
        row.contactInformation?.phoneNumbers?.[0]?.value ?? "+****************";
      const email =
        row.contactInformation?.emails?.[0]?.value ??
        "<EMAIL>";

      return (
        <Box
          padding={"16px"}
          color={"black"}
          sx={{ height: "100%", fontSize: "0.875rem" }}
          fontWeight={"regular"}
          display={"flex flex-col"}
          alignItems={"center"}
        >
          <Typography variant="body2" sx={{ lineHeight: 1.2 }}>
            {phoneNumber}
          </Typography>
          <Typography variant="body2" sx={{ lineHeight: 1.2 }}>
            {email}
          </Typography>
        </Box>
      );
    },
  },
  {
    field: "address",
    headerName: "Address",
    flex: 30.5,
    minWidth: 300,
    renderHeader: () => (
      <Box
        sx={{
          display: "flex",
          fontWeight: "medium",
          alignItems: "center",
          gap: 0.5,
          padding: "16px",
          fontSize: "0.875rem",
        }}
      >
        Address
      </Box>
    ),
    renderCell: (params) => {
      const row = params.row;
      const address = row.contactInformation?.addresses?.[0]?.address;
      const line1 = address?.line1 ?? "9874 McFrank Avenue, ";
      const line2 = address?.line2 ? `, ${address.line2}` : "Unit 985";
      const city = address?.city ?? "Gig Harbor";
      const postalCode = address?.postalCode ?? "98335";
      const country = address?.state ?? "WA";

      return (
        <Box
          padding={"16px"}
          sx={{ height: "100%", fontSize: "0.875rem" }}
          color={"black"}
          display={"flex flex-col"}
          alignItems={"center"}
        >
          <Typography variant="body2" sx={{ lineHeight: 1.2 }}>
            {line1}
          </Typography>
          <Typography variant="body2" sx={{ lineHeight: 1.2 }}>
            {line2}
            {city}, {country} {postalCode}
          </Typography>
        </Box>
      );
    },
  },
  {
    field: "status",
    headerName: "Status",
    flex: 8.5,
    minWidth: 100,
    renderHeader: () => (
      <Box
        sx={{
          display: "flex",
          fontWeight: "medium",
          alignItems: "center",
          gap: 0.5,
          fontSize: "0.875rem",
          padding: "16px",
        }}
      >
        Status
      </Box>
    ),
    renderCell: (_params) => {
      const status = ["Active", "Draft", "Archived"][
        Math.floor(Math.random() * 3)
      ];
      const chipProps = getStatusChipProps(status);

      return (
        <Box
          padding={"16px"}
          sx={{ height: "100%", fontSize: "0.875rem" }}
          display={"flex"}
          alignItems={"center"}
        >
          <Chip
            label={status}
            size="small"
            {...chipProps}
            sx={{
              borderRadius: 5,
              minWidth: 70,
              fontSize: "0.75rem",
              fontWeight: 500,
            }}
          />
        </Box>
      );
    },
  },
];

export const Route = createFileRoute("/_dashboard/patients/")({
  component: PatientsPage,
  validateSearch: queryParams,
  loaderDeps: ({ search }) => ({ ...search }),
  loader: async ({ context: { queryClient }, deps }) => {
    return queryClient.fetchQuery(
      postApiPatientSearchOptions({
        scopes: [PatientCreate.value, PatientRead.value],
        responseType: "json",
        body: buildPatientQueryDto(Object.keys(deps) ? deps : undefined),
      })
    );
  },
  pendingComponent: () => <LoadingComponent />,
});

interface SearchParams {
  FirstName: string;
  LastName: string;
  Location: string;
  Status: string;
  DateOfBirth: Dayjs | null;
  StudyDate: Dayjs | null;
}

interface SearchDeps
  extends Partial<Omit<SearchParams, "DateOfBirth" | "StudyDate">> {
  DateOfBirth?: string;
  StudyDate?: string;
}

function PatientsPage() {
  const navigate = Route.useNavigate();

  const deps = Route.useLoaderDeps();
  const [userSearchValue, setUserearchValue] = useState("");
  const [searchValue, setSearchValue] = useState("");
  const debouncedSearch = useMemo(
    () =>
      debounce((value: string) => {
        setSearchValue(value);
      }, 300),
    []
  );

  const [searchParams, setSearchParams] = useState<SearchParams>({
    FirstName: "",
    LastName: "",
    Location: "",
    Status: "all",
    DateOfBirth: null,
    StudyDate: null,
  });

  const [paginationModel, setPaginationModel] = useState({
    pageSize: 100,
    page: 0,
  });

  const [filterModel, setFilterModel] = useState<GridFilterModel>({
    items: [],
  });
  const filterButtonRef = useRef<HTMLButtonElement | null>(null);
  const apiRef = useGridApiRef();

  const { data, isFetching } = useQuery(
    postApiPatientSearchOptions({
      scopes: [PatientCreate.value, PatientRead.value],
      responseType: "json",
      query: paginationQueryParams(paginationModel),
      body: buildPatientQueryDto(Object.keys(deps) ? deps : undefined),
    })
  );

  const { createNewPatient, isCreatingPatient } = usePatientCreate();

  const rowCountRef = useRef(data?.totalCount || 0);

  const rowCount = useMemo(() => {
    if (data?.totalCount !== undefined) {
      rowCountRef.current = data?.totalCount;
    }
    return rowCountRef.current;
  }, [data?.totalCount]);

  const statusOptions = [
    { label: "All Status", value: "all" },
    { label: "Active", value: "Active" },
    { label: "Archived", value: "Archived" },
    { label: "Draft", value: "Draft" },
  ];

  const debouncedNavigate = useMemo(
    () =>
      debounce((searchParams: SearchParams) => {
        const deps = Object.keys(searchParams).reduce((acc, key) => {
          const val = searchParams[key as keyof typeof searchParams];
          if (typeof val === "string") {
            acc[key as keyof typeof searchParams] =
              val === "" ? undefined : val;
          }
          if (val instanceof dayjs && val.isValid()) {
            acc[key as keyof typeof searchParams] = val.format("YYYY-MM-DD");
          }
          if (val === null) {
            acc[key as keyof typeof searchParams] = undefined;
          }
          return acc;
        }, {} as SearchDeps);
        navigate({
          search: (old) => ({
            ...old,
            ...deps,
          }),
        });
      }, 300),
    [navigate]
  );

  useEffect(() => {
    if (userSearchValue !== searchValue && searchValue) {
      debouncedSearch(userSearchValue);
    }
  }, [debouncedSearch, searchValue, userSearchValue]);

  useEffect(() => {
    if (
      Object.keys(searchParams).some(
        (key) =>
          searchParams[key as keyof typeof searchParams] !== "" ||
          searchParams[key as keyof typeof searchParams] !== null
      )
    ) {
      debouncedNavigate(searchParams);
    }
  }, [debouncedNavigate, searchParams]);

  const handleClickNewPatient = async () => {
    createNewPatient(
      {
        flowState: {
          progress: 0,
          status: "InProgress",
          lastUpdate: dayjs().format("MMM D, YYYY h:mm A"),
        },
        stepState: {
          BasicInformation: "InProgress",
          Contacts: "NotStarted",
          Addresses: "NotStarted",
          Insurances: "NotStarted",
          Guardians: "NotStarted",
          ClinicalConsiderations: "NotStarted",
        },
      },
      (data) => {
        navigate({
          to: "/patients/$patientId/patient-information",
          params: { patientId: data.entityId! },
        });
      }
    );
  };

  return (
    <PageContainer
      title="Patients"
      icon={PeopleOutline}
      actions={
        <Stack direction="row" alignItems="center" spacing={"16px"}>
          <TextField
            value={userSearchValue}
            onChange={(e) => setUserearchValue(e.target.value)}
            variant="outlined"
            placeholder="Search"
            size="small"
            sx={{ width: "300px" }}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <Search />
                </InputAdornment>
              ),
            }}
          />
          <FormControl size="small" sx={{ width: "180px" }}>
            <InputLabel id="status-label">Status</InputLabel>
            <Select
              labelId="status-label"
              id="status-select"
              value={searchParams.Status}
              onChange={(e) => {
                setSearchParams((prev) => ({
                  ...prev,
                  Status: e.target.value,
                }));
              }}
              label="Status"
            >
              {statusOptions.map((option) => (
                <MenuItem key={option.value} value={option.value}>
                  {option.label}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
          <IconButton
            ref={filterButtonRef}
            onClick={() => {
              apiRef?.current?.showFilterPanel();
            }}
          >
            <FilterAlt />
          </IconButton>

          <Button
            variant="contained"
            color="primary"
            loading={isCreatingPatient}
            startIcon={<Add />}
            sx={{ height: "36px" }}
            onClick={handleClickNewPatient}
          >
            Create New Patient
          </Button>
        </Stack>
      }
    >
      {filterModel.items.some((filter) => filter.value) && (
        <StyledCardFooter
          sx={{
            display: "flex",
            flexWrap: "wrap",
            gap: "1px",
            padding: "20px",
          }}
        >
          {filterModel.items.map(
            (filter, index) =>
              filter.value && (
                <Chip
                  key={index}
                  label={`${filter.value}`}
                  onDelete={() => {
                    const newItems = [...filterModel.items];
                    newItems.splice(index, 1);
                    setFilterModel({ items: newItems });
                  }}
                  size="small"
                  variant="filled"
                  color="primary"
                  sx={{
                    borderRadius: 5,
                    minWidth: 70,
                    fontSize: "0.75rem",
                    fontWeight: 500,
                  }}
                />
              )
          )}
        </StyledCardFooter>
      )}

      <Box
        sx={{
          minHeight: "calc(100vh - 250px)",
          width: "100%",
          display: "flex",
          flexDirection: "column",
        }}
      >
        <DataGridPro
          className="custom-data-grid"
          loading={isFetching}
          rows={data?.items ?? []}
          rowCount={rowCount}
          paginationMode="server"
          paginationModel={paginationModel}
          onPaginationModelChange={setPaginationModel}
          pageSizeOptions={[10, 25, 50, 100]}
          pagination
          getRowHeight={() => "auto"}
          columns={columns}
          sx={{
            border: "none",
            borderRadius: "none",
            ".css-13socp2-MuiDataGrid-root": {
              border: "none",
              borderRadius: "none",
            },
          }}
          filterModel={filterModel}
          onFilterModelChange={setFilterModel}
          filterMode="server"
          filterDebounceMs={500}
          apiRef={apiRef}
        />
      </Box>
    </PageContainer>
  );
}
