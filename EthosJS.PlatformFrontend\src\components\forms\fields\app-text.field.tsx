import { useFieldContext } from '@hooks/form-context';
import { TextFieldProps, TextField } from '@mui/material';
import { FieldPropType } from './FieldPropType';
import { digitsOnly } from '@utils/maskers';

export default function AppTextField({
	label,
	required,
	inputMode,
	...textFieldProps
}: FieldPropType & TextFieldProps) {
	const field = useFieldContext<string | number>();

	const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
		const value =
			inputMode === 'numeric' && e.target.value ? digitsOnly(e.target.value) : e.target.value;
		field.handleChange(value);
	};

	const shouldShowError = field.getMeta().isTouched || field.getMeta().isDirty;

	return (
		<TextField
			{...textFieldProps}
			inputMode={inputMode}
			label={label}
			required={required}
			value={field.state.value}
			onChange={handleChange}
			error={shouldShowError && field.getMeta().errors.length > 0}
			slotProps={{
				inputLabel: {
					shrink: field.state.value != null && field.state.value !== '',
				},
			}}
			helperText={
				shouldShowError
					? field
							.getMeta()
							.errors.map((error) => error.message)
							.join(', ')
					: undefined
			}
			fullWidth
		/>
	);
}
