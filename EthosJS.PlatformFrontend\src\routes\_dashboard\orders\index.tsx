import {
  Add,
  ShoppingCart,
  FilterAlt,
  Search,
  ShoppingCartOutlined,
} from "@mui/icons-material";
import {
  Box,
  Button,
  Chip,
  debounce,
  FormControl,
  IconButton,
  InputAdornment,
  InputLabel,
  MenuItem,
  Select,
  Stack,
  TextField,
  Typography,
} from "@mui/material";
import { useQuery } from "@tanstack/react-query";
import { createFileRoute } from "@tanstack/react-router";
import { useEffect, useMemo, useRef, useState } from "react";
import LoadingComponent from "@components/loading-component";
import PageContainer from "@components/page-container";
import { PatientCreate, PatientRead } from "@auth/scopes";
import {
  DataGridPro,
  GridColDef,
  GridFilterModel,
  useGridApiRef,
} from "@mui/x-data-grid-pro";
import { paginationQueryParams } from "@utils/query";
import { EthosWorkflowsApiOrderDto } from "@client/workflows";
import { postApiOrderSearchOptions } from "@client/workflows/@tanstack/react-query.gen";
import StyledCardFooter from "@components/card-footer";

// Extended order type for display
interface OrderDisplayData extends EthosWorkflowsApiOrderDto {
  patientName?: string;
  patientDOB?: string;
  studies?: string[];
  careLocation?: {
    name: string;
    contact: string;
    address: string;
  };
  orderingPhysician?: string;
  interpretingPhysician?: string;
  status?: string;
}

// Sample data for orders
const sampleOrderData: OrderDisplayData[] = [
  {
    id: "#001",
    patientId: "PAT-001",
    careLocationId: "LOC-001",
    primaryCarePhysicianId: "PHY-001",
    referringPhysicianId: "PHY-002",
    interpretingPhysicianId: "PHY-001",
    patientName: "John Smith",
    patientDOB: "1985-03-15",
    studies: ["Sleep Study", "Home Sleep Test"],
    careLocation: {
      name: "Tukwila Sleep Center",
      contact: "(555) 123-4567",
      address: "12500 Tukwila International Blvd, Seattle, WA 98168",
    },
    orderingPhysician: "Dr. Sarah Johnson",
    interpretingPhysician: "Dr. Sarah Johnson",
    status: "In Progress",
  },
  {
    id: "#002",
    patientId: "PAT-002",
    careLocationId: "LOC-002",
    primaryCarePhysicianId: "PHY-003",
    referringPhysicianId: "PHY-004",
    interpretingPhysicianId: "PHY-005",
    patientName: "Emily Davis",
    patientDOB: "1992-07-22",
    studies: ["CPAP Titration"],
    careLocation: {
      name: "Seattle Sleep Clinic",
      contact: "(555) 987-6543",
      address: "1500 Madison St, Seattle, WA 98104",
    },
    orderingPhysician: "Dr. Michael Chen",
    interpretingPhysician: "Dr. Lisa Rodriguez",
    status: "Completed",
  },
  {
    id: "#003",
    patientId: "PAT-003",
    careLocationId: "LOC-001",
    primaryCarePhysicianId: "PHY-001",
    referringPhysicianId: "PHY-001",
    interpretingPhysicianId: "PHY-001",
    patientName: "Robert Wilson",
    patientDOB: "1978-11-08",
    studies: ["Sleep Study", "MSLT"],
    careLocation: {
      name: "Tukwila Sleep Center",
      contact: "(555) 123-4567",
      address: "12500 Tukwila International Blvd, Seattle, WA 98168",
    },
    orderingPhysician: "Dr. Sarah Johnson",
    interpretingPhysician: "Dr. Sarah Johnson",
    status: "Completed",
  },
  {
    id: "#004",
    patientId: "PAT-004",
    careLocationId: "LOC-003",
    primaryCarePhysicianId: "PHY-006",
    referringPhysicianId: "PHY-007",
    interpretingPhysicianId: "PHY-008",
    patientName: "Maria Garcia",
    patientDOB: "1990-05-12",
    studies: ["Home Sleep Test"],
    careLocation: {
      name: "Bellevue Sleep Center",
      contact: "(555) 456-7890",
      address: "2000 116th Ave NE, Bellevue, WA 98004",
    },
    orderingPhysician: "Dr. James Park",
    interpretingPhysician: "Dr. Amanda White",
    status: "Draft",
  },
];

// Helper function to get status chip color and style
const getStatusChipProps = (status: string) => {
  switch (status?.toLowerCase()) {
    case "completed":
      return { color: "success" as const, variant: "outlined" as const };
    case "in progress":
      return { color: "primary" as const, variant: "outlined" as const };
    case "draft":
      return { color: "warning" as const, variant: "outlined" as const };
    default:
      return { color: "default" as const, variant: "outlined" as const };
  }
};

const columns: GridColDef<OrderDisplayData>[] = [
  {
    field: "orderInformation",
    headerName: "Order Information",
    flex: 16.6631,
    minWidth: 200,
    sortable: true,
    renderHeader: () => (
      <Box
        sx={{
          display: "flex",
          fontWeight: "medium",
          alignItems: "center",
          gap: 0.5,
          fontSize: "0.875rem",
          padding: "16px",
        }}
      >
        Order Information
      </Box>
    ),
    renderCell: (params) => {
      const row = params.row;
      const orderId = row.id ?? "N/A";
      const patientName = row.patientName ?? "Unknown Patient";
      const dateOfBirth = row.patientDOB
        ? new Date(row.patientDOB).toLocaleDateString()
        : "N/A";

      return (
        <Box
          sx={{ height: "100%", fontSize: "0.875rem" }}
          color="black"
          padding={"16px"}
          display={"flex flex-col"}
          alignItems={"center"}
        >
          <Typography
            variant="body2"
            sx={{ fontWeight: "bold", lineHeight: 1.2 }}
          >
            {orderId}
          </Typography>
          <Typography
            variant="body2"
            sx={{ fontWeight: "regular", lineHeight: 1.2 }}
          >
            Patient: {patientName}
          </Typography>
          <Typography
            variant="body2"
            sx={{ fontWeight: "regular", lineHeight: 1.2 }}
          >
            DOB: {dateOfBirth}
          </Typography>
        </Box>
      );
    },
  },
  {
    field: "studies",
    headerName: "Studies",
    flex: 16.66,
    minWidth: 200,
    renderHeader: () => (
      <Box
        sx={{
          display: "flex",
          fontWeight: "medium",
          alignItems: "center",
          gap: 0.5,
          fontSize: "0.875rem",
          padding: "16px",
        }}
      >
        Studies
      </Box>
    ),
    renderCell: (params) => {
      const row = params.row;
      const studies = row.studies ?? ["No Studies"];

      return (
        <Box
          sx={{ height: "100%", fontSize: "0.875rem" }}
          color="black"
          padding={"16px"}
          display={"flex"}
          alignItems={"center"}
          flexWrap={"wrap"}
          gap={"6px"}
        >
          {studies.map((study, index) => (
            <Chip
              key={index}
              label={study}
              size="small"
              variant="outlined"
              color="default"
              sx={{
                fontSize: "0.8125rem",
                fontWeight: "regular",
                borderRadius: 5,
              }}
            />
          ))}
        </Box>
      );
    },
  },
  {
    field: "careLocation",
    headerName: "Care Location",
    flex: 16.66,
    minWidth: 200,
    renderHeader: () => (
      <Box
        sx={{
          display: "flex",
          fontWeight: "medium",
          alignItems: "center",
          gap: 0.5,
          fontSize: "0.875rem",
          padding: "16px",
        }}
      >
        Care Location
      </Box>
    ),
    renderCell: (params) => {
      const row = params.row;
      const locationName = row.careLocation?.name ?? "Unknown Location";
      const contactNumber = row.careLocation?.contact ?? "N/A";
      const address = row.careLocation?.address ?? "N/A";

      return (
        <Box
          sx={{ height: "100%", fontSize: "0.875rem" }}
          color="black"
          padding={"16px"}
          display={"flex flex-col"}
          alignItems={"center"}
        >
          <Typography
            variant="body2"
            sx={{ fontWeight: "bold", lineHeight: 1.2 }}
          >
            {locationName}
          </Typography>
          <Typography
            variant="body2"
            sx={{ fontWeight: "regular", lineHeight: 1.2 }}
          >
            {contactNumber}
          </Typography>
          <Typography
            variant="body2"
            sx={{ fontWeight: "regular", lineHeight: 1.2 }}
          >
            {address}
          </Typography>
        </Box>
      );
    },
  },
  {
    field: "orderingPhysician",
    headerName: "Ordering Physician",
    flex: 16.66,
    minWidth: 200,
    renderHeader: () => (
      <Box
        sx={{
          display: "flex",
          fontWeight: "medium",
          alignItems: "center",
          gap: 0.5,
          fontSize: "0.875rem",
          padding: "16px",
        }}
      >
        Ordering Physician
      </Box>
    ),
    renderCell: (params) => {
      const row = params.row;
      const physicianName = row.orderingPhysician ?? "Unknown Physician";

      return (
        <Box
          sx={{ height: "100%", fontSize: "0.875rem" }}
          color="black"
          padding={"16px"}
          display={"flex"}
          alignItems={"center"}
        >
          <Typography
            variant="body2"
            sx={{ fontWeight: "bold", lineHeight: 1.2 }}
          >
            {physicianName}
          </Typography>
        </Box>
      );
    },
  },
  {
    field: "interpretingPhysician",
    headerName: "Interpreting Physician",
    flex: 16.66,
    minWidth: 200,
    renderHeader: () => (
      <Box
        sx={{
          display: "flex",
          fontWeight: "medium",
          alignItems: "center",
          gap: 0.5,
          fontSize: "0.875rem",
          padding: "16px",
        }}
      >
        Interpreting Physician
      </Box>
    ),
    renderCell: (params) => {
      const row = params.row;
      const orderingPhysicianId = row.primaryCarePhysicianId;
      const interpretingPhysicianId = row.interpretingPhysicianId;
      const isSamePhysician = orderingPhysicianId === interpretingPhysicianId;
      const interpretingPhysicianName =
        row.interpretingPhysician ?? "Unknown Physician";

      return (
        <Box
          sx={{ height: "100%", fontSize: "0.875rem" }}
          color="black"
          padding={"16px"}
          display={"flex"}
          alignItems={"center"}
        >
          <Typography
            variant="body2"
            sx={{
              fontWeight: isSamePhysician ? "regular" : "bold",
              fontStyle: isSamePhysician ? "italic" : "normal",
              lineHeight: 1.2,
            }}
          >
            {isSamePhysician ? "Same as Ordering" : interpretingPhysicianName}
          </Typography>
        </Box>
      );
    },
  },
  {
    field: "status",
    headerName: "Status",
    flex: 16.66,
    minWidth: 200,
    renderHeader: () => (
      <Box
        sx={{
          display: "flex",
          fontWeight: "medium",
          alignItems: "center",
          gap: 0.5,
          fontSize: "0.875rem",
          padding: "16px",
        }}
      >
        Status
      </Box>
    ),
    renderCell: (params) => {
      const row = params.row;
      const status = row.status ?? "Unknown";
      const chipProps = getStatusChipProps(status);

      return (
        <Box
          sx={{ height: "100%", fontSize: "0.875rem" }}
          color="black"
          padding={"16px"}
          display={"flex flex-col"}
          alignContent={"center"}
        >
          <Chip
            label={status}
            size="small"
            {...chipProps}
            sx={{
              minWidth: 80,
              fontSize: "0.8125rem",
              fontWeight: "regular",
              borderRadius: 5,
            }}
          />
          {status.toLocaleLowerCase() === "in progress" && (
            <Typography
              variant="body2"
              sx={{ fontWeight: "regular", lineHeight: 1.2, marginTop: "8px" }}
            >
              Insurance
            </Typography>
          )}
        </Box>
      );
    },
  },
];

interface SearchParams {
  Status: string;
}

interface SearchDeps extends Partial<SearchParams> {
  DateOfBirth?: string;
  StudyDate?: string;
}

export const Route = createFileRoute("/_dashboard/orders/")({
  component: RouteComponent,
  pendingComponent: () => <LoadingComponent />,
  loader: ({ context: { queryClient } }) =>
    queryClient.ensureQueryData(
      postApiOrderSearchOptions({
        scopes: [PatientCreate.value, PatientRead.value],
        responseType: "json",
      })
    ),
});

function RouteComponent() {
  const navigate = Route.useNavigate();

  const [paginationModel, setPaginationModel] = useState({
    pageSize: 100,
    page: 0,
  });

  const [filterModel, setFilterModel] = useState<GridFilterModel>({
    items: [],
  });
  const filterButtonRef = useRef<HTMLButtonElement | null>(null);
  const apiRef = useGridApiRef();
  const [userSearchValue, setUserearchValue] = useState("");
  const [searchValue, setSearchValue] = useState("");
  const debouncedSearch = useMemo(
    () =>
      debounce((value: string) => {
        setSearchValue(value);
      }, 300),
    []
  );
  const [searchParams, setSearchParams] = useState<SearchParams>({
    Status: "all",
  });

  const statusOptions = [
    { label: "All Status", value: "all" },
    { label: "Completed", value: "Completed" },
    { label: "Draft", value: "Draft" },
    { label: "In Progress", value: "In Progress" },
  ];

  // Build search parameters from current state
  const searchQueryParams = useMemo(() => {
    const params: any = {};

    // Add search value for order search
    if (searchValue) {
      // Search in order ID, patient name, etc.
      params.searchTerm = searchValue;
    }

    // Add status filter if not "all"
    if (searchParams.Status && searchParams.Status !== "all") {
      params.Status = searchParams.Status;
    }

    return Object.keys(params).length > 0 ? params : undefined;
  }, [searchValue, searchParams.Status]);

  const { data, isFetching } = useQuery(
    postApiOrderSearchOptions({
      query: {
        ...paginationQueryParams(paginationModel),
        ...searchQueryParams,
      },
      scopes: [PatientCreate.value, PatientRead.value],
      responseType: "json",
    })
  );

  const rowCountRef = useRef(data?.totalCount || 0);
  const rowCount = useMemo(() => {
    if (data?.totalCount !== undefined) {
      rowCountRef.current = data?.totalCount;
    }
    return rowCountRef.current;
  }, [data?.totalCount]);

  const debouncedNavigate = useMemo(
    () =>
      debounce((searchParams: SearchParams) => {
        const deps = Object.keys(searchParams).reduce((acc, key) => {
          const val = searchParams[key as keyof typeof searchParams];
          if (typeof val === "string") {
            acc[key as keyof typeof searchParams] =
              val === "" ? undefined : val;
          }
          if (val === null) {
            acc[key as keyof typeof searchParams] = undefined;
          }
          return acc;
        }, {} as SearchDeps);
        navigate({
          search: (old) => ({
            ...old,
            ...deps,
          }),
        });
      }, 300),
    [navigate]
  );

  useEffect(() => {
    if (userSearchValue !== searchValue && searchValue) {
      debouncedSearch(userSearchValue);
    }
  }, [debouncedSearch, searchValue, userSearchValue]);

  useEffect(() => {
    if (
      Object.keys(searchParams).some(
        (key) =>
          searchParams[key as keyof typeof searchParams] !== "" ||
          searchParams[key as keyof typeof searchParams] !== null
      )
    ) {
      debouncedNavigate(searchParams);
    }
  }, [debouncedNavigate, searchParams]);

  return (
    <PageContainer
      title="Orders"
      icon={ShoppingCartOutlined}
      actions={
        <Stack direction="row" alignItems="center" spacing={"16px"}>
          <TextField
            value={userSearchValue}
            onChange={(e) => {
              setUserearchValue(e.target.value);
              debouncedSearch(e.target.value);
            }}
            variant="outlined"
            placeholder="Search"
            size="small"
            sx={{ width: "300px" }}
            slotProps={{
              input: {
                startAdornment: (
                  <InputAdornment position="start">
                    <Search />
                  </InputAdornment>
                ),
              },
            }}
          />
          <FormControl size="small" sx={{ width: "180px" }}>
            <InputLabel id="status-label">Status</InputLabel>
            <Select
              labelId="status-label"
              id="status-select"
              value={searchParams.Status}
              onChange={(e) => {
                setSearchParams((prev) => ({
                  ...prev,
                  Status: e.target.value,
                }));
              }}
              label="Status"
            >
              {statusOptions.map((option) => (
                <MenuItem key={option.value} value={option.value}>
                  {option.label}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
          <IconButton
            ref={filterButtonRef}
            onClick={() => {
              apiRef?.current?.showFilterPanel();
            }}
          >
            <FilterAlt />
          </IconButton>

          <Button
            variant="contained"
            color="primary"
            // loading={isCreatingOrder}
            startIcon={<Add />}
            sx={{ height: "36px" }}
            // onClick={handleClickNewOrder}
          >
            Create New Order
          </Button>
        </Stack>
      }
    >
      {filterModel.items.some((filter) => filter.value) && (
        <StyledCardFooter
          sx={{
            display: "flex",
            flexWrap: "wrap",
            gap: "1px",
            padding: "20px",
          }}
        >
          {filterModel.items.map(
            (filter, index) =>
              filter.value && (
                <Chip
                  key={index}
                  label={`${filter.value}`}
                  onDelete={() => {
                    const newItems = [...filterModel.items];
                    newItems.splice(index, 1);
                    setFilterModel({ items: newItems });
                  }}
                  size="small"
                  variant="filled"
                  color="primary"
                  sx={{
                    borderRadius: 5,
                    minWidth: 70,
                    fontSize: "0.75rem",
                    fontWeight: 500,
                  }}
                />
              )
          )}
        </StyledCardFooter>
      )}
      <Box
        sx={{
          minHeight: 400,
          width: "100%",
          display: "flex",
          flexDirection: "column",
        }}
      >
        <DataGridPro
          className="custom-data-grid"
          loading={false}
          rows={sampleOrderData}
          rowCount={sampleOrderData.length}
          paginationMode="client"
          paginationModel={paginationModel}
          onPaginationModelChange={setPaginationModel}
          pageSizeOptions={[10, 25, 50, 100]}
          pagination
          columns={columns}
          getRowHeight={() => "auto"}
          sx={{
            "& .MuiDataGrid-cell": {
              display: "flex",
              alignItems: "center",
              lineHeight: "unset !important",
              maxHeight: "none !important",
              whiteSpace: "normal",
            },
            "& .MuiDataGrid-row": {
              maxHeight: "none !important",
            },
          }}
          filterModel={filterModel}
          onFilterModelChange={setFilterModel}
          filterMode="server"
          filterDebounceMs={500}
          apiRef={apiRef}
        />
      </Box>
    </PageContainer>
  );
}
