import {
  Add,
  <PERSON>Cart,
  PunchClock,
  Arrow<PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON>Alt,
  Search,
} from "@mui/icons-material";
import {
  Box,
  Button,
  Chip,
  FormControl,
  IconButton,
  InputAdornment,
  InputLabel,
  MenuItem,
  Popover,
  Select,
  Stack,
  TextField,
  Typography,
} from "@mui/material";
import { useQuery } from "@tanstack/react-query";
import { createFileRoute } from "@tanstack/react-router";
import { useMemo, useRef, useState } from "react";
import LoadingComponent from "@components/loading-component";
import PageContainer from "@components/page-container";
import { PatientCreate, PatientRead } from "@auth/scopes";
import { DataGridPro, GridColDef } from "@mui/x-data-grid-pro";
import { paginationQueryParams } from "@utils/query";
import { EthosWorkflowsApiOrderDto } from "@client/workflows";
import { postApiOrderSearchOptions } from "@client/workflows/@tanstack/react-query.gen";

// Filter types
interface FilterItem {
  id: string;
  column: string;
  operator: string;
  value: string;
  label: string;
}

interface FilterModalState {
  open: boolean;
  anchorEl: HTMLElement | null;
  column: string;
  operator: string;
  value: string;
}

// Helper function to get status chip color and style
const getStatusChipProps = (status: string) => {
  switch (status?.toLowerCase()) {
    case "active":
    case "completed":
      return { color: "success" as const, variant: "filled" as const };
    case "pending":
    case "in progress":
      return { color: "warning" as const, variant: "filled" as const };
    case "cancelled":
    case "archived":
      return { color: "default" as const, variant: "outlined" as const };
    default:
      return { color: "primary" as const, variant: "filled" as const };
  }
};

const columns: GridColDef<EthosWorkflowsApiOrderDto>[] = [
  {
    field: "orderInformation",
    headerName: "Order Information",
    width: 430.33,
    sortable: true,
    renderHeader: () => (
      <Box sx={{ display: "flex", alignItems: "center", gap: 0.5 }}>
        Order Information
        <ArrowUpward sx={{ fontSize: 16, opacity: 0.7 }} />
      </Box>
    ),
    renderCell: (params) => {
      const row = params.row;
      const orderId = row.id ?? "N/A";
      // For now using placeholder data - replace with actual patient data when available
      const patientName = "Patient Name"; // This should come from expanded order data
      const dateOfBirth = "01/01/1990"; // This should come from patient data

      return (
        <Box sx={{ py: 1, height: "100%" }}>
          <Typography variant="body2" sx={{ fontWeight: 600, lineHeight: 1.2 }}>
            {orderId}
          </Typography>
          <Typography
            variant="caption"
            sx={{ color: "text.secondary", lineHeight: 1.2 }}
          >
            Patient: {patientName}
          </Typography>
          <Typography
            variant="caption"
            sx={{ color: "text.secondary", lineHeight: 1.2, display: "block" }}
          >
            DOB: {dateOfBirth}
          </Typography>
        </Box>
      );
    },
  },
  {
    field: "studies",
    headerName: "Studies",
    width: 430.33,
    sortable: true,
    renderHeader: () => (
      <Box sx={{ display: "flex", alignItems: "center", gap: 0.5 }}>
        Studies
        <ArrowUpward sx={{ fontSize: 16, opacity: 0.7 }} />
      </Box>
    ),
    renderCell: (_params) => {
      // For now using placeholder data - replace with actual study data when available
      const studies = ["Sleep Study", "Home Sleep Test"]; // This should come from actual order data

      return (
        <Box sx={{ py: 1, display: "flex", flexWrap: "wrap", gap: 0.5 }}>
          {studies.map((study, index) => (
            <Chip
              key={index}
              label={study}
              size="small"
              variant="filled"
              color="primary"
              sx={{
                fontSize: "0.75rem",
                fontWeight: 500,
              }}
            />
          ))}
        </Box>
      );
    },
  },
  {
    field: "careLocation",
    headerName: "Care Location",
    width: 430.33,
    sortable: true,
    renderHeader: () => (
      <Box sx={{ display: "flex", alignItems: "center", gap: 0.5 }}>
        Care Location
        <ArrowUpward sx={{ fontSize: 16, opacity: 0.7 }} />
      </Box>
    ),
    renderCell: (_params) => {
      // For now using placeholder data - replace with actual care location data when available
      const locationName = "Tukwila Sleep Center"; // This should come from care location data
      const contactNumber = "(*************"; // This should come from care location data
      const address = "12500 Tukwila International Blvd, Seattle, WA 98168"; // This should come from care location data

      return (
        <Box sx={{ py: 1, height: "100%" }}>
          <Typography variant="body2" sx={{ fontWeight: 600, lineHeight: 1.2 }}>
            {locationName}
          </Typography>
          <Typography
            variant="caption"
            sx={{ color: "text.secondary", lineHeight: 1.2 }}
          >
            {contactNumber}
          </Typography>
          <Typography
            variant="caption"
            sx={{ color: "text.secondary", lineHeight: 1.2, display: "block" }}
          >
            {address}
          </Typography>
        </Box>
      );
    },
  },
  {
    field: "orderingPhysician",
    headerName: "Ordering Physician",
    width: 200,
    sortable: true,
    renderHeader: () => (
      <Box sx={{ display: "flex", alignItems: "center", gap: 0.5 }}>
        Ordering Physician
        <ArrowUpward sx={{ fontSize: 16, opacity: 0.7 }} />
      </Box>
    ),
    renderCell: (_params) => {
      // For now using placeholder data - replace with actual physician data when available
      const physicianName = "Dr. John Smith"; // This should come from physician data

      return (
        <Box sx={{ py: 1 }}>
          <Typography variant="body2" sx={{ lineHeight: 1.2 }}>
            {physicianName}
          </Typography>
        </Box>
      );
    },
  },
  {
    field: "interpretingPhysician",
    headerName: "Interpreting Physician",
    width: 200,
    sortable: true,
    renderHeader: () => (
      <Box sx={{ display: "flex", alignItems: "center", gap: 0.5 }}>
        Interpreting Physician
        <ArrowUpward sx={{ fontSize: 16, opacity: 0.7 }} />
      </Box>
    ),
    renderCell: (params) => {
      const row = params.row;
      // For now using placeholder logic - replace with actual physician comparison when available
      const orderingPhysicianId = row.primaryCarePhysicianId; // This should be the actual ordering physician ID
      const interpretingPhysicianId = row.interpretingPhysicianId;
      const isSamePhysician = orderingPhysicianId === interpretingPhysicianId;
      const interpretingPhysicianName = "Dr. Jane Doe"; // This should come from physician data

      return (
        <Box sx={{ py: 1 }}>
          <Typography variant="body2" sx={{ lineHeight: 1.2 }}>
            {isSamePhysician ? "Same as Ordering" : interpretingPhysicianName}
          </Typography>
        </Box>
      );
    },
  },
  {
    field: "status",
    headerName: "Status",
    width: 120,
    sortable: true,
    renderHeader: () => (
      <Box sx={{ display: "flex", alignItems: "center", gap: 0.5 }}>
        Status
        <ArrowUpward sx={{ fontSize: 16, opacity: 0.7 }} />
      </Box>
    ),
    renderCell: (_params) => {
      // For now using placeholder status - replace with actual status field when available
      const status = "In Progress"; // This should come from the actual order status
      const chipProps = getStatusChipProps(status);

      return (
        <Chip
          label={status}
          size="small"
          {...chipProps}
          sx={{
            minWidth: 70,
            fontSize: "0.75rem",
            fontWeight: 500,
          }}
        />
      );
    },
  },
];

export const Route = createFileRoute("/_dashboard/orders/")({
  component: RouteComponent,
  pendingComponent: () => <LoadingComponent />,
  loader: ({ context: { queryClient } }) =>
    queryClient.ensureQueryData(
      postApiOrderSearchOptions({
        scopes: [PatientCreate.value, PatientRead.value],
        responseType: "json",
      })
    ),
});

function RouteComponent() {
  const [paginationModel, setPaginationModel] = useState({
    pageSize: 100,
    page: 0,
  });

  // Filter state
  const [filters, setFilters] = useState<FilterItem[]>([]);
  const [filterModal, setFilterModal] = useState<FilterModalState>({
    open: false,
    anchorEl: null,
    column: "",
    operator: "contains",
    value: "",
  });

  // Filter options
  const columnOptions = [
    { value: "orderInformation", label: "Order Information" },
    { value: "studies", label: "Studies" },
    { value: "careLocation", label: "Care Location" },
    { value: "orderingPhysician", label: "Ordering Physician" },
    { value: "interpretingPhysician", label: "Interpreting Physician" },
    { value: "status", label: "Status" },
  ];

  const operatorOptions = [
    { value: "contains", label: "Contains" },
    { value: "equals", label: "Equals" },
    { value: "startsWith", label: "Starts with" },
    { value: "endsWith", label: "Ends with" },
  ];

  // Filter functions
  const handleOpenFilterModal = (event: React.MouseEvent<HTMLElement>) => {
    setFilterModal({
      ...filterModal,
      open: true,
      anchorEl: event.currentTarget,
    });
  };

  const handleCloseFilterModal = () => {
    setFilterModal({
      ...filterModal,
      open: false,
      anchorEl: null,
    });
  };

  const handleAddFilter = () => {
    if (filterModal.column && filterModal.value) {
      const newFilter: FilterItem = {
        id: Date.now().toString(),
        column: filterModal.column,
        operator: filterModal.operator,
        value: filterModal.value,
        label: `${columnOptions.find((c) => c.value === filterModal.column)?.label} ${operatorOptions.find((o) => o.value === filterModal.operator)?.label.toLowerCase()} "${filterModal.value}"`,
      };
      setFilters([...filters, newFilter]);
      setFilterModal({
        open: false,
        anchorEl: null,
        column: "",
        operator: "contains",
        value: "",
      });
    }
  };

  const handleRemoveFilter = (filterId: string) => {
    setFilters(filters.filter((f) => f.id !== filterId));
  };

  const { data, isFetching } = useQuery(
    postApiOrderSearchOptions({
      query: paginationQueryParams(paginationModel),
      scopes: [PatientCreate.value, PatientRead.value],
      responseType: "json",
    })
  );

  const rowCountRef = useRef(data?.totalCount || 0);
  const rowCount = useMemo(() => {
    if (data?.totalCount !== undefined) {
      rowCountRef.current = data?.totalCount;
    }
    return rowCountRef.current;
  }, [data?.totalCount]);

  return (
    <PageContainer title="Orders" icon={ShoppingCart} actions={}>
      <Box sx={{ p: 2, flexGrow: 1, overflow: "auto", minHeight: 0 }}>
        {/* <Box sx={{ display: 'flex', gap: 2, mb: "25px", flexWrap: 'wrap' }}>
                    <StatsCard
                        title="Completed"
                        mainContent="56"
                        subContent="Today"
                        icon={CheckCircle}
                        iconType='success'
                        type="info"
                    />
                    <StatsCard
                        title="Upcoming Appointments"
                        mainContent="5"
                        subContent="+ 2"
                        icon={Timeline}
                        iconType='info'
                        type="info"
                    />
                    <StatsCard
                        title="Pre-verified"
                        mainContent="70%"
                        subContent="Auto-checked"
                        icon={ShieldMoon}
                        iconType='success'
                        type="success"
                    />
                    <StatsCard
                        title="AI Processing"
                        mainContent="78%"
                        subContent="Auto-verified"
                        icon={Person4}
                        iconType='info'
                        type="info"
                    />
                    <StatsCard
                        title="Needs Review"
                        mainContent="8"
                        subContent="Manual check"
                        icon={CalendarMonth}
                        iconType='error'
                        type="error"
                    />
                    <StatsCard
                        title="Avg. Processing"
                        mainContent="2.5m"
                        subContent="Per order"
                        icon={CalendarToday}
                        iconType='success'
                        type="info"
                    />
                    <StatsCard
                        title="Total Orders"
                        mainContent="156"
                        subContent="+12 today"
                        icon={Checklist}
                        iconType='primary'
                        type="success"
                    />
                </Box> */}
        <Stack
          direction="row"
          spacing={2}
          sx={{
            justifyContent: "space-between",
            alignItems: "center",
            mb: 2,
          }}
        >
          <TextField
            placeholder="Search"
            size="small"
            sx={{ width: "300px" }}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <Search />
                </InputAdornment>
              ),
            }}
          />
          <IconButton
            onClick={handleOpenFilterModal}
            size="small"
            sx={{ color: "grey" }}
          >
            <FilterAlt />
          </IconButton>
          <Button
            variant="contained"
            color="secondary"
            startIcon={<PunchClock />}
          >
            Recent Orders
          </Button>
          <Button variant="contained" color="primary" endIcon={<Add />}>
            New Order
          </Button>
        </Stack>

        {/* Filter chips display */}
        {filters.length > 0 && (
          <Box sx={{ display: "flex", flexWrap: "wrap", gap: 1, mb: 1 }}>
            {filters.map((filter) => (
              <Chip
                key={filter.id}
                label={filter.label}
                onDelete={() => handleRemoveFilter(filter.id)}
                deleteIcon={<Close />}
                size="small"
                variant="outlined"
                color="primary"
              />
            ))}
          </Box>
        )}
        <Box
          sx={{
            minHeight: 400,
            maxHeight: "100%",
            width: "100%",
            display: "flex",
            flexDirection: "column",
            mb: 2,
          }}
        >
          <DataGridPro
            loading={isFetching}
            rows={data?.items ?? []}
            rowCount={rowCount}
            paginationMode="server"
            paginationModel={paginationModel}
            onPaginationModelChange={setPaginationModel}
            pageSizeOptions={[10, 25, 50, 100]}
            pagination
            columns={columns}
            getRowHeight={() => "auto"}
            sx={{
              "& .MuiDataGrid-cell": {
                display: "flex",
                alignItems: "center",
                lineHeight: "unset !important",
                maxHeight: "none !important",
                whiteSpace: "normal",
              },
              "& .MuiDataGrid-row": {
                maxHeight: "none !important",
              },
            }}
          />
        </Box>

        {/* Filter Modal */}
        <Popover
          open={filterModal.open}
          anchorEl={filterModal.anchorEl}
          onClose={handleCloseFilterModal}
          anchorOrigin={{
            vertical: "bottom",
            horizontal: "left",
          }}
          transformOrigin={{
            vertical: "top",
            horizontal: "left",
          }}
        >
          <Box sx={{ p: 2, minWidth: 300 }}>
            <Typography variant="h6" sx={{ mb: 2 }}>
              Add Filter
            </Typography>

            <Stack spacing={2}>
              <FormControl fullWidth size="small">
                <InputLabel>Column</InputLabel>
                <Select
                  value={filterModal.column}
                  label="Column"
                  onChange={(e) =>
                    setFilterModal({ ...filterModal, column: e.target.value })
                  }
                >
                  {columnOptions.map((option) => (
                    <MenuItem key={option.value} value={option.value}>
                      {option.label}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>

              <FormControl fullWidth size="small">
                <InputLabel>Operator</InputLabel>
                <Select
                  value={filterModal.operator}
                  label="Operator"
                  onChange={(e) =>
                    setFilterModal({ ...filterModal, operator: e.target.value })
                  }
                >
                  {operatorOptions.map((option) => (
                    <MenuItem key={option.value} value={option.value}>
                      {option.label}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>

              <TextField
                fullWidth
                size="small"
                label="Value"
                value={filterModal.value}
                onChange={(e) =>
                  setFilterModal({ ...filterModal, value: e.target.value })
                }
              />

              <Stack direction="row" spacing={1} justifyContent="flex-end">
                <Button onClick={handleCloseFilterModal}>Cancel</Button>
                <Button
                  variant="contained"
                  onClick={handleAddFilter}
                  disabled={!filterModal.column || !filterModal.value}
                >
                  Add Filter
                </Button>
              </Stack>
            </Stack>
          </Box>
        </Popover>
      </Box>
    </PageContainer>
  );
}
