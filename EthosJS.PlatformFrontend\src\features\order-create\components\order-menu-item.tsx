import MenuItem from '@components/menu/menu-item';
import { MonitorUp } from 'lucide-react';
import useOrderSearch from '../hooks/use-order-search';

export default function OrderMenuItem({
	patientId,
	onClick,
}: {
	patientId: string;
	onClick: (orderId: string) => void;
}) {
	const { data: orders, isFetching: isFetchingOrders } = useOrderSearch({ patientId });

	return (
		<MenuItem
			title="Order"
			value="order"
			icon={MonitorUp}
			size="large"
			onClick={() => {
				if (orders?.length) {
					onClick(orders[0].id);
				}
			}}
			disabled={isFetchingOrders || !orders?.length}
		/>
	);
}
