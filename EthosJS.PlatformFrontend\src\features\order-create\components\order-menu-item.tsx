import MenuItem from '@components/menu/menu-item';
import { MonitorUp } from 'lucide-react';
import useOrderSearch from '../hooks/use-order-search';
import useOrder from '../hooks/use-order';
import { OrderState } from '../types';
import { Status } from '@config/status';

export default function OrderMenuItem({
	patientId,
	onClick,
	orderId,
	selected,
}: {
	patientId: string;
	orderId: string;
	selected: boolean;
	onClick: (orderId: string) => void;
}) {
	const { orderData } = useOrder({ orderId });
	const { data: orders, isFetching: isFetchingOrders } = useOrderSearch({ patientId });

	const { data } = orderData ?? {};
	const orderState = (data?._state as unknown as OrderState) ?? {};
	const { flowState } = orderState ?? {};
	const status = flowState?.status ?? 'NotStarted';

	console.log('order menu item', flowState);

	const getStatus = () => {
		switch (status) {
			case 'Complete':
				return Status.Success;
			case 'InProgress':
				return Status.Process;
			case 'Error':
				return Status.Error;
			case 'Warning':
				return Status.Warning;
			case 'NotStarted':
			default:
				return Status.NotStarted;
		}
	};

	return (
		<MenuItem
			title="Order"
			value="order"
			icon={MonitorUp}
			size="large"
			selected={selected}
			onClick={() => {
				if (orders?.length) {
					onClick(orders[0].id);
				}
			}}
			status={getStatus()}
			disabled={isFetchingOrders}
		/>
	);
}
