import { IPrimitiveQuery } from './core';

// Draft primitive query types
export interface DraftQ extends IPrimitiveQuery {
  $type: 'WithEntityType' | 'WithEntityId' | 'WithPatientFirstName' | 'WithPatientLastName';
}

// Helper functions for DraftQ
export const DraftQuery = {
  withEntityType: (entityType: string): DraftQ => ({
    $type: 'WithEntityType',
    EntityType: entityType
  }),

  withEntityId: (entityId: string): DraftQ => ({
    $type: 'WithEntityId',
    EntityId: entityId
  }),

  withPatientFirstName: (firstName: string): DraftQ => ({
    $type: 'WithPatientFirstName',
    FirstName: firstName
  }),

  withPatientLastName: (lastName: string): DraftQ => ({
    $type: 'WithPatientLastName',
    LastName: lastName
  })
};
