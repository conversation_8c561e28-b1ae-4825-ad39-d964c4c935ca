const menuItems = [
	{
		stateKey: 'AppointmentCreation',
		title: 'Appointment Creation',
		value: 'appointment-creation',
		icon: User,
		size: 'medium',
		path: '/appointment-creation/dashboard',
	},
	{
		stateKey: 'FollowUpCall',
		title: 'Follow-up Call',
		value: 'follow-up-call',
		icon: User,
		size: 'medium',
		path: '/follow-up-call',
	},
] as const;

import SecondaryMenu from '@components/menu/secondary-menu';
import MenuItem from '@components/menu/menu-item';
import { Calendar, User } from 'lucide-react';
import { Status } from '@config/status';
import SelectionControls from './selection-controls';

interface SchedulingMenuProps {
	patientId: string;
	orderId: string;
	studyId: string;
	activePath: string;
	onClick: (path: string) => void;
}

export default function SchedulingMenu({
	patientId,
	orderId,
	studyId,
	activePath,
	onClick,
}: SchedulingMenuProps) {
	return (
		<SecondaryMenu
			headerProps={{
				title: 'Schedule',
				subtitle: undefined,
				icon: Calendar,
				showIcon: true,
				type: 'Workflow',
				progress: 0,
				color: 'success',
				description: 'Select the order and study  information needed for make a schedule.',
			}}
			topContainer={
				<SelectionControls
					studyId={studyId}
					patientId={patientId}
					orderId={orderId}
				/>
			}>
			{menuItems.map((item) => (
				<MenuItem
					key={item.value}
					title={item.title}
					value={item.value}
					icon={item.icon}
					size={item.size}
					status={Status.NotStarted}
					selected={activePath.includes(item.path)}
					onClick={() => {
						onClick(item.path);
					}}
				/>
			))}
		</SecondaryMenu>
	);
}
