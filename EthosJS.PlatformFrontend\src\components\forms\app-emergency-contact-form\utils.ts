import { EthosWorkflowsApiEmergencyContactDto } from '@client/workflows';
import { z } from 'zod';

export const defaultValues: EthosWorkflowsApiEmergencyContactDto = {
	relationship: null!,
	prefix: null,
	firstName: '',
	lastName: '',
	middleName: '',
	suffix: null,
	contactInformation: '',
};

export const emergencyContactSchema = z.any().transform((raw) => {
	return {
		...raw,
		middleName: raw.middleName === '' ? null : raw.middleName,
	};
});

export function emergencyContactFormOptions(savedData?: EthosWorkflowsApiEmergencyContactDto) {
	return {
		defaultValues: {
			...defaultValues,
			...(savedData ? savedData : {}),
		},
	};
}

export function emergencyContactTransformer(
	data: EthosWorkflowsApiEmergencyContactDto
): EthosWorkflowsApiEmergencyContactDto {
	const { data: parsed } = emergencyContactSchema.safeParse(data);
	return parsed as EthosWorkflowsApiEmergencyContactDto;
}
