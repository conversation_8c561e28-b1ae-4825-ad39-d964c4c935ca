import { createFileRoute } from '@tanstack/react-router';
import { z } from 'zod';
import useCreateAppointmentStore from '@hooks/use-create-appointment-store';
import Steps from '@features/scheduling/components/create/steps';

const validateSearch = z.object({
	orderId: z.string(),
	studyId: z.string().optional(),
});

export const Route = createFileRoute(
	'/_dashboard/patients/$patientId/schedule/appointment-creation/dashboard/create'
)({
	component: RouteComponent,
	validateSearch,
});

function RouteComponent() {
	const { patientId } = Route.useParams();
	const { orderId, studyId } = Route.useSearch();
	const navigate = Route.useNavigate();

	const { actions } = useCreateAppointmentStore();

	const onCancelCreate = () => {
		navigate({
			to: '/patients/$patientId/schedule/appointment-creation/dashboard',
			params: { patientId },
			search: { orderId, studyId },
			replace: true,
		});
		actions.resetState();
	};

	return (
		<Steps
			patientId={patientId}
			studyId={studyId!}
			onCancelCreate={onCancelCreate}
		/>
	);
}
