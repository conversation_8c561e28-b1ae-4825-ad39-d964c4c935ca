import { Box, Card, List, Typography, useTheme } from "@mui/material";
import LeftMenu, { LeftMenuProps, Status } from "../left-menu";
import HalfCircleProgress from "../half-circle-progress";
import { Flow } from "@workflows/Flow";
import { useStore } from "@tanstack/react-store";
import FlowLeftMenu from "./flow-left-menu";

interface ProgressPanelProps {
    title: string;
    selectedFlow: Flow;
    items: Flow[];
    setSelectedFlow: (item: Flow) => void;
}

export default function ProgressPanel(props: ProgressPanelProps) {
    const { title, items, selectedFlow, setSelectedFlow } = props;

    const progress = useStore(selectedFlow.getStore(), (state) => state.progress);

    return (
        <Card sx={{ width: 300, borderRadius: 1.5, p: 1.5, }}>
            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 1.5 }}>
                <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'flex-start', mb: 1.5 }}>
                    <Typography variant="asapCondensed" sx={{ color: 'success.main', textTransform: 'capitalize' }}>{title.toLocaleUpperCase()}</Typography>
                    {/* {subtitle && <Typography variant="body2" sx={{
                        fontWeight: 500,
                        mt: 0.5,
                    }}>{subtitle}</Typography>} */}
                </Box>
                <HalfCircleProgress value={progress} color="success" />
            </Box>
            <List>
                {items.map((item, index) => (
                    <FlowLeftMenu
                        key={index}
                        flow={item}
                        flowNumber={index + 1}
                        onClick={() => {
                            setSelectedFlow(item);
                        }}
                    />
                ))}
            </List>
        </Card>
    )
}
