import { Edit } from '@mui/icons-material';
import { Box, Chip, IconButton, Stack } from '@mui/material';

interface ChipData {
	label?: string;
	value: string;
	action?: React.ReactNode;
	hideSeperator?: boolean;
	color?: 'default' | 'primary' | 'secondary' | 'error' | 'info' | 'success' | 'warning';
}

interface ChipSummaryProps {
	items: ChipData[];
	onEdit?: () => void;
	hideSeperator?: boolean;
	variant?: 'filled' | 'outlined';
}

export default function ChipSummary({
	items,
	onEdit,
	hideSeperator = false,
	variant = 'outlined',
}: ChipSummaryProps) {
	return (
		<Stack
			direction="row"
			justifyContent="space-between"
			alignItems="flex-start">
			<Stack
				direction="row"
				spacing={1}
				flexWrap="wrap"
				useFlexGap>
				{items.map((item, index) => (
					<Chip
						key={index}
						variant={variant}
						sx={{
							borderRadius: 6,
						}}
						color={item.color ?? 'default'}
						size="small"
						label={
							<Box component="span">
								<Box
									component="span"
									sx={{ fontWeight: 'bold', mr: 0.5 }}>
									{item.label}
									{`${(item?.hideSeperator ?? hideSeperator) ? '' : ':'}`}
								</Box>
								{item.value}
							</Box>
						}
					/>
				))}
			</Stack>
			{onEdit && (
				<IconButton onClick={onEdit}>
					<Edit />
				</IconButton>
			)}
		</Stack>
	);
}

export type { ChipSummaryProps, ChipData };
