import { PatientCreate, PatientRead } from '@auth/scopes';
import {
	EthosModelQueryDto1EthosModelOrderQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null,
	EthosModelQueryDto1EthosModelStudyQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null,
} from '@client/workflows';
import {
	postApiOrderSearchOptions,
	postApiStudySearchOptions,
} from '@client/workflows/@tanstack/react-query.gen';
import { Autocomplete, Stack, TextField } from '@mui/material';
import { useQuery } from '@tanstack/react-query';
import { Query, StudyQuery, OrderQuery } from '@utils/query-dsl';
import { useEffect } from 'react';

interface SelectionControlsProps {
	patientId: string;
	orderId: string;
	studyId?: string;
	onChange: (orderId: string, studyId?: string) => void;
}

export default function SelectionControls({
	patientId,
	orderId,
	studyId,
	onChange,
}: SelectionControlsProps) {
	const { data: orders } = useQuery({
		...postApiOrderSearchOptions({
			scopes: [PatientCreate.value, PatientRead.value],
			responseType: 'json',
			body: Query.literal(
				OrderQuery.withPatientId(patientId)
			) as unknown as EthosModelQueryDto1EthosModelOrderQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null,
		}),
	});

	const { data: studies } = useQuery({
		...postApiStudySearchOptions({
			scopes: [PatientCreate.value, PatientRead.value],
			responseType: 'json',
			body: Query.literal(
				StudyQuery.withOrderId(orderId)
			) as unknown as EthosModelQueryDto1EthosModelStudyQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null,
		}),
		enabled: !!patientId && !!orderId,
	});

	useEffect(() => {
		if (studies?.items && studies?.items.length > 0 && !studyId) {
			onChange(orderId, studies?.items[0].id);
		}
	}, [studies, orderId, studyId, onChange]);

	return (
		<Stack
			direction="column"
			gap={2}
			sx={{ py: 2 }}>
			<Autocomplete
				disablePortal
				sx={{ minWidth: 200 }}
				value={orderId}
				onChange={(_, newValue) => {
					onChange(newValue);
				}}
				defaultValue={orderId}
				disableClearable
				renderInput={(params) => (
					<TextField
						{...params}
						label="Order Id"
					/>
				)}
				options={orders?.items?.map((item) => item.id) ?? []}
				filterOptions={(x) => x}
			/>
			<Autocomplete
				disablePortal
				sx={{ minWidth: 200 }}
				value={studyId ?? null}
				onChange={(_, newValue) => {
					onChange(orderId, newValue ?? '');
				}}
				renderInput={(params) => (
					<TextField
						{...params}
						label="Study Id"
					/>
				)}
				options={studies?.items?.map((item) => item.id) ?? []}
				filterOptions={(x) => x}
			/>
		</Stack>
	);
}
