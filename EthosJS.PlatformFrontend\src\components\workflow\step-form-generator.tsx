import { useMemo } from "react";
import { useAppForm } from "@hooks/app-form"
import { Box, Button } from "@mui/material";
import { FormFieldComponent } from "@components/forms/app-form-field";
import StepCardControl from "@components/step-card-control";
import LoadingComponent from "@components/loading-component";
import { useStore } from "@tanstack/react-store";
import { IValidationRule } from "../../workflows/TypeDef";
import { PredefinedFormMap } from "@config/forms";
import { PredefinedFormProps } from "@components/forms/predefined-form-props";

export type PredefinedFieldType = 'PhoneNumber' | 'SSN' | 'Email' | 'Date' | 'Text' | 'TimeRange' | 'Radio'

export interface FieldBase {
    name: string;
    label: string;
    validation?: IValidationRule[];  // Add validation rules array
}

export type BasicField = FieldBase & {
    type: 'Basic';
    fieldType: PredefinedFieldType;
}

export type FormField = FieldBase & {
    type: 'Form';
    fields: Field[];
}

export type ArrayField = FieldBase & {
    type: 'Array';
    innerType: Field;
}

export type OptionsField = FieldBase & {
    type: 'Options';
    options: string[];
}

export type CheckboxField = FieldBase & {
    type: 'Checkbox';
    defaultChecked?: boolean;
}

export type Field = BasicField | FormField | ArrayField | OptionsField | CheckboxField;


// export type StepFormGeneratorProps = {
//     formFields: Field[];
//     onSubmit: (values: Record<string, any>) => void;
//     onSaveDraft: (values: Record<string, any>) => void;
//     isLoading: boolean;
// }

export interface StepFormGeneratorProps extends PredefinedFormProps {
    formFields: Field[];
}

export default function StepFormGenerator({ formFields, errors, savedData, onSubmit, onSaveDraft, isLoading, isFinal }: StepFormGeneratorProps) {
    const form = useAppForm({
        defaultValues: {
            ...savedData
        },
        defaultState: {
            errorMap: errors
        },
        onSubmit: async ({ value }) => {
            onSubmit(value as Record<string, any>);
        }
    });

    const values = useStore(form.store, (state) => state.values);

    // Memoize the form fields rendering to prevent unnecessary re-renders
    const fieldComponents = useMemo(() => {
        return formFields.map((fieldDef, index) => {
            // Check if we have a custom form component for this field
            if (fieldDef.type === 'Form') {
                const CustomForm = PredefinedFormMap[fieldDef.label];
                if (CustomForm) {
                    return <CustomForm key={`${fieldDef.name}-${index}`} form={form} fieldDef={fieldDef} />;
                }
            }
            // Fallback to generated form
            return <FormFieldComponent key={`${fieldDef.name}-${index}`} form={form} fieldDef={fieldDef} />;
        });
    }, [formFields, form]);

    return (
        <Box
            component='form'
            sx={{ height: '100%' }}
            onSubmit={(e) => {
                e.preventDefault()
                form.handleSubmit()
            }}
        >
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                {fieldComponents}
            </Box>
            <StepCardControl>
                <Button
                    variant="outlined"
                    color="primary"
                    onClick={() => onSaveDraft(values as Record<string, any>)}
                >
                    Save Draft
                </Button>
                <form.Subscribe
                    selector={({ isDirty, canSubmit, errorMap, isSubmitting }) => ({
                        isDirty,
                        canSubmit,
                        errorMap,
                        isSubmitting
                    })}
                    children={({ isDirty, canSubmit, isSubmitting }) => {
                        return (
                            <Button
                                variant="contained"
                                color="primary"
                                type="submit"
                                loading={isSubmitting}
                                disabled={!isDirty || !canSubmit}
                            >
                                {isFinal ? 'Submit' : 'Next'}
                            </Button>
                        )
                    }}
                />
            </StepCardControl>
            {
                isLoading && <Box sx={{ position: 'absolute', top: 0, left: 0, width: '100%', height: '100%', zIndex: 1000, backgroundColor: 'rgba(255, 255, 255, 0.5)' }}>
                    <LoadingComponent />
                </Box>
            }
        </Box>
    );
}
