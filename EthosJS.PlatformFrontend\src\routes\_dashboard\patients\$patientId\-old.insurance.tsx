import LeftMenu, { Status } from '@components/left-menu';
import StepCard from '@components/step-card';
import { VerifiedUser } from '@mui/icons-material';
import { Box, CardContent, List } from '@mui/material';
import { createFileRoute, Outlet } from '@tanstack/react-router';
import { z } from 'zod';
import { Receipt, FileCheck } from 'lucide-react';
import SelectionControls from './insurance/-content/selection-controls';

export const verifyInsuranceSteps = [
  {
    name: 'Insurance Verification',
    key: 'VerifyInsurance',
    icon: Receipt,
    route: 'insurance-verification',
    status: Status.InProgress,
    data: {},
    from: undefined,
    to: 'InsuranceVerified'
  },
  {
    name: 'Prior Authorization',
    key: 'VerificationResults',
    icon: FileCheck,
    route: 'prior-authorization',
    status: Status.None,
    data: {},
    from: 'VerifyInsurance',
    to: 'ResultsReviewed'
  }
];

const VerifyInsuranceParams = z.object({
  orderId: z.string(),
  studyId: z.string().optional()
});

export const Route = createFileRoute('/_dashboard/patients/$patientId/insurance')({
  component: RouteComponent,
  validateSearch: VerifyInsuranceParams,
  loaderDeps: ({ search }) => ({ ...search })
});

function RouteComponent() {
  const { patientId } = Route.useParams();
  const { patientWfId, orderId, studyId } = Route.useLoaderDeps();
  const navigate = Route.useNavigate();

  return (
    <Box sx={{ flex: 1, minHeight: 0 }}>
      <StepCard title="Verify Insurance" icon={VerifiedUser} showHeader={false} isLoading={false}>
        <CardContent
          sx={{
            overflow: 'auto',
            minHeight: 0,
            flex: 1,
            display: 'flex',
            gap: 2,
            height: '100%'
          }}>
          <Box sx={{ overflow: 'auto', height: '100%' }}>
            <SelectionControls
              patientId={patientId}
              orderId={orderId}
              studyId={studyId}
              onChange={(orderId, studyId) => {
                navigate({
                  to: `/patients/$patientId/insurance/insurance-verification`,
                  params: { patientId },
                  search: { patientWfId, orderId, studyId }
                });
              }}
            />
            <List sx={{ width: 220 }} disablePadding>
              <LeftMenu
                name="Insurance Verification"
                icon={Receipt}
                status={Status.InProgress}
                stepNumber={1}
                onClick={() => {
                  navigate({
                    to: `/patients/$patientId/insurance/insurance-verification`,
                    params: { patientId },
                    search: { patientWfId, orderId, studyId }
                  });
                }}
              />
              <LeftMenu
                name="Prior Authorization"
                icon={FileCheck}
                status={Status.None}
                stepNumber={2}
                onClick={() => {
                  navigate({
                    to: `/patients/$patientId/insurance/prior-authorization`,
                    params: { patientId },
                    search: { patientWfId, orderId, studyId }
                  });
                }}
              />
            </List>
          </Box>
          <Box sx={{ flex: 1, overflow: 'auto', height: '100%' }}>
            <Outlet />
          </Box>
        </CardContent>
      </StepCard>
    </Box>
  );
}
