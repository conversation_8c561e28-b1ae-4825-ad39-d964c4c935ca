import React, { useEffect, useImperativeHandle, useState } from 'react';
import { Stack, Grid2 as Grid, Box, Typography, InputAdornment } from '@mui/material';
import { useAppForm } from '@hooks/app-form';
import { useStore } from '@tanstack/react-store';
import usePreStudyStore from '@hooks/use-pre-study-store';
import CardWarning from '@components/card-warning';
import { z } from 'zod';
import NotesContainer, { Note, NoteItem } from '@components/notes-container';
import { NoteAdd } from '@mui/icons-material';
import { StickyNote } from 'lucide-react';

interface VitalSignsStepProps {
  formRef: React.RefObject<any>;
  studyId: string;
  patientId: string;
}

// Validation schema for vital signs
const vitalSignsSchema = z.object({
  unit: z.string().min(1, 'Unit selection is required'),
  bodyTemperature: z.string().min(1, 'Body temperature is required'),
  systolicPressure: z.string().min(1, 'Systolic pressure is required'),
  diastolicPressure: z.string().min(1, 'Diastolic pressure is required'),
  pulseRate: z.string().min(1, 'Pulse rate is required'),
  respiratoryRate: z.string().min(1, 'Respiratory rate is required'),
  oxygenSaturation: z.string().min(1, 'Oxygen saturation is required')
});

const VitalSignsStep = ({ formRef, studyId, patientId }: VitalSignsStepProps) => {
  const [notes, setNotes] = useState<Note[]>([]);
  const { store, actions } = usePreStudyStore();
  const { values } = useStore(store, (state) => state);

  const form = useAppForm({
    defaultValues: {
      unit: values.vitalSigns?.unit || '',
      bodyTemperature: values.vitalSigns?.bodyTemperature || '',
      systolicPressure: values.vitalSigns?.systolicPressure || '',
      diastolicPressure: values.vitalSigns?.diastolicPressure || '',
      pulseRate: values.vitalSigns?.pulseRate || '',
      respiratoryRate: values.vitalSigns?.respiratoryRate || '',
      oxygenSaturation: values.vitalSigns?.oxygenSaturation || ''
    },
    validators: {
      onChange: ({ value }) => {
        const result = vitalSignsSchema.safeParse(value);
        return result.success ? undefined : result.error.format();
      }
    },
    onSubmit({ value }) {
      actions.setValues({
        vitalSigns: {
          unit: value.unit,
          bodyTemperature: value.bodyTemperature,
          systolicPressure: value.systolicPressure,
          diastolicPressure: value.diastolicPressure,
          pulseRate: value.pulseRate,
          respiratoryRate: value.respiratoryRate,
          oxygenSaturation: value.oxygenSaturation
        }
      });
      actions.moveNext();
    }
  });

  const handleAddNote = (content: string) => {
    const newNote: Note = {
      id: Date.now().toString(),
      header: 'Added by John Scott, May 7, 2025 at 9:15 AM',
      content
    };
    setNotes((prev) => [...prev, newNote]);
  };

  const handleEditNote = (id: string, updatedNote: Omit<Note, 'id'>) => {
    setNotes((prev) => prev.map((note) => (note.id === id ? { ...updatedNote, id } : note)));
  };

  const handleDeleteNote = (id: string) => {
    setNotes((prev) => prev.filter((note) => note.id !== id));
  };

  const { values: formValues } = useStore(form.store, ({ values }) => ({ values }));

  // Update store whenever form values change
  useEffect(() => {
    actions.setValues({
      vitalSigns: {
        unit: formValues.unit,
        bodyTemperature: formValues.bodyTemperature,
        systolicPressure: formValues.systolicPressure,
        diastolicPressure: formValues.diastolicPressure,
        pulseRate: formValues.pulseRate,
        respiratoryRate: formValues.respiratoryRate,
        oxygenSaturation: formValues.oxygenSaturation
      }
    });
  }, [formValues, actions]);

  // Expose form to parent component
  useImperativeHandle(formRef, () => ({
    handleSubmit: () => form.handleSubmit()
  }));

  // Unit options
  const unitOptions = [
    { label: 'Imperial', value: 'imperial' },
    { label: 'Metric', value: 'metric' }
  ];

  return (
    <Stack gap="12px">
      <CardWarning
        title="Manual Vital Signs Entry"
        subtitle="All vitals must be measured manually and entered into the system. No automated device connectivity available."
      />

      <Stack gap={'16px'}>
        {/* Unit Selection and Body Temperature */}
        <Grid container spacing={2}>
          <Grid size={4}>
            <form.AppField
              name="unit"
              children={(field) => (
                <field.AppSelectField
                  label="Unit"
                  options={unitOptions}
                  required
                  placeholder="Select measurement unit"
                />
              )}
            />
          </Grid>
          <Grid size={8}>
            <form.AppField
              name="bodyTemperature"
              children={(field) => {
                const fahrenheit = parseFloat(field.state.value);
                const celsius =
                  !isNaN(fahrenheit) && isFinite(fahrenheit) ? ((fahrenheit - 32) * 5) / 9 : null;

                return (
                  <Stack spacing={0.5}>
                    <field.AppTextField
                      label="Body Temperature (°F)"
                      required
                      inputMode="decimal"
                    />
                    {celsius !== null && (
                      <Typography padding={'0 0 0 14px'} variant="body2" color="text.secondary">
                        {celsius.toFixed(1)}°C
                      </Typography>
                    )}
                  </Stack>
                );
              }}
            />
          </Grid>
        </Grid>

        {/* Blood Pressure */}
        <Grid container spacing={1} alignItems="center">
          <Grid size={5.5}>
            <form.AppField
              name="systolicPressure"
              children={(field) => (
                <field.AppTextField label="SYSTOLIC mm Hg" inputMode="numeric" />
              )}
            />
          </Grid>
          <Grid size={1} sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
            <Typography variant="h5" color="gray">
              /
            </Typography>
          </Grid>
          <Grid size={5.5}>
            <form.AppField
              name="diastolicPressure"
              children={(field) => (
                <field.AppTextField label="DIASTOLIC mm Hg" inputMode="numeric" />
              )}
            />
          </Grid>
        </Grid>

        {/* Pulse Rate */}
        <Grid container spacing={2}>
          <Grid size={12}>
            <form.AppField
              name="pulseRate"
              children={(field) => (
                <field.AppTextField label="Pulse Rate (bpm)" inputMode="numeric" />
              )}
            />
          </Grid>
        </Grid>

        {/* Respiratory Rate */}
        <Grid container spacing={2}>
          <Grid size={12}>
            <form.AppField
              name="respiratoryRate"
              children={(field) => (
                <field.AppTextField
                  label="Respiratory Rate (breaths/min)"
                  inputMode="numeric"
                />
              )}
            />
          </Grid>
        </Grid>

        {/* Oxygen Saturation */}
        <Grid container spacing={2}>
          <Grid size={12}>
            <form.AppField
              name="oxygenSaturation"
              children={(field) => (
                <field.AppTextField label="Oxygen Saturation (%)" inputMode="numeric" />
              )}
            />
          </Grid>
        </Grid>
      </Stack>
      {/* Vital Notes */}
      <NotesContainer
        title="Vitals Notes"
        icon={<NoteAdd />}
        onAddNote={handleAddNote}
        onDeleteNote={handleDeleteNote}
        allowAdd={true}
        allowDelete={true}
        placeholder=""
        maxLength={500}>
        {notes.map((note) => (
          <NoteItem
            icon={<StickyNote />}
            key={note.id}
            note={note}
            allowEdit={true}
            onEdit={(updatedNote) => handleEditNote(note.id, updatedNote)}
          />
        ))}
      </NotesContainer>
    </Stack>
  );
};

export default VitalSignsStep;
