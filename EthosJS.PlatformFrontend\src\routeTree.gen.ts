/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

// Import Routes

import { Route as rootRoute } from './routes/__root'
import { Route as DashboardImport } from './routes/_dashboard'
import { Route as IndexImport } from './routes/index'
import { Route as DashboardDashboardImport } from './routes/_dashboard/_dashboard'
import { Route as DashboardTechnicianIndexImport } from './routes/_dashboard/technician/index'
import { Route as DashboardScheduleIndexImport } from './routes/_dashboard/schedule/index'
import { Route as DashboardPatientsIndexImport } from './routes/_dashboard/patients/index'
import { Route as DashboardOrdersIndexImport } from './routes/_dashboard/orders/index'
import { Route as DashboardInsuranceIndexImport } from './routes/_dashboard/insurance/index'
import { Route as DashboardPatientsPatientIdRouteImport } from './routes/_dashboard/patients/$patientId/route'
import { Route as DashboardPatientsPatientIdVisitImport } from './routes/_dashboard/patients/$patientId/visit'
import { Route as DashboardPatientsPatientIdScheduleRouteImport } from './routes/_dashboard/patients/$patientId/schedule/route'
import { Route as DashboardPatientsPatientIdPatientInformationRouteImport } from './routes/_dashboard/patients/$patientId/patient-information/route'
import { Route as DashboardPatientsPatientIdOrderRouteImport } from './routes/_dashboard/patients/$patientId/order/route'
import { Route as DashboardPatientsPatientIdInsuranceRouteImport } from './routes/_dashboard/patients/$patientId/insurance/route'
import { Route as DashboardPatientsPatientIdScheduleFollowUpCallImport } from './routes/_dashboard/patients/$patientId/schedule/follow-up-call'
import { Route as DashboardPatientsPatientIdPatientInformationInsurancesImport } from './routes/_dashboard/patients/$patientId/patient-information/insurances'
import { Route as DashboardPatientsPatientIdPatientInformationGuardiansImport } from './routes/_dashboard/patients/$patientId/patient-information/guardians'
import { Route as DashboardPatientsPatientIdPatientInformationContactInformationImport } from './routes/_dashboard/patients/$patientId/patient-information/contact-information'
import { Route as DashboardPatientsPatientIdPatientInformationClinicalConsiderationsImport } from './routes/_dashboard/patients/$patientId/patient-information/clinical-considerations'
import { Route as DashboardPatientsPatientIdPatientInformationBasicInformationImport } from './routes/_dashboard/patients/$patientId/patient-information/basic-information'
import { Route as DashboardPatientsPatientIdPatientInformationAddressesImport } from './routes/_dashboard/patients/$patientId/patient-information/addresses'
import { Route as DashboardPatientsPatientIdOrderStudyImport } from './routes/_dashboard/patients/$patientId/order/study'
import { Route as DashboardPatientsPatientIdOrderPhysiciansImport } from './routes/_dashboard/patients/$patientId/order/physicians'
import { Route as DashboardPatientsPatientIdOrderConfirmationImport } from './routes/_dashboard/patients/$patientId/order/confirmation'
import { Route as DashboardPatientsPatientIdOrderCareLocationImport } from './routes/_dashboard/patients/$patientId/order/care-location'
import { Route as DashboardPatientsPatientIdInsurancePriorAuthorizationImport } from './routes/_dashboard/patients/$patientId/insurance/prior-authorization'
import { Route as DashboardPatientsPatientIdInsuranceInsuranceVerificationImport } from './routes/_dashboard/patients/$patientId/insurance/insurance-verification'
import { Route as DashboardPatientsPatientIdScheduleAppointmentCreationRouteImport } from './routes/_dashboard/patients/$patientId/schedule/appointment-creation/route'
import { Route as DashboardPatientsPatientIdVisitPreStudyIndexImport } from './routes/_dashboard/patients/$patientId/visit/pre-study/index'
import { Route as DashboardPatientsPatientIdScheduleAppointmentCreationNotificationsImport } from './routes/_dashboard/patients/$patientId/schedule/appointment-creation/notifications'
import { Route as DashboardPatientsPatientIdScheduleAppointmentCreationNotesImport } from './routes/_dashboard/patients/$patientId/schedule/appointment-creation/notes'
import { Route as DashboardPatientsPatientIdScheduleAppointmentCreationFilesImport } from './routes/_dashboard/patients/$patientId/schedule/appointment-creation/files'
import { Route as DashboardPatientsPatientIdScheduleAppointmentCreationDashboardRouteImport } from './routes/_dashboard/patients/$patientId/schedule/appointment-creation/dashboard/route'
import { Route as DashboardPatientsPatientIdScheduleAppointmentCreationDashboardIndexImport } from './routes/_dashboard/patients/$patientId/schedule/appointment-creation/dashboard/index'
import { Route as DashboardPatientsPatientIdScheduleAppointmentCreationDashboardPreviewImport } from './routes/_dashboard/patients/$patientId/schedule/appointment-creation/dashboard/preview'
import { Route as DashboardPatientsPatientIdScheduleAppointmentCreationDashboardCreateImport } from './routes/_dashboard/patients/$patientId/schedule/appointment-creation/dashboard/create'

// Create/Update Routes

const DashboardRoute = DashboardImport.update({
  id: '/_dashboard',
  getParentRoute: () => rootRoute,
} as any)

const IndexRoute = IndexImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => rootRoute,
} as any)

const DashboardDashboardRoute = DashboardDashboardImport.update({
  id: '/_dashboard',
  getParentRoute: () => DashboardRoute,
} as any)

const DashboardTechnicianIndexRoute = DashboardTechnicianIndexImport.update({
  id: '/technician/',
  path: '/technician/',
  getParentRoute: () => DashboardRoute,
} as any)

const DashboardScheduleIndexRoute = DashboardScheduleIndexImport.update({
  id: '/schedule/',
  path: '/schedule/',
  getParentRoute: () => DashboardRoute,
} as any)

const DashboardPatientsIndexRoute = DashboardPatientsIndexImport.update({
  id: '/patients/',
  path: '/patients/',
  getParentRoute: () => DashboardRoute,
} as any)

const DashboardOrdersIndexRoute = DashboardOrdersIndexImport.update({
  id: '/orders/',
  path: '/orders/',
  getParentRoute: () => DashboardRoute,
} as any)

const DashboardInsuranceIndexRoute = DashboardInsuranceIndexImport.update({
  id: '/insurance/',
  path: '/insurance/',
  getParentRoute: () => DashboardRoute,
} as any)

const DashboardPatientsPatientIdRouteRoute =
  DashboardPatientsPatientIdRouteImport.update({
    id: '/patients/$patientId',
    path: '/patients/$patientId',
    getParentRoute: () => DashboardRoute,
  } as any)

const DashboardPatientsPatientIdVisitRoute =
  DashboardPatientsPatientIdVisitImport.update({
    id: '/visit',
    path: '/visit',
    getParentRoute: () => DashboardPatientsPatientIdRouteRoute,
  } as any)

const DashboardPatientsPatientIdScheduleRouteRoute =
  DashboardPatientsPatientIdScheduleRouteImport.update({
    id: '/schedule',
    path: '/schedule',
    getParentRoute: () => DashboardPatientsPatientIdRouteRoute,
  } as any)

const DashboardPatientsPatientIdPatientInformationRouteRoute =
  DashboardPatientsPatientIdPatientInformationRouteImport.update({
    id: '/patient-information',
    path: '/patient-information',
    getParentRoute: () => DashboardPatientsPatientIdRouteRoute,
  } as any)

const DashboardPatientsPatientIdOrderRouteRoute =
  DashboardPatientsPatientIdOrderRouteImport.update({
    id: '/order',
    path: '/order',
    getParentRoute: () => DashboardPatientsPatientIdRouteRoute,
  } as any)

const DashboardPatientsPatientIdInsuranceRouteRoute =
  DashboardPatientsPatientIdInsuranceRouteImport.update({
    id: '/insurance',
    path: '/insurance',
    getParentRoute: () => DashboardPatientsPatientIdRouteRoute,
  } as any)

const DashboardPatientsPatientIdScheduleFollowUpCallRoute =
  DashboardPatientsPatientIdScheduleFollowUpCallImport.update({
    id: '/follow-up-call',
    path: '/follow-up-call',
    getParentRoute: () => DashboardPatientsPatientIdScheduleRouteRoute,
  } as any)

const DashboardPatientsPatientIdPatientInformationInsurancesRoute =
  DashboardPatientsPatientIdPatientInformationInsurancesImport.update({
    id: '/insurances',
    path: '/insurances',
    getParentRoute: () =>
      DashboardPatientsPatientIdPatientInformationRouteRoute,
  } as any)

const DashboardPatientsPatientIdPatientInformationGuardiansRoute =
  DashboardPatientsPatientIdPatientInformationGuardiansImport.update({
    id: '/guardians',
    path: '/guardians',
    getParentRoute: () =>
      DashboardPatientsPatientIdPatientInformationRouteRoute,
  } as any)

const DashboardPatientsPatientIdPatientInformationContactInformationRoute =
  DashboardPatientsPatientIdPatientInformationContactInformationImport.update({
    id: '/contact-information',
    path: '/contact-information',
    getParentRoute: () =>
      DashboardPatientsPatientIdPatientInformationRouteRoute,
  } as any)

const DashboardPatientsPatientIdPatientInformationClinicalConsiderationsRoute =
  DashboardPatientsPatientIdPatientInformationClinicalConsiderationsImport.update(
    {
      id: '/clinical-considerations',
      path: '/clinical-considerations',
      getParentRoute: () =>
        DashboardPatientsPatientIdPatientInformationRouteRoute,
    } as any,
  )

const DashboardPatientsPatientIdPatientInformationBasicInformationRoute =
  DashboardPatientsPatientIdPatientInformationBasicInformationImport.update({
    id: '/basic-information',
    path: '/basic-information',
    getParentRoute: () =>
      DashboardPatientsPatientIdPatientInformationRouteRoute,
  } as any)

const DashboardPatientsPatientIdPatientInformationAddressesRoute =
  DashboardPatientsPatientIdPatientInformationAddressesImport.update({
    id: '/addresses',
    path: '/addresses',
    getParentRoute: () =>
      DashboardPatientsPatientIdPatientInformationRouteRoute,
  } as any)

const DashboardPatientsPatientIdOrderStudyRoute =
  DashboardPatientsPatientIdOrderStudyImport.update({
    id: '/study',
    path: '/study',
    getParentRoute: () => DashboardPatientsPatientIdOrderRouteRoute,
  } as any)

const DashboardPatientsPatientIdOrderPhysiciansRoute =
  DashboardPatientsPatientIdOrderPhysiciansImport.update({
    id: '/physicians',
    path: '/physicians',
    getParentRoute: () => DashboardPatientsPatientIdOrderRouteRoute,
  } as any)

const DashboardPatientsPatientIdOrderConfirmationRoute =
  DashboardPatientsPatientIdOrderConfirmationImport.update({
    id: '/confirmation',
    path: '/confirmation',
    getParentRoute: () => DashboardPatientsPatientIdOrderRouteRoute,
  } as any)

const DashboardPatientsPatientIdOrderCareLocationRoute =
  DashboardPatientsPatientIdOrderCareLocationImport.update({
    id: '/care-location',
    path: '/care-location',
    getParentRoute: () => DashboardPatientsPatientIdOrderRouteRoute,
  } as any)

const DashboardPatientsPatientIdInsurancePriorAuthorizationRoute =
  DashboardPatientsPatientIdInsurancePriorAuthorizationImport.update({
    id: '/prior-authorization',
    path: '/prior-authorization',
    getParentRoute: () => DashboardPatientsPatientIdInsuranceRouteRoute,
  } as any)

const DashboardPatientsPatientIdInsuranceInsuranceVerificationRoute =
  DashboardPatientsPatientIdInsuranceInsuranceVerificationImport.update({
    id: '/insurance-verification',
    path: '/insurance-verification',
    getParentRoute: () => DashboardPatientsPatientIdInsuranceRouteRoute,
  } as any)

const DashboardPatientsPatientIdScheduleAppointmentCreationRouteRoute =
  DashboardPatientsPatientIdScheduleAppointmentCreationRouteImport.update({
    id: '/appointment-creation',
    path: '/appointment-creation',
    getParentRoute: () => DashboardPatientsPatientIdScheduleRouteRoute,
  } as any)

const DashboardPatientsPatientIdVisitPreStudyIndexRoute =
  DashboardPatientsPatientIdVisitPreStudyIndexImport.update({
    id: '/pre-study/',
    path: '/pre-study/',
    getParentRoute: () => DashboardPatientsPatientIdVisitRoute,
  } as any)

const DashboardPatientsPatientIdScheduleAppointmentCreationNotificationsRoute =
  DashboardPatientsPatientIdScheduleAppointmentCreationNotificationsImport.update(
    {
      id: '/notifications',
      path: '/notifications',
      getParentRoute: () =>
        DashboardPatientsPatientIdScheduleAppointmentCreationRouteRoute,
    } as any,
  )

const DashboardPatientsPatientIdScheduleAppointmentCreationNotesRoute =
  DashboardPatientsPatientIdScheduleAppointmentCreationNotesImport.update({
    id: '/notes',
    path: '/notes',
    getParentRoute: () =>
      DashboardPatientsPatientIdScheduleAppointmentCreationRouteRoute,
  } as any)

const DashboardPatientsPatientIdScheduleAppointmentCreationFilesRoute =
  DashboardPatientsPatientIdScheduleAppointmentCreationFilesImport.update({
    id: '/files',
    path: '/files',
    getParentRoute: () =>
      DashboardPatientsPatientIdScheduleAppointmentCreationRouteRoute,
  } as any)

const DashboardPatientsPatientIdScheduleAppointmentCreationDashboardRouteRoute =
  DashboardPatientsPatientIdScheduleAppointmentCreationDashboardRouteImport.update(
    {
      id: '/dashboard',
      path: '/dashboard',
      getParentRoute: () =>
        DashboardPatientsPatientIdScheduleAppointmentCreationRouteRoute,
    } as any,
  )

const DashboardPatientsPatientIdScheduleAppointmentCreationDashboardIndexRoute =
  DashboardPatientsPatientIdScheduleAppointmentCreationDashboardIndexImport.update(
    {
      id: '/',
      path: '/',
      getParentRoute: () =>
        DashboardPatientsPatientIdScheduleAppointmentCreationDashboardRouteRoute,
    } as any,
  )

const DashboardPatientsPatientIdScheduleAppointmentCreationDashboardPreviewRoute =
  DashboardPatientsPatientIdScheduleAppointmentCreationDashboardPreviewImport.update(
    {
      id: '/preview',
      path: '/preview',
      getParentRoute: () =>
        DashboardPatientsPatientIdScheduleAppointmentCreationDashboardRouteRoute,
    } as any,
  )

const DashboardPatientsPatientIdScheduleAppointmentCreationDashboardCreateRoute =
  DashboardPatientsPatientIdScheduleAppointmentCreationDashboardCreateImport.update(
    {
      id: '/create',
      path: '/create',
      getParentRoute: () =>
        DashboardPatientsPatientIdScheduleAppointmentCreationDashboardRouteRoute,
    } as any,
  )

// Populate the FileRoutesByPath interface

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/': {
      id: '/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof IndexImport
      parentRoute: typeof rootRoute
    }
    '/_dashboard': {
      id: '/_dashboard'
      path: ''
      fullPath: ''
      preLoaderRoute: typeof DashboardImport
      parentRoute: typeof rootRoute
    }
    '/_dashboard/_dashboard': {
      id: '/_dashboard/_dashboard'
      path: ''
      fullPath: ''
      preLoaderRoute: typeof DashboardDashboardImport
      parentRoute: typeof DashboardImport
    }
    '/_dashboard/patients/$patientId': {
      id: '/_dashboard/patients/$patientId'
      path: '/patients/$patientId'
      fullPath: '/patients/$patientId'
      preLoaderRoute: typeof DashboardPatientsPatientIdRouteImport
      parentRoute: typeof DashboardImport
    }
    '/_dashboard/insurance/': {
      id: '/_dashboard/insurance/'
      path: '/insurance'
      fullPath: '/insurance'
      preLoaderRoute: typeof DashboardInsuranceIndexImport
      parentRoute: typeof DashboardImport
    }
    '/_dashboard/orders/': {
      id: '/_dashboard/orders/'
      path: '/orders'
      fullPath: '/orders'
      preLoaderRoute: typeof DashboardOrdersIndexImport
      parentRoute: typeof DashboardImport
    }
    '/_dashboard/patients/': {
      id: '/_dashboard/patients/'
      path: '/patients'
      fullPath: '/patients'
      preLoaderRoute: typeof DashboardPatientsIndexImport
      parentRoute: typeof DashboardImport
    }
    '/_dashboard/schedule/': {
      id: '/_dashboard/schedule/'
      path: '/schedule'
      fullPath: '/schedule'
      preLoaderRoute: typeof DashboardScheduleIndexImport
      parentRoute: typeof DashboardImport
    }
    '/_dashboard/technician/': {
      id: '/_dashboard/technician/'
      path: '/technician'
      fullPath: '/technician'
      preLoaderRoute: typeof DashboardTechnicianIndexImport
      parentRoute: typeof DashboardImport
    }
    '/_dashboard/patients/$patientId/insurance': {
      id: '/_dashboard/patients/$patientId/insurance'
      path: '/insurance'
      fullPath: '/patients/$patientId/insurance'
      preLoaderRoute: typeof DashboardPatientsPatientIdInsuranceRouteImport
      parentRoute: typeof DashboardPatientsPatientIdRouteImport
    }
    '/_dashboard/patients/$patientId/order': {
      id: '/_dashboard/patients/$patientId/order'
      path: '/order'
      fullPath: '/patients/$patientId/order'
      preLoaderRoute: typeof DashboardPatientsPatientIdOrderRouteImport
      parentRoute: typeof DashboardPatientsPatientIdRouteImport
    }
    '/_dashboard/patients/$patientId/patient-information': {
      id: '/_dashboard/patients/$patientId/patient-information'
      path: '/patient-information'
      fullPath: '/patients/$patientId/patient-information'
      preLoaderRoute: typeof DashboardPatientsPatientIdPatientInformationRouteImport
      parentRoute: typeof DashboardPatientsPatientIdRouteImport
    }
    '/_dashboard/patients/$patientId/schedule': {
      id: '/_dashboard/patients/$patientId/schedule'
      path: '/schedule'
      fullPath: '/patients/$patientId/schedule'
      preLoaderRoute: typeof DashboardPatientsPatientIdScheduleRouteImport
      parentRoute: typeof DashboardPatientsPatientIdRouteImport
    }
    '/_dashboard/patients/$patientId/visit': {
      id: '/_dashboard/patients/$patientId/visit'
      path: '/visit'
      fullPath: '/patients/$patientId/visit'
      preLoaderRoute: typeof DashboardPatientsPatientIdVisitImport
      parentRoute: typeof DashboardPatientsPatientIdRouteImport
    }
    '/_dashboard/patients/$patientId/schedule/appointment-creation': {
      id: '/_dashboard/patients/$patientId/schedule/appointment-creation'
      path: '/appointment-creation'
      fullPath: '/patients/$patientId/schedule/appointment-creation'
      preLoaderRoute: typeof DashboardPatientsPatientIdScheduleAppointmentCreationRouteImport
      parentRoute: typeof DashboardPatientsPatientIdScheduleRouteImport
    }
    '/_dashboard/patients/$patientId/insurance/insurance-verification': {
      id: '/_dashboard/patients/$patientId/insurance/insurance-verification'
      path: '/insurance-verification'
      fullPath: '/patients/$patientId/insurance/insurance-verification'
      preLoaderRoute: typeof DashboardPatientsPatientIdInsuranceInsuranceVerificationImport
      parentRoute: typeof DashboardPatientsPatientIdInsuranceRouteImport
    }
    '/_dashboard/patients/$patientId/insurance/prior-authorization': {
      id: '/_dashboard/patients/$patientId/insurance/prior-authorization'
      path: '/prior-authorization'
      fullPath: '/patients/$patientId/insurance/prior-authorization'
      preLoaderRoute: typeof DashboardPatientsPatientIdInsurancePriorAuthorizationImport
      parentRoute: typeof DashboardPatientsPatientIdInsuranceRouteImport
    }
    '/_dashboard/patients/$patientId/order/care-location': {
      id: '/_dashboard/patients/$patientId/order/care-location'
      path: '/care-location'
      fullPath: '/patients/$patientId/order/care-location'
      preLoaderRoute: typeof DashboardPatientsPatientIdOrderCareLocationImport
      parentRoute: typeof DashboardPatientsPatientIdOrderRouteImport
    }
    '/_dashboard/patients/$patientId/order/confirmation': {
      id: '/_dashboard/patients/$patientId/order/confirmation'
      path: '/confirmation'
      fullPath: '/patients/$patientId/order/confirmation'
      preLoaderRoute: typeof DashboardPatientsPatientIdOrderConfirmationImport
      parentRoute: typeof DashboardPatientsPatientIdOrderRouteImport
    }
    '/_dashboard/patients/$patientId/order/physicians': {
      id: '/_dashboard/patients/$patientId/order/physicians'
      path: '/physicians'
      fullPath: '/patients/$patientId/order/physicians'
      preLoaderRoute: typeof DashboardPatientsPatientIdOrderPhysiciansImport
      parentRoute: typeof DashboardPatientsPatientIdOrderRouteImport
    }
    '/_dashboard/patients/$patientId/order/study': {
      id: '/_dashboard/patients/$patientId/order/study'
      path: '/study'
      fullPath: '/patients/$patientId/order/study'
      preLoaderRoute: typeof DashboardPatientsPatientIdOrderStudyImport
      parentRoute: typeof DashboardPatientsPatientIdOrderRouteImport
    }
    '/_dashboard/patients/$patientId/patient-information/addresses': {
      id: '/_dashboard/patients/$patientId/patient-information/addresses'
      path: '/addresses'
      fullPath: '/patients/$patientId/patient-information/addresses'
      preLoaderRoute: typeof DashboardPatientsPatientIdPatientInformationAddressesImport
      parentRoute: typeof DashboardPatientsPatientIdPatientInformationRouteImport
    }
    '/_dashboard/patients/$patientId/patient-information/basic-information': {
      id: '/_dashboard/patients/$patientId/patient-information/basic-information'
      path: '/basic-information'
      fullPath: '/patients/$patientId/patient-information/basic-information'
      preLoaderRoute: typeof DashboardPatientsPatientIdPatientInformationBasicInformationImport
      parentRoute: typeof DashboardPatientsPatientIdPatientInformationRouteImport
    }
    '/_dashboard/patients/$patientId/patient-information/clinical-considerations': {
      id: '/_dashboard/patients/$patientId/patient-information/clinical-considerations'
      path: '/clinical-considerations'
      fullPath: '/patients/$patientId/patient-information/clinical-considerations'
      preLoaderRoute: typeof DashboardPatientsPatientIdPatientInformationClinicalConsiderationsImport
      parentRoute: typeof DashboardPatientsPatientIdPatientInformationRouteImport
    }
    '/_dashboard/patients/$patientId/patient-information/contact-information': {
      id: '/_dashboard/patients/$patientId/patient-information/contact-information'
      path: '/contact-information'
      fullPath: '/patients/$patientId/patient-information/contact-information'
      preLoaderRoute: typeof DashboardPatientsPatientIdPatientInformationContactInformationImport
      parentRoute: typeof DashboardPatientsPatientIdPatientInformationRouteImport
    }
    '/_dashboard/patients/$patientId/patient-information/guardians': {
      id: '/_dashboard/patients/$patientId/patient-information/guardians'
      path: '/guardians'
      fullPath: '/patients/$patientId/patient-information/guardians'
      preLoaderRoute: typeof DashboardPatientsPatientIdPatientInformationGuardiansImport
      parentRoute: typeof DashboardPatientsPatientIdPatientInformationRouteImport
    }
    '/_dashboard/patients/$patientId/patient-information/insurances': {
      id: '/_dashboard/patients/$patientId/patient-information/insurances'
      path: '/insurances'
      fullPath: '/patients/$patientId/patient-information/insurances'
      preLoaderRoute: typeof DashboardPatientsPatientIdPatientInformationInsurancesImport
      parentRoute: typeof DashboardPatientsPatientIdPatientInformationRouteImport
    }
    '/_dashboard/patients/$patientId/schedule/follow-up-call': {
      id: '/_dashboard/patients/$patientId/schedule/follow-up-call'
      path: '/follow-up-call'
      fullPath: '/patients/$patientId/schedule/follow-up-call'
      preLoaderRoute: typeof DashboardPatientsPatientIdScheduleFollowUpCallImport
      parentRoute: typeof DashboardPatientsPatientIdScheduleRouteImport
    }
    '/_dashboard/patients/$patientId/schedule/appointment-creation/dashboard': {
      id: '/_dashboard/patients/$patientId/schedule/appointment-creation/dashboard'
      path: '/dashboard'
      fullPath: '/patients/$patientId/schedule/appointment-creation/dashboard'
      preLoaderRoute: typeof DashboardPatientsPatientIdScheduleAppointmentCreationDashboardRouteImport
      parentRoute: typeof DashboardPatientsPatientIdScheduleAppointmentCreationRouteImport
    }
    '/_dashboard/patients/$patientId/schedule/appointment-creation/files': {
      id: '/_dashboard/patients/$patientId/schedule/appointment-creation/files'
      path: '/files'
      fullPath: '/patients/$patientId/schedule/appointment-creation/files'
      preLoaderRoute: typeof DashboardPatientsPatientIdScheduleAppointmentCreationFilesImport
      parentRoute: typeof DashboardPatientsPatientIdScheduleAppointmentCreationRouteImport
    }
    '/_dashboard/patients/$patientId/schedule/appointment-creation/notes': {
      id: '/_dashboard/patients/$patientId/schedule/appointment-creation/notes'
      path: '/notes'
      fullPath: '/patients/$patientId/schedule/appointment-creation/notes'
      preLoaderRoute: typeof DashboardPatientsPatientIdScheduleAppointmentCreationNotesImport
      parentRoute: typeof DashboardPatientsPatientIdScheduleAppointmentCreationRouteImport
    }
    '/_dashboard/patients/$patientId/schedule/appointment-creation/notifications': {
      id: '/_dashboard/patients/$patientId/schedule/appointment-creation/notifications'
      path: '/notifications'
      fullPath: '/patients/$patientId/schedule/appointment-creation/notifications'
      preLoaderRoute: typeof DashboardPatientsPatientIdScheduleAppointmentCreationNotificationsImport
      parentRoute: typeof DashboardPatientsPatientIdScheduleAppointmentCreationRouteImport
    }
    '/_dashboard/patients/$patientId/visit/pre-study/': {
      id: '/_dashboard/patients/$patientId/visit/pre-study/'
      path: '/pre-study'
      fullPath: '/patients/$patientId/visit/pre-study'
      preLoaderRoute: typeof DashboardPatientsPatientIdVisitPreStudyIndexImport
      parentRoute: typeof DashboardPatientsPatientIdVisitImport
    }
    '/_dashboard/patients/$patientId/schedule/appointment-creation/dashboard/create': {
      id: '/_dashboard/patients/$patientId/schedule/appointment-creation/dashboard/create'
      path: '/create'
      fullPath: '/patients/$patientId/schedule/appointment-creation/dashboard/create'
      preLoaderRoute: typeof DashboardPatientsPatientIdScheduleAppointmentCreationDashboardCreateImport
      parentRoute: typeof DashboardPatientsPatientIdScheduleAppointmentCreationDashboardRouteImport
    }
    '/_dashboard/patients/$patientId/schedule/appointment-creation/dashboard/preview': {
      id: '/_dashboard/patients/$patientId/schedule/appointment-creation/dashboard/preview'
      path: '/preview'
      fullPath: '/patients/$patientId/schedule/appointment-creation/dashboard/preview'
      preLoaderRoute: typeof DashboardPatientsPatientIdScheduleAppointmentCreationDashboardPreviewImport
      parentRoute: typeof DashboardPatientsPatientIdScheduleAppointmentCreationDashboardRouteImport
    }
    '/_dashboard/patients/$patientId/schedule/appointment-creation/dashboard/': {
      id: '/_dashboard/patients/$patientId/schedule/appointment-creation/dashboard/'
      path: '/'
      fullPath: '/patients/$patientId/schedule/appointment-creation/dashboard/'
      preLoaderRoute: typeof DashboardPatientsPatientIdScheduleAppointmentCreationDashboardIndexImport
      parentRoute: typeof DashboardPatientsPatientIdScheduleAppointmentCreationDashboardRouteImport
    }
  }
}

// Create and export the route tree

interface DashboardPatientsPatientIdInsuranceRouteRouteChildren {
  DashboardPatientsPatientIdInsuranceInsuranceVerificationRoute: typeof DashboardPatientsPatientIdInsuranceInsuranceVerificationRoute
  DashboardPatientsPatientIdInsurancePriorAuthorizationRoute: typeof DashboardPatientsPatientIdInsurancePriorAuthorizationRoute
}

const DashboardPatientsPatientIdInsuranceRouteRouteChildren: DashboardPatientsPatientIdInsuranceRouteRouteChildren =
  {
    DashboardPatientsPatientIdInsuranceInsuranceVerificationRoute:
      DashboardPatientsPatientIdInsuranceInsuranceVerificationRoute,
    DashboardPatientsPatientIdInsurancePriorAuthorizationRoute:
      DashboardPatientsPatientIdInsurancePriorAuthorizationRoute,
  }

const DashboardPatientsPatientIdInsuranceRouteRouteWithChildren =
  DashboardPatientsPatientIdInsuranceRouteRoute._addFileChildren(
    DashboardPatientsPatientIdInsuranceRouteRouteChildren,
  )

interface DashboardPatientsPatientIdOrderRouteRouteChildren {
  DashboardPatientsPatientIdOrderCareLocationRoute: typeof DashboardPatientsPatientIdOrderCareLocationRoute
  DashboardPatientsPatientIdOrderConfirmationRoute: typeof DashboardPatientsPatientIdOrderConfirmationRoute
  DashboardPatientsPatientIdOrderPhysiciansRoute: typeof DashboardPatientsPatientIdOrderPhysiciansRoute
  DashboardPatientsPatientIdOrderStudyRoute: typeof DashboardPatientsPatientIdOrderStudyRoute
}

const DashboardPatientsPatientIdOrderRouteRouteChildren: DashboardPatientsPatientIdOrderRouteRouteChildren =
  {
    DashboardPatientsPatientIdOrderCareLocationRoute:
      DashboardPatientsPatientIdOrderCareLocationRoute,
    DashboardPatientsPatientIdOrderConfirmationRoute:
      DashboardPatientsPatientIdOrderConfirmationRoute,
    DashboardPatientsPatientIdOrderPhysiciansRoute:
      DashboardPatientsPatientIdOrderPhysiciansRoute,
    DashboardPatientsPatientIdOrderStudyRoute:
      DashboardPatientsPatientIdOrderStudyRoute,
  }

const DashboardPatientsPatientIdOrderRouteRouteWithChildren =
  DashboardPatientsPatientIdOrderRouteRoute._addFileChildren(
    DashboardPatientsPatientIdOrderRouteRouteChildren,
  )

interface DashboardPatientsPatientIdPatientInformationRouteRouteChildren {
  DashboardPatientsPatientIdPatientInformationAddressesRoute: typeof DashboardPatientsPatientIdPatientInformationAddressesRoute
  DashboardPatientsPatientIdPatientInformationBasicInformationRoute: typeof DashboardPatientsPatientIdPatientInformationBasicInformationRoute
  DashboardPatientsPatientIdPatientInformationClinicalConsiderationsRoute: typeof DashboardPatientsPatientIdPatientInformationClinicalConsiderationsRoute
  DashboardPatientsPatientIdPatientInformationContactInformationRoute: typeof DashboardPatientsPatientIdPatientInformationContactInformationRoute
  DashboardPatientsPatientIdPatientInformationGuardiansRoute: typeof DashboardPatientsPatientIdPatientInformationGuardiansRoute
  DashboardPatientsPatientIdPatientInformationInsurancesRoute: typeof DashboardPatientsPatientIdPatientInformationInsurancesRoute
}

const DashboardPatientsPatientIdPatientInformationRouteRouteChildren: DashboardPatientsPatientIdPatientInformationRouteRouteChildren =
  {
    DashboardPatientsPatientIdPatientInformationAddressesRoute:
      DashboardPatientsPatientIdPatientInformationAddressesRoute,
    DashboardPatientsPatientIdPatientInformationBasicInformationRoute:
      DashboardPatientsPatientIdPatientInformationBasicInformationRoute,
    DashboardPatientsPatientIdPatientInformationClinicalConsiderationsRoute:
      DashboardPatientsPatientIdPatientInformationClinicalConsiderationsRoute,
    DashboardPatientsPatientIdPatientInformationContactInformationRoute:
      DashboardPatientsPatientIdPatientInformationContactInformationRoute,
    DashboardPatientsPatientIdPatientInformationGuardiansRoute:
      DashboardPatientsPatientIdPatientInformationGuardiansRoute,
    DashboardPatientsPatientIdPatientInformationInsurancesRoute:
      DashboardPatientsPatientIdPatientInformationInsurancesRoute,
  }

const DashboardPatientsPatientIdPatientInformationRouteRouteWithChildren =
  DashboardPatientsPatientIdPatientInformationRouteRoute._addFileChildren(
    DashboardPatientsPatientIdPatientInformationRouteRouteChildren,
  )

interface DashboardPatientsPatientIdScheduleAppointmentCreationDashboardRouteRouteChildren {
  DashboardPatientsPatientIdScheduleAppointmentCreationDashboardCreateRoute: typeof DashboardPatientsPatientIdScheduleAppointmentCreationDashboardCreateRoute
  DashboardPatientsPatientIdScheduleAppointmentCreationDashboardPreviewRoute: typeof DashboardPatientsPatientIdScheduleAppointmentCreationDashboardPreviewRoute
  DashboardPatientsPatientIdScheduleAppointmentCreationDashboardIndexRoute: typeof DashboardPatientsPatientIdScheduleAppointmentCreationDashboardIndexRoute
}

const DashboardPatientsPatientIdScheduleAppointmentCreationDashboardRouteRouteChildren: DashboardPatientsPatientIdScheduleAppointmentCreationDashboardRouteRouteChildren =
  {
    DashboardPatientsPatientIdScheduleAppointmentCreationDashboardCreateRoute:
      DashboardPatientsPatientIdScheduleAppointmentCreationDashboardCreateRoute,
    DashboardPatientsPatientIdScheduleAppointmentCreationDashboardPreviewRoute:
      DashboardPatientsPatientIdScheduleAppointmentCreationDashboardPreviewRoute,
    DashboardPatientsPatientIdScheduleAppointmentCreationDashboardIndexRoute:
      DashboardPatientsPatientIdScheduleAppointmentCreationDashboardIndexRoute,
  }

const DashboardPatientsPatientIdScheduleAppointmentCreationDashboardRouteRouteWithChildren =
  DashboardPatientsPatientIdScheduleAppointmentCreationDashboardRouteRoute._addFileChildren(
    DashboardPatientsPatientIdScheduleAppointmentCreationDashboardRouteRouteChildren,
  )

interface DashboardPatientsPatientIdScheduleAppointmentCreationRouteRouteChildren {
  DashboardPatientsPatientIdScheduleAppointmentCreationDashboardRouteRoute: typeof DashboardPatientsPatientIdScheduleAppointmentCreationDashboardRouteRouteWithChildren
  DashboardPatientsPatientIdScheduleAppointmentCreationFilesRoute: typeof DashboardPatientsPatientIdScheduleAppointmentCreationFilesRoute
  DashboardPatientsPatientIdScheduleAppointmentCreationNotesRoute: typeof DashboardPatientsPatientIdScheduleAppointmentCreationNotesRoute
  DashboardPatientsPatientIdScheduleAppointmentCreationNotificationsRoute: typeof DashboardPatientsPatientIdScheduleAppointmentCreationNotificationsRoute
}

const DashboardPatientsPatientIdScheduleAppointmentCreationRouteRouteChildren: DashboardPatientsPatientIdScheduleAppointmentCreationRouteRouteChildren =
  {
    DashboardPatientsPatientIdScheduleAppointmentCreationDashboardRouteRoute:
      DashboardPatientsPatientIdScheduleAppointmentCreationDashboardRouteRouteWithChildren,
    DashboardPatientsPatientIdScheduleAppointmentCreationFilesRoute:
      DashboardPatientsPatientIdScheduleAppointmentCreationFilesRoute,
    DashboardPatientsPatientIdScheduleAppointmentCreationNotesRoute:
      DashboardPatientsPatientIdScheduleAppointmentCreationNotesRoute,
    DashboardPatientsPatientIdScheduleAppointmentCreationNotificationsRoute:
      DashboardPatientsPatientIdScheduleAppointmentCreationNotificationsRoute,
  }

const DashboardPatientsPatientIdScheduleAppointmentCreationRouteRouteWithChildren =
  DashboardPatientsPatientIdScheduleAppointmentCreationRouteRoute._addFileChildren(
    DashboardPatientsPatientIdScheduleAppointmentCreationRouteRouteChildren,
  )

interface DashboardPatientsPatientIdScheduleRouteRouteChildren {
  DashboardPatientsPatientIdScheduleAppointmentCreationRouteRoute: typeof DashboardPatientsPatientIdScheduleAppointmentCreationRouteRouteWithChildren
  DashboardPatientsPatientIdScheduleFollowUpCallRoute: typeof DashboardPatientsPatientIdScheduleFollowUpCallRoute
}

const DashboardPatientsPatientIdScheduleRouteRouteChildren: DashboardPatientsPatientIdScheduleRouteRouteChildren =
  {
    DashboardPatientsPatientIdScheduleAppointmentCreationRouteRoute:
      DashboardPatientsPatientIdScheduleAppointmentCreationRouteRouteWithChildren,
    DashboardPatientsPatientIdScheduleFollowUpCallRoute:
      DashboardPatientsPatientIdScheduleFollowUpCallRoute,
  }

const DashboardPatientsPatientIdScheduleRouteRouteWithChildren =
  DashboardPatientsPatientIdScheduleRouteRoute._addFileChildren(
    DashboardPatientsPatientIdScheduleRouteRouteChildren,
  )

interface DashboardPatientsPatientIdVisitRouteChildren {
  DashboardPatientsPatientIdVisitPreStudyIndexRoute: typeof DashboardPatientsPatientIdVisitPreStudyIndexRoute
}

const DashboardPatientsPatientIdVisitRouteChildren: DashboardPatientsPatientIdVisitRouteChildren =
  {
    DashboardPatientsPatientIdVisitPreStudyIndexRoute:
      DashboardPatientsPatientIdVisitPreStudyIndexRoute,
  }

const DashboardPatientsPatientIdVisitRouteWithChildren =
  DashboardPatientsPatientIdVisitRoute._addFileChildren(
    DashboardPatientsPatientIdVisitRouteChildren,
  )

interface DashboardPatientsPatientIdRouteRouteChildren {
  DashboardPatientsPatientIdInsuranceRouteRoute: typeof DashboardPatientsPatientIdInsuranceRouteRouteWithChildren
  DashboardPatientsPatientIdOrderRouteRoute: typeof DashboardPatientsPatientIdOrderRouteRouteWithChildren
  DashboardPatientsPatientIdPatientInformationRouteRoute: typeof DashboardPatientsPatientIdPatientInformationRouteRouteWithChildren
  DashboardPatientsPatientIdScheduleRouteRoute: typeof DashboardPatientsPatientIdScheduleRouteRouteWithChildren
  DashboardPatientsPatientIdVisitRoute: typeof DashboardPatientsPatientIdVisitRouteWithChildren
}

const DashboardPatientsPatientIdRouteRouteChildren: DashboardPatientsPatientIdRouteRouteChildren =
  {
    DashboardPatientsPatientIdInsuranceRouteRoute:
      DashboardPatientsPatientIdInsuranceRouteRouteWithChildren,
    DashboardPatientsPatientIdOrderRouteRoute:
      DashboardPatientsPatientIdOrderRouteRouteWithChildren,
    DashboardPatientsPatientIdPatientInformationRouteRoute:
      DashboardPatientsPatientIdPatientInformationRouteRouteWithChildren,
    DashboardPatientsPatientIdScheduleRouteRoute:
      DashboardPatientsPatientIdScheduleRouteRouteWithChildren,
    DashboardPatientsPatientIdVisitRoute:
      DashboardPatientsPatientIdVisitRouteWithChildren,
  }

const DashboardPatientsPatientIdRouteRouteWithChildren =
  DashboardPatientsPatientIdRouteRoute._addFileChildren(
    DashboardPatientsPatientIdRouteRouteChildren,
  )

interface DashboardRouteChildren {
  DashboardDashboardRoute: typeof DashboardDashboardRoute
  DashboardPatientsPatientIdRouteRoute: typeof DashboardPatientsPatientIdRouteRouteWithChildren
  DashboardInsuranceIndexRoute: typeof DashboardInsuranceIndexRoute
  DashboardOrdersIndexRoute: typeof DashboardOrdersIndexRoute
  DashboardPatientsIndexRoute: typeof DashboardPatientsIndexRoute
  DashboardScheduleIndexRoute: typeof DashboardScheduleIndexRoute
  DashboardTechnicianIndexRoute: typeof DashboardTechnicianIndexRoute
}

const DashboardRouteChildren: DashboardRouteChildren = {
  DashboardDashboardRoute: DashboardDashboardRoute,
  DashboardPatientsPatientIdRouteRoute:
    DashboardPatientsPatientIdRouteRouteWithChildren,
  DashboardInsuranceIndexRoute: DashboardInsuranceIndexRoute,
  DashboardOrdersIndexRoute: DashboardOrdersIndexRoute,
  DashboardPatientsIndexRoute: DashboardPatientsIndexRoute,
  DashboardScheduleIndexRoute: DashboardScheduleIndexRoute,
  DashboardTechnicianIndexRoute: DashboardTechnicianIndexRoute,
}

const DashboardRouteWithChildren = DashboardRoute._addFileChildren(
  DashboardRouteChildren,
)

export interface FileRoutesByFullPath {
  '/': typeof IndexRoute
  '': typeof DashboardDashboardRoute
  '/patients/$patientId': typeof DashboardPatientsPatientIdRouteRouteWithChildren
  '/insurance': typeof DashboardInsuranceIndexRoute
  '/orders': typeof DashboardOrdersIndexRoute
  '/patients': typeof DashboardPatientsIndexRoute
  '/schedule': typeof DashboardScheduleIndexRoute
  '/technician': typeof DashboardTechnicianIndexRoute
  '/patients/$patientId/insurance': typeof DashboardPatientsPatientIdInsuranceRouteRouteWithChildren
  '/patients/$patientId/order': typeof DashboardPatientsPatientIdOrderRouteRouteWithChildren
  '/patients/$patientId/patient-information': typeof DashboardPatientsPatientIdPatientInformationRouteRouteWithChildren
  '/patients/$patientId/schedule': typeof DashboardPatientsPatientIdScheduleRouteRouteWithChildren
  '/patients/$patientId/visit': typeof DashboardPatientsPatientIdVisitRouteWithChildren
  '/patients/$patientId/schedule/appointment-creation': typeof DashboardPatientsPatientIdScheduleAppointmentCreationRouteRouteWithChildren
  '/patients/$patientId/insurance/insurance-verification': typeof DashboardPatientsPatientIdInsuranceInsuranceVerificationRoute
  '/patients/$patientId/insurance/prior-authorization': typeof DashboardPatientsPatientIdInsurancePriorAuthorizationRoute
  '/patients/$patientId/order/care-location': typeof DashboardPatientsPatientIdOrderCareLocationRoute
  '/patients/$patientId/order/confirmation': typeof DashboardPatientsPatientIdOrderConfirmationRoute
  '/patients/$patientId/order/physicians': typeof DashboardPatientsPatientIdOrderPhysiciansRoute
  '/patients/$patientId/order/study': typeof DashboardPatientsPatientIdOrderStudyRoute
  '/patients/$patientId/patient-information/addresses': typeof DashboardPatientsPatientIdPatientInformationAddressesRoute
  '/patients/$patientId/patient-information/basic-information': typeof DashboardPatientsPatientIdPatientInformationBasicInformationRoute
  '/patients/$patientId/patient-information/clinical-considerations': typeof DashboardPatientsPatientIdPatientInformationClinicalConsiderationsRoute
  '/patients/$patientId/patient-information/contact-information': typeof DashboardPatientsPatientIdPatientInformationContactInformationRoute
  '/patients/$patientId/patient-information/guardians': typeof DashboardPatientsPatientIdPatientInformationGuardiansRoute
  '/patients/$patientId/patient-information/insurances': typeof DashboardPatientsPatientIdPatientInformationInsurancesRoute
  '/patients/$patientId/schedule/follow-up-call': typeof DashboardPatientsPatientIdScheduleFollowUpCallRoute
  '/patients/$patientId/schedule/appointment-creation/dashboard': typeof DashboardPatientsPatientIdScheduleAppointmentCreationDashboardRouteRouteWithChildren
  '/patients/$patientId/schedule/appointment-creation/files': typeof DashboardPatientsPatientIdScheduleAppointmentCreationFilesRoute
  '/patients/$patientId/schedule/appointment-creation/notes': typeof DashboardPatientsPatientIdScheduleAppointmentCreationNotesRoute
  '/patients/$patientId/schedule/appointment-creation/notifications': typeof DashboardPatientsPatientIdScheduleAppointmentCreationNotificationsRoute
  '/patients/$patientId/visit/pre-study': typeof DashboardPatientsPatientIdVisitPreStudyIndexRoute
  '/patients/$patientId/schedule/appointment-creation/dashboard/create': typeof DashboardPatientsPatientIdScheduleAppointmentCreationDashboardCreateRoute
  '/patients/$patientId/schedule/appointment-creation/dashboard/preview': typeof DashboardPatientsPatientIdScheduleAppointmentCreationDashboardPreviewRoute
  '/patients/$patientId/schedule/appointment-creation/dashboard/': typeof DashboardPatientsPatientIdScheduleAppointmentCreationDashboardIndexRoute
}

export interface FileRoutesByTo {
  '/': typeof IndexRoute
  '': typeof DashboardDashboardRoute
  '/patients/$patientId': typeof DashboardPatientsPatientIdRouteRouteWithChildren
  '/insurance': typeof DashboardInsuranceIndexRoute
  '/orders': typeof DashboardOrdersIndexRoute
  '/patients': typeof DashboardPatientsIndexRoute
  '/schedule': typeof DashboardScheduleIndexRoute
  '/technician': typeof DashboardTechnicianIndexRoute
  '/patients/$patientId/insurance': typeof DashboardPatientsPatientIdInsuranceRouteRouteWithChildren
  '/patients/$patientId/order': typeof DashboardPatientsPatientIdOrderRouteRouteWithChildren
  '/patients/$patientId/patient-information': typeof DashboardPatientsPatientIdPatientInformationRouteRouteWithChildren
  '/patients/$patientId/schedule': typeof DashboardPatientsPatientIdScheduleRouteRouteWithChildren
  '/patients/$patientId/visit': typeof DashboardPatientsPatientIdVisitRouteWithChildren
  '/patients/$patientId/schedule/appointment-creation': typeof DashboardPatientsPatientIdScheduleAppointmentCreationRouteRouteWithChildren
  '/patients/$patientId/insurance/insurance-verification': typeof DashboardPatientsPatientIdInsuranceInsuranceVerificationRoute
  '/patients/$patientId/insurance/prior-authorization': typeof DashboardPatientsPatientIdInsurancePriorAuthorizationRoute
  '/patients/$patientId/order/care-location': typeof DashboardPatientsPatientIdOrderCareLocationRoute
  '/patients/$patientId/order/confirmation': typeof DashboardPatientsPatientIdOrderConfirmationRoute
  '/patients/$patientId/order/physicians': typeof DashboardPatientsPatientIdOrderPhysiciansRoute
  '/patients/$patientId/order/study': typeof DashboardPatientsPatientIdOrderStudyRoute
  '/patients/$patientId/patient-information/addresses': typeof DashboardPatientsPatientIdPatientInformationAddressesRoute
  '/patients/$patientId/patient-information/basic-information': typeof DashboardPatientsPatientIdPatientInformationBasicInformationRoute
  '/patients/$patientId/patient-information/clinical-considerations': typeof DashboardPatientsPatientIdPatientInformationClinicalConsiderationsRoute
  '/patients/$patientId/patient-information/contact-information': typeof DashboardPatientsPatientIdPatientInformationContactInformationRoute
  '/patients/$patientId/patient-information/guardians': typeof DashboardPatientsPatientIdPatientInformationGuardiansRoute
  '/patients/$patientId/patient-information/insurances': typeof DashboardPatientsPatientIdPatientInformationInsurancesRoute
  '/patients/$patientId/schedule/follow-up-call': typeof DashboardPatientsPatientIdScheduleFollowUpCallRoute
  '/patients/$patientId/schedule/appointment-creation/files': typeof DashboardPatientsPatientIdScheduleAppointmentCreationFilesRoute
  '/patients/$patientId/schedule/appointment-creation/notes': typeof DashboardPatientsPatientIdScheduleAppointmentCreationNotesRoute
  '/patients/$patientId/schedule/appointment-creation/notifications': typeof DashboardPatientsPatientIdScheduleAppointmentCreationNotificationsRoute
  '/patients/$patientId/visit/pre-study': typeof DashboardPatientsPatientIdVisitPreStudyIndexRoute
  '/patients/$patientId/schedule/appointment-creation/dashboard/create': typeof DashboardPatientsPatientIdScheduleAppointmentCreationDashboardCreateRoute
  '/patients/$patientId/schedule/appointment-creation/dashboard/preview': typeof DashboardPatientsPatientIdScheduleAppointmentCreationDashboardPreviewRoute
  '/patients/$patientId/schedule/appointment-creation/dashboard': typeof DashboardPatientsPatientIdScheduleAppointmentCreationDashboardIndexRoute
}

export interface FileRoutesById {
  __root__: typeof rootRoute
  '/': typeof IndexRoute
  '/_dashboard': typeof DashboardRouteWithChildren
  '/_dashboard/_dashboard': typeof DashboardDashboardRoute
  '/_dashboard/patients/$patientId': typeof DashboardPatientsPatientIdRouteRouteWithChildren
  '/_dashboard/insurance/': typeof DashboardInsuranceIndexRoute
  '/_dashboard/orders/': typeof DashboardOrdersIndexRoute
  '/_dashboard/patients/': typeof DashboardPatientsIndexRoute
  '/_dashboard/schedule/': typeof DashboardScheduleIndexRoute
  '/_dashboard/technician/': typeof DashboardTechnicianIndexRoute
  '/_dashboard/patients/$patientId/insurance': typeof DashboardPatientsPatientIdInsuranceRouteRouteWithChildren
  '/_dashboard/patients/$patientId/order': typeof DashboardPatientsPatientIdOrderRouteRouteWithChildren
  '/_dashboard/patients/$patientId/patient-information': typeof DashboardPatientsPatientIdPatientInformationRouteRouteWithChildren
  '/_dashboard/patients/$patientId/schedule': typeof DashboardPatientsPatientIdScheduleRouteRouteWithChildren
  '/_dashboard/patients/$patientId/visit': typeof DashboardPatientsPatientIdVisitRouteWithChildren
  '/_dashboard/patients/$patientId/schedule/appointment-creation': typeof DashboardPatientsPatientIdScheduleAppointmentCreationRouteRouteWithChildren
  '/_dashboard/patients/$patientId/insurance/insurance-verification': typeof DashboardPatientsPatientIdInsuranceInsuranceVerificationRoute
  '/_dashboard/patients/$patientId/insurance/prior-authorization': typeof DashboardPatientsPatientIdInsurancePriorAuthorizationRoute
  '/_dashboard/patients/$patientId/order/care-location': typeof DashboardPatientsPatientIdOrderCareLocationRoute
  '/_dashboard/patients/$patientId/order/confirmation': typeof DashboardPatientsPatientIdOrderConfirmationRoute
  '/_dashboard/patients/$patientId/order/physicians': typeof DashboardPatientsPatientIdOrderPhysiciansRoute
  '/_dashboard/patients/$patientId/order/study': typeof DashboardPatientsPatientIdOrderStudyRoute
  '/_dashboard/patients/$patientId/patient-information/addresses': typeof DashboardPatientsPatientIdPatientInformationAddressesRoute
  '/_dashboard/patients/$patientId/patient-information/basic-information': typeof DashboardPatientsPatientIdPatientInformationBasicInformationRoute
  '/_dashboard/patients/$patientId/patient-information/clinical-considerations': typeof DashboardPatientsPatientIdPatientInformationClinicalConsiderationsRoute
  '/_dashboard/patients/$patientId/patient-information/contact-information': typeof DashboardPatientsPatientIdPatientInformationContactInformationRoute
  '/_dashboard/patients/$patientId/patient-information/guardians': typeof DashboardPatientsPatientIdPatientInformationGuardiansRoute
  '/_dashboard/patients/$patientId/patient-information/insurances': typeof DashboardPatientsPatientIdPatientInformationInsurancesRoute
  '/_dashboard/patients/$patientId/schedule/follow-up-call': typeof DashboardPatientsPatientIdScheduleFollowUpCallRoute
  '/_dashboard/patients/$patientId/schedule/appointment-creation/dashboard': typeof DashboardPatientsPatientIdScheduleAppointmentCreationDashboardRouteRouteWithChildren
  '/_dashboard/patients/$patientId/schedule/appointment-creation/files': typeof DashboardPatientsPatientIdScheduleAppointmentCreationFilesRoute
  '/_dashboard/patients/$patientId/schedule/appointment-creation/notes': typeof DashboardPatientsPatientIdScheduleAppointmentCreationNotesRoute
  '/_dashboard/patients/$patientId/schedule/appointment-creation/notifications': typeof DashboardPatientsPatientIdScheduleAppointmentCreationNotificationsRoute
  '/_dashboard/patients/$patientId/visit/pre-study/': typeof DashboardPatientsPatientIdVisitPreStudyIndexRoute
  '/_dashboard/patients/$patientId/schedule/appointment-creation/dashboard/create': typeof DashboardPatientsPatientIdScheduleAppointmentCreationDashboardCreateRoute
  '/_dashboard/patients/$patientId/schedule/appointment-creation/dashboard/preview': typeof DashboardPatientsPatientIdScheduleAppointmentCreationDashboardPreviewRoute
  '/_dashboard/patients/$patientId/schedule/appointment-creation/dashboard/': typeof DashboardPatientsPatientIdScheduleAppointmentCreationDashboardIndexRoute
}

export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths:
    | '/'
    | ''
    | '/patients/$patientId'
    | '/insurance'
    | '/orders'
    | '/patients'
    | '/schedule'
    | '/technician'
    | '/patients/$patientId/insurance'
    | '/patients/$patientId/order'
    | '/patients/$patientId/patient-information'
    | '/patients/$patientId/schedule'
    | '/patients/$patientId/visit'
    | '/patients/$patientId/schedule/appointment-creation'
    | '/patients/$patientId/insurance/insurance-verification'
    | '/patients/$patientId/insurance/prior-authorization'
    | '/patients/$patientId/order/care-location'
    | '/patients/$patientId/order/confirmation'
    | '/patients/$patientId/order/physicians'
    | '/patients/$patientId/order/study'
    | '/patients/$patientId/patient-information/addresses'
    | '/patients/$patientId/patient-information/basic-information'
    | '/patients/$patientId/patient-information/clinical-considerations'
    | '/patients/$patientId/patient-information/contact-information'
    | '/patients/$patientId/patient-information/guardians'
    | '/patients/$patientId/patient-information/insurances'
    | '/patients/$patientId/schedule/follow-up-call'
    | '/patients/$patientId/schedule/appointment-creation/dashboard'
    | '/patients/$patientId/schedule/appointment-creation/files'
    | '/patients/$patientId/schedule/appointment-creation/notes'
    | '/patients/$patientId/schedule/appointment-creation/notifications'
    | '/patients/$patientId/visit/pre-study'
    | '/patients/$patientId/schedule/appointment-creation/dashboard/create'
    | '/patients/$patientId/schedule/appointment-creation/dashboard/preview'
    | '/patients/$patientId/schedule/appointment-creation/dashboard/'
  fileRoutesByTo: FileRoutesByTo
  to:
    | '/'
    | ''
    | '/patients/$patientId'
    | '/insurance'
    | '/orders'
    | '/patients'
    | '/schedule'
    | '/technician'
    | '/patients/$patientId/insurance'
    | '/patients/$patientId/order'
    | '/patients/$patientId/patient-information'
    | '/patients/$patientId/schedule'
    | '/patients/$patientId/visit'
    | '/patients/$patientId/schedule/appointment-creation'
    | '/patients/$patientId/insurance/insurance-verification'
    | '/patients/$patientId/insurance/prior-authorization'
    | '/patients/$patientId/order/care-location'
    | '/patients/$patientId/order/confirmation'
    | '/patients/$patientId/order/physicians'
    | '/patients/$patientId/order/study'
    | '/patients/$patientId/patient-information/addresses'
    | '/patients/$patientId/patient-information/basic-information'
    | '/patients/$patientId/patient-information/clinical-considerations'
    | '/patients/$patientId/patient-information/contact-information'
    | '/patients/$patientId/patient-information/guardians'
    | '/patients/$patientId/patient-information/insurances'
    | '/patients/$patientId/schedule/follow-up-call'
    | '/patients/$patientId/schedule/appointment-creation/files'
    | '/patients/$patientId/schedule/appointment-creation/notes'
    | '/patients/$patientId/schedule/appointment-creation/notifications'
    | '/patients/$patientId/visit/pre-study'
    | '/patients/$patientId/schedule/appointment-creation/dashboard/create'
    | '/patients/$patientId/schedule/appointment-creation/dashboard/preview'
    | '/patients/$patientId/schedule/appointment-creation/dashboard'
  id:
    | '__root__'
    | '/'
    | '/_dashboard'
    | '/_dashboard/_dashboard'
    | '/_dashboard/patients/$patientId'
    | '/_dashboard/insurance/'
    | '/_dashboard/orders/'
    | '/_dashboard/patients/'
    | '/_dashboard/schedule/'
    | '/_dashboard/technician/'
    | '/_dashboard/patients/$patientId/insurance'
    | '/_dashboard/patients/$patientId/order'
    | '/_dashboard/patients/$patientId/patient-information'
    | '/_dashboard/patients/$patientId/schedule'
    | '/_dashboard/patients/$patientId/visit'
    | '/_dashboard/patients/$patientId/schedule/appointment-creation'
    | '/_dashboard/patients/$patientId/insurance/insurance-verification'
    | '/_dashboard/patients/$patientId/insurance/prior-authorization'
    | '/_dashboard/patients/$patientId/order/care-location'
    | '/_dashboard/patients/$patientId/order/confirmation'
    | '/_dashboard/patients/$patientId/order/physicians'
    | '/_dashboard/patients/$patientId/order/study'
    | '/_dashboard/patients/$patientId/patient-information/addresses'
    | '/_dashboard/patients/$patientId/patient-information/basic-information'
    | '/_dashboard/patients/$patientId/patient-information/clinical-considerations'
    | '/_dashboard/patients/$patientId/patient-information/contact-information'
    | '/_dashboard/patients/$patientId/patient-information/guardians'
    | '/_dashboard/patients/$patientId/patient-information/insurances'
    | '/_dashboard/patients/$patientId/schedule/follow-up-call'
    | '/_dashboard/patients/$patientId/schedule/appointment-creation/dashboard'
    | '/_dashboard/patients/$patientId/schedule/appointment-creation/files'
    | '/_dashboard/patients/$patientId/schedule/appointment-creation/notes'
    | '/_dashboard/patients/$patientId/schedule/appointment-creation/notifications'
    | '/_dashboard/patients/$patientId/visit/pre-study/'
    | '/_dashboard/patients/$patientId/schedule/appointment-creation/dashboard/create'
    | '/_dashboard/patients/$patientId/schedule/appointment-creation/dashboard/preview'
    | '/_dashboard/patients/$patientId/schedule/appointment-creation/dashboard/'
  fileRoutesById: FileRoutesById
}

export interface RootRouteChildren {
  IndexRoute: typeof IndexRoute
  DashboardRoute: typeof DashboardRouteWithChildren
}

const rootRouteChildren: RootRouteChildren = {
  IndexRoute: IndexRoute,
  DashboardRoute: DashboardRouteWithChildren,
}

export const routeTree = rootRoute
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()

/* ROUTE_MANIFEST_START
{
  "routes": {
    "__root__": {
      "filePath": "__root.tsx",
      "children": [
        "/",
        "/_dashboard"
      ]
    },
    "/": {
      "filePath": "index.tsx"
    },
    "/_dashboard": {
      "filePath": "_dashboard.tsx",
      "children": [
        "/_dashboard/_dashboard",
        "/_dashboard/patients/$patientId",
        "/_dashboard/insurance/",
        "/_dashboard/orders/",
        "/_dashboard/patients/",
        "/_dashboard/schedule/",
        "/_dashboard/technician/"
      ]
    },
    "/_dashboard/_dashboard": {
      "filePath": "_dashboard/_dashboard.tsx",
      "parent": "/_dashboard"
    },
    "/_dashboard/patients/$patientId": {
      "filePath": "_dashboard/patients/$patientId/route.tsx",
      "parent": "/_dashboard",
      "children": [
        "/_dashboard/patients/$patientId/insurance",
        "/_dashboard/patients/$patientId/order",
        "/_dashboard/patients/$patientId/patient-information",
        "/_dashboard/patients/$patientId/schedule",
        "/_dashboard/patients/$patientId/visit"
      ]
    },
    "/_dashboard/insurance/": {
      "filePath": "_dashboard/insurance/index.tsx",
      "parent": "/_dashboard"
    },
    "/_dashboard/orders/": {
      "filePath": "_dashboard/orders/index.tsx",
      "parent": "/_dashboard"
    },
    "/_dashboard/patients/": {
      "filePath": "_dashboard/patients/index.tsx",
      "parent": "/_dashboard"
    },
    "/_dashboard/schedule/": {
      "filePath": "_dashboard/schedule/index.tsx",
      "parent": "/_dashboard"
    },
    "/_dashboard/technician/": {
      "filePath": "_dashboard/technician/index.tsx",
      "parent": "/_dashboard"
    },
    "/_dashboard/patients/$patientId/insurance": {
      "filePath": "_dashboard/patients/$patientId/insurance/route.tsx",
      "parent": "/_dashboard/patients/$patientId",
      "children": [
        "/_dashboard/patients/$patientId/insurance/insurance-verification",
        "/_dashboard/patients/$patientId/insurance/prior-authorization"
      ]
    },
    "/_dashboard/patients/$patientId/order": {
      "filePath": "_dashboard/patients/$patientId/order/route.tsx",
      "parent": "/_dashboard/patients/$patientId",
      "children": [
        "/_dashboard/patients/$patientId/order/care-location",
        "/_dashboard/patients/$patientId/order/confirmation",
        "/_dashboard/patients/$patientId/order/physicians",
        "/_dashboard/patients/$patientId/order/study"
      ]
    },
    "/_dashboard/patients/$patientId/patient-information": {
      "filePath": "_dashboard/patients/$patientId/patient-information/route.tsx",
      "parent": "/_dashboard/patients/$patientId",
      "children": [
        "/_dashboard/patients/$patientId/patient-information/addresses",
        "/_dashboard/patients/$patientId/patient-information/basic-information",
        "/_dashboard/patients/$patientId/patient-information/clinical-considerations",
        "/_dashboard/patients/$patientId/patient-information/contact-information",
        "/_dashboard/patients/$patientId/patient-information/guardians",
        "/_dashboard/patients/$patientId/patient-information/insurances"
      ]
    },
    "/_dashboard/patients/$patientId/schedule": {
      "filePath": "_dashboard/patients/$patientId/schedule/route.tsx",
      "parent": "/_dashboard/patients/$patientId",
      "children": [
        "/_dashboard/patients/$patientId/schedule/appointment-creation",
        "/_dashboard/patients/$patientId/schedule/follow-up-call"
      ]
    },
    "/_dashboard/patients/$patientId/visit": {
      "filePath": "_dashboard/patients/$patientId/visit.tsx",
      "parent": "/_dashboard/patients/$patientId",
      "children": [
        "/_dashboard/patients/$patientId/visit/pre-study/"
      ]
    },
    "/_dashboard/patients/$patientId/schedule/appointment-creation": {
      "filePath": "_dashboard/patients/$patientId/schedule/appointment-creation/route.tsx",
      "parent": "/_dashboard/patients/$patientId/schedule",
      "children": [
        "/_dashboard/patients/$patientId/schedule/appointment-creation/dashboard",
        "/_dashboard/patients/$patientId/schedule/appointment-creation/files",
        "/_dashboard/patients/$patientId/schedule/appointment-creation/notes",
        "/_dashboard/patients/$patientId/schedule/appointment-creation/notifications"
      ]
    },
    "/_dashboard/patients/$patientId/insurance/insurance-verification": {
      "filePath": "_dashboard/patients/$patientId/insurance/insurance-verification.tsx",
      "parent": "/_dashboard/patients/$patientId/insurance"
    },
    "/_dashboard/patients/$patientId/insurance/prior-authorization": {
      "filePath": "_dashboard/patients/$patientId/insurance/prior-authorization.tsx",
      "parent": "/_dashboard/patients/$patientId/insurance"
    },
    "/_dashboard/patients/$patientId/order/care-location": {
      "filePath": "_dashboard/patients/$patientId/order/care-location.tsx",
      "parent": "/_dashboard/patients/$patientId/order"
    },
    "/_dashboard/patients/$patientId/order/confirmation": {
      "filePath": "_dashboard/patients/$patientId/order/confirmation.tsx",
      "parent": "/_dashboard/patients/$patientId/order"
    },
    "/_dashboard/patients/$patientId/order/physicians": {
      "filePath": "_dashboard/patients/$patientId/order/physicians.tsx",
      "parent": "/_dashboard/patients/$patientId/order"
    },
    "/_dashboard/patients/$patientId/order/study": {
      "filePath": "_dashboard/patients/$patientId/order/study.tsx",
      "parent": "/_dashboard/patients/$patientId/order"
    },
    "/_dashboard/patients/$patientId/patient-information/addresses": {
      "filePath": "_dashboard/patients/$patientId/patient-information/addresses.tsx",
      "parent": "/_dashboard/patients/$patientId/patient-information"
    },
    "/_dashboard/patients/$patientId/patient-information/basic-information": {
      "filePath": "_dashboard/patients/$patientId/patient-information/basic-information.tsx",
      "parent": "/_dashboard/patients/$patientId/patient-information"
    },
    "/_dashboard/patients/$patientId/patient-information/clinical-considerations": {
      "filePath": "_dashboard/patients/$patientId/patient-information/clinical-considerations.tsx",
      "parent": "/_dashboard/patients/$patientId/patient-information"
    },
    "/_dashboard/patients/$patientId/patient-information/contact-information": {
      "filePath": "_dashboard/patients/$patientId/patient-information/contact-information.tsx",
      "parent": "/_dashboard/patients/$patientId/patient-information"
    },
    "/_dashboard/patients/$patientId/patient-information/guardians": {
      "filePath": "_dashboard/patients/$patientId/patient-information/guardians.tsx",
      "parent": "/_dashboard/patients/$patientId/patient-information"
    },
    "/_dashboard/patients/$patientId/patient-information/insurances": {
      "filePath": "_dashboard/patients/$patientId/patient-information/insurances.tsx",
      "parent": "/_dashboard/patients/$patientId/patient-information"
    },
    "/_dashboard/patients/$patientId/schedule/follow-up-call": {
      "filePath": "_dashboard/patients/$patientId/schedule/follow-up-call.tsx",
      "parent": "/_dashboard/patients/$patientId/schedule"
    },
    "/_dashboard/patients/$patientId/schedule/appointment-creation/dashboard": {
      "filePath": "_dashboard/patients/$patientId/schedule/appointment-creation/dashboard/route.tsx",
      "parent": "/_dashboard/patients/$patientId/schedule/appointment-creation",
      "children": [
        "/_dashboard/patients/$patientId/schedule/appointment-creation/dashboard/create",
        "/_dashboard/patients/$patientId/schedule/appointment-creation/dashboard/preview",
        "/_dashboard/patients/$patientId/schedule/appointment-creation/dashboard/"
      ]
    },
    "/_dashboard/patients/$patientId/schedule/appointment-creation/files": {
      "filePath": "_dashboard/patients/$patientId/schedule/appointment-creation/files.tsx",
      "parent": "/_dashboard/patients/$patientId/schedule/appointment-creation"
    },
    "/_dashboard/patients/$patientId/schedule/appointment-creation/notes": {
      "filePath": "_dashboard/patients/$patientId/schedule/appointment-creation/notes.tsx",
      "parent": "/_dashboard/patients/$patientId/schedule/appointment-creation"
    },
    "/_dashboard/patients/$patientId/schedule/appointment-creation/notifications": {
      "filePath": "_dashboard/patients/$patientId/schedule/appointment-creation/notifications.tsx",
      "parent": "/_dashboard/patients/$patientId/schedule/appointment-creation"
    },
    "/_dashboard/patients/$patientId/visit/pre-study/": {
      "filePath": "_dashboard/patients/$patientId/visit/pre-study/index.tsx",
      "parent": "/_dashboard/patients/$patientId/visit"
    },
    "/_dashboard/patients/$patientId/schedule/appointment-creation/dashboard/create": {
      "filePath": "_dashboard/patients/$patientId/schedule/appointment-creation/dashboard/create.tsx",
      "parent": "/_dashboard/patients/$patientId/schedule/appointment-creation/dashboard"
    },
    "/_dashboard/patients/$patientId/schedule/appointment-creation/dashboard/preview": {
      "filePath": "_dashboard/patients/$patientId/schedule/appointment-creation/dashboard/preview.tsx",
      "parent": "/_dashboard/patients/$patientId/schedule/appointment-creation/dashboard"
    },
    "/_dashboard/patients/$patientId/schedule/appointment-creation/dashboard/": {
      "filePath": "_dashboard/patients/$patientId/schedule/appointment-creation/dashboard/index.tsx",
      "parent": "/_dashboard/patients/$patientId/schedule/appointment-creation/dashboard"
    }
  }
}
ROUTE_MANIFEST_END */
