import React, { cloneElement, CSSProperties, isValidElement } from 'react';
import { Stack, Box, Typography, useTheme, ListItem as ListItemPrimitive, ListItemProps as ListItemPrimitiveProps } from '@mui/material';
import { ReactNode } from '@tanstack/react-router';

type Size = keyof typeof ICON_SIZE_S;

type ListItemProps = {
  icon?: React.ReactNode;
  title: String;
  subTitle?: String;
  extra?: React.ReactNode;
  size: Size
  leading?: ReactNode
} & ListItemPrimitiveProps;

const ICON_SIZE_S: Record<string, CSSProperties> = {
  large: {
    width: '1.875rem',
    height: '1.875rem'
  },
  small: {
    width: '1.5rem',
    height: '1.5rem'
  }
}

const ListItem = ({ icon, title, subTitle, extra, size, leading, ...rest }: ListItemProps) => {
  const { palette } = useTheme();

  return (
    <ListItemPrimitive
      sx={{
        display: 'flex',
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        userSelect: 'none',
      }}
      {...rest}
    >
      {/* leading */}
      {leading && <Stack direction={'row'} gap={1} alignItems={'center'}>{leading}</Stack>}

      <Stack flexDirection="row" alignItems="center" gap='12px'>
        <Box
          sx={{
            color: palette.primary.dark,
          }}
        >
          {isValidElement(icon) && cloneElement(icon, { style: (ICON_SIZE_S[size]) } as any)}
        </Box>
        <Stack >
          <Typography variant={size == 'large' ? 'h6' : 'body1'} color='primary.dark'>{title} </Typography>
          {subTitle && (
            <Typography
              variant={'asapCondensed'}
              color='primary.dark'
            >
              {subTitle}
            </Typography>
          )}
        </Stack>
      </Stack>

      {/* extra */}
      {extra && <Stack direction={'row'} gap={1} alignItems={'center'}>{extra}</Stack>}
    </ListItemPrimitive>
  );
};

export default ListItem;
