import React from 'react';
import { Box, Button, ButtonProps, Stack } from '@mui/material';
import CardFooter from '@components/card-footer';

type ButtonData = {
  label: string;
  onClick?: ButtonProps['onClick'];
  disabled?: boolean;
  'data-testid'?: string;
}

export interface MainCardFooterProps {
  primaryButton1?: ButtonData;
  primaryButton2?: ButtonData;
  secondaryButton1?: ButtonData;
  secondaryButton2?: ButtonData;
  color?: 'primary' | 'success' | 'error' | 'warning' | 'gray';
  backgroundColor: string;
}

const MainCardFooter: React.FC<MainCardFooterProps> = ({
  primaryButton1,
  primaryButton2,
  secondaryButton1,
  secondaryButton2,
  color = 'primary',
  backgroundColor,
}) => {
  return (
    <CardFooter sx={{ backgroundColor }}>
      <Stack direction="row" justifyContent="space-between" width="100%">
        <Box display="flex" flexDirection="row" gap={2}>
          {secondaryButton2 && (
            <Button
              onClick={secondaryButton2.onClick}
              disabled={secondaryButton2.disabled}
              color={color === 'gray' ? 'primary' : color}
              {...secondaryButton2}
            >
              {secondaryButton2.label}
            </Button>
          )}
          {secondaryButton1 && (
            <Button
              onClick={secondaryButton1.onClick}
              disabled={secondaryButton1.disabled}
              color={color === 'gray' ? 'primary' : color}
              {...secondaryButton1}
            >
              {secondaryButton1.label}
            </Button>
          )}
        </Box>
        <Box display="flex" flexDirection="row" gap={2}>
          {primaryButton2 && (
            <Button
              variant="outlined"
              onClick={primaryButton2.onClick}
              disabled={primaryButton2.disabled}
              color={color === 'gray' ? 'primary' : color}
              {...primaryButton2}
            >
              {primaryButton2.label}
            </Button>
          )}
          {primaryButton1 && (
            <Button
              variant="contained"
              onClick={primaryButton1.onClick}
              disabled={primaryButton1.disabled}
              color={color === 'gray' ? 'primary' : color}
              {...primaryButton1}
            >
              {primaryButton1.label}
            </Button>
          )}
        </Box>
      </Stack>
    </CardFooter>
  );
};

export default MainCardFooter;