import {
	<PERSON><PERSON>,
	TextField,
	InputAdornment,
	Box,
	Button,
	Grid,
	debounce,
	CircularProgress,
} from '@mui/material';
import { Hospital, Search } from 'lucide-react';
import ChipSummary from '@components/chip-summary';
import { useEffect, useMemo, useState } from 'react';
import { useAppForm } from '@hooks/app-form';
import { useStore } from '@tanstack/react-store';
import StepCardControl from '@components/step-card-control';
import { useQuery } from '@tanstack/react-query';
import { PatientCreate, PatientRead } from '@auth/scopes';
import { Query, CareLocationQuery } from '@utils/query-dsl';
import { postApiCareLocationSearchOptions } from '@client/workflows/@tanstack/react-query.gen';
import MainCardContainer from '@components/main-container/main-card-container';
import { OrderState, StepProps } from '@features/order-create/types';
import useOrder from '@features/order-create/hooks/use-order';
import dayjs from 'dayjs';
import { map } from 'lodash';
import NotificationBanner from '@components/notification-banner';

type Location = {
	id: string;
	label: string;
	data: Array<Array<{ label?: string; value: string }>>;
};

type FormDataTypes = {
	location: Location | null;
};

const defaultValues: FormDataTypes = {
	location: null,
};

export default function CareLocation({ orderId, successCallback }: StepProps) {
	const [userSearchValue, setUserearchValue] = useState('');
	const [searchValue, setSearchValue] = useState('');

	const { orderData, updateOrder } = useOrder({ orderId });

	const form = useAppForm({
		defaultValues,
		onSubmit: ({ value }) => {
			const { data } = orderData;
			const { _state } = data;
			const { flowState, stepState } = _state as unknown as OrderState;
			updateOrder(
				{
					...data,
					careLocationId: value.location?.id,
				},
				{
					flowState: {
						...flowState,
						status: 'InProgress',
						progress: 50,
						lastUpdate: dayjs().format('MMM D, YYYY h:mm A'),
					},
					stepState: {
						...stepState,
						AddCareLocation: 'Complete',
						AddPhysicians: 'InProgress',
					},
				},
				successCallback
			);
		},
	});

	const values = useStore(form.store, (state) => state.values);

	const debouncedSearch = useMemo(
		() =>
			debounce((value: string) => {
				setSearchValue(value);
			}, 300),
		[]
	);

	const onSelect = (selectedValue: Location) => {
		if (selectedValue === values.location) {
			form.setFieldValue('location', null);
			return;
		}
		form.setFieldValue('location', selectedValue);
	};

	useEffect(() => {
		debouncedSearch(userSearchValue);
	}, [debouncedSearch, searchValue, userSearchValue]);

	const {
		data: careLocations,
		isFetching: isCareLocationFetching,
		error,
	} = useQuery({
		...postApiCareLocationSearchOptions({
			scopes: [PatientCreate.value, PatientRead.value],
			responseType: 'json',
			// @ts-expect-error Type Missmatch
			body: searchValue
				? Query.literal(CareLocationQuery.withApproximateName(searchValue))
				: undefined,
		}),
		select: (data) =>
			map(data?.items, (item) => ({
				id: item.id,
				label: item.name,
				data: [
					map(item.contactDetail?.phoneNumbers, (phoneNumber) => ({
						label: 'Phone',
						value: phoneNumber.phoneNumber,
					})),
					map(item.contactDetail?.emails, (email) => ({
						label: 'Email',
						value: email.email,
					})),
					map(item.supportedEncounterTypes, (encounterType) => ({
						label: 'Encounter Type',
						value: encounterType.toString(),
					})),
					map(item.supportedStudyTypes, (studyType) => ({
						label: 'Study Type',
						value: studyType.toString(),
					})),
				],
			})) ?? [],
		initialData: {
			items: [],
		},
	});

	return (
		<Box
			component="form"
			p={2}
			sx={{ height: '100%' }}
			onSubmit={(e) => {
				e.preventDefault();
				form.handleSubmit();
			}}>
			<MainCardContainer
				title="Care Location"
				icon={<Hospital />}
				color="primary"
				emphasis={values.location ? 'high' : 'low'}
				containerSx={{ height: '100%', display: 'flex', flexDirection: 'column' }}
				sx={{ flex: 1, minHeight: 0, mb: -2 }}
				descriptionSubheader="Select the care location where the study will be performed."
				descriptionText="The physician selection will be based on your location choice.">
				<Stack sx={{ height: '100%', minHeight: 0 }}>
					<Grid
						container
						spacing={2}>
						<Grid
							item
							md={2}>
							<TextField
								label="All Types"
								fullWidth
								disabled
							/>
						</Grid>
						<Grid
							item
							md={10}>
							<TextField
								value={userSearchValue}
								onChange={(e) => setUserearchValue(e.target.value)}
								variant="outlined"
								placeholder="Search locations by name or address.."
								fullWidth
								slotProps={{
									input: {
										startAdornment: (
											<InputAdornment position="start">
												<Search />
											</InputAdornment>
										),
									},
								}}
							/>
						</Grid>
					</Grid>
					<NotificationBanner
						message={error?.message}
						severity="error"
						scrollIntoView
					/>
					{isCareLocationFetching ? (
						<Box sx={{ py: 2, display: 'flex', justifyContent: 'center' }}>
							<CircularProgress />
						</Box>
					) : (
						<Stack
							spacing={2}
							sx={{ mt: 2, flex: 1, overflow: 'auto', minHeight: 0 }}>
							{Array.isArray(careLocations) &&
								careLocations?.map((item, index) => {
									const { label: title, data } = item;

									const checked = item?.id === values?.location?.id;

									return (
										<Box key={index.toString()}>
											<MainCardContainer
												title={title}
												color={checked ? 'primary' : 'gray'}
												emphasis={checked ? 'high' : 'low'}
												sx={{ minHeight: 'content' }}
												onClick={() => onSelect(item)}
												secondaryActionType="Selection"
												selectionSelected={checked}
												selectionLabel={checked ? 'Selected' : 'Select'}
												onSelectionChange={() => void onSelect(item)}>
												<Stack gap={1}>
													{data.map((d, dIndex) => (
														<ChipSummary
															key={dIndex.toString()}
															items={d.map(({ ...item }) => ({
																label: item.label ? item.label : '',
																value: item.value,
															}))}
															hideSeperator
														/>
													))}
												</Stack>
											</MainCardContainer>
										</Box>
									);
								})}
						</Stack>
					)}
				</Stack>
			</MainCardContainer>
			<Box>
				<StepCardControl>
					<Button
						variant="outlined"
						color="primary">
						Save Draft
					</Button>
					<form.Subscribe>
						{({ isDirty, canSubmit, isSubmitting }) => (
							<Button
								variant="contained"
								color="primary"
								type="submit"
								loading={isSubmitting}
								disabled={!isDirty || !canSubmit}>
								Next
							</Button>
						)}
					</form.Subscribe>
				</StepCardControl>
			</Box>
		</Box>
	);
}
