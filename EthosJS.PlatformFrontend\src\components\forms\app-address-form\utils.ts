import { EthosWorkflowsApiAddressWithUseTypeDto } from '@client/workflows';

// TODO: Get this from reference data
export const USA_CODE = 1541;

export const defaultValues = {
	use: null!,
	type: null!,
	address: {
		line1: '',
		line2: '',
		city: '',
		state: null!,
		postalCode: '',
		country: USA_CODE,
	},
};

export function addressFormOptions(
	savedData?: EthosWorkflowsApiAddressWithUseTypeDto,
	use?: number
) {
	return {
		defaultValues: {
			...defaultValues,
			use: use ?? defaultValues.use,
			...(savedData ? savedData : {}),
		},
	};
}
