import ChipSummary from "@components/chip-summary";
import List from "@components/list";
import MainCardContainer from "@components/main-container/main-card-container";
import useCreateAppointmentStore from "@hooks/use-create-appointment-store";
import { Stack } from "@mui/material";
import { Signature } from "lucide-react";

const confirmedResource = [
   'Female technician preferred',
   'Patient needs interpreter (Specify: Spanish)',
   'Equipment Reserved',
   'Room Reserved'
]

const Review = () => {
   const { store, actions } = useCreateAppointmentStore();

   return (
      <Stack gap={2}>
         <MainCardContainer title="Resources Confirmed">
            <ChipSummary hideSeperator items={confirmedResource.map((i) => ({ value: i }))} />
         </MainCardContainer>
         <MainCardContainer title="Appointment Details">
            <ChipSummary
               items={[
                  {
                     label: 'Date',
                     value: ' October 22, 2025'
                  },
                  {
                     label: 'Time',
                     value: '11:00 AM - 5:00 PM'
                  },
                  {
                     label: 'Study Type',
                     value: 'PSG (Polysomnography)'
                  },
                  {
                     label: 'Location',
                     value: 'Northwest Sleep Center'
                  },
                  {
                     label: 'Scheduled By',
                     value: '<PERSON>'
                  }
               ]}
            />
         </MainCardContainer>
         <MainCardContainer title="Enter any additional notes for this appointment" primaryActionType="Add">
            <List
               items={[
                  {
                     title: 'Appointment Confirmation',
                     subTitle: 'Do you confirm all appointment details are correct?',
                     value: 'isConfirmed',
                     icon: <Signature />
                  }
               ]}
               selectable
               renderSelectionLabel={() => 'Yes'}
               selectedItems={store.state.values.isConfirmed ? ['isConfirmed'] : []}
               onSelectItems={(value) => {
                  actions.setValues({ isConfirmed: !!value.length })
               }}
            />
         </MainCardContainer>
      </Stack>
   )
}

export default Review;