import SideBar from '@components/global-navigation/sidebar';
import { APP_BRANDING } from '@config/app';
import { Box, IconButton, Typography } from '@mui/material';
import { useNavigate } from '@tanstack/react-router';

import { Notifications, Home, Logout, Security } from '@mui/icons-material';
import LiveSupportLink from '@components/live-support-link';
import { useMsal } from '@azure/msal-react';

import Header from '@components/global-navigation/header/index';
import Breadcrumbs from '@components/breadcrumbs';
import { useAccount } from '@azure/msal-react';
import { useEffect, useMemo, useState } from 'react';
import { ShoppingBag, Users } from 'lucide-react';

interface IDashboard {
	children: React.ReactNode;
}

const Dashboard: React.FC<IDashboard> = ({ children }) => {
	const navigate = useNavigate();
	const { instance } = useMsal();

	const account = useAccount();

	const [open, setOpen] = useState(() => {
		const savedState = localStorage.getItem('sidebarState');
		return savedState ? JSON.parse(savedState) : true;
	});

	const userName = account?.name || account?.username || 'Username unavailable';
	const role = 'Role unavailable';

	const handleLogout = useMemo(
		() => async () => {
			try {
				await instance.logoutRedirect();
			} catch (error) {
				console.error(error);
			}
		},
		[instance]
	);

	// Update localStorage when sidebar state changes
	useEffect(() => {
		localStorage.setItem('sidebarState', JSON.stringify(open));
	}, [open]);

	return (
		<Box
			sx={{
				display: 'flex',
				width: '100vw',
				height: '100vh',
				overflow: 'hidden',
			}}>
			<SideBar
				collapsed={open}
				onToggleCollapse={setOpen}
				menuProps={{
					items: [
						{
							text: 'Patients',
							icon: <Users />,
							path: '/patients',
						},
						{
							text: 'Orders',
							icon: <ShoppingBag />,
							path: '/orders',
						},
						{
							text: 'Insurance',
							icon: <Security />,
							path: '/insurance',
						},
					],
				}}
				header={
					<Box
						sx={{
							display: 'inline-flex',
							justifyContent: 'start',
							alignItems: 'center',
							overflowX: 'hidden',
						}}>
						<img
							alt="brand-logo"
							src="/images/brand.svg"
							width="180px"
						/>
					</Box>
				}
				brandingLabelProps={{
					label: APP_BRANDING.SIDER.BRANDING_LABEL,
				}}
				footer={{
					expanded: (
						<Box sx={{ width: '100%', px: 1 }}>
							<LiveSupportLink sideBarOpen={true} />
						</Box>
					),
					collapsed: (
						<Box sx={{ width: '100%', justifyContent: 'center', display: 'flex', px: 1 }}>
							<LiveSupportLink sideBarOpen={false} />
						</Box>
					),
				}}
			/>
			<Box
				component="main"
				sx={{ flex: 1, display: 'flex', flexDirection: 'column', minWidth: 0 }}>
				<Header
					{...{
						content: (
							<>
								{import.meta.env.VITE_APP_ENV && (
									<Box sx={{ display: 'flex', alignItems: 'center' }}>
										<Typography
											variant="caption"
											sx={{
												px: 1,
												py: 0.5,
												borderRadius: 1,
												fontWeight: 'bold',
											}}>
											ENVIRONMENT
										</Typography>
										<Typography
											variant="caption"
											sx={{
												bgcolor: 'warning.main',
												color: 'warning.contrastText',
												px: 1,
												py: 0.5,
												borderRadius: 1,
												fontWeight: 'bold',
											}}>
											{import.meta.env.VITE_APP_ENV}
										</Typography>
									</Box>
								)}
								<Box sx={{ flexGrow: 1, flexShrink: 1 }}></Box>
							</>
						),
						profileProps: {
							title: userName,
							description: role,
							popoverProps: {
								onMenuItemSelect: ({ value }) => {
									if (value === 'logout') handleLogout();
								},
								items: [
									{
										title: 'Logout',
										value: 'logout',
										leadingIcon: <Logout />,
									},
								],
							},
						},
						actionItems: [
							<IconButton
								size="small"
								color="inherit"
								aria-label="notifications"
								onClick={() => navigate({ to: '/' })}>
								<Home />
							</IconButton>,
							<IconButton key="notifications">
								<Notifications />
							</IconButton>,
						],
					}}
				/>
				<Breadcrumbs />
				<Box sx={{ flex: 1, minHeight: 0 }}>{children}</Box>
			</Box>
		</Box>
	);
};

export default Dashboard;
