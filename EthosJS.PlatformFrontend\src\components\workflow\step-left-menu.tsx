import { Step } from "src/workflows/Step";
import LeftMenu from "../left-menu";
import { Person } from "@mui/icons-material";
import { useStore } from "@tanstack/react-store";

export default function StepLeftMenu({ onClick, step, stepNumber }: { onClick: (step: Step) => void; step: Step; stepNumber: number }) {

    const { status, stepMenuDisable } = useStore(step.stepStore, (state) => ({
        status: state.status,
        // stepMenuDisable: state.stepMenuDisable
        stepMenuDisable: false
    }));

    return (
        <LeftMenu name={step.displayName} icon={Person} status={status} stepNumber={stepNumber} disabled={stepMenuDisable} onClick={() => onClick(step)} />
    )
}