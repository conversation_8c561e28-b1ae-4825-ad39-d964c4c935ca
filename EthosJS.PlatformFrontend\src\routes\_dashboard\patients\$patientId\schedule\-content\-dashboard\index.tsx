import ChipSummary from "@components/chip-summary"
import CollapsibleMainCardContainer from "@components/collapsible-main-card-container"
import MainCardContainer, { MainCardContainerProps } from "@components/main-container/main-card-container"
import { ArrowForward, ListAltOutlined } from "@mui/icons-material"
import { Button, ButtonProps, Stack } from "@mui/material"
import { Contact, MapPin, PhoneCall } from "lucide-react"
import AccountCircleOutlinedIcon from '@mui/icons-material/AccountCircleOutlined';
import MonitorHeartOutlinedIcon from '@mui/icons-material/MonitorHeartOutlined';
import SmartButtonOutlinedIcon from '@mui/icons-material/SmartButtonOutlined';
import { useQuery } from "@tanstack/react-query"
import { getApiPatientByIdOptions, getApiStudyByIdOptions } from "@client/workflows/@tanstack/react-query.gen"
import { PatientCreate, PatientRead } from "@auth/scopes"
import { useMemo } from "react"

interface IDashboard {
  onStartCreateAppoinment?: ButtonProps['onClick']
  studyId: string
  patientId: string
}

const Dashboard = ({ studyId, patientId, onStartCreateAppoinment }: IDashboard) => {

  return (
    <Stack gap={2}>
      <MainCardContainer
        title='Create an appointment'
        icon={<SmartButtonOutlinedIcon />}
        customAction={(
          <Button
            color='warning'
            variant='contained'
            startIcon={<ArrowForward />}
            onClick={onStartCreateAppoinment}
          >
            Start Here
          </Button>
        )}
      />
      <StudyDetails studyId={studyId} />
      <PatientDetails patientId={patientId} />
    </Stack>
  )
}

const subContainerProps: Partial<MainCardContainerProps> = {
  color: 'gray',
  emphasis: 'low'
}

const StudyDetails = ({ studyId }: { studyId: string }) => {

  useQuery({
    ...getApiStudyByIdOptions({
      scopes: [PatientCreate.value, PatientRead.value],
      path: { id: studyId }
    }),
    enabled: !!studyId
  });

  return (
    <CollapsibleMainCardContainer
      mainContainerProps={{
        title: 'Study Details',
        icon: <MonitorHeartOutlinedIcon />,
      }}
      defaultCollapse
    >
      <Stack gap={2}>
        <MainCardContainer {...subContainerProps} icon={<MonitorHeartOutlinedIcon />} title='Study Information' >
          <ChipSummary
            items={[
              {
                label: 'Provider',
                value: 'Dr. Sarah Brown'
              },
              {
                label: 'Encounter Type',
                value: 'Sleep'
              },
              {
                label: 'Study Type',
                value: 'PSG (Polysomnography)'
              }
            ]}
          />
        </MainCardContainer>
        <MainCardContainer {...subContainerProps} icon={<MonitorHeartOutlinedIcon />} title='Study Parameters' >
          <ChipSummary
            hideSeperator
            items={[
              {
                value: 'ETCO2 Monitoring'
              },
              {
                value: 'Extended EEG'
              },
              {
                value: 'Parasomnia Assessment'
              },
              {
                value: 'Wheelchair accessible'
              },
              {
                value: 'Female technician preferred'
              }
            ]}
          />
        </MainCardContainer>
        <MainCardContainer {...subContainerProps} icon={<MapPin />} title='Location Details' >
          <ChipSummary
            items={[
              {
                label: 'Care Location',
                value: 'Northwest Sleep Center'
              },
              {
                label: 'Phone',
                value: '(*************'
              },
              {
                label: 'Fax',
                value: '(*************'
              },
              {
                label: 'Address',
                value: '1708 S Yakima Avenue, Ste 105 Tacoma WA 98405-5307'
              }
            ]}
          />
        </MainCardContainer>
      </Stack>
    </CollapsibleMainCardContainer>
  )
}

const mergeString = (strings: (string | undefined | null)[], devider: string = ' ') => {
  return strings.filter(Boolean).join(devider)
}

const PatientDetails = ({ patientId }: { patientId: string }) => {

  const { data: patientDetails } = useQuery({
    ...getApiPatientByIdOptions({
      responseType: 'json',
      scopes: [PatientCreate.value, PatientRead.value],
      path: { id: patientId }
    }),
  });

  const identityName = useMemo(() => patientDetails?.names?.at(0), [patientDetails?.names]);

  return (
    <CollapsibleMainCardContainer
      mainContainerProps={{
        title: 'Patient Details',
        icon: <AccountCircleOutlinedIcon />,
      }}
      defaultCollapse
    >
      <Stack gap={2}>
        <MainCardContainer
          {...subContainerProps}
          icon={<Contact />}
          title='Patient Information'
        >
          <ChipSummary
            items={[
              {
                label: 'Full Name',
                value: mergeString([identityName?.firstName, identityName?.lastName])
              },
              {
                label: 'Patient ID',
                value: '82-1757564'
              },
              {
                label: 'DOB',
                value: '06/01/1969'
              }
            ]}
          />
        </MainCardContainer>
        <MainCardContainer {...subContainerProps} icon={<PhoneCall />} title='Contact Information' >
          <ChipSummary
            items={[
              {
                label: 'Primary',
                value: '(*************'
              },
              {
                label: 'Alternative',
                value: '(*************'
              },
              {
                label: 'Best time',
                value: 'Afternoons (1-5 PM)'
              }
            ]}
          />
        </MainCardContainer>
        <MainCardContainer {...subContainerProps} icon={<ListAltOutlined />} title='Patient Preferences & Requirements' >
          <ChipSummary
            hideSeperator
            items={[
              {
                value: 'Female technician preferred'
              },
              {
                value: 'Patient needs interpreter (Specify: Spanish)'
              },
              {
                value: 'Special accommodations required'
              },
              {
                value: 'Wheelchair accessible room needed'
              }
            ]}
          />
        </MainCardContainer>
      </Stack>
    </CollapsibleMainCardContainer>
  )
}

export default Dashboard;
