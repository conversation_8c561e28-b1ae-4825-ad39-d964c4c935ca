import { defineConfig, defaultPlugins, UserConfig } from '@hey-api/openapi-ts';
import * as dotenv from 'dotenv';
import * as path from 'path';
import * as fs from 'fs';

// Load environment variables from .env.local if it exists
const envLocalPath = path.resolve(process.cwd(), '.env.local');
const envLocaldevPath = path.resolve(process.cwd(), '.env.localdev');

if (fs.existsSync(envLocalPath)) {
	dotenv.config({ path: envLocalPath });
} else if (fs.existsSync(envLocaldevPath)) {
	dotenv.config({ path: envLocaldevPath });
} else {
	dotenv.config();
}

// Determine which configuration to use based on NODE_ENV
const env = process.env.NODE_ENV || 'workflows';

// Get API URLs from environment variables or use defaults for local development
const workflowsApiUrl = process.env.VITE_WORKFLOWS_API_URL || 'http://localhost:4000';
const refdataApiUrl = process.env.VITE_REFDATA_API_URL || 'http://localhost:4004';
const rolesApiUrl = process.env.VITE_ROLES_API_URL || 'http://localhost:4006';

// Common plugins configuration
const commonPlugins: UserConfig['plugins'] = [
	...defaultPlugins,
	'@hey-api/client-axios',
	'@tanstack/react-query',
];

// Configuration for workflows API
const workflowsConfig: UserConfig = {
	input: `${workflowsApiUrl}/api/openapi.json`,
	output: 'src/client/workflows',
	plugins: commonPlugins,
};

// Configuration for reference data API
const refdataConfig: UserConfig = {
	input: `${refdataApiUrl}/api/openapi.json`,
	output: 'src/client/refdata',
	plugins: commonPlugins,
};

// Configuration for roles API
const rolesConfig: UserConfig = {
	input: `${rolesApiUrl}/api/openapi.json`,
	output: 'src/client/roles',
	plugins: commonPlugins,
};

// Export the appropriate configuration based on NODE_ENV
export default defineConfig(
	env === 'refdata' ? refdataConfig : env === 'roles' ? rolesConfig : workflowsConfig
);
