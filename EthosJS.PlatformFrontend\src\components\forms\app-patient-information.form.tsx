import ChipSummary from '@components/chip-summary';
import MainCardContainer from '@components/main-container/main-card-container';
import { PatientBasicInformationData } from '@features/patient-create/types';
import { withForm } from '@hooks/app-form';
import { Grid2 as Grid } from '@mui/material';
import { useStore } from '@tanstack/react-store';
import { formHasErrors } from '@utils/forms';
import { maskSocialSecurity } from '@utils/maskers';
import { UserCircle } from 'lucide-react';
import { useState } from 'react';

const PatientInformationForm = withForm({
	props: {
		isUpdate: false,
	},
	render: function Render({ form, isUpdate }) {
		const hasErrors = formHasErrors(form, 'patientInformation');
		const values = useStore(form.store, (state) => state.values) as PatientBasicInformationData;
		const [editView, setEditView] = useState(!isUpdate);

		return (
			<MainCardContainer
				title="Patient Information"
				icon={<UserCircle />}
				color={hasErrors ? 'error' : 'primary'}
				emphasis={hasErrors ? 'high' : 'low'}
				primaryActionType={editView ? 'none' : 'Edit'}
				onPrimaryAction={() => setEditView(true)}>
				{editView ? (
					<Grid
						container
						spacing={2}>
						<Grid
							size={{
								xs: 12,
								sm: 4,
							}}>
							<form.AppField
								name="patientInformation.prefix"
								children={(field) => (
									<field.AppAutocompleteField
										key="patientInformation.prefix.namePrefix"
										label="Prefix"
										referenceDataSetName="namePrefix"
										data-testid="patientInformation.prefix"
									/>
								)}
							/>
						</Grid>
						<Grid size={{ xs: 0, sm: 8 }}></Grid>
						<Grid
							size={{
								xs: 12,
								sm: 4,
							}}>
							<form.AppField
								name="patientInformation.firstName"
								children={(field) => (
									<field.AppTextField
										label="First Name"
										required
									/>
								)}
							/>
						</Grid>
						<Grid size={{ xs: 12, sm: 4 }}>
							<form.AppField
								name="patientInformation.middleName"
								children={(field) => <field.AppTextField label="Middle Name" />}
							/>
						</Grid>
						<Grid size={{ xs: 12, sm: 4 }}>
							<form.AppField
								name="patientInformation.lastName"
								children={(field) => (
									<field.AppTextField
										label="Last Name"
										required
									/>
								)}
							/>
						</Grid>
						<Grid size={{ xs: 12, sm: 4 }}>
							<form.AppField
								name="patientInformation.suffix"
								children={(field) => (
									<field.AppAutocompleteField
										label="Suffix"
										referenceDataSetName="nameSuffix"
									/>
								)}
							/>
						</Grid>
						<Grid size={{ xs: 0, sm: 8 }}></Grid>
						<Grid size={{ xs: 12, md: 6 }}>
							<form.AppField
								name="patientInformation.ssn"
								children={(field) => (
									<field.AppSsnField
										label={'Social Security number'}
										required
									/>
								)}
							/>
						</Grid>
					</Grid>
				) : (
					<ChipSummary
						items={[
							{
								label: 'First Name',
								value: values?.patientInformation?.firstName || '',
							},
							{
								label: 'Last Name',
								value: values?.patientInformation?.lastName || '',
							},
							{
								label: 'Social Security Number',
								value: values?.patientInformation?.ssn
									? maskSocialSecurity(values?.patientInformation?.ssn)
									: '',
							},
							{
								label: 'Ethos Patient ID',
								value: values?.patientInformation?.mrn ?? 'N/A',
							},
						]}
					/>
				)}
			</MainCardContainer>
		);
	},
});

export default PatientInformationForm;
