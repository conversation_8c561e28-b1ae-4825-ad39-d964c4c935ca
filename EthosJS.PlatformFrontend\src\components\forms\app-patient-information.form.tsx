import MainCardContainer from '@components/main-container/main-card-container';
import { withForm } from '@hooks/app-form';
import { Grid2 as Grid } from '@mui/material';
import { formHasErrors } from '@utils/forms';
import { UserCircle } from 'lucide-react';

const PatientInformationForm = withForm({
	render: function Render({ form }) {
		const hasErrors = formHasErrors(form, 'patientInformation');
		return (
			<MainCardContainer
				title="Patient Information"
				icon={<UserCircle />}
				color={hasErrors ? 'error' : 'primary'}
				emphasis={hasErrors ? 'high' : 'low'}>
				<Grid
					container
					spacing={2}>
					<Grid
						size={{
							xs: 12,
							sm: 4,
						}}>
						<form.AppField
							name="patientInformation.prefix"
							children={(field) => (
								<field.AppAutocompleteField
									key="patientInformation.prefix.namePrefix"
									label="Prefix"
									referenceDataSetName="namePrefix"
									data-testid="patientInformation.prefix"
								/>
							)}
						/>
					</Grid>
					<Grid size={{ xs: 0, sm: 8 }}></Grid>
					<Grid
						size={{
							xs: 12,
							sm: 4,
						}}>
						<form.AppField
							name="patientInformation.firstName"
							children={(field) => (
								<field.AppTextField
									label="First Name"
									required
								/>
							)}
						/>
					</Grid>
					<Grid size={{ xs: 12, sm: 4 }}>
						<form.AppField
							name="patientInformation.middleName"
							children={(field) => <field.AppTextField label="Middle Name" />}
						/>
					</Grid>
					<Grid size={{ xs: 12, sm: 4 }}>
						<form.AppField
							name="patientInformation.lastName"
							children={(field) => (
								<field.AppTextField
									label="Last Name"
									required
								/>
							)}
						/>
					</Grid>
					<Grid size={{ xs: 12, sm: 4 }}>
						<form.AppField
							name="patientInformation.suffix"
							children={(field) => (
								<field.AppAutocompleteField
									label="Suffix"
									referenceDataSetName="nameSuffix"
								/>
							)}
						/>
					</Grid>
					<Grid size={{ xs: 0, sm: 8 }}></Grid>
					<Grid size={{ xs: 12, md: 6 }}>
						<form.AppField
							name="patientInformation.ssn"
							children={(field) => (
								<field.AppSsnField
									label={'Social Security number'}
									required
								/>
							)}
						/>
					</Grid>
				</Grid>
			</MainCardContainer>
		);
	},
});

export default PatientInformationForm;
