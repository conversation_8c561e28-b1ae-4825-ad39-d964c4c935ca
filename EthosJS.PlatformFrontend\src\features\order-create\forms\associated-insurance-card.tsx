import { useState } from 'react';
import { Button, CardContent, FormControl, lighten, MenuItem, Select, Stack } from '@mui/material';
import ChipSummary from '@components/chip-summary';
import MainCardContainer from '@components/main-container/main-card-container';
import {
	getAddressSummaryItems,
	getBasicSummaryItems,
	getContactSummaryItems,
} from '@components/forms/create-order/utils';
import Card from '@components/card';
import CardHeader from '@components/card-header';
import { withForm } from '@hooks/app-form';
import { map } from 'lodash';
import {
	EthosWorkflowsApiCreateStudyDto,
	EthosWorkflowsApiInsuranceOutputDto,
} from '@client/workflows';
import { formOptions } from '@tanstack/react-form';
import { useRefDataValues } from '@hooks/use-ref-data-values';

const associatedInsuranceFormOptions = formOptions({
	defaultValues: {
		insurances: [] as EthosWorkflowsApiCreateStudyDto['insurances'],
	},
	props: {
		insurances: [] as Array<EthosWorkflowsApiInsuranceOutputDto>,
	},
});

const AssociatedInsurances = withForm({
	...associatedInsuranceFormOptions,
	render: function Render({ insurances, form }) {
		const [activeTabs, setActiveTabs] = useState<{ [key: string]: string }>({});

		const handleTabChange = (insuranceId: string, newTab: string) => {
			setActiveTabs((prev) => ({
				...prev,
				[insuranceId]: newTab,
			}));
		};

		const { values: insuranceCarriers } = useRefDataValues({
			ids: map(insurances, 'insuranceCarrier') as number[],
		});

		return (
			<MainCardContainer
				title="Associated Insurances"
				color="primary"
				emphasis="low"
				descriptionSubheader="Select the insurance priority and add a guarantor.">
				<Stack sx={{ display: 'flex', flexDirection: 'column', gap: 3 }}>
					{insurances
						.filter((insurance) => insurance.id !== null && insurance.id !== undefined)
						.map((insurance, index) => {
							const insIdStr = insurance.id!.toString();
							const activeTab = activeTabs[insIdStr] || 'basic';

							const tabButton = (label: string, key: string) => (
								<Button
									key={key}
									onClick={() => handleTabChange(insIdStr, key)}
									variant={activeTab === key ? 'contained' : 'outlined'}>
									{label}
								</Button>
							);

							const currentAssoc: string[] = form.getFieldValue('insurances') || [];
							const priorityIndex = currentAssoc.indexOf(insIdStr);

							return (
								<Card
									key={index}
									color="primary">
									<CardHeader
										title={insuranceCarriers ? (insuranceCarriers[index]?.title ?? '') : ''}
										action={
											<FormControl size="small">
												<Select
													value={priorityIndex}
													onChange={(e) => {
														const priority = Number(e.target.value);
														const currentInsurances = [...(form.getFieldValue('insurances') || [])];
														const idx = currentInsurances.indexOf(insIdStr);
														if (idx !== -1) {
															currentInsurances.splice(idx, 1);
														}
														if (priority !== -1) {
															currentInsurances[priority] = insIdStr;
														}
														form.setFieldValue('insurances', currentInsurances);
													}}>
													<MenuItem value={-1}>Not Selected</MenuItem>
													<MenuItem
														value={0}
														disabled={Boolean(currentAssoc[0] && currentAssoc[0] !== insIdStr)}>
														Primary
													</MenuItem>
													<MenuItem
														value={1}
														disabled={
															insurances.length < 2 ||
															Boolean(currentAssoc[1] && currentAssoc[1] !== insIdStr)
														}>
														Secondary
													</MenuItem>
													<MenuItem
														value={2}
														disabled={
															insurances.length < 3 ||
															Boolean(currentAssoc[2] && currentAssoc[2] !== insIdStr)
														}>
														Tertiary
													</MenuItem>
												</Select>
											</FormControl>
										}
									/>
									<CardContent
										sx={(theme) => ({
											backgroundColor: lighten(theme.palette.primary.light, 0.95),
										})}>
										<Stack
											direction="row"
											spacing={2}
											mb={2}>
											{tabButton('Basic Details', 'basic')}
											{tabButton('Contact Info', 'contact')}
											{tabButton('Address', 'address')}
										</Stack>
										{activeTab === 'basic' && (
											<ChipSummary items={getBasicSummaryItems(insurance)} />
										)}
										{activeTab === 'contact' && (
											<ChipSummary items={getContactSummaryItems(insurance)} />
										)}
										{activeTab === 'address' && (
											<ChipSummary items={getAddressSummaryItems(insurance)} />
										)}
									</CardContent>
								</Card>
							);
						})}
				</Stack>
			</MainCardContainer>
		);
	},
});

export default AssociatedInsurances;
