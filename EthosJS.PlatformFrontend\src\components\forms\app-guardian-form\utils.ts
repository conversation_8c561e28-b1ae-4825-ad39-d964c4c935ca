import { EthosWorkflowsApiGuardianDto } from '@client/workflows';
import { LegalDocumentData } from '../app-legal-documents.form';

const defaultValues = {
	guardianBasicInformation: {
		firstName: '',
		lastName: '',
		middleName: '',
		prefix: null!,
		suffix: null!,
		guardianType: null!,
	},
	guardianDemographics: {
		dateOfBirth: '',
		gender: null!,
		idNumber: '',
		idType: null!,
		relationShipToPatient: '',
		ssn: '',
	},
	phoneNumbers: [],
	emails: [],
	addresses: [],
	legalDocuments: [] as LegalDocumentData[],
};

function guardianFormOptions(savedData?: EthosWorkflowsApiGuardianDto) {
	return {
		defaultValues: {
			...defaultValues,
			...(savedData ? savedData : {}),
		},
	};
}

export { defaultValues, guardianFormOptions };
