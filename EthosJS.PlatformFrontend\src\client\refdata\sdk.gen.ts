// This file is auto-generated by @hey-api/openapi-ts

import { type Options as ClientOptions, type TDataShape, type Client, formDataBodySerializer } from '@hey-api/client-axios';
import type { PostApiReferenceBootstrapData, DeleteApiReferenceSetsBySetIdData, GetApiReferenceSetsBySetIdData, DeleteApiReferenceSetsData, GetApiReferenceSetsData, GetApiReferenceSetsResponse, PostApiReferenceSetsData, GetApiReferenceJobsByJobIdData, GetApiReferenceListTypesData, GetApiReferenceListTypesResponse, GetApiReferenceListsByListIdData, PostApiReferenceListsData, GetApiReferenceListsByListIdValuesData, PostApiReferenceListsByListIdValuesData, PutApiReferenceListsByListIdValuesData, GetApiReferenceSetsBySetIdValuesData, PostApiReferenceSetsBySetIdValuesData, GetApiReferenceSetsBySetIdAlternatesData, PostApiReferenceSetsBySetIdAlternatesData, PostApiReferenceSetsBySetIdAlternatesProcessData, GetApiReferenceSetsBySetIdValuesByValueIdData, GetApiReferenceSetsKeysByIdData, GetApiReferenceSetsKeysData, GetApiReferenceSetsValuesData, PostApiReferenceValidateData, GetApiReferenceSearchKeysData, GetApiReferenceSearchValuesData, PostApiReferenceInternalImportCsvData, PostApiReferenceInternalImportCsvResponse, PostApiReferenceInternalIntakeData, PostApiReferenceInternalIntakeResponse } from './types.gen';
import { client as _heyApiClient } from './client.gen';

export type Options<TData extends TDataShape = TDataShape, ThrowOnError extends boolean = boolean> = ClientOptions<TData, ThrowOnError> & {
    /**
     * You can provide a client instance returned by `createClient()` instead of
     * individual options. This might be also useful if you want to implement a
     * custom client.
     */
    client?: Client;
    /**
     * You can pass arbitrary values through the `meta` object. This can be
     * used to access values that aren't defined as part of the SDK function.
     */
    meta?: Record<string, unknown>;
};

export const postApiReferenceBootstrap = <ThrowOnError extends boolean = false>(options?: Options<PostApiReferenceBootstrapData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<unknown, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/reference/bootstrap',
        ...options
    });
};

export const deleteApiReferenceSetsBySetId = <ThrowOnError extends boolean = false>(options: Options<DeleteApiReferenceSetsBySetIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<unknown, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/reference/sets/{setId}',
        ...options
    });
};

export const getApiReferenceSetsBySetId = <ThrowOnError extends boolean = false>(options: Options<GetApiReferenceSetsBySetIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<unknown, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/reference/sets/{setId}',
        ...options
    });
};

export const deleteApiReferenceSets = <ThrowOnError extends boolean = false>(options?: Options<DeleteApiReferenceSetsData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).delete<unknown, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/reference/sets',
        ...options
    });
};

export const getApiReferenceSets = <ThrowOnError extends boolean = false>(options?: Options<GetApiReferenceSetsData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiReferenceSetsResponse, unknown, ThrowOnError>({
        responseType: 'text',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/reference/sets',
        ...options
    });
};

export const postApiReferenceSets = <ThrowOnError extends boolean = false>(options?: Options<PostApiReferenceSetsData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<unknown, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/reference/sets',
        ...options,
        headers: {
            'Content-Type': 'application/json-patch+json',
            ...options?.headers
        }
    });
};

export const getApiReferenceJobsByJobId = <ThrowOnError extends boolean = false>(options: Options<GetApiReferenceJobsByJobIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<unknown, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/reference/jobs/{jobId}',
        ...options
    });
};

export const getApiReferenceListTypes = <ThrowOnError extends boolean = false>(options?: Options<GetApiReferenceListTypesData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiReferenceListTypesResponse, unknown, ThrowOnError>({
        responseType: 'text',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/reference/listTypes',
        ...options
    });
};

export const getApiReferenceListsByListId = <ThrowOnError extends boolean = false>(options: Options<GetApiReferenceListsByListIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<unknown, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/reference/lists/{listId}',
        ...options
    });
};

export const postApiReferenceLists = <ThrowOnError extends boolean = false>(options?: Options<PostApiReferenceListsData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<unknown, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/reference/lists',
        ...options,
        headers: {
            'Content-Type': 'application/json-patch+json',
            ...options?.headers
        }
    });
};

export const getApiReferenceListsByListIdValues = <ThrowOnError extends boolean = false>(options: Options<GetApiReferenceListsByListIdValuesData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<unknown, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/reference/lists/{listId}/values',
        ...options
    });
};

export const postApiReferenceListsByListIdValues = <ThrowOnError extends boolean = false>(options: Options<PostApiReferenceListsByListIdValuesData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<unknown, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/reference/lists/{listId}/values',
        ...options,
        headers: {
            'Content-Type': 'application/json-patch+json',
            ...options?.headers
        }
    });
};

export const putApiReferenceListsByListIdValues = <ThrowOnError extends boolean = false>(options: Options<PutApiReferenceListsByListIdValuesData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<unknown, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/reference/lists/{listId}/values',
        ...options,
        headers: {
            'Content-Type': 'application/json-patch+json',
            ...options?.headers
        }
    });
};

export const getApiReferenceSetsBySetIdValues = <ThrowOnError extends boolean = false>(options: Options<GetApiReferenceSetsBySetIdValuesData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<unknown, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/reference/sets/{setId}/values',
        ...options
    });
};

export const postApiReferenceSetsBySetIdValues = <ThrowOnError extends boolean = false>(options: Options<PostApiReferenceSetsBySetIdValuesData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<unknown, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/reference/sets/{setId}/values',
        ...options,
        headers: {
            'Content-Type': 'application/json-patch+json',
            ...options?.headers
        }
    });
};

export const getApiReferenceSetsBySetIdAlternates = <ThrowOnError extends boolean = false>(options: Options<GetApiReferenceSetsBySetIdAlternatesData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<unknown, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/reference/sets/{setId}/alternates',
        ...options
    });
};

export const postApiReferenceSetsBySetIdAlternates = <ThrowOnError extends boolean = false>(options: Options<PostApiReferenceSetsBySetIdAlternatesData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<unknown, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/reference/sets/{setId}/alternates',
        ...options,
        headers: {
            'Content-Type': 'application/json-patch+json',
            ...options?.headers
        }
    });
};

export const postApiReferenceSetsBySetIdAlternatesProcess = <ThrowOnError extends boolean = false>(options: Options<PostApiReferenceSetsBySetIdAlternatesProcessData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<unknown, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/reference/sets/{setId}/alternates/process',
        ...options,
        headers: {
            'Content-Type': 'application/json-patch+json',
            ...options?.headers
        }
    });
};

export const getApiReferenceSetsBySetIdValuesByValueId = <ThrowOnError extends boolean = false>(options: Options<GetApiReferenceSetsBySetIdValuesByValueIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<unknown, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/reference/sets/{setId}/values/{valueId}',
        ...options
    });
};

export const getApiReferenceSetsKeysById = <ThrowOnError extends boolean = false>(options: Options<GetApiReferenceSetsKeysByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<unknown, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/reference/sets/keys/{id}',
        ...options
    });
};

export const getApiReferenceSetsKeys = <ThrowOnError extends boolean = false>(options?: Options<GetApiReferenceSetsKeysData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<unknown, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/reference/sets/keys',
        ...options
    });
};

export const getApiReferenceSetsValues = <ThrowOnError extends boolean = false>(options?: Options<GetApiReferenceSetsValuesData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<unknown, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/reference/sets/values',
        ...options
    });
};

export const postApiReferenceValidate = <ThrowOnError extends boolean = false>(options?: Options<PostApiReferenceValidateData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<unknown, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/reference/validate',
        ...options,
        headers: {
            'Content-Type': 'application/json-patch+json',
            ...options?.headers
        }
    });
};

export const getApiReferenceSearchKeys = <ThrowOnError extends boolean = false>(options?: Options<GetApiReferenceSearchKeysData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<unknown, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/reference/search/keys',
        ...options
    });
};

export const getApiReferenceSearchValues = <ThrowOnError extends boolean = false>(options?: Options<GetApiReferenceSearchValuesData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<unknown, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/reference/search/values',
        ...options
    });
};

export const postApiReferenceInternalImportCsv = <ThrowOnError extends boolean = false>(options?: Options<PostApiReferenceInternalImportCsvData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiReferenceInternalImportCsvResponse, unknown, ThrowOnError>({
        ...formDataBodySerializer,
        responseType: 'text',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/reference/internal/importCsv',
        ...options,
        headers: {
            'Content-Type': null,
            ...options?.headers
        }
    });
};

export const postApiReferenceInternalIntake = <ThrowOnError extends boolean = false>(options?: Options<PostApiReferenceInternalIntakeData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiReferenceInternalIntakeResponse, unknown, ThrowOnError>({
        responseType: 'text',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/reference/internal/intake',
        ...options,
        headers: {
            'Content-Type': 'application/json-patch+json',
            ...options?.headers
        }
    });
};