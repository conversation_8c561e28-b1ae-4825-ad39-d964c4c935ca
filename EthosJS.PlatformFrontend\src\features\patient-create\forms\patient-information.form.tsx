import DemographicsForm from '@components/forms/app-demographics.form';
import PatientInformationForm from '@components/forms/app-patient-information.form';
import PhysicalMeasurementsForm from '@components/forms/app-physical-measurements.form';
import StepCardControl from '@components/step-card-control';
import { useAppForm } from '@hooks/app-form';
import { Box, Button, CardContent } from '@mui/material';
import { formOptions, useStore } from '@tanstack/react-form';
import { FormProps } from '@components/forms/predefined-form-props';
import { useMemo, useState } from 'react';
import { PatientBasicInformationData } from '../types/form-types';
import { patientBasicInformationTransformer } from './transformers';

const patientInformationOptions = (savedData?: PatientBasicInformationData) =>
	formOptions({
		defaultValues: {
			patientInformation: {
				prefix: null,
				firstName: '',
				middleName: '',
				lastName: '',
				suffix: null,
				ssn: '',
				mrn: '',
				...savedData?.patientInformation,
			},
			demographics: {
				dateOfBirth: '',
				birthSex: null,
				ethnicity: null,
				gender: null,
				maritalStatus: null,
				race: null,
				...savedData?.demographics,
			},
			physicalMeasurements: {
				heightInches: '',
				weightPounds: '',
				neckSize: '',
				bmi: '',
				...savedData?.physicalMeasurements,
			},
		},
	});

export default function PatientInformation({
	onSubmit,
	onSaveDraft,
	savedData,
	onValidate,
	isUpdate,
}: FormProps<PatientBasicInformationData>) {
	const options = useMemo(() => patientInformationOptions(savedData), [savedData]);
	const form = useAppForm({
		...options,
		validators: {
			onChangeAsyncDebounceMs: 500,
			onChangeAsync: async ({ value }) => {
				const res = await onValidate(patientBasicInformationTransformer<typeof value>(value));
				return res;
			},
			onSubmitAsync: async ({ value }) => {
				const res = await onValidate(patientBasicInformationTransformer<typeof value>(value));
				console.log('onSubmitAsync', res);
				return res;
			},
		},
		onSubmit: async ({ value }) => {
			onSubmit(patientBasicInformationTransformer(value));
		},
	});

	const values = useStore(form.store, (state) => state.values);

	return (
		<Box
			component="form"
			sx={{ height: '100%' }}
			onSubmit={(e) => {
				e.preventDefault();
				form.handleSubmit();
			}}
			data-testid="patient-information-form">
			<CardContent sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
				<PatientInformationForm
					/* @ts-expect-error Type Mismatch */
					form={form}
					isUpdate={!!isUpdate}
				/>
				{/* @ts-expect-error Type Mismatch */}
				<DemographicsForm form={form} />
				{/* @ts-expect-error Type Mismatch */}
				<PhysicalMeasurementsForm form={form} />
			</CardContent>
			<StepCardControl>
				{!isUpdate && (
					<Button
						variant="outlined"
						color="primary"
						onClick={() => onSaveDraft(patientBasicInformationTransformer(values))}>
						Save Draft
					</Button>
				)}
				<form.Subscribe
					selector={({ isSubmitting }) => ({
						isSubmitting,
					})}
					children={({ isSubmitting }) => {
						return (
							<Button
								variant="contained"
								color="primary"
								type="submit"
								loading={isSubmitting}
								disabled={isSubmitting}>
								{isUpdate ? 'Update' : 'Next'}
							</Button>
						);
					}}
				/>
			</StepCardControl>
		</Box>
	);
}
