{"name": "ethos-platform-fe", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint .", "preview": "vite preview", "openapi": "npm run openapi:workflows && npm run openapi:refdata", "openapi-with-roles": "npm run openapi:workflows && npm run openapi:refdata && npm run openapi:roles", "openapi:workflows": "NODE_ENV=workflows openapi-ts", "openapi:refdata": "NODE_ENV=refdata openapi-ts", "openapi:roles": "NODE_ENV=roles openapi-ts"}, "dependencies": {"@azure/msal-browser": "^4.14.0", "@azure/msal-react": "^3.0.14", "@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.1", "@fontsource/roboto": "^5.2.6", "@hey-api/client-axios": "^0.6.3", "@mui/icons-material": "^6.4.12", "@mui/material": "^6.4.12", "@mui/utils": "^6.4.9", "@mui/x-data-grid-pro": "^8.6.0", "@mui/x-date-pickers-pro": "^7.29.4", "@mui/x-license": "^7.29.1", "@tanstack/react-form": "^1.12.4", "@tanstack/react-query": "^5.81.5", "@tanstack/react-router": "^1.124.0", "@tanstack/react-store": "^0.7.1", "@tanstack/store": "^0.7.1", "@tanstack/zod-adapter": "^1.124.0", "@tanstack/zod-form-adapter": "^0.42.1", "@toolpad/core": "^0.12.1", "@vitejs/plugin-react": "^4.6.0", "axios": "^1.10.0", "babel-plugin-react-compiler": "19.1.0-rc.2", "dayjs": "^1.11.13", "dotenv": "^16.6.1", "lodash": "^4.17.21", "lucide-react": "^0.486.0", "react": "^19.1.0", "react-dom": "^19.1.0", "zod": "^3.25.68"}, "devDependencies": {"@eslint/js": "^9.30.1", "@hey-api/openapi-ts": "^0.64.15", "@tanstack/router-devtools": "^1.124.0", "@tanstack/router-plugin": "^1.124.0", "@types/lodash": "^4.17.20", "@types/node": "^22.16.0", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@typescript-eslint/eslint-plugin": "^8.35.1", "@typescript-eslint/parser": "^8.35.1", "eslint": "^9.30.1", "eslint-config-prettier": "^10.1.5", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^15.15.0", "path": "^0.12.7", "prettier": "3.5.3", "typescript": "~5.6.3", "typescript-eslint": "^8.35.1", "vite": "^6.3.5"}}