import { <PERSON>, But<PERSON>, Stack } from '@mui/material';

import { useStore } from '@tanstack/react-form';

import { useAppForm } from '@hooks/app-form';
import StepCardControl from '@components/step-card-control';
import AddPhysicianForm from '../app-physician-form/app-physician.form';
import { PredefinedFormProps } from '../predefined-form-props';
import LoadingComponent from '@components/loading-component';
import { withPhysicianSearch } from '../with-physician-search';
import { EthosWorkflowsWorkflowAddNewOrderIAddNewOrderRequest_AddPhysicians } from '@client/workflows';

const WithPhysicianSearch = withPhysicianSearch(AddPhysicianForm);

const defaultValues: EthosWorkflowsWorkflowAddNewOrderIAddNewOrderRequest_AddPhysicians = {
	orderingPhysician: undefined,
	interpretingPhysician: undefined,
	referringPhysician: null,
	primaryCarePhysician: null,
};

export default function AddPhysiciansForm({
	onSubmit,
	isLoading,
	savedData,
}: PredefinedFormProps<EthosWorkflowsWorkflowAddNewOrderIAddNewOrderRequest_AddPhysicians>) {
	const form = useAppForm({
		defaultValues: savedData ?? defaultValues,
		defaultState: {},
		onSubmit: ({ value }) => {
			onSubmit(value);
		},
	});

	const values = useStore(form.store, (state) => state.values);

	const onSelectPysicians = (fieldName: keyof typeof defaultValues, fieldValue: string | null) => {
		form.setFieldValue(fieldName, fieldValue);
	};
	if (isLoading) {
		return <LoadingComponent />;
	}

	const careLoction = 'Tukwila Sleep Center';

	return (
		<Stack
			gap={2}
			p={2}
			component="form"
			onSubmit={(e) => {
				e.preventDefault();
				form.handleSubmit();
			}}>
			<WithPhysicianSearch
				title="1. Ordering Physician *"
				isOptional={false}
				attentionTitle={`Recommended physicians associated with ${careLoction}`}
				value={values['orderingPhysician'] ?? null}
				onSelect={(selectedOption) => onSelectPysicians('orderingPhysician', selectedOption)}
				onAdd={(selectedOption) => onSelectPysicians('orderingPhysician', selectedOption)}
				onCancel={() => onSelectPysicians('orderingPhysician', null)}
			/>
			<WithPhysicianSearch
				title="2. Interpreting Physician *"
				isOptional={false}
				attentionTitle={`Recommended physicians associated with ${careLoction}`}
				value={values['interpretingPhysician'] ?? null}
				onSelect={(selectedOption) => onSelectPysicians('interpretingPhysician', selectedOption)}
				onAdd={(selectedOption) => onSelectPysicians('interpretingPhysician', selectedOption)}
				onCancel={() => onSelectPysicians('interpretingPhysician', null)}
			/>
			<WithPhysicianSearch
				title="3. Referring Physician (Optional)"
				isOptional={true}
				attentionTitle={`Recommended physicians associated with ${careLoction}`}
				value={values['referringPhysician'] ?? null}
				onSelect={(selectedOption) => onSelectPysicians('referringPhysician', selectedOption)}
				onAdd={(selectedOption) => onSelectPysicians('referringPhysician', selectedOption)}
				onCancel={() => onSelectPysicians('referringPhysician', null)}
			/>
			<WithPhysicianSearch
				title="4. Primary Care Physician (Optional)"
				isOptional={true}
				attentionTitle={`Recommended physicians associated with ${careLoction}`}
				value={values['primaryCarePhysician'] ?? null}
				onSelect={(selectedOption) => onSelectPysicians('primaryCarePhysician', selectedOption)}
				onAdd={(selectedOption) => onSelectPysicians('primaryCarePhysician', selectedOption)}
				onCancel={() => onSelectPysicians('primaryCarePhysician', null)}
			/>

			<Box>
				<StepCardControl>
					<Button
						variant="outlined"
						color="primary">
						Save Draft
					</Button>
					<form.Subscribe>
						{({ isSubmitting }) => {
							const isValid = !!values.orderingPhysician && !!values.interpretingPhysician;
							return (
								<Button
									variant="contained"
									color="primary"
									type="submit"
									loading={isSubmitting}
									disabled={!isValid}>
									Next
								</Button>
							);
						}}
					</form.Subscribe>
				</StepCardControl>
			</Box>
		</Stack>
	);
}
