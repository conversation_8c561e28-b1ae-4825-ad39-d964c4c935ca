// This file is auto-generated by @hey-api/openapi-ts

import type { Options as ClientOptions, TDataShape, Client } from '@hey-api/client-axios';
import type {
	GetApiAddNewOrderValidationRulesData,
	GetApiAddNewOrderValidationRulesResponse,
	GetApiAddNewOrderStateByIdData,
	GetApiAddNewOrderStateByIdResponse,
	PostApiAddNewOrderStartData,
	PostApiAddNewOrderStartResponse,
	PostApiAddNewOrderRewindData,
	PostApiAddNewOrderListData,
	PostApiAddNewOrderListResponse,
	PostApiAddNewOrderAddStudyData,
	PostApiAddNewOrderAddStudyResponse,
	PostApiAddNewOrderAddStudyDraftData,
	PostApiAddNewOrderAddStudyDraftResponse,
	PostApiAddNewOrderAddStudyValidateData,
	PostApiAddNewOrderAddStudyValidateResponse,
	PostApiAddNewOrderAddCareLocationData,
	PostApiAddNewOrderAddCareLocationResponse,
	PostApiAddNewOrderAddCareLocationDraftData,
	PostApiAddNewOrderAddCareLocationDraftResponse,
	PostApiAddNewOrderAddCareLocationValidateData,
	PostApiAddNewOrderAddCareLocationValidateResponse,
	PostApiAddNewOrderAddPhysiciansData,
	PostApiAddNewOrderAddPhysiciansResponse,
	PostApiAddNewOrderAddPhysiciansDraftData,
	PostApiAddNewOrderAddPhysiciansDraftResponse,
	PostApiAddNewOrderAddPhysiciansValidateData,
	PostApiAddNewOrderAddPhysiciansValidateResponse,
	PostApiAddNewOrderReviewAndSubmitOrderData,
	PostApiAddNewOrderReviewAndSubmitOrderResponse,
	PostApiAddNewOrderReviewAndSubmitOrderDraftData,
	PostApiAddNewOrderReviewAndSubmitOrderDraftResponse,
	PostApiAddNewOrderReviewAndSubmitOrderValidateData,
	PostApiAddNewOrderReviewAndSubmitOrderValidateResponse,
	GetApiAddNewPatientValidationRulesData,
	GetApiAddNewPatientValidationRulesResponse,
	GetApiAddNewPatientStateByIdData,
	GetApiAddNewPatientStateByIdResponse,
	PostApiAddNewPatientStartData,
	PostApiAddNewPatientStartResponse,
	PostApiAddNewPatientRewindData,
	PostApiAddNewPatientListData,
	PostApiAddNewPatientListResponse,
	PostApiAddNewPatientAddBasicInformationData,
	PostApiAddNewPatientAddBasicInformationResponse,
	PostApiAddNewPatientAddBasicInformationDraftData,
	PostApiAddNewPatientAddBasicInformationDraftResponse,
	PostApiAddNewPatientAddBasicInformationValidateData,
	PostApiAddNewPatientAddBasicInformationValidateResponse,
	PostApiAddNewPatientAddContactsData,
	PostApiAddNewPatientAddContactsResponse,
	PostApiAddNewPatientAddContactsDraftData,
	PostApiAddNewPatientAddContactsDraftResponse,
	PostApiAddNewPatientAddContactsValidateData,
	PostApiAddNewPatientAddContactsValidateResponse,
	PostApiAddNewPatientAddAddressesData,
	PostApiAddNewPatientAddAddressesResponse,
	PostApiAddNewPatientAddAddressesDraftData,
	PostApiAddNewPatientAddAddressesDraftResponse,
	PostApiAddNewPatientAddAddressesValidateData,
	PostApiAddNewPatientAddAddressesValidateResponse,
	PostApiAddNewPatientAddInsurancesData,
	PostApiAddNewPatientAddInsurancesResponse,
	PostApiAddNewPatientAddInsurancesDraftData,
	PostApiAddNewPatientAddInsurancesDraftResponse,
	PostApiAddNewPatientAddInsurancesValidateData,
	PostApiAddNewPatientAddInsurancesValidateResponse,
	PostApiAddNewPatientAddGuardiansData,
	PostApiAddNewPatientAddGuardiansResponse,
	PostApiAddNewPatientAddGuardiansDraftData,
	PostApiAddNewPatientAddGuardiansDraftResponse,
	PostApiAddNewPatientAddGuardiansValidateData,
	PostApiAddNewPatientAddGuardiansValidateResponse,
	PostApiAddNewPatientAddClinicalInformationData,
	PostApiAddNewPatientAddClinicalInformationResponse,
	PostApiAddNewPatientAddClinicalInformationDraftData,
	PostApiAddNewPatientAddClinicalInformationDraftResponse,
	PostApiAddNewPatientAddClinicalInformationValidateData,
	PostApiAddNewPatientAddClinicalInformationValidateResponse,
	GetApiCareLocationByIdData,
	GetApiCareLocationByIdResponse,
	GetApiCareLocationByIdError,
	PatchApiCareLocationByIdData,
	PatchApiCareLocationByIdResponse,
	PatchApiCareLocationByIdError,
	PutApiCareLocationByIdData,
	PutApiCareLocationByIdResponse,
	PutApiCareLocationByIdError,
	PostApiCareLocationSearchData,
	PostApiCareLocationSearchResponse,
	GetApiCareLocationData,
	GetApiCareLocationResponse,
	PostApiCareLocationData,
	PostApiCareLocationResponse,
	PostApiCareLocationError,
	PostApiCareLocationDraftData,
	PostApiCareLocationDraftResponse,
	PostApiCareLocationDraftError,
	GetApiCareLocationDraftByEntityIdData,
	GetApiCareLocationDraftByEntityIdResponse,
	GetApiCareLocationDraftByEntityIdError,
	PutApiCareLocationDraftByEntityIdData,
	PutApiCareLocationDraftByEntityIdResponse,
	PutApiCareLocationDraftByEntityIdError,
	PostApiCareLocationDraftByEntityIdCommitData,
	PostApiCareLocationDraftByEntityIdCommitResponse,
	PostApiCareLocationDraftByEntityIdCommitError,
	PostApiCareLocationDraftValidateData,
	PostApiCareLocationDraftValidateResponse,
	PostApiCareLocationDraftValidateError,
	PostApiCareLocationDraftByEntityIdValidateData,
	PostApiCareLocationDraftByEntityIdValidateResponse,
	PostApiCareLocationDraftByEntityIdValidateError,
	GetApiDraftByIdData,
	GetApiDraftByIdResponse,
	GetApiDraftByIdError,
	PatchApiDraftByIdData,
	PatchApiDraftByIdResponse,
	PatchApiDraftByIdError,
	PutApiDraftByIdData,
	PutApiDraftByIdResponse,
	PutApiDraftByIdError,
	PostApiDraftSearchData,
	PostApiDraftSearchResponse,
	GetApiDraftData,
	GetApiDraftResponse,
	PostApiDraftData,
	PostApiDraftResponse,
	PostApiDraftError,
	PostApiDraftDraftData,
	PostApiDraftDraftResponse,
	PostApiDraftDraftError,
	GetApiDraftDraftByEntityIdData,
	GetApiDraftDraftByEntityIdResponse,
	GetApiDraftDraftByEntityIdError,
	PutApiDraftDraftByEntityIdData,
	PutApiDraftDraftByEntityIdResponse,
	PutApiDraftDraftByEntityIdError,
	PostApiDraftDraftByEntityIdCommitData,
	PostApiDraftDraftByEntityIdCommitResponse,
	PostApiDraftDraftByEntityIdCommitError,
	PostApiDraftDraftValidateData,
	PostApiDraftDraftValidateResponse,
	PostApiDraftDraftValidateError,
	PostApiDraftDraftByEntityIdValidateData,
	PostApiDraftDraftByEntityIdValidateResponse,
	PostApiDraftDraftByEntityIdValidateError,
	GetApiEquipmentByIdData,
	GetApiEquipmentByIdResponse,
	GetApiEquipmentByIdError,
	PatchApiEquipmentByIdData,
	PatchApiEquipmentByIdResponse,
	PatchApiEquipmentByIdError,
	PutApiEquipmentByIdData,
	PutApiEquipmentByIdResponse,
	PutApiEquipmentByIdError,
	PostApiEquipmentSearchData,
	PostApiEquipmentSearchResponse,
	GetApiEquipmentData,
	GetApiEquipmentResponse,
	PostApiEquipmentData,
	PostApiEquipmentResponse,
	PostApiEquipmentError,
	PostApiEquipmentDraftData,
	PostApiEquipmentDraftResponse,
	PostApiEquipmentDraftError,
	GetApiEquipmentDraftByEntityIdData,
	GetApiEquipmentDraftByEntityIdResponse,
	GetApiEquipmentDraftByEntityIdError,
	PutApiEquipmentDraftByEntityIdData,
	PutApiEquipmentDraftByEntityIdResponse,
	PutApiEquipmentDraftByEntityIdError,
	PostApiEquipmentDraftByEntityIdCommitData,
	PostApiEquipmentDraftByEntityIdCommitResponse,
	PostApiEquipmentDraftByEntityIdCommitError,
	PostApiEquipmentDraftValidateData,
	PostApiEquipmentDraftValidateResponse,
	PostApiEquipmentDraftValidateError,
	PostApiEquipmentDraftByEntityIdValidateData,
	PostApiEquipmentDraftByEntityIdValidateResponse,
	PostApiEquipmentDraftByEntityIdValidateError,
	PostApiFileRequestUploadTokenData,
	PostApiFileRequestUploadTokenResponse,
	PostApiFileRequestUploadTokenError,
	PostApiFileUploadData,
	PostApiFileUploadResponse,
	PostApiFileUploadError,
	GetApiFileStatusByFileIdData,
	GetApiFileStatusByFileIdResponse,
	GetApiFileStatusByFileIdError,
	GetApiHealthData,
	GetApiHealthResponse,
	GetApiInsuranceByIdData,
	GetApiInsuranceByIdResponse,
	GetApiInsuranceByIdError,
	PatchApiInsuranceByIdData,
	PatchApiInsuranceByIdResponse,
	PatchApiInsuranceByIdError,
	PutApiInsuranceByIdData,
	PutApiInsuranceByIdResponse,
	PutApiInsuranceByIdError,
	PostApiInsuranceSearchData,
	PostApiInsuranceSearchResponse,
	GetApiInsuranceData,
	GetApiInsuranceResponse,
	PostApiInsuranceData,
	PostApiInsuranceResponse,
	PostApiInsuranceError,
	PostApiInsuranceDraftData,
	PostApiInsuranceDraftResponse,
	PostApiInsuranceDraftError,
	GetApiInsuranceDraftByEntityIdData,
	GetApiInsuranceDraftByEntityIdResponse,
	GetApiInsuranceDraftByEntityIdError,
	PutApiInsuranceDraftByEntityIdData,
	PutApiInsuranceDraftByEntityIdResponse,
	PutApiInsuranceDraftByEntityIdError,
	PostApiInsuranceDraftByEntityIdCommitData,
	PostApiInsuranceDraftByEntityIdCommitResponse,
	PostApiInsuranceDraftByEntityIdCommitError,
	PostApiInsuranceDraftValidateData,
	PostApiInsuranceDraftValidateResponse,
	PostApiInsuranceDraftValidateError,
	PostApiInsuranceDraftByEntityIdValidateData,
	PostApiInsuranceDraftByEntityIdValidateResponse,
	PostApiInsuranceDraftByEntityIdValidateError,
	PostApiInsuranceVerificationStartData,
	PostApiInsuranceVerificationStartError,
	GetApiInsuranceVerificationStatusByJobIdData,
	GetApiInsuranceVerificationStatusByJobIdResponse,
	GetApiInsuranceVerificationStatusByJobIdError,
	PostApiInsuranceVerificationSearchData,
	PostApiInsuranceVerificationSearchResponse,
	PostApiInsuranceVerificationSearchError,
	PostApiInsuranceVerificationFastauthWebhookData,
	PostApiInsuranceVerificationFastauthWebhookError,
	PostApiLoginData,
	GetApiLoginCheckData,
	PostApiMockDbDeleteData,
	GetApiMockDbResetData,
	GetApiNoteByIdData,
	GetApiNoteByIdResponse,
	GetApiNoteByIdError,
	PatchApiNoteByIdData,
	PatchApiNoteByIdResponse,
	PatchApiNoteByIdError,
	PutApiNoteByIdData,
	PutApiNoteByIdResponse,
	PutApiNoteByIdError,
	PostApiNoteSearchData,
	PostApiNoteSearchResponse,
	GetApiNoteData,
	GetApiNoteResponse,
	PostApiNoteData,
	PostApiNoteResponse,
	PostApiNoteError,
	PostApiNoteDraftData,
	PostApiNoteDraftResponse,
	PostApiNoteDraftError,
	GetApiNoteDraftByEntityIdData,
	GetApiNoteDraftByEntityIdResponse,
	GetApiNoteDraftByEntityIdError,
	PutApiNoteDraftByEntityIdData,
	PutApiNoteDraftByEntityIdResponse,
	PutApiNoteDraftByEntityIdError,
	PostApiNoteDraftByEntityIdCommitData,
	PostApiNoteDraftByEntityIdCommitResponse,
	PostApiNoteDraftByEntityIdCommitError,
	PostApiNoteDraftValidateData,
	PostApiNoteDraftValidateResponse,
	PostApiNoteDraftValidateError,
	PostApiNoteDraftByEntityIdValidateData,
	PostApiNoteDraftByEntityIdValidateResponse,
	PostApiNoteDraftByEntityIdValidateError,
	GetApiOrderByIdData,
	GetApiOrderByIdResponse,
	GetApiOrderByIdError,
	PatchApiOrderByIdData,
	PatchApiOrderByIdResponse,
	PatchApiOrderByIdError,
	PutApiOrderByIdData,
	PutApiOrderByIdResponse,
	PutApiOrderByIdError,
	PostApiOrderSearchData,
	PostApiOrderSearchResponse,
	GetApiOrderData,
	GetApiOrderResponse,
	PostApiOrderData,
	PostApiOrderResponse,
	PostApiOrderError,
	PostApiOrderDraftData,
	PostApiOrderDraftResponse,
	PostApiOrderDraftError,
	GetApiOrderDraftByEntityIdData,
	GetApiOrderDraftByEntityIdResponse,
	GetApiOrderDraftByEntityIdError,
	PutApiOrderDraftByEntityIdData,
	PutApiOrderDraftByEntityIdResponse,
	PutApiOrderDraftByEntityIdError,
	PostApiOrderDraftByEntityIdCommitData,
	PostApiOrderDraftByEntityIdCommitResponse,
	PostApiOrderDraftByEntityIdCommitError,
	PostApiOrderDraftValidateData,
	PostApiOrderDraftValidateResponse,
	PostApiOrderDraftValidateError,
	PostApiOrderDraftByEntityIdValidateData,
	PostApiOrderDraftByEntityIdValidateResponse,
	PostApiOrderDraftByEntityIdValidateError,
	GetApiPatientByIdData,
	GetApiPatientByIdResponse,
	GetApiPatientByIdError,
	PatchApiPatientByIdData,
	PatchApiPatientByIdResponse,
	PatchApiPatientByIdError,
	PutApiPatientByIdData,
	PutApiPatientByIdResponse,
	PutApiPatientByIdError,
	PostApiPatientSearchData,
	PostApiPatientSearchResponse,
	GetApiPatientData,
	GetApiPatientResponse,
	PostApiPatientData,
	PostApiPatientResponse,
	PostApiPatientError,
	PostApiPatientDraftData,
	PostApiPatientDraftResponse,
	PostApiPatientDraftError,
	GetApiPatientDraftByEntityIdData,
	GetApiPatientDraftByEntityIdResponse,
	GetApiPatientDraftByEntityIdError,
	PutApiPatientDraftByEntityIdData,
	PutApiPatientDraftByEntityIdResponse,
	PutApiPatientDraftByEntityIdError,
	PostApiPatientDraftByEntityIdCommitData,
	PostApiPatientDraftByEntityIdCommitResponse,
	PostApiPatientDraftByEntityIdCommitError,
	PostApiPatientDraftValidateData,
	PostApiPatientDraftValidateResponse,
	PostApiPatientDraftValidateError,
	PostApiPatientDraftByEntityIdValidateData,
	PostApiPatientDraftByEntityIdValidateResponse,
	PostApiPatientDraftByEntityIdValidateError,
	GetApiPatientAppointmentByIdData,
	GetApiPatientAppointmentByIdResponse,
	GetApiPatientAppointmentByIdError,
	PatchApiPatientAppointmentByIdData,
	PatchApiPatientAppointmentByIdResponse,
	PatchApiPatientAppointmentByIdError,
	PutApiPatientAppointmentByIdData,
	PutApiPatientAppointmentByIdResponse,
	PutApiPatientAppointmentByIdError,
	PostApiPatientAppointmentSearchData,
	PostApiPatientAppointmentSearchResponse,
	GetApiPatientAppointmentData,
	GetApiPatientAppointmentResponse,
	PostApiPatientAppointmentData,
	PostApiPatientAppointmentResponse,
	PostApiPatientAppointmentError,
	PostApiPatientAppointmentDraftData,
	PostApiPatientAppointmentDraftResponse,
	PostApiPatientAppointmentDraftError,
	GetApiPatientAppointmentDraftByEntityIdData,
	GetApiPatientAppointmentDraftByEntityIdResponse,
	GetApiPatientAppointmentDraftByEntityIdError,
	PutApiPatientAppointmentDraftByEntityIdData,
	PutApiPatientAppointmentDraftByEntityIdResponse,
	PutApiPatientAppointmentDraftByEntityIdError,
	PostApiPatientAppointmentDraftByEntityIdCommitData,
	PostApiPatientAppointmentDraftByEntityIdCommitResponse,
	PostApiPatientAppointmentDraftByEntityIdCommitError,
	PostApiPatientAppointmentDraftValidateData,
	PostApiPatientAppointmentDraftValidateResponse,
	PostApiPatientAppointmentDraftValidateError,
	PostApiPatientAppointmentDraftByEntityIdValidateData,
	PostApiPatientAppointmentDraftByEntityIdValidateResponse,
	PostApiPatientAppointmentDraftByEntityIdValidateError,
	GetApiPatientAppointmentConfirmationByIdData,
	GetApiPatientAppointmentConfirmationByIdResponse,
	GetApiPatientAppointmentConfirmationByIdError,
	PatchApiPatientAppointmentConfirmationByIdData,
	PatchApiPatientAppointmentConfirmationByIdResponse,
	PatchApiPatientAppointmentConfirmationByIdError,
	PutApiPatientAppointmentConfirmationByIdData,
	PutApiPatientAppointmentConfirmationByIdResponse,
	PutApiPatientAppointmentConfirmationByIdError,
	PostApiPatientAppointmentConfirmationSearchData,
	PostApiPatientAppointmentConfirmationSearchResponse,
	GetApiPatientAppointmentConfirmationData,
	GetApiPatientAppointmentConfirmationResponse,
	PostApiPatientAppointmentConfirmationData,
	PostApiPatientAppointmentConfirmationResponse,
	PostApiPatientAppointmentConfirmationError,
	PostApiPatientAppointmentConfirmationDraftData,
	PostApiPatientAppointmentConfirmationDraftResponse,
	PostApiPatientAppointmentConfirmationDraftError,
	GetApiPatientAppointmentConfirmationDraftByEntityIdData,
	GetApiPatientAppointmentConfirmationDraftByEntityIdResponse,
	GetApiPatientAppointmentConfirmationDraftByEntityIdError,
	PutApiPatientAppointmentConfirmationDraftByEntityIdData,
	PutApiPatientAppointmentConfirmationDraftByEntityIdResponse,
	PutApiPatientAppointmentConfirmationDraftByEntityIdError,
	PostApiPatientAppointmentConfirmationDraftByEntityIdCommitData,
	PostApiPatientAppointmentConfirmationDraftByEntityIdCommitResponse,
	PostApiPatientAppointmentConfirmationDraftByEntityIdCommitError,
	PostApiPatientAppointmentConfirmationDraftValidateData,
	PostApiPatientAppointmentConfirmationDraftValidateResponse,
	PostApiPatientAppointmentConfirmationDraftValidateError,
	PostApiPatientAppointmentConfirmationDraftByEntityIdValidateData,
	PostApiPatientAppointmentConfirmationDraftByEntityIdValidateResponse,
	PostApiPatientAppointmentConfirmationDraftByEntityIdValidateError,
	GetApiPhysicianByIdData,
	GetApiPhysicianByIdResponse,
	GetApiPhysicianByIdError,
	PatchApiPhysicianByIdData,
	PatchApiPhysicianByIdResponse,
	PatchApiPhysicianByIdError,
	PutApiPhysicianByIdData,
	PutApiPhysicianByIdResponse,
	PutApiPhysicianByIdError,
	PostApiPhysicianSearchData,
	PostApiPhysicianSearchResponse,
	GetApiPhysicianData,
	GetApiPhysicianResponse,
	PostApiPhysicianData,
	PostApiPhysicianResponse,
	PostApiPhysicianError,
	PostApiPhysicianDraftData,
	PostApiPhysicianDraftResponse,
	PostApiPhysicianDraftError,
	GetApiPhysicianDraftByEntityIdData,
	GetApiPhysicianDraftByEntityIdResponse,
	GetApiPhysicianDraftByEntityIdError,
	PutApiPhysicianDraftByEntityIdData,
	PutApiPhysicianDraftByEntityIdResponse,
	PutApiPhysicianDraftByEntityIdError,
	PostApiPhysicianDraftByEntityIdCommitData,
	PostApiPhysicianDraftByEntityIdCommitResponse,
	PostApiPhysicianDraftByEntityIdCommitError,
	PostApiPhysicianDraftValidateData,
	PostApiPhysicianDraftValidateResponse,
	PostApiPhysicianDraftValidateError,
	PostApiPhysicianDraftByEntityIdValidateData,
	PostApiPhysicianDraftByEntityIdValidateResponse,
	PostApiPhysicianDraftByEntityIdValidateError,
	GetApiProviderByIdData,
	GetApiProviderByIdResponse,
	GetApiProviderByIdError,
	PatchApiProviderByIdData,
	PatchApiProviderByIdResponse,
	PatchApiProviderByIdError,
	PutApiProviderByIdData,
	PutApiProviderByIdResponse,
	PutApiProviderByIdError,
	PostApiProviderSearchData,
	PostApiProviderSearchResponse,
	GetApiProviderData,
	GetApiProviderResponse,
	PostApiProviderData,
	PostApiProviderResponse,
	PostApiProviderError,
	PostApiProviderDraftData,
	PostApiProviderDraftResponse,
	PostApiProviderDraftError,
	GetApiProviderDraftByEntityIdData,
	GetApiProviderDraftByEntityIdResponse,
	GetApiProviderDraftByEntityIdError,
	PutApiProviderDraftByEntityIdData,
	PutApiProviderDraftByEntityIdResponse,
	PutApiProviderDraftByEntityIdError,
	PostApiProviderDraftByEntityIdCommitData,
	PostApiProviderDraftByEntityIdCommitResponse,
	PostApiProviderDraftByEntityIdCommitError,
	PostApiProviderDraftValidateData,
	PostApiProviderDraftValidateResponse,
	PostApiProviderDraftValidateError,
	PostApiProviderDraftByEntityIdValidateData,
	PostApiProviderDraftByEntityIdValidateResponse,
	PostApiProviderDraftByEntityIdValidateError,
	GetPyapiZipData,
	GetTsapiZipData,
	GetApiRoomByIdData,
	GetApiRoomByIdResponse,
	GetApiRoomByIdError,
	PatchApiRoomByIdData,
	PatchApiRoomByIdResponse,
	PatchApiRoomByIdError,
	PutApiRoomByIdData,
	PutApiRoomByIdResponse,
	PutApiRoomByIdError,
	PostApiRoomSearchData,
	PostApiRoomSearchResponse,
	GetApiRoomData,
	GetApiRoomResponse,
	PostApiRoomData,
	PostApiRoomResponse,
	PostApiRoomError,
	PostApiRoomDraftData,
	PostApiRoomDraftResponse,
	PostApiRoomDraftError,
	GetApiRoomDraftByEntityIdData,
	GetApiRoomDraftByEntityIdResponse,
	GetApiRoomDraftByEntityIdError,
	PutApiRoomDraftByEntityIdData,
	PutApiRoomDraftByEntityIdResponse,
	PutApiRoomDraftByEntityIdError,
	PostApiRoomDraftByEntityIdCommitData,
	PostApiRoomDraftByEntityIdCommitResponse,
	PostApiRoomDraftByEntityIdCommitError,
	PostApiRoomDraftValidateData,
	PostApiRoomDraftValidateResponse,
	PostApiRoomDraftValidateError,
	PostApiRoomDraftByEntityIdValidateData,
	PostApiRoomDraftByEntityIdValidateResponse,
	PostApiRoomDraftByEntityIdValidateError,
	PostApiSchedulingFindSlotsData,
	PostApiSchedulingFindSlotsResponse,
	GetApiSchedulingConstraintByIdData,
	GetApiSchedulingConstraintByIdResponse,
	GetApiSchedulingConstraintByIdError,
	PatchApiSchedulingConstraintByIdData,
	PatchApiSchedulingConstraintByIdResponse,
	PatchApiSchedulingConstraintByIdError,
	PutApiSchedulingConstraintByIdData,
	PutApiSchedulingConstraintByIdResponse,
	PutApiSchedulingConstraintByIdError,
	PostApiSchedulingConstraintSearchData,
	PostApiSchedulingConstraintSearchResponse,
	GetApiSchedulingConstraintData,
	GetApiSchedulingConstraintResponse,
	PostApiSchedulingConstraintData,
	PostApiSchedulingConstraintResponse,
	PostApiSchedulingConstraintError,
	PostApiSchedulingConstraintDraftData,
	PostApiSchedulingConstraintDraftResponse,
	PostApiSchedulingConstraintDraftError,
	GetApiSchedulingConstraintDraftByEntityIdData,
	GetApiSchedulingConstraintDraftByEntityIdResponse,
	GetApiSchedulingConstraintDraftByEntityIdError,
	PutApiSchedulingConstraintDraftByEntityIdData,
	PutApiSchedulingConstraintDraftByEntityIdResponse,
	PutApiSchedulingConstraintDraftByEntityIdError,
	PostApiSchedulingConstraintDraftByEntityIdCommitData,
	PostApiSchedulingConstraintDraftByEntityIdCommitResponse,
	PostApiSchedulingConstraintDraftByEntityIdCommitError,
	PostApiSchedulingConstraintDraftValidateData,
	PostApiSchedulingConstraintDraftValidateResponse,
	PostApiSchedulingConstraintDraftValidateError,
	PostApiSchedulingConstraintDraftByEntityIdValidateData,
	PostApiSchedulingConstraintDraftByEntityIdValidateResponse,
	PostApiSchedulingConstraintDraftByEntityIdValidateError,
	GetApiStudyByIdData,
	GetApiStudyByIdResponse,
	GetApiStudyByIdError,
	PatchApiStudyByIdData,
	PatchApiStudyByIdResponse,
	PatchApiStudyByIdError,
	PutApiStudyByIdData,
	PutApiStudyByIdResponse,
	PutApiStudyByIdError,
	PostApiStudySearchData,
	PostApiStudySearchResponse,
	GetApiStudyData,
	GetApiStudyResponse,
	PostApiStudyData,
	PostApiStudyResponse,
	PostApiStudyError,
	PostApiStudyDraftData,
	PostApiStudyDraftResponse,
	PostApiStudyDraftError,
	GetApiStudyDraftByEntityIdData,
	GetApiStudyDraftByEntityIdResponse,
	GetApiStudyDraftByEntityIdError,
	PutApiStudyDraftByEntityIdData,
	PutApiStudyDraftByEntityIdResponse,
	PutApiStudyDraftByEntityIdError,
	PostApiStudyDraftByEntityIdCommitData,
	PostApiStudyDraftByEntityIdCommitResponse,
	PostApiStudyDraftByEntityIdCommitError,
	PostApiStudyDraftValidateData,
	PostApiStudyDraftValidateResponse,
	PostApiStudyDraftValidateError,
	PostApiStudyDraftByEntityIdValidateData,
	PostApiStudyDraftByEntityIdValidateResponse,
	PostApiStudyDraftByEntityIdValidateError,
	GetApiTechnicianByIdData,
	GetApiTechnicianByIdResponse,
	GetApiTechnicianByIdError,
	PatchApiTechnicianByIdData,
	PatchApiTechnicianByIdResponse,
	PatchApiTechnicianByIdError,
	PutApiTechnicianByIdData,
	PutApiTechnicianByIdResponse,
	PutApiTechnicianByIdError,
	PostApiTechnicianSearchData,
	PostApiTechnicianSearchResponse,
	GetApiTechnicianData,
	GetApiTechnicianResponse,
	PostApiTechnicianData,
	PostApiTechnicianResponse,
	PostApiTechnicianError,
	PostApiTechnicianDraftData,
	PostApiTechnicianDraftResponse,
	PostApiTechnicianDraftError,
	GetApiTechnicianDraftByEntityIdData,
	GetApiTechnicianDraftByEntityIdResponse,
	GetApiTechnicianDraftByEntityIdError,
	PutApiTechnicianDraftByEntityIdData,
	PutApiTechnicianDraftByEntityIdResponse,
	PutApiTechnicianDraftByEntityIdError,
	PostApiTechnicianDraftByEntityIdCommitData,
	PostApiTechnicianDraftByEntityIdCommitResponse,
	PostApiTechnicianDraftByEntityIdCommitError,
	PostApiTechnicianDraftValidateData,
	PostApiTechnicianDraftValidateResponse,
	PostApiTechnicianDraftValidateError,
	PostApiTechnicianDraftByEntityIdValidateData,
	PostApiTechnicianDraftByEntityIdValidateResponse,
	PostApiTechnicianDraftByEntityIdValidateError,
	GetApiTechnicianAppointmentByIdData,
	GetApiTechnicianAppointmentByIdResponse,
	GetApiTechnicianAppointmentByIdError,
	PatchApiTechnicianAppointmentByIdData,
	PatchApiTechnicianAppointmentByIdResponse,
	PatchApiTechnicianAppointmentByIdError,
	PutApiTechnicianAppointmentByIdData,
	PutApiTechnicianAppointmentByIdResponse,
	PutApiTechnicianAppointmentByIdError,
	PostApiTechnicianAppointmentSearchData,
	PostApiTechnicianAppointmentSearchResponse,
	GetApiTechnicianAppointmentData,
	GetApiTechnicianAppointmentResponse,
	PostApiTechnicianAppointmentData,
	PostApiTechnicianAppointmentResponse,
	PostApiTechnicianAppointmentError,
	PostApiTechnicianAppointmentDraftData,
	PostApiTechnicianAppointmentDraftResponse,
	PostApiTechnicianAppointmentDraftError,
	GetApiTechnicianAppointmentDraftByEntityIdData,
	GetApiTechnicianAppointmentDraftByEntityIdResponse,
	GetApiTechnicianAppointmentDraftByEntityIdError,
	PutApiTechnicianAppointmentDraftByEntityIdData,
	PutApiTechnicianAppointmentDraftByEntityIdResponse,
	PutApiTechnicianAppointmentDraftByEntityIdError,
	PostApiTechnicianAppointmentDraftByEntityIdCommitData,
	PostApiTechnicianAppointmentDraftByEntityIdCommitResponse,
	PostApiTechnicianAppointmentDraftByEntityIdCommitError,
	PostApiTechnicianAppointmentDraftValidateData,
	PostApiTechnicianAppointmentDraftValidateResponse,
	PostApiTechnicianAppointmentDraftValidateError,
	PostApiTechnicianAppointmentDraftByEntityIdValidateData,
	PostApiTechnicianAppointmentDraftByEntityIdValidateResponse,
	PostApiTechnicianAppointmentDraftByEntityIdValidateError,
	GetApiWorkflowFlowsData,
	GetApiWorkflowMyTasksData,
	GetApiWorkflowProfileData,
} from './types.gen';
import { client as _heyApiClient } from './client.gen';

export type Options<
	TData extends TDataShape = TDataShape,
	ThrowOnError extends boolean = boolean,
> = ClientOptions<TData, ThrowOnError> & {
	/**
	 * You can provide a client instance returned by `createClient()` instead of
	 * individual options. This might be also useful if you want to implement a
	 * custom client.
	 */
	client?: Client;
	/**
	 * You can pass arbitrary values through the `meta` object. This can be
	 * used to access values that aren't defined as part of the SDK function.
	 */
	meta?: Record<string, unknown>;
};

export const getApiAddNewOrderValidationRules = <ThrowOnError extends boolean = false>(
	options?: Options<GetApiAddNewOrderValidationRulesData, ThrowOnError>
) => {
	return (options?.client ?? _heyApiClient).get<
		GetApiAddNewOrderValidationRulesResponse,
		unknown,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/AddNewOrder/validation-rules',
		...options,
	});
};

export const getApiAddNewOrderStateById = <ThrowOnError extends boolean = false>(
	options: Options<GetApiAddNewOrderStateByIdData, ThrowOnError>
) => {
	return (options.client ?? _heyApiClient).get<
		GetApiAddNewOrderStateByIdResponse,
		unknown,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/AddNewOrder/state/{id}',
		...options,
	});
};

export const postApiAddNewOrderStart = <ThrowOnError extends boolean = false>(
	options?: Options<PostApiAddNewOrderStartData, ThrowOnError>
) => {
	return (options?.client ?? _heyApiClient).post<
		PostApiAddNewOrderStartResponse,
		unknown,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/AddNewOrder/start',
		...options,
		headers: {
			'Content-Type': 'application/json',
			...options?.headers,
		},
	});
};

export const postApiAddNewOrderRewind = <ThrowOnError extends boolean = false>(
	options?: Options<PostApiAddNewOrderRewindData, ThrowOnError>
) => {
	return (options?.client ?? _heyApiClient).post<unknown, unknown, ThrowOnError>({
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/AddNewOrder/rewind',
		...options,
	});
};

export const postApiAddNewOrderList = <ThrowOnError extends boolean = false>(
	options?: Options<PostApiAddNewOrderListData, ThrowOnError>
) => {
	return (options?.client ?? _heyApiClient).post<
		PostApiAddNewOrderListResponse,
		unknown,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/AddNewOrder/list',
		...options,
		headers: {
			'Content-Type': 'application/json',
			...options?.headers,
		},
	});
};

export const postApiAddNewOrderAddStudy = <ThrowOnError extends boolean = false>(
	options?: Options<PostApiAddNewOrderAddStudyData, ThrowOnError>
) => {
	return (options?.client ?? _heyApiClient).post<
		PostApiAddNewOrderAddStudyResponse,
		unknown,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/AddNewOrder/add-study',
		...options,
		headers: {
			'Content-Type': 'application/json',
			...options?.headers,
		},
	});
};

export const postApiAddNewOrderAddStudyDraft = <ThrowOnError extends boolean = false>(
	options?: Options<PostApiAddNewOrderAddStudyDraftData, ThrowOnError>
) => {
	return (options?.client ?? _heyApiClient).post<
		PostApiAddNewOrderAddStudyDraftResponse,
		unknown,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/AddNewOrder/add-study/draft',
		...options,
		headers: {
			'Content-Type': 'application/json',
			...options?.headers,
		},
	});
};

export const postApiAddNewOrderAddStudyValidate = <ThrowOnError extends boolean = false>(
	options?: Options<PostApiAddNewOrderAddStudyValidateData, ThrowOnError>
) => {
	return (options?.client ?? _heyApiClient).post<
		PostApiAddNewOrderAddStudyValidateResponse,
		unknown,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/AddNewOrder/add-study/validate',
		...options,
		headers: {
			'Content-Type': 'application/json',
			...options?.headers,
		},
	});
};

export const postApiAddNewOrderAddCareLocation = <ThrowOnError extends boolean = false>(
	options?: Options<PostApiAddNewOrderAddCareLocationData, ThrowOnError>
) => {
	return (options?.client ?? _heyApiClient).post<
		PostApiAddNewOrderAddCareLocationResponse,
		unknown,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/AddNewOrder/add-care-location',
		...options,
		headers: {
			'Content-Type': 'application/json',
			...options?.headers,
		},
	});
};

export const postApiAddNewOrderAddCareLocationDraft = <ThrowOnError extends boolean = false>(
	options?: Options<PostApiAddNewOrderAddCareLocationDraftData, ThrowOnError>
) => {
	return (options?.client ?? _heyApiClient).post<
		PostApiAddNewOrderAddCareLocationDraftResponse,
		unknown,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/AddNewOrder/add-care-location/draft',
		...options,
		headers: {
			'Content-Type': 'application/json',
			...options?.headers,
		},
	});
};

export const postApiAddNewOrderAddCareLocationValidate = <ThrowOnError extends boolean = false>(
	options?: Options<PostApiAddNewOrderAddCareLocationValidateData, ThrowOnError>
) => {
	return (options?.client ?? _heyApiClient).post<
		PostApiAddNewOrderAddCareLocationValidateResponse,
		unknown,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/AddNewOrder/add-care-location/validate',
		...options,
		headers: {
			'Content-Type': 'application/json',
			...options?.headers,
		},
	});
};

export const postApiAddNewOrderAddPhysicians = <ThrowOnError extends boolean = false>(
	options?: Options<PostApiAddNewOrderAddPhysiciansData, ThrowOnError>
) => {
	return (options?.client ?? _heyApiClient).post<
		PostApiAddNewOrderAddPhysiciansResponse,
		unknown,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/AddNewOrder/add-physicians',
		...options,
		headers: {
			'Content-Type': 'application/json',
			...options?.headers,
		},
	});
};

export const postApiAddNewOrderAddPhysiciansDraft = <ThrowOnError extends boolean = false>(
	options?: Options<PostApiAddNewOrderAddPhysiciansDraftData, ThrowOnError>
) => {
	return (options?.client ?? _heyApiClient).post<
		PostApiAddNewOrderAddPhysiciansDraftResponse,
		unknown,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/AddNewOrder/add-physicians/draft',
		...options,
		headers: {
			'Content-Type': 'application/json',
			...options?.headers,
		},
	});
};

export const postApiAddNewOrderAddPhysiciansValidate = <ThrowOnError extends boolean = false>(
	options?: Options<PostApiAddNewOrderAddPhysiciansValidateData, ThrowOnError>
) => {
	return (options?.client ?? _heyApiClient).post<
		PostApiAddNewOrderAddPhysiciansValidateResponse,
		unknown,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/AddNewOrder/add-physicians/validate',
		...options,
		headers: {
			'Content-Type': 'application/json',
			...options?.headers,
		},
	});
};

export const postApiAddNewOrderReviewAndSubmitOrder = <ThrowOnError extends boolean = false>(
	options?: Options<PostApiAddNewOrderReviewAndSubmitOrderData, ThrowOnError>
) => {
	return (options?.client ?? _heyApiClient).post<
		PostApiAddNewOrderReviewAndSubmitOrderResponse,
		unknown,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/AddNewOrder/review-and-submit-order',
		...options,
		headers: {
			'Content-Type': 'application/json',
			...options?.headers,
		},
	});
};

export const postApiAddNewOrderReviewAndSubmitOrderDraft = <ThrowOnError extends boolean = false>(
	options?: Options<PostApiAddNewOrderReviewAndSubmitOrderDraftData, ThrowOnError>
) => {
	return (options?.client ?? _heyApiClient).post<
		PostApiAddNewOrderReviewAndSubmitOrderDraftResponse,
		unknown,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/AddNewOrder/review-and-submit-order/draft',
		...options,
		headers: {
			'Content-Type': 'application/json',
			...options?.headers,
		},
	});
};

export const postApiAddNewOrderReviewAndSubmitOrderValidate = <
	ThrowOnError extends boolean = false,
>(
	options?: Options<PostApiAddNewOrderReviewAndSubmitOrderValidateData, ThrowOnError>
) => {
	return (options?.client ?? _heyApiClient).post<
		PostApiAddNewOrderReviewAndSubmitOrderValidateResponse,
		unknown,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/AddNewOrder/review-and-submit-order/validate',
		...options,
		headers: {
			'Content-Type': 'application/json',
			...options?.headers,
		},
	});
};

export const getApiAddNewPatientValidationRules = <ThrowOnError extends boolean = false>(
	options?: Options<GetApiAddNewPatientValidationRulesData, ThrowOnError>
) => {
	return (options?.client ?? _heyApiClient).get<
		GetApiAddNewPatientValidationRulesResponse,
		unknown,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/AddNewPatient/validation-rules',
		...options,
	});
};

export const getApiAddNewPatientStateById = <ThrowOnError extends boolean = false>(
	options: Options<GetApiAddNewPatientStateByIdData, ThrowOnError>
) => {
	return (options.client ?? _heyApiClient).get<
		GetApiAddNewPatientStateByIdResponse,
		unknown,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/AddNewPatient/state/{id}',
		...options,
	});
};

export const postApiAddNewPatientStart = <ThrowOnError extends boolean = false>(
	options?: Options<PostApiAddNewPatientStartData, ThrowOnError>
) => {
	return (options?.client ?? _heyApiClient).post<
		PostApiAddNewPatientStartResponse,
		unknown,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/AddNewPatient/start',
		...options,
		headers: {
			'Content-Type': 'application/json',
			...options?.headers,
		},
	});
};

export const postApiAddNewPatientRewind = <ThrowOnError extends boolean = false>(
	options?: Options<PostApiAddNewPatientRewindData, ThrowOnError>
) => {
	return (options?.client ?? _heyApiClient).post<unknown, unknown, ThrowOnError>({
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/AddNewPatient/rewind',
		...options,
	});
};

export const postApiAddNewPatientList = <ThrowOnError extends boolean = false>(
	options?: Options<PostApiAddNewPatientListData, ThrowOnError>
) => {
	return (options?.client ?? _heyApiClient).post<
		PostApiAddNewPatientListResponse,
		unknown,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/AddNewPatient/list',
		...options,
		headers: {
			'Content-Type': 'application/json',
			...options?.headers,
		},
	});
};

export const postApiAddNewPatientAddBasicInformation = <ThrowOnError extends boolean = false>(
	options?: Options<PostApiAddNewPatientAddBasicInformationData, ThrowOnError>
) => {
	return (options?.client ?? _heyApiClient).post<
		PostApiAddNewPatientAddBasicInformationResponse,
		unknown,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/AddNewPatient/add-basic-information',
		...options,
		headers: {
			'Content-Type': 'application/json',
			...options?.headers,
		},
	});
};

export const postApiAddNewPatientAddBasicInformationDraft = <ThrowOnError extends boolean = false>(
	options?: Options<PostApiAddNewPatientAddBasicInformationDraftData, ThrowOnError>
) => {
	return (options?.client ?? _heyApiClient).post<
		PostApiAddNewPatientAddBasicInformationDraftResponse,
		unknown,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/AddNewPatient/add-basic-information/draft',
		...options,
		headers: {
			'Content-Type': 'application/json',
			...options?.headers,
		},
	});
};

export const postApiAddNewPatientAddBasicInformationValidate = <
	ThrowOnError extends boolean = false,
>(
	options?: Options<PostApiAddNewPatientAddBasicInformationValidateData, ThrowOnError>
) => {
	return (options?.client ?? _heyApiClient).post<
		PostApiAddNewPatientAddBasicInformationValidateResponse,
		unknown,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/AddNewPatient/add-basic-information/validate',
		...options,
		headers: {
			'Content-Type': 'application/json',
			...options?.headers,
		},
	});
};

export const postApiAddNewPatientAddContacts = <ThrowOnError extends boolean = false>(
	options?: Options<PostApiAddNewPatientAddContactsData, ThrowOnError>
) => {
	return (options?.client ?? _heyApiClient).post<
		PostApiAddNewPatientAddContactsResponse,
		unknown,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/AddNewPatient/add-contacts',
		...options,
		headers: {
			'Content-Type': 'application/json',
			...options?.headers,
		},
	});
};

export const postApiAddNewPatientAddContactsDraft = <ThrowOnError extends boolean = false>(
	options?: Options<PostApiAddNewPatientAddContactsDraftData, ThrowOnError>
) => {
	return (options?.client ?? _heyApiClient).post<
		PostApiAddNewPatientAddContactsDraftResponse,
		unknown,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/AddNewPatient/add-contacts/draft',
		...options,
		headers: {
			'Content-Type': 'application/json',
			...options?.headers,
		},
	});
};

export const postApiAddNewPatientAddContactsValidate = <ThrowOnError extends boolean = false>(
	options?: Options<PostApiAddNewPatientAddContactsValidateData, ThrowOnError>
) => {
	return (options?.client ?? _heyApiClient).post<
		PostApiAddNewPatientAddContactsValidateResponse,
		unknown,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/AddNewPatient/add-contacts/validate',
		...options,
		headers: {
			'Content-Type': 'application/json',
			...options?.headers,
		},
	});
};

export const postApiAddNewPatientAddAddresses = <ThrowOnError extends boolean = false>(
	options?: Options<PostApiAddNewPatientAddAddressesData, ThrowOnError>
) => {
	return (options?.client ?? _heyApiClient).post<
		PostApiAddNewPatientAddAddressesResponse,
		unknown,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/AddNewPatient/add-addresses',
		...options,
		headers: {
			'Content-Type': 'application/json',
			...options?.headers,
		},
	});
};

export const postApiAddNewPatientAddAddressesDraft = <ThrowOnError extends boolean = false>(
	options?: Options<PostApiAddNewPatientAddAddressesDraftData, ThrowOnError>
) => {
	return (options?.client ?? _heyApiClient).post<
		PostApiAddNewPatientAddAddressesDraftResponse,
		unknown,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/AddNewPatient/add-addresses/draft',
		...options,
		headers: {
			'Content-Type': 'application/json',
			...options?.headers,
		},
	});
};

export const postApiAddNewPatientAddAddressesValidate = <ThrowOnError extends boolean = false>(
	options?: Options<PostApiAddNewPatientAddAddressesValidateData, ThrowOnError>
) => {
	return (options?.client ?? _heyApiClient).post<
		PostApiAddNewPatientAddAddressesValidateResponse,
		unknown,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/AddNewPatient/add-addresses/validate',
		...options,
		headers: {
			'Content-Type': 'application/json',
			...options?.headers,
		},
	});
};

export const postApiAddNewPatientAddInsurances = <ThrowOnError extends boolean = false>(
	options?: Options<PostApiAddNewPatientAddInsurancesData, ThrowOnError>
) => {
	return (options?.client ?? _heyApiClient).post<
		PostApiAddNewPatientAddInsurancesResponse,
		unknown,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/AddNewPatient/add-insurances',
		...options,
		headers: {
			'Content-Type': 'application/json',
			...options?.headers,
		},
	});
};

export const postApiAddNewPatientAddInsurancesDraft = <ThrowOnError extends boolean = false>(
	options?: Options<PostApiAddNewPatientAddInsurancesDraftData, ThrowOnError>
) => {
	return (options?.client ?? _heyApiClient).post<
		PostApiAddNewPatientAddInsurancesDraftResponse,
		unknown,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/AddNewPatient/add-insurances/draft',
		...options,
		headers: {
			'Content-Type': 'application/json',
			...options?.headers,
		},
	});
};

export const postApiAddNewPatientAddInsurancesValidate = <ThrowOnError extends boolean = false>(
	options?: Options<PostApiAddNewPatientAddInsurancesValidateData, ThrowOnError>
) => {
	return (options?.client ?? _heyApiClient).post<
		PostApiAddNewPatientAddInsurancesValidateResponse,
		unknown,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/AddNewPatient/add-insurances/validate',
		...options,
		headers: {
			'Content-Type': 'application/json',
			...options?.headers,
		},
	});
};

export const postApiAddNewPatientAddGuardians = <ThrowOnError extends boolean = false>(
	options?: Options<PostApiAddNewPatientAddGuardiansData, ThrowOnError>
) => {
	return (options?.client ?? _heyApiClient).post<
		PostApiAddNewPatientAddGuardiansResponse,
		unknown,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/AddNewPatient/add-guardians',
		...options,
		headers: {
			'Content-Type': 'application/json',
			...options?.headers,
		},
	});
};

export const postApiAddNewPatientAddGuardiansDraft = <ThrowOnError extends boolean = false>(
	options?: Options<PostApiAddNewPatientAddGuardiansDraftData, ThrowOnError>
) => {
	return (options?.client ?? _heyApiClient).post<
		PostApiAddNewPatientAddGuardiansDraftResponse,
		unknown,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/AddNewPatient/add-guardians/draft',
		...options,
		headers: {
			'Content-Type': 'application/json',
			...options?.headers,
		},
	});
};

export const postApiAddNewPatientAddGuardiansValidate = <ThrowOnError extends boolean = false>(
	options?: Options<PostApiAddNewPatientAddGuardiansValidateData, ThrowOnError>
) => {
	return (options?.client ?? _heyApiClient).post<
		PostApiAddNewPatientAddGuardiansValidateResponse,
		unknown,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/AddNewPatient/add-guardians/validate',
		...options,
		headers: {
			'Content-Type': 'application/json',
			...options?.headers,
		},
	});
};

export const postApiAddNewPatientAddClinicalInformation = <ThrowOnError extends boolean = false>(
	options?: Options<PostApiAddNewPatientAddClinicalInformationData, ThrowOnError>
) => {
	return (options?.client ?? _heyApiClient).post<
		PostApiAddNewPatientAddClinicalInformationResponse,
		unknown,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/AddNewPatient/add-clinical-information',
		...options,
		headers: {
			'Content-Type': 'application/json',
			...options?.headers,
		},
	});
};

export const postApiAddNewPatientAddClinicalInformationDraft = <
	ThrowOnError extends boolean = false,
>(
	options?: Options<PostApiAddNewPatientAddClinicalInformationDraftData, ThrowOnError>
) => {
	return (options?.client ?? _heyApiClient).post<
		PostApiAddNewPatientAddClinicalInformationDraftResponse,
		unknown,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/AddNewPatient/add-clinical-information/draft',
		...options,
		headers: {
			'Content-Type': 'application/json',
			...options?.headers,
		},
	});
};

export const postApiAddNewPatientAddClinicalInformationValidate = <
	ThrowOnError extends boolean = false,
>(
	options?: Options<PostApiAddNewPatientAddClinicalInformationValidateData, ThrowOnError>
) => {
	return (options?.client ?? _heyApiClient).post<
		PostApiAddNewPatientAddClinicalInformationValidateResponse,
		unknown,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/AddNewPatient/add-clinical-information/validate',
		...options,
		headers: {
			'Content-Type': 'application/json',
			...options?.headers,
		},
	});
};

export const getApiCareLocationById = <ThrowOnError extends boolean = false>(
	options: Options<GetApiCareLocationByIdData, ThrowOnError>
) => {
	return (options.client ?? _heyApiClient).get<
		GetApiCareLocationByIdResponse,
		GetApiCareLocationByIdError,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/CareLocation/{id}',
		...options,
	});
};

export const patchApiCareLocationById = <ThrowOnError extends boolean = false>(
	options: Options<PatchApiCareLocationByIdData, ThrowOnError>
) => {
	return (options.client ?? _heyApiClient).patch<
		PatchApiCareLocationByIdResponse,
		PatchApiCareLocationByIdError,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/CareLocation/{id}',
		...options,
		headers: {
			'Content-Type': 'application/json',
			...options?.headers,
		},
	});
};

export const putApiCareLocationById = <ThrowOnError extends boolean = false>(
	options: Options<PutApiCareLocationByIdData, ThrowOnError>
) => {
	return (options.client ?? _heyApiClient).put<
		PutApiCareLocationByIdResponse,
		PutApiCareLocationByIdError,
		ThrowOnError
	>({
		responseType: 'text',
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/CareLocation/{id}',
		...options,
		headers: {
			'Content-Type': 'application/json',
			...options?.headers,
		},
	});
};

export const postApiCareLocationSearch = <ThrowOnError extends boolean = false>(
	options?: Options<PostApiCareLocationSearchData, ThrowOnError>
) => {
	return (options?.client ?? _heyApiClient).post<
		PostApiCareLocationSearchResponse,
		unknown,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/CareLocation/search',
		...options,
		headers: {
			'Content-Type': 'application/json',
			...options?.headers,
		},
	});
};

export const getApiCareLocation = <ThrowOnError extends boolean = false>(
	options?: Options<GetApiCareLocationData, ThrowOnError>
) => {
	return (options?.client ?? _heyApiClient).get<GetApiCareLocationResponse, unknown, ThrowOnError>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/CareLocation',
		...options,
	});
};

export const postApiCareLocation = <ThrowOnError extends boolean = false>(
	options?: Options<PostApiCareLocationData, ThrowOnError>
) => {
	return (options?.client ?? _heyApiClient).post<
		PostApiCareLocationResponse,
		PostApiCareLocationError,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/CareLocation',
		...options,
		headers: {
			'Content-Type': 'application/json',
			...options?.headers,
		},
	});
};

export const postApiCareLocationDraft = <ThrowOnError extends boolean = false>(
	options?: Options<PostApiCareLocationDraftData, ThrowOnError>
) => {
	return (options?.client ?? _heyApiClient).post<
		PostApiCareLocationDraftResponse,
		PostApiCareLocationDraftError,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/CareLocation/draft',
		...options,
		headers: {
			'Content-Type': 'application/json',
			...options?.headers,
		},
	});
};

export const getApiCareLocationDraftByEntityId = <ThrowOnError extends boolean = false>(
	options: Options<GetApiCareLocationDraftByEntityIdData, ThrowOnError>
) => {
	return (options.client ?? _heyApiClient).get<
		GetApiCareLocationDraftByEntityIdResponse,
		GetApiCareLocationDraftByEntityIdError,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/CareLocation/draft/{entityId}',
		...options,
	});
};

export const putApiCareLocationDraftByEntityId = <ThrowOnError extends boolean = false>(
	options: Options<PutApiCareLocationDraftByEntityIdData, ThrowOnError>
) => {
	return (options.client ?? _heyApiClient).put<
		PutApiCareLocationDraftByEntityIdResponse,
		PutApiCareLocationDraftByEntityIdError,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/CareLocation/draft/{entityId}',
		...options,
		headers: {
			'Content-Type': 'application/json',
			...options?.headers,
		},
	});
};

export const postApiCareLocationDraftByEntityIdCommit = <ThrowOnError extends boolean = false>(
	options: Options<PostApiCareLocationDraftByEntityIdCommitData, ThrowOnError>
) => {
	return (options.client ?? _heyApiClient).post<
		PostApiCareLocationDraftByEntityIdCommitResponse,
		PostApiCareLocationDraftByEntityIdCommitError,
		ThrowOnError
	>({
		responseType: 'text',
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/CareLocation/draft/{entityId}/commit',
		...options,
	});
};

export const postApiCareLocationDraftValidate = <ThrowOnError extends boolean = false>(
	options?: Options<PostApiCareLocationDraftValidateData, ThrowOnError>
) => {
	return (options?.client ?? _heyApiClient).post<
		PostApiCareLocationDraftValidateResponse,
		PostApiCareLocationDraftValidateError,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/CareLocation/draft/validate',
		...options,
		headers: {
			'Content-Type': 'application/json',
			...options?.headers,
		},
	});
};

export const postApiCareLocationDraftByEntityIdValidate = <ThrowOnError extends boolean = false>(
	options: Options<PostApiCareLocationDraftByEntityIdValidateData, ThrowOnError>
) => {
	return (options.client ?? _heyApiClient).post<
		PostApiCareLocationDraftByEntityIdValidateResponse,
		PostApiCareLocationDraftByEntityIdValidateError,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/CareLocation/draft/{entityId}/validate',
		...options,
	});
};

export const getApiDraftById = <ThrowOnError extends boolean = false>(
	options: Options<GetApiDraftByIdData, ThrowOnError>
) => {
	return (options.client ?? _heyApiClient).get<
		GetApiDraftByIdResponse,
		GetApiDraftByIdError,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/Draft/{id}',
		...options,
	});
};

export const patchApiDraftById = <ThrowOnError extends boolean = false>(
	options: Options<PatchApiDraftByIdData, ThrowOnError>
) => {
	return (options.client ?? _heyApiClient).patch<
		PatchApiDraftByIdResponse,
		PatchApiDraftByIdError,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/Draft/{id}',
		...options,
		headers: {
			'Content-Type': 'application/json',
			...options?.headers,
		},
	});
};

export const putApiDraftById = <ThrowOnError extends boolean = false>(
	options: Options<PutApiDraftByIdData, ThrowOnError>
) => {
	return (options.client ?? _heyApiClient).put<
		PutApiDraftByIdResponse,
		PutApiDraftByIdError,
		ThrowOnError
	>({
		responseType: 'text',
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/Draft/{id}',
		...options,
		headers: {
			'Content-Type': 'application/json',
			...options?.headers,
		},
	});
};

export const postApiDraftSearch = <ThrowOnError extends boolean = false>(
	options?: Options<PostApiDraftSearchData, ThrowOnError>
) => {
	return (options?.client ?? _heyApiClient).post<PostApiDraftSearchResponse, unknown, ThrowOnError>(
		{
			responseType: 'text',
			security: [
				{
					scheme: 'bearer',
					type: 'http',
				},
			],
			url: '/api/Draft/search',
			...options,
			headers: {
				'Content-Type': 'application/json',
				...options?.headers,
			},
		}
	);
};

export const getApiDraft = <ThrowOnError extends boolean = false>(
	options?: Options<GetApiDraftData, ThrowOnError>
) => {
	return (options?.client ?? _heyApiClient).get<GetApiDraftResponse, unknown, ThrowOnError>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/Draft',
		...options,
	});
};

export const postApiDraft = <ThrowOnError extends boolean = false>(
	options?: Options<PostApiDraftData, ThrowOnError>
) => {
	return (options?.client ?? _heyApiClient).post<
		PostApiDraftResponse,
		PostApiDraftError,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/Draft',
		...options,
		headers: {
			'Content-Type': 'application/json',
			...options?.headers,
		},
	});
};

export const postApiDraftDraft = <ThrowOnError extends boolean = false>(
	options?: Options<PostApiDraftDraftData, ThrowOnError>
) => {
	return (options?.client ?? _heyApiClient).post<
		PostApiDraftDraftResponse,
		PostApiDraftDraftError,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/Draft/draft',
		...options,
		headers: {
			'Content-Type': 'application/json',
			...options?.headers,
		},
	});
};

export const getApiDraftDraftByEntityId = <ThrowOnError extends boolean = false>(
	options: Options<GetApiDraftDraftByEntityIdData, ThrowOnError>
) => {
	return (options.client ?? _heyApiClient).get<
		GetApiDraftDraftByEntityIdResponse,
		GetApiDraftDraftByEntityIdError,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/Draft/draft/{entityId}',
		...options,
	});
};

export const putApiDraftDraftByEntityId = <ThrowOnError extends boolean = false>(
	options: Options<PutApiDraftDraftByEntityIdData, ThrowOnError>
) => {
	return (options.client ?? _heyApiClient).put<
		PutApiDraftDraftByEntityIdResponse,
		PutApiDraftDraftByEntityIdError,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/Draft/draft/{entityId}',
		...options,
		headers: {
			'Content-Type': 'application/json',
			...options?.headers,
		},
	});
};

export const postApiDraftDraftByEntityIdCommit = <ThrowOnError extends boolean = false>(
	options: Options<PostApiDraftDraftByEntityIdCommitData, ThrowOnError>
) => {
	return (options.client ?? _heyApiClient).post<
		PostApiDraftDraftByEntityIdCommitResponse,
		PostApiDraftDraftByEntityIdCommitError,
		ThrowOnError
	>({
		responseType: 'text',
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/Draft/draft/{entityId}/commit',
		...options,
	});
};

export const postApiDraftDraftValidate = <ThrowOnError extends boolean = false>(
	options?: Options<PostApiDraftDraftValidateData, ThrowOnError>
) => {
	return (options?.client ?? _heyApiClient).post<
		PostApiDraftDraftValidateResponse,
		PostApiDraftDraftValidateError,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/Draft/draft/validate',
		...options,
		headers: {
			'Content-Type': 'application/json',
			...options?.headers,
		},
	});
};

export const postApiDraftDraftByEntityIdValidate = <ThrowOnError extends boolean = false>(
	options: Options<PostApiDraftDraftByEntityIdValidateData, ThrowOnError>
) => {
	return (options.client ?? _heyApiClient).post<
		PostApiDraftDraftByEntityIdValidateResponse,
		PostApiDraftDraftByEntityIdValidateError,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/Draft/draft/{entityId}/validate',
		...options,
	});
};

export const getApiEquipmentById = <ThrowOnError extends boolean = false>(
	options: Options<GetApiEquipmentByIdData, ThrowOnError>
) => {
	return (options.client ?? _heyApiClient).get<
		GetApiEquipmentByIdResponse,
		GetApiEquipmentByIdError,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/Equipment/{id}',
		...options,
	});
};

export const patchApiEquipmentById = <ThrowOnError extends boolean = false>(
	options: Options<PatchApiEquipmentByIdData, ThrowOnError>
) => {
	return (options.client ?? _heyApiClient).patch<
		PatchApiEquipmentByIdResponse,
		PatchApiEquipmentByIdError,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/Equipment/{id}',
		...options,
		headers: {
			'Content-Type': 'application/json',
			...options?.headers,
		},
	});
};

export const putApiEquipmentById = <ThrowOnError extends boolean = false>(
	options: Options<PutApiEquipmentByIdData, ThrowOnError>
) => {
	return (options.client ?? _heyApiClient).put<
		PutApiEquipmentByIdResponse,
		PutApiEquipmentByIdError,
		ThrowOnError
	>({
		responseType: 'text',
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/Equipment/{id}',
		...options,
		headers: {
			'Content-Type': 'application/json',
			...options?.headers,
		},
	});
};

export const postApiEquipmentSearch = <ThrowOnError extends boolean = false>(
	options?: Options<PostApiEquipmentSearchData, ThrowOnError>
) => {
	return (options?.client ?? _heyApiClient).post<
		PostApiEquipmentSearchResponse,
		unknown,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/Equipment/search',
		...options,
		headers: {
			'Content-Type': 'application/json',
			...options?.headers,
		},
	});
};

export const getApiEquipment = <ThrowOnError extends boolean = false>(
	options?: Options<GetApiEquipmentData, ThrowOnError>
) => {
	return (options?.client ?? _heyApiClient).get<GetApiEquipmentResponse, unknown, ThrowOnError>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/Equipment',
		...options,
	});
};

export const postApiEquipment = <ThrowOnError extends boolean = false>(
	options?: Options<PostApiEquipmentData, ThrowOnError>
) => {
	return (options?.client ?? _heyApiClient).post<
		PostApiEquipmentResponse,
		PostApiEquipmentError,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/Equipment',
		...options,
		headers: {
			'Content-Type': 'application/json',
			...options?.headers,
		},
	});
};

export const postApiEquipmentDraft = <ThrowOnError extends boolean = false>(
	options?: Options<PostApiEquipmentDraftData, ThrowOnError>
) => {
	return (options?.client ?? _heyApiClient).post<
		PostApiEquipmentDraftResponse,
		PostApiEquipmentDraftError,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/Equipment/draft',
		...options,
		headers: {
			'Content-Type': 'application/json',
			...options?.headers,
		},
	});
};

export const getApiEquipmentDraftByEntityId = <ThrowOnError extends boolean = false>(
	options: Options<GetApiEquipmentDraftByEntityIdData, ThrowOnError>
) => {
	return (options.client ?? _heyApiClient).get<
		GetApiEquipmentDraftByEntityIdResponse,
		GetApiEquipmentDraftByEntityIdError,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/Equipment/draft/{entityId}',
		...options,
	});
};

export const putApiEquipmentDraftByEntityId = <ThrowOnError extends boolean = false>(
	options: Options<PutApiEquipmentDraftByEntityIdData, ThrowOnError>
) => {
	return (options.client ?? _heyApiClient).put<
		PutApiEquipmentDraftByEntityIdResponse,
		PutApiEquipmentDraftByEntityIdError,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/Equipment/draft/{entityId}',
		...options,
		headers: {
			'Content-Type': 'application/json',
			...options?.headers,
		},
	});
};

export const postApiEquipmentDraftByEntityIdCommit = <ThrowOnError extends boolean = false>(
	options: Options<PostApiEquipmentDraftByEntityIdCommitData, ThrowOnError>
) => {
	return (options.client ?? _heyApiClient).post<
		PostApiEquipmentDraftByEntityIdCommitResponse,
		PostApiEquipmentDraftByEntityIdCommitError,
		ThrowOnError
	>({
		responseType: 'text',
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/Equipment/draft/{entityId}/commit',
		...options,
	});
};

export const postApiEquipmentDraftValidate = <ThrowOnError extends boolean = false>(
	options?: Options<PostApiEquipmentDraftValidateData, ThrowOnError>
) => {
	return (options?.client ?? _heyApiClient).post<
		PostApiEquipmentDraftValidateResponse,
		PostApiEquipmentDraftValidateError,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/Equipment/draft/validate',
		...options,
		headers: {
			'Content-Type': 'application/json',
			...options?.headers,
		},
	});
};

export const postApiEquipmentDraftByEntityIdValidate = <ThrowOnError extends boolean = false>(
	options: Options<PostApiEquipmentDraftByEntityIdValidateData, ThrowOnError>
) => {
	return (options.client ?? _heyApiClient).post<
		PostApiEquipmentDraftByEntityIdValidateResponse,
		PostApiEquipmentDraftByEntityIdValidateError,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/Equipment/draft/{entityId}/validate',
		...options,
	});
};

export const postApiFileRequestUploadToken = <ThrowOnError extends boolean = false>(
	options?: Options<PostApiFileRequestUploadTokenData, ThrowOnError>
) => {
	return (options?.client ?? _heyApiClient).post<
		PostApiFileRequestUploadTokenResponse,
		PostApiFileRequestUploadTokenError,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/File/request-upload-token',
		...options,
		headers: {
			'Content-Type': 'application/json',
			...options?.headers,
		},
	});
};

export const postApiFileUpload = <ThrowOnError extends boolean = false>(
	options: Options<PostApiFileUploadData, ThrowOnError>
) => {
	return (options.client ?? _heyApiClient).post<
		PostApiFileUploadResponse,
		PostApiFileUploadError,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/File/upload',
		...options,
	});
};

export const getApiFileStatusByFileId = <ThrowOnError extends boolean = false>(
	options: Options<GetApiFileStatusByFileIdData, ThrowOnError>
) => {
	return (options.client ?? _heyApiClient).get<
		GetApiFileStatusByFileIdResponse,
		GetApiFileStatusByFileIdError,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/File/status/{fileId}',
		...options,
	});
};

export const getApiHealth = <ThrowOnError extends boolean = false>(
	options?: Options<GetApiHealthData, ThrowOnError>
) => {
	return (options?.client ?? _heyApiClient).get<GetApiHealthResponse, unknown, ThrowOnError>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/Health',
		...options,
	});
};

export const getApiInsuranceById = <ThrowOnError extends boolean = false>(
	options: Options<GetApiInsuranceByIdData, ThrowOnError>
) => {
	return (options.client ?? _heyApiClient).get<
		GetApiInsuranceByIdResponse,
		GetApiInsuranceByIdError,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/Insurance/{id}',
		...options,
	});
};

export const patchApiInsuranceById = <ThrowOnError extends boolean = false>(
	options: Options<PatchApiInsuranceByIdData, ThrowOnError>
) => {
	return (options.client ?? _heyApiClient).patch<
		PatchApiInsuranceByIdResponse,
		PatchApiInsuranceByIdError,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/Insurance/{id}',
		...options,
		headers: {
			'Content-Type': 'application/json',
			...options?.headers,
		},
	});
};

export const putApiInsuranceById = <ThrowOnError extends boolean = false>(
	options: Options<PutApiInsuranceByIdData, ThrowOnError>
) => {
	return (options.client ?? _heyApiClient).put<
		PutApiInsuranceByIdResponse,
		PutApiInsuranceByIdError,
		ThrowOnError
	>({
		responseType: 'text',
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/Insurance/{id}',
		...options,
		headers: {
			'Content-Type': 'application/json',
			...options?.headers,
		},
	});
};

export const postApiInsuranceSearch = <ThrowOnError extends boolean = false>(
	options?: Options<PostApiInsuranceSearchData, ThrowOnError>
) => {
	return (options?.client ?? _heyApiClient).post<
		PostApiInsuranceSearchResponse,
		unknown,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/Insurance/search',
		...options,
		headers: {
			'Content-Type': 'application/json',
			...options?.headers,
		},
	});
};

export const getApiInsurance = <ThrowOnError extends boolean = false>(
	options?: Options<GetApiInsuranceData, ThrowOnError>
) => {
	return (options?.client ?? _heyApiClient).get<GetApiInsuranceResponse, unknown, ThrowOnError>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/Insurance',
		...options,
	});
};

export const postApiInsurance = <ThrowOnError extends boolean = false>(
	options?: Options<PostApiInsuranceData, ThrowOnError>
) => {
	return (options?.client ?? _heyApiClient).post<
		PostApiInsuranceResponse,
		PostApiInsuranceError,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/Insurance',
		...options,
		headers: {
			'Content-Type': 'application/json',
			...options?.headers,
		},
	});
};

export const postApiInsuranceDraft = <ThrowOnError extends boolean = false>(
	options?: Options<PostApiInsuranceDraftData, ThrowOnError>
) => {
	return (options?.client ?? _heyApiClient).post<
		PostApiInsuranceDraftResponse,
		PostApiInsuranceDraftError,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/Insurance/draft',
		...options,
		headers: {
			'Content-Type': 'application/json',
			...options?.headers,
		},
	});
};

export const getApiInsuranceDraftByEntityId = <ThrowOnError extends boolean = false>(
	options: Options<GetApiInsuranceDraftByEntityIdData, ThrowOnError>
) => {
	return (options.client ?? _heyApiClient).get<
		GetApiInsuranceDraftByEntityIdResponse,
		GetApiInsuranceDraftByEntityIdError,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/Insurance/draft/{entityId}',
		...options,
	});
};

export const putApiInsuranceDraftByEntityId = <ThrowOnError extends boolean = false>(
	options: Options<PutApiInsuranceDraftByEntityIdData, ThrowOnError>
) => {
	return (options.client ?? _heyApiClient).put<
		PutApiInsuranceDraftByEntityIdResponse,
		PutApiInsuranceDraftByEntityIdError,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/Insurance/draft/{entityId}',
		...options,
		headers: {
			'Content-Type': 'application/json',
			...options?.headers,
		},
	});
};

export const postApiInsuranceDraftByEntityIdCommit = <ThrowOnError extends boolean = false>(
	options: Options<PostApiInsuranceDraftByEntityIdCommitData, ThrowOnError>
) => {
	return (options.client ?? _heyApiClient).post<
		PostApiInsuranceDraftByEntityIdCommitResponse,
		PostApiInsuranceDraftByEntityIdCommitError,
		ThrowOnError
	>({
		responseType: 'text',
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/Insurance/draft/{entityId}/commit',
		...options,
	});
};

export const postApiInsuranceDraftValidate = <ThrowOnError extends boolean = false>(
	options?: Options<PostApiInsuranceDraftValidateData, ThrowOnError>
) => {
	return (options?.client ?? _heyApiClient).post<
		PostApiInsuranceDraftValidateResponse,
		PostApiInsuranceDraftValidateError,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/Insurance/draft/validate',
		...options,
		headers: {
			'Content-Type': 'application/json',
			...options?.headers,
		},
	});
};

export const postApiInsuranceDraftByEntityIdValidate = <ThrowOnError extends boolean = false>(
	options: Options<PostApiInsuranceDraftByEntityIdValidateData, ThrowOnError>
) => {
	return (options.client ?? _heyApiClient).post<
		PostApiInsuranceDraftByEntityIdValidateResponse,
		PostApiInsuranceDraftByEntityIdValidateError,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/Insurance/draft/{entityId}/validate',
		...options,
	});
};

export const postApiInsuranceVerificationStart = <ThrowOnError extends boolean = false>(
	options?: Options<PostApiInsuranceVerificationStartData, ThrowOnError>
) => {
	return (options?.client ?? _heyApiClient).post<
		unknown,
		PostApiInsuranceVerificationStartError,
		ThrowOnError
	>({
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/InsuranceVerification/start',
		...options,
		headers: {
			'Content-Type': 'application/json',
			...options?.headers,
		},
	});
};

export const getApiInsuranceVerificationStatusByJobId = <ThrowOnError extends boolean = false>(
	options: Options<GetApiInsuranceVerificationStatusByJobIdData, ThrowOnError>
) => {
	return (options.client ?? _heyApiClient).get<
		GetApiInsuranceVerificationStatusByJobIdResponse,
		GetApiInsuranceVerificationStatusByJobIdError,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/InsuranceVerification/status/{jobId}',
		...options,
	});
};

export const postApiInsuranceVerificationSearch = <ThrowOnError extends boolean = false>(
	options?: Options<PostApiInsuranceVerificationSearchData, ThrowOnError>
) => {
	return (options?.client ?? _heyApiClient).post<
		PostApiInsuranceVerificationSearchResponse,
		PostApiInsuranceVerificationSearchError,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/InsuranceVerification/search',
		...options,
		headers: {
			'Content-Type': 'application/json',
			...options?.headers,
		},
	});
};

export const postApiInsuranceVerificationFastauthWebhook = <ThrowOnError extends boolean = false>(
	options?: Options<PostApiInsuranceVerificationFastauthWebhookData, ThrowOnError>
) => {
	return (options?.client ?? _heyApiClient).post<
		unknown,
		PostApiInsuranceVerificationFastauthWebhookError,
		ThrowOnError
	>({
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/InsuranceVerification/fastauth-webhook',
		...options,
	});
};

export const postApiLogin = <ThrowOnError extends boolean = false>(
	options?: Options<PostApiLoginData, ThrowOnError>
) => {
	return (options?.client ?? _heyApiClient).post<unknown, unknown, ThrowOnError>({
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/Login',
		...options,
		headers: {
			'Content-Type': 'application/json',
			...options?.headers,
		},
	});
};

export const getApiLoginCheck = <ThrowOnError extends boolean = false>(
	options?: Options<GetApiLoginCheckData, ThrowOnError>
) => {
	return (options?.client ?? _heyApiClient).get<unknown, unknown, ThrowOnError>({
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/Login/check',
		...options,
	});
};

export const postApiMockDbDelete = <ThrowOnError extends boolean = false>(
	options?: Options<PostApiMockDbDeleteData, ThrowOnError>
) => {
	return (options?.client ?? _heyApiClient).post<unknown, unknown, ThrowOnError>({
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/MockDb/delete',
		...options,
		headers: {
			'Content-Type': 'application/json',
			...options?.headers,
		},
	});
};

export const getApiMockDbReset = <ThrowOnError extends boolean = false>(
	options?: Options<GetApiMockDbResetData, ThrowOnError>
) => {
	return (options?.client ?? _heyApiClient).get<unknown, unknown, ThrowOnError>({
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/MockDb/reset',
		...options,
	});
};

export const getApiNoteById = <ThrowOnError extends boolean = false>(
	options: Options<GetApiNoteByIdData, ThrowOnError>
) => {
	return (options.client ?? _heyApiClient).get<
		GetApiNoteByIdResponse,
		GetApiNoteByIdError,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/Note/{id}',
		...options,
	});
};

export const patchApiNoteById = <ThrowOnError extends boolean = false>(
	options: Options<PatchApiNoteByIdData, ThrowOnError>
) => {
	return (options.client ?? _heyApiClient).patch<
		PatchApiNoteByIdResponse,
		PatchApiNoteByIdError,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/Note/{id}',
		...options,
		headers: {
			'Content-Type': 'application/json',
			...options?.headers,
		},
	});
};

export const putApiNoteById = <ThrowOnError extends boolean = false>(
	options: Options<PutApiNoteByIdData, ThrowOnError>
) => {
	return (options.client ?? _heyApiClient).put<
		PutApiNoteByIdResponse,
		PutApiNoteByIdError,
		ThrowOnError
	>({
		responseType: 'text',
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/Note/{id}',
		...options,
		headers: {
			'Content-Type': 'application/json',
			...options?.headers,
		},
	});
};

export const postApiNoteSearch = <ThrowOnError extends boolean = false>(
	options?: Options<PostApiNoteSearchData, ThrowOnError>
) => {
	return (options?.client ?? _heyApiClient).post<PostApiNoteSearchResponse, unknown, ThrowOnError>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/Note/search',
		...options,
		headers: {
			'Content-Type': 'application/json',
			...options?.headers,
		},
	});
};

export const getApiNote = <ThrowOnError extends boolean = false>(
	options?: Options<GetApiNoteData, ThrowOnError>
) => {
	return (options?.client ?? _heyApiClient).get<GetApiNoteResponse, unknown, ThrowOnError>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/Note',
		...options,
	});
};

export const postApiNote = <ThrowOnError extends boolean = false>(
	options?: Options<PostApiNoteData, ThrowOnError>
) => {
	return (options?.client ?? _heyApiClient).post<
		PostApiNoteResponse,
		PostApiNoteError,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/Note',
		...options,
		headers: {
			'Content-Type': 'application/json',
			...options?.headers,
		},
	});
};

export const postApiNoteDraft = <ThrowOnError extends boolean = false>(
	options?: Options<PostApiNoteDraftData, ThrowOnError>
) => {
	return (options?.client ?? _heyApiClient).post<
		PostApiNoteDraftResponse,
		PostApiNoteDraftError,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/Note/draft',
		...options,
		headers: {
			'Content-Type': 'application/json',
			...options?.headers,
		},
	});
};

export const getApiNoteDraftByEntityId = <ThrowOnError extends boolean = false>(
	options: Options<GetApiNoteDraftByEntityIdData, ThrowOnError>
) => {
	return (options.client ?? _heyApiClient).get<
		GetApiNoteDraftByEntityIdResponse,
		GetApiNoteDraftByEntityIdError,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/Note/draft/{entityId}',
		...options,
	});
};

export const putApiNoteDraftByEntityId = <ThrowOnError extends boolean = false>(
	options: Options<PutApiNoteDraftByEntityIdData, ThrowOnError>
) => {
	return (options.client ?? _heyApiClient).put<
		PutApiNoteDraftByEntityIdResponse,
		PutApiNoteDraftByEntityIdError,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/Note/draft/{entityId}',
		...options,
		headers: {
			'Content-Type': 'application/json',
			...options?.headers,
		},
	});
};

export const postApiNoteDraftByEntityIdCommit = <ThrowOnError extends boolean = false>(
	options: Options<PostApiNoteDraftByEntityIdCommitData, ThrowOnError>
) => {
	return (options.client ?? _heyApiClient).post<
		PostApiNoteDraftByEntityIdCommitResponse,
		PostApiNoteDraftByEntityIdCommitError,
		ThrowOnError
	>({
		responseType: 'text',
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/Note/draft/{entityId}/commit',
		...options,
	});
};

export const postApiNoteDraftValidate = <ThrowOnError extends boolean = false>(
	options?: Options<PostApiNoteDraftValidateData, ThrowOnError>
) => {
	return (options?.client ?? _heyApiClient).post<
		PostApiNoteDraftValidateResponse,
		PostApiNoteDraftValidateError,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/Note/draft/validate',
		...options,
		headers: {
			'Content-Type': 'application/json',
			...options?.headers,
		},
	});
};

export const postApiNoteDraftByEntityIdValidate = <ThrowOnError extends boolean = false>(
	options: Options<PostApiNoteDraftByEntityIdValidateData, ThrowOnError>
) => {
	return (options.client ?? _heyApiClient).post<
		PostApiNoteDraftByEntityIdValidateResponse,
		PostApiNoteDraftByEntityIdValidateError,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/Note/draft/{entityId}/validate',
		...options,
	});
};

export const getApiOrderById = <ThrowOnError extends boolean = false>(
	options: Options<GetApiOrderByIdData, ThrowOnError>
) => {
	return (options.client ?? _heyApiClient).get<
		GetApiOrderByIdResponse,
		GetApiOrderByIdError,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/Order/{id}',
		...options,
	});
};

export const patchApiOrderById = <ThrowOnError extends boolean = false>(
	options: Options<PatchApiOrderByIdData, ThrowOnError>
) => {
	return (options.client ?? _heyApiClient).patch<
		PatchApiOrderByIdResponse,
		PatchApiOrderByIdError,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/Order/{id}',
		...options,
		headers: {
			'Content-Type': 'application/json',
			...options?.headers,
		},
	});
};

export const putApiOrderById = <ThrowOnError extends boolean = false>(
	options: Options<PutApiOrderByIdData, ThrowOnError>
) => {
	return (options.client ?? _heyApiClient).put<
		PutApiOrderByIdResponse,
		PutApiOrderByIdError,
		ThrowOnError
	>({
		responseType: 'text',
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/Order/{id}',
		...options,
		headers: {
			'Content-Type': 'application/json',
			...options?.headers,
		},
	});
};

export const postApiOrderSearch = <ThrowOnError extends boolean = false>(
	options?: Options<PostApiOrderSearchData, ThrowOnError>
) => {
	return (options?.client ?? _heyApiClient).post<PostApiOrderSearchResponse, unknown, ThrowOnError>(
		{
			responseType: 'text',
			security: [
				{
					scheme: 'bearer',
					type: 'http',
				},
			],
			url: '/api/Order/search',
			...options,
			headers: {
				'Content-Type': 'application/json',
				...options?.headers,
			},
		}
	);
};

export const getApiOrder = <ThrowOnError extends boolean = false>(
	options?: Options<GetApiOrderData, ThrowOnError>
) => {
	return (options?.client ?? _heyApiClient).get<GetApiOrderResponse, unknown, ThrowOnError>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/Order',
		...options,
	});
};

export const postApiOrder = <ThrowOnError extends boolean = false>(
	options?: Options<PostApiOrderData, ThrowOnError>
) => {
	return (options?.client ?? _heyApiClient).post<
		PostApiOrderResponse,
		PostApiOrderError,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/Order',
		...options,
		headers: {
			'Content-Type': 'application/json',
			...options?.headers,
		},
	});
};

export const postApiOrderDraft = <ThrowOnError extends boolean = false>(
	options?: Options<PostApiOrderDraftData, ThrowOnError>
) => {
	return (options?.client ?? _heyApiClient).post<
		PostApiOrderDraftResponse,
		PostApiOrderDraftError,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/Order/draft',
		...options,
		headers: {
			'Content-Type': 'application/json',
			...options?.headers,
		},
	});
};

export const getApiOrderDraftByEntityId = <ThrowOnError extends boolean = false>(
	options: Options<GetApiOrderDraftByEntityIdData, ThrowOnError>
) => {
	return (options.client ?? _heyApiClient).get<
		GetApiOrderDraftByEntityIdResponse,
		GetApiOrderDraftByEntityIdError,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/Order/draft/{entityId}',
		...options,
	});
};

export const putApiOrderDraftByEntityId = <ThrowOnError extends boolean = false>(
	options: Options<PutApiOrderDraftByEntityIdData, ThrowOnError>
) => {
	return (options.client ?? _heyApiClient).put<
		PutApiOrderDraftByEntityIdResponse,
		PutApiOrderDraftByEntityIdError,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/Order/draft/{entityId}',
		...options,
		headers: {
			'Content-Type': 'application/json',
			...options?.headers,
		},
	});
};

export const postApiOrderDraftByEntityIdCommit = <ThrowOnError extends boolean = false>(
	options: Options<PostApiOrderDraftByEntityIdCommitData, ThrowOnError>
) => {
	return (options.client ?? _heyApiClient).post<
		PostApiOrderDraftByEntityIdCommitResponse,
		PostApiOrderDraftByEntityIdCommitError,
		ThrowOnError
	>({
		responseType: 'text',
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/Order/draft/{entityId}/commit',
		...options,
	});
};

export const postApiOrderDraftValidate = <ThrowOnError extends boolean = false>(
	options?: Options<PostApiOrderDraftValidateData, ThrowOnError>
) => {
	return (options?.client ?? _heyApiClient).post<
		PostApiOrderDraftValidateResponse,
		PostApiOrderDraftValidateError,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/Order/draft/validate',
		...options,
		headers: {
			'Content-Type': 'application/json',
			...options?.headers,
		},
	});
};

export const postApiOrderDraftByEntityIdValidate = <ThrowOnError extends boolean = false>(
	options: Options<PostApiOrderDraftByEntityIdValidateData, ThrowOnError>
) => {
	return (options.client ?? _heyApiClient).post<
		PostApiOrderDraftByEntityIdValidateResponse,
		PostApiOrderDraftByEntityIdValidateError,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/Order/draft/{entityId}/validate',
		...options,
	});
};

export const getApiPatientById = <ThrowOnError extends boolean = false>(
	options: Options<GetApiPatientByIdData, ThrowOnError>
) => {
	return (options.client ?? _heyApiClient).get<
		GetApiPatientByIdResponse,
		GetApiPatientByIdError,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/Patient/{id}',
		...options,
	});
};

export const patchApiPatientById = <ThrowOnError extends boolean = false>(
	options: Options<PatchApiPatientByIdData, ThrowOnError>
) => {
	return (options.client ?? _heyApiClient).patch<
		PatchApiPatientByIdResponse,
		PatchApiPatientByIdError,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/Patient/{id}',
		...options,
		headers: {
			'Content-Type': 'application/json',
			...options?.headers,
		},
	});
};

export const putApiPatientById = <ThrowOnError extends boolean = false>(
	options: Options<PutApiPatientByIdData, ThrowOnError>
) => {
	return (options.client ?? _heyApiClient).put<
		PutApiPatientByIdResponse,
		PutApiPatientByIdError,
		ThrowOnError
	>({
		responseType: 'text',
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/Patient/{id}',
		...options,
		headers: {
			'Content-Type': 'application/json',
			...options?.headers,
		},
	});
};

export const postApiPatientSearch = <ThrowOnError extends boolean = false>(
	options?: Options<PostApiPatientSearchData, ThrowOnError>
) => {
	return (options?.client ?? _heyApiClient).post<
		PostApiPatientSearchResponse,
		unknown,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/Patient/search',
		...options,
		headers: {
			'Content-Type': 'application/json',
			...options?.headers,
		},
	});
};

export const getApiPatient = <ThrowOnError extends boolean = false>(
	options?: Options<GetApiPatientData, ThrowOnError>
) => {
	return (options?.client ?? _heyApiClient).get<GetApiPatientResponse, unknown, ThrowOnError>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/Patient',
		...options,
	});
};

export const postApiPatient = <ThrowOnError extends boolean = false>(
	options?: Options<PostApiPatientData, ThrowOnError>
) => {
	return (options?.client ?? _heyApiClient).post<
		PostApiPatientResponse,
		PostApiPatientError,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/Patient',
		...options,
		headers: {
			'Content-Type': 'application/json',
			...options?.headers,
		},
	});
};

export const postApiPatientDraft = <ThrowOnError extends boolean = false>(
	options?: Options<PostApiPatientDraftData, ThrowOnError>
) => {
	return (options?.client ?? _heyApiClient).post<
		PostApiPatientDraftResponse,
		PostApiPatientDraftError,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/Patient/draft',
		...options,
		headers: {
			'Content-Type': 'application/json',
			...options?.headers,
		},
	});
};

export const getApiPatientDraftByEntityId = <ThrowOnError extends boolean = false>(
	options: Options<GetApiPatientDraftByEntityIdData, ThrowOnError>
) => {
	return (options.client ?? _heyApiClient).get<
		GetApiPatientDraftByEntityIdResponse,
		GetApiPatientDraftByEntityIdError,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/Patient/draft/{entityId}',
		...options,
	});
};

export const putApiPatientDraftByEntityId = <ThrowOnError extends boolean = false>(
	options: Options<PutApiPatientDraftByEntityIdData, ThrowOnError>
) => {
	return (options.client ?? _heyApiClient).put<
		PutApiPatientDraftByEntityIdResponse,
		PutApiPatientDraftByEntityIdError,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/Patient/draft/{entityId}',
		...options,
		headers: {
			'Content-Type': 'application/json',
			...options?.headers,
		},
	});
};

export const postApiPatientDraftByEntityIdCommit = <ThrowOnError extends boolean = false>(
	options: Options<PostApiPatientDraftByEntityIdCommitData, ThrowOnError>
) => {
	return (options.client ?? _heyApiClient).post<
		PostApiPatientDraftByEntityIdCommitResponse,
		PostApiPatientDraftByEntityIdCommitError,
		ThrowOnError
	>({
		responseType: 'text',
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/Patient/draft/{entityId}/commit',
		...options,
	});
};

export const postApiPatientDraftValidate = <ThrowOnError extends boolean = false>(
	options?: Options<PostApiPatientDraftValidateData, ThrowOnError>
) => {
	return (options?.client ?? _heyApiClient).post<
		PostApiPatientDraftValidateResponse,
		PostApiPatientDraftValidateError,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/Patient/draft/validate',
		...options,
		headers: {
			'Content-Type': 'application/json',
			...options?.headers,
		},
	});
};

export const postApiPatientDraftByEntityIdValidate = <ThrowOnError extends boolean = false>(
	options: Options<PostApiPatientDraftByEntityIdValidateData, ThrowOnError>
) => {
	return (options.client ?? _heyApiClient).post<
		PostApiPatientDraftByEntityIdValidateResponse,
		PostApiPatientDraftByEntityIdValidateError,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/Patient/draft/{entityId}/validate',
		...options,
	});
};

export const getApiPatientAppointmentById = <ThrowOnError extends boolean = false>(
	options: Options<GetApiPatientAppointmentByIdData, ThrowOnError>
) => {
	return (options.client ?? _heyApiClient).get<
		GetApiPatientAppointmentByIdResponse,
		GetApiPatientAppointmentByIdError,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/PatientAppointment/{id}',
		...options,
	});
};

export const patchApiPatientAppointmentById = <ThrowOnError extends boolean = false>(
	options: Options<PatchApiPatientAppointmentByIdData, ThrowOnError>
) => {
	return (options.client ?? _heyApiClient).patch<
		PatchApiPatientAppointmentByIdResponse,
		PatchApiPatientAppointmentByIdError,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/PatientAppointment/{id}',
		...options,
		headers: {
			'Content-Type': 'application/json',
			...options?.headers,
		},
	});
};

export const putApiPatientAppointmentById = <ThrowOnError extends boolean = false>(
	options: Options<PutApiPatientAppointmentByIdData, ThrowOnError>
) => {
	return (options.client ?? _heyApiClient).put<
		PutApiPatientAppointmentByIdResponse,
		PutApiPatientAppointmentByIdError,
		ThrowOnError
	>({
		responseType: 'text',
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/PatientAppointment/{id}',
		...options,
		headers: {
			'Content-Type': 'application/json',
			...options?.headers,
		},
	});
};

export const postApiPatientAppointmentSearch = <ThrowOnError extends boolean = false>(
	options?: Options<PostApiPatientAppointmentSearchData, ThrowOnError>
) => {
	return (options?.client ?? _heyApiClient).post<
		PostApiPatientAppointmentSearchResponse,
		unknown,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/PatientAppointment/search',
		...options,
		headers: {
			'Content-Type': 'application/json',
			...options?.headers,
		},
	});
};

export const getApiPatientAppointment = <ThrowOnError extends boolean = false>(
	options?: Options<GetApiPatientAppointmentData, ThrowOnError>
) => {
	return (options?.client ?? _heyApiClient).get<
		GetApiPatientAppointmentResponse,
		unknown,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/PatientAppointment',
		...options,
	});
};

export const postApiPatientAppointment = <ThrowOnError extends boolean = false>(
	options?: Options<PostApiPatientAppointmentData, ThrowOnError>
) => {
	return (options?.client ?? _heyApiClient).post<
		PostApiPatientAppointmentResponse,
		PostApiPatientAppointmentError,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/PatientAppointment',
		...options,
		headers: {
			'Content-Type': 'application/json',
			...options?.headers,
		},
	});
};

export const postApiPatientAppointmentDraft = <ThrowOnError extends boolean = false>(
	options?: Options<PostApiPatientAppointmentDraftData, ThrowOnError>
) => {
	return (options?.client ?? _heyApiClient).post<
		PostApiPatientAppointmentDraftResponse,
		PostApiPatientAppointmentDraftError,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/PatientAppointment/draft',
		...options,
		headers: {
			'Content-Type': 'application/json',
			...options?.headers,
		},
	});
};

export const getApiPatientAppointmentDraftByEntityId = <ThrowOnError extends boolean = false>(
	options: Options<GetApiPatientAppointmentDraftByEntityIdData, ThrowOnError>
) => {
	return (options.client ?? _heyApiClient).get<
		GetApiPatientAppointmentDraftByEntityIdResponse,
		GetApiPatientAppointmentDraftByEntityIdError,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/PatientAppointment/draft/{entityId}',
		...options,
	});
};

export const putApiPatientAppointmentDraftByEntityId = <ThrowOnError extends boolean = false>(
	options: Options<PutApiPatientAppointmentDraftByEntityIdData, ThrowOnError>
) => {
	return (options.client ?? _heyApiClient).put<
		PutApiPatientAppointmentDraftByEntityIdResponse,
		PutApiPatientAppointmentDraftByEntityIdError,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/PatientAppointment/draft/{entityId}',
		...options,
		headers: {
			'Content-Type': 'application/json',
			...options?.headers,
		},
	});
};

export const postApiPatientAppointmentDraftByEntityIdCommit = <
	ThrowOnError extends boolean = false,
>(
	options: Options<PostApiPatientAppointmentDraftByEntityIdCommitData, ThrowOnError>
) => {
	return (options.client ?? _heyApiClient).post<
		PostApiPatientAppointmentDraftByEntityIdCommitResponse,
		PostApiPatientAppointmentDraftByEntityIdCommitError,
		ThrowOnError
	>({
		responseType: 'text',
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/PatientAppointment/draft/{entityId}/commit',
		...options,
	});
};

export const postApiPatientAppointmentDraftValidate = <ThrowOnError extends boolean = false>(
	options?: Options<PostApiPatientAppointmentDraftValidateData, ThrowOnError>
) => {
	return (options?.client ?? _heyApiClient).post<
		PostApiPatientAppointmentDraftValidateResponse,
		PostApiPatientAppointmentDraftValidateError,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/PatientAppointment/draft/validate',
		...options,
		headers: {
			'Content-Type': 'application/json',
			...options?.headers,
		},
	});
};

export const postApiPatientAppointmentDraftByEntityIdValidate = <
	ThrowOnError extends boolean = false,
>(
	options: Options<PostApiPatientAppointmentDraftByEntityIdValidateData, ThrowOnError>
) => {
	return (options.client ?? _heyApiClient).post<
		PostApiPatientAppointmentDraftByEntityIdValidateResponse,
		PostApiPatientAppointmentDraftByEntityIdValidateError,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/PatientAppointment/draft/{entityId}/validate',
		...options,
	});
};

export const getApiPatientAppointmentConfirmationById = <ThrowOnError extends boolean = false>(
	options: Options<GetApiPatientAppointmentConfirmationByIdData, ThrowOnError>
) => {
	return (options.client ?? _heyApiClient).get<
		GetApiPatientAppointmentConfirmationByIdResponse,
		GetApiPatientAppointmentConfirmationByIdError,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/PatientAppointmentConfirmation/{id}',
		...options,
	});
};

export const patchApiPatientAppointmentConfirmationById = <ThrowOnError extends boolean = false>(
	options: Options<PatchApiPatientAppointmentConfirmationByIdData, ThrowOnError>
) => {
	return (options.client ?? _heyApiClient).patch<
		PatchApiPatientAppointmentConfirmationByIdResponse,
		PatchApiPatientAppointmentConfirmationByIdError,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/PatientAppointmentConfirmation/{id}',
		...options,
		headers: {
			'Content-Type': 'application/json',
			...options?.headers,
		},
	});
};

export const putApiPatientAppointmentConfirmationById = <ThrowOnError extends boolean = false>(
	options: Options<PutApiPatientAppointmentConfirmationByIdData, ThrowOnError>
) => {
	return (options.client ?? _heyApiClient).put<
		PutApiPatientAppointmentConfirmationByIdResponse,
		PutApiPatientAppointmentConfirmationByIdError,
		ThrowOnError
	>({
		responseType: 'text',
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/PatientAppointmentConfirmation/{id}',
		...options,
		headers: {
			'Content-Type': 'application/json',
			...options?.headers,
		},
	});
};

export const postApiPatientAppointmentConfirmationSearch = <ThrowOnError extends boolean = false>(
	options?: Options<PostApiPatientAppointmentConfirmationSearchData, ThrowOnError>
) => {
	return (options?.client ?? _heyApiClient).post<
		PostApiPatientAppointmentConfirmationSearchResponse,
		unknown,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/PatientAppointmentConfirmation/search',
		...options,
		headers: {
			'Content-Type': 'application/json',
			...options?.headers,
		},
	});
};

export const getApiPatientAppointmentConfirmation = <ThrowOnError extends boolean = false>(
	options?: Options<GetApiPatientAppointmentConfirmationData, ThrowOnError>
) => {
	return (options?.client ?? _heyApiClient).get<
		GetApiPatientAppointmentConfirmationResponse,
		unknown,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/PatientAppointmentConfirmation',
		...options,
	});
};

export const postApiPatientAppointmentConfirmation = <ThrowOnError extends boolean = false>(
	options?: Options<PostApiPatientAppointmentConfirmationData, ThrowOnError>
) => {
	return (options?.client ?? _heyApiClient).post<
		PostApiPatientAppointmentConfirmationResponse,
		PostApiPatientAppointmentConfirmationError,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/PatientAppointmentConfirmation',
		...options,
		headers: {
			'Content-Type': 'application/json',
			...options?.headers,
		},
	});
};

export const postApiPatientAppointmentConfirmationDraft = <ThrowOnError extends boolean = false>(
	options?: Options<PostApiPatientAppointmentConfirmationDraftData, ThrowOnError>
) => {
	return (options?.client ?? _heyApiClient).post<
		PostApiPatientAppointmentConfirmationDraftResponse,
		PostApiPatientAppointmentConfirmationDraftError,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/PatientAppointmentConfirmation/draft',
		...options,
		headers: {
			'Content-Type': 'application/json',
			...options?.headers,
		},
	});
};

export const getApiPatientAppointmentConfirmationDraftByEntityId = <
	ThrowOnError extends boolean = false,
>(
	options: Options<GetApiPatientAppointmentConfirmationDraftByEntityIdData, ThrowOnError>
) => {
	return (options.client ?? _heyApiClient).get<
		GetApiPatientAppointmentConfirmationDraftByEntityIdResponse,
		GetApiPatientAppointmentConfirmationDraftByEntityIdError,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/PatientAppointmentConfirmation/draft/{entityId}',
		...options,
	});
};

export const putApiPatientAppointmentConfirmationDraftByEntityId = <
	ThrowOnError extends boolean = false,
>(
	options: Options<PutApiPatientAppointmentConfirmationDraftByEntityIdData, ThrowOnError>
) => {
	return (options.client ?? _heyApiClient).put<
		PutApiPatientAppointmentConfirmationDraftByEntityIdResponse,
		PutApiPatientAppointmentConfirmationDraftByEntityIdError,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/PatientAppointmentConfirmation/draft/{entityId}',
		...options,
		headers: {
			'Content-Type': 'application/json',
			...options?.headers,
		},
	});
};

export const postApiPatientAppointmentConfirmationDraftByEntityIdCommit = <
	ThrowOnError extends boolean = false,
>(
	options: Options<PostApiPatientAppointmentConfirmationDraftByEntityIdCommitData, ThrowOnError>
) => {
	return (options.client ?? _heyApiClient).post<
		PostApiPatientAppointmentConfirmationDraftByEntityIdCommitResponse,
		PostApiPatientAppointmentConfirmationDraftByEntityIdCommitError,
		ThrowOnError
	>({
		responseType: 'text',
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/PatientAppointmentConfirmation/draft/{entityId}/commit',
		...options,
	});
};

export const postApiPatientAppointmentConfirmationDraftValidate = <
	ThrowOnError extends boolean = false,
>(
	options?: Options<PostApiPatientAppointmentConfirmationDraftValidateData, ThrowOnError>
) => {
	return (options?.client ?? _heyApiClient).post<
		PostApiPatientAppointmentConfirmationDraftValidateResponse,
		PostApiPatientAppointmentConfirmationDraftValidateError,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/PatientAppointmentConfirmation/draft/validate',
		...options,
		headers: {
			'Content-Type': 'application/json',
			...options?.headers,
		},
	});
};

export const postApiPatientAppointmentConfirmationDraftByEntityIdValidate = <
	ThrowOnError extends boolean = false,
>(
	options: Options<PostApiPatientAppointmentConfirmationDraftByEntityIdValidateData, ThrowOnError>
) => {
	return (options.client ?? _heyApiClient).post<
		PostApiPatientAppointmentConfirmationDraftByEntityIdValidateResponse,
		PostApiPatientAppointmentConfirmationDraftByEntityIdValidateError,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/PatientAppointmentConfirmation/draft/{entityId}/validate',
		...options,
	});
};

export const getApiPhysicianById = <ThrowOnError extends boolean = false>(
	options: Options<GetApiPhysicianByIdData, ThrowOnError>
) => {
	return (options.client ?? _heyApiClient).get<
		GetApiPhysicianByIdResponse,
		GetApiPhysicianByIdError,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/Physician/{id}',
		...options,
	});
};

export const patchApiPhysicianById = <ThrowOnError extends boolean = false>(
	options: Options<PatchApiPhysicianByIdData, ThrowOnError>
) => {
	return (options.client ?? _heyApiClient).patch<
		PatchApiPhysicianByIdResponse,
		PatchApiPhysicianByIdError,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/Physician/{id}',
		...options,
		headers: {
			'Content-Type': 'application/json',
			...options?.headers,
		},
	});
};

export const putApiPhysicianById = <ThrowOnError extends boolean = false>(
	options: Options<PutApiPhysicianByIdData, ThrowOnError>
) => {
	return (options.client ?? _heyApiClient).put<
		PutApiPhysicianByIdResponse,
		PutApiPhysicianByIdError,
		ThrowOnError
	>({
		responseType: 'text',
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/Physician/{id}',
		...options,
		headers: {
			'Content-Type': 'application/json',
			...options?.headers,
		},
	});
};

export const postApiPhysicianSearch = <ThrowOnError extends boolean = false>(
	options?: Options<PostApiPhysicianSearchData, ThrowOnError>
) => {
	return (options?.client ?? _heyApiClient).post<
		PostApiPhysicianSearchResponse,
		unknown,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/Physician/search',
		...options,
		headers: {
			'Content-Type': 'application/json',
			...options?.headers,
		},
	});
};

export const getApiPhysician = <ThrowOnError extends boolean = false>(
	options?: Options<GetApiPhysicianData, ThrowOnError>
) => {
	return (options?.client ?? _heyApiClient).get<GetApiPhysicianResponse, unknown, ThrowOnError>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/Physician',
		...options,
	});
};

export const postApiPhysician = <ThrowOnError extends boolean = false>(
	options?: Options<PostApiPhysicianData, ThrowOnError>
) => {
	return (options?.client ?? _heyApiClient).post<
		PostApiPhysicianResponse,
		PostApiPhysicianError,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/Physician',
		...options,
		headers: {
			'Content-Type': 'application/json',
			...options?.headers,
		},
	});
};

export const postApiPhysicianDraft = <ThrowOnError extends boolean = false>(
	options?: Options<PostApiPhysicianDraftData, ThrowOnError>
) => {
	return (options?.client ?? _heyApiClient).post<
		PostApiPhysicianDraftResponse,
		PostApiPhysicianDraftError,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/Physician/draft',
		...options,
		headers: {
			'Content-Type': 'application/json',
			...options?.headers,
		},
	});
};

export const getApiPhysicianDraftByEntityId = <ThrowOnError extends boolean = false>(
	options: Options<GetApiPhysicianDraftByEntityIdData, ThrowOnError>
) => {
	return (options.client ?? _heyApiClient).get<
		GetApiPhysicianDraftByEntityIdResponse,
		GetApiPhysicianDraftByEntityIdError,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/Physician/draft/{entityId}',
		...options,
	});
};

export const putApiPhysicianDraftByEntityId = <ThrowOnError extends boolean = false>(
	options: Options<PutApiPhysicianDraftByEntityIdData, ThrowOnError>
) => {
	return (options.client ?? _heyApiClient).put<
		PutApiPhysicianDraftByEntityIdResponse,
		PutApiPhysicianDraftByEntityIdError,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/Physician/draft/{entityId}',
		...options,
		headers: {
			'Content-Type': 'application/json',
			...options?.headers,
		},
	});
};

export const postApiPhysicianDraftByEntityIdCommit = <ThrowOnError extends boolean = false>(
	options: Options<PostApiPhysicianDraftByEntityIdCommitData, ThrowOnError>
) => {
	return (options.client ?? _heyApiClient).post<
		PostApiPhysicianDraftByEntityIdCommitResponse,
		PostApiPhysicianDraftByEntityIdCommitError,
		ThrowOnError
	>({
		responseType: 'text',
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/Physician/draft/{entityId}/commit',
		...options,
	});
};

export const postApiPhysicianDraftValidate = <ThrowOnError extends boolean = false>(
	options?: Options<PostApiPhysicianDraftValidateData, ThrowOnError>
) => {
	return (options?.client ?? _heyApiClient).post<
		PostApiPhysicianDraftValidateResponse,
		PostApiPhysicianDraftValidateError,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/Physician/draft/validate',
		...options,
		headers: {
			'Content-Type': 'application/json',
			...options?.headers,
		},
	});
};

export const postApiPhysicianDraftByEntityIdValidate = <ThrowOnError extends boolean = false>(
	options: Options<PostApiPhysicianDraftByEntityIdValidateData, ThrowOnError>
) => {
	return (options.client ?? _heyApiClient).post<
		PostApiPhysicianDraftByEntityIdValidateResponse,
		PostApiPhysicianDraftByEntityIdValidateError,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/Physician/draft/{entityId}/validate',
		...options,
	});
};

export const getApiProviderById = <ThrowOnError extends boolean = false>(
	options: Options<GetApiProviderByIdData, ThrowOnError>
) => {
	return (options.client ?? _heyApiClient).get<
		GetApiProviderByIdResponse,
		GetApiProviderByIdError,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/Provider/{id}',
		...options,
	});
};

export const patchApiProviderById = <ThrowOnError extends boolean = false>(
	options: Options<PatchApiProviderByIdData, ThrowOnError>
) => {
	return (options.client ?? _heyApiClient).patch<
		PatchApiProviderByIdResponse,
		PatchApiProviderByIdError,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/Provider/{id}',
		...options,
		headers: {
			'Content-Type': 'application/json',
			...options?.headers,
		},
	});
};

export const putApiProviderById = <ThrowOnError extends boolean = false>(
	options: Options<PutApiProviderByIdData, ThrowOnError>
) => {
	return (options.client ?? _heyApiClient).put<
		PutApiProviderByIdResponse,
		PutApiProviderByIdError,
		ThrowOnError
	>({
		responseType: 'text',
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/Provider/{id}',
		...options,
		headers: {
			'Content-Type': 'application/json',
			...options?.headers,
		},
	});
};

export const postApiProviderSearch = <ThrowOnError extends boolean = false>(
	options?: Options<PostApiProviderSearchData, ThrowOnError>
) => {
	return (options?.client ?? _heyApiClient).post<
		PostApiProviderSearchResponse,
		unknown,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/Provider/search',
		...options,
		headers: {
			'Content-Type': 'application/json',
			...options?.headers,
		},
	});
};

export const getApiProvider = <ThrowOnError extends boolean = false>(
	options?: Options<GetApiProviderData, ThrowOnError>
) => {
	return (options?.client ?? _heyApiClient).get<GetApiProviderResponse, unknown, ThrowOnError>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/Provider',
		...options,
	});
};

export const postApiProvider = <ThrowOnError extends boolean = false>(
	options?: Options<PostApiProviderData, ThrowOnError>
) => {
	return (options?.client ?? _heyApiClient).post<
		PostApiProviderResponse,
		PostApiProviderError,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/Provider',
		...options,
		headers: {
			'Content-Type': 'application/json',
			...options?.headers,
		},
	});
};

export const postApiProviderDraft = <ThrowOnError extends boolean = false>(
	options?: Options<PostApiProviderDraftData, ThrowOnError>
) => {
	return (options?.client ?? _heyApiClient).post<
		PostApiProviderDraftResponse,
		PostApiProviderDraftError,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/Provider/draft',
		...options,
		headers: {
			'Content-Type': 'application/json',
			...options?.headers,
		},
	});
};

export const getApiProviderDraftByEntityId = <ThrowOnError extends boolean = false>(
	options: Options<GetApiProviderDraftByEntityIdData, ThrowOnError>
) => {
	return (options.client ?? _heyApiClient).get<
		GetApiProviderDraftByEntityIdResponse,
		GetApiProviderDraftByEntityIdError,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/Provider/draft/{entityId}',
		...options,
	});
};

export const putApiProviderDraftByEntityId = <ThrowOnError extends boolean = false>(
	options: Options<PutApiProviderDraftByEntityIdData, ThrowOnError>
) => {
	return (options.client ?? _heyApiClient).put<
		PutApiProviderDraftByEntityIdResponse,
		PutApiProviderDraftByEntityIdError,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/Provider/draft/{entityId}',
		...options,
		headers: {
			'Content-Type': 'application/json',
			...options?.headers,
		},
	});
};

export const postApiProviderDraftByEntityIdCommit = <ThrowOnError extends boolean = false>(
	options: Options<PostApiProviderDraftByEntityIdCommitData, ThrowOnError>
) => {
	return (options.client ?? _heyApiClient).post<
		PostApiProviderDraftByEntityIdCommitResponse,
		PostApiProviderDraftByEntityIdCommitError,
		ThrowOnError
	>({
		responseType: 'text',
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/Provider/draft/{entityId}/commit',
		...options,
	});
};

export const postApiProviderDraftValidate = <ThrowOnError extends boolean = false>(
	options?: Options<PostApiProviderDraftValidateData, ThrowOnError>
) => {
	return (options?.client ?? _heyApiClient).post<
		PostApiProviderDraftValidateResponse,
		PostApiProviderDraftValidateError,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/Provider/draft/validate',
		...options,
		headers: {
			'Content-Type': 'application/json',
			...options?.headers,
		},
	});
};

export const postApiProviderDraftByEntityIdValidate = <ThrowOnError extends boolean = false>(
	options: Options<PostApiProviderDraftByEntityIdValidateData, ThrowOnError>
) => {
	return (options.client ?? _heyApiClient).post<
		PostApiProviderDraftByEntityIdValidateResponse,
		PostApiProviderDraftByEntityIdValidateError,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/Provider/draft/{entityId}/validate',
		...options,
	});
};

export const getPyapiZip = <ThrowOnError extends boolean = false>(
	options?: Options<GetPyapiZipData, ThrowOnError>
) => {
	return (options?.client ?? _heyApiClient).get<unknown, unknown, ThrowOnError>({
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/pyapi.zip',
		...options,
	});
};

// export const getTsapiZip = <ThrowOnError extends boolean = false>(
// 	options?: Options<GetTsapiZipData, ThrowOnError>
// ) => {
// 	return (options?.client ?? _heyApiClient).get<unknown, unknown, ThrowOnError>({
// 		security: [
// 			{
// 				scheme: 'bearer',
// 				type: 'http',
// 			},
// 		],
// 		url: '/tsapi.zip',
// 		...options,
// 	});
// };

export const getTsapiZip = <ThrowOnError extends boolean = false>(
	options?: Options<GetTsapiZipData, ThrowOnError>
) => {
	return (options?.client ?? _heyApiClient).get<unknown, unknown, ThrowOnError>({
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/tsapi.zip',
		...options,
	});
};

export const getApiRoomById = <ThrowOnError extends boolean = false>(
	options: Options<GetApiRoomByIdData, ThrowOnError>
) => {
	return (options.client ?? _heyApiClient).get<
		GetApiRoomByIdResponse,
		GetApiRoomByIdError,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/Room/{id}',
		...options,
	});
};

export const patchApiRoomById = <ThrowOnError extends boolean = false>(
	options: Options<PatchApiRoomByIdData, ThrowOnError>
) => {
	return (options.client ?? _heyApiClient).patch<
		PatchApiRoomByIdResponse,
		PatchApiRoomByIdError,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/Room/{id}',
		...options,
		headers: {
			'Content-Type': 'application/json',
			...options?.headers,
		},
	});
};

export const putApiRoomById = <ThrowOnError extends boolean = false>(
	options: Options<PutApiRoomByIdData, ThrowOnError>
) => {
	return (options.client ?? _heyApiClient).put<
		PutApiRoomByIdResponse,
		PutApiRoomByIdError,
		ThrowOnError
	>({
		responseType: 'text',
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/Room/{id}',
		...options,
		headers: {
			'Content-Type': 'application/json',
			...options?.headers,
		},
	});
};

export const postApiRoomSearch = <ThrowOnError extends boolean = false>(
	options?: Options<PostApiRoomSearchData, ThrowOnError>
) => {
	return (options?.client ?? _heyApiClient).post<PostApiRoomSearchResponse, unknown, ThrowOnError>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/Room/search',
		...options,
		headers: {
			'Content-Type': 'application/json',
			...options?.headers,
		},
	});
};

export const getApiRoom = <ThrowOnError extends boolean = false>(
	options?: Options<GetApiRoomData, ThrowOnError>
) => {
	return (options?.client ?? _heyApiClient).get<GetApiRoomResponse, unknown, ThrowOnError>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/Room',
		...options,
	});
};

export const postApiRoom = <ThrowOnError extends boolean = false>(
	options?: Options<PostApiRoomData, ThrowOnError>
) => {
	return (options?.client ?? _heyApiClient).post<
		PostApiRoomResponse,
		PostApiRoomError,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/Room',
		...options,
		headers: {
			'Content-Type': 'application/json',
			...options?.headers,
		},
	});
};

export const postApiRoomDraft = <ThrowOnError extends boolean = false>(
	options?: Options<PostApiRoomDraftData, ThrowOnError>
) => {
	return (options?.client ?? _heyApiClient).post<
		PostApiRoomDraftResponse,
		PostApiRoomDraftError,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/Room/draft',
		...options,
		headers: {
			'Content-Type': 'application/json',
			...options?.headers,
		},
	});
};

export const getApiRoomDraftByEntityId = <ThrowOnError extends boolean = false>(
	options: Options<GetApiRoomDraftByEntityIdData, ThrowOnError>
) => {
	return (options.client ?? _heyApiClient).get<
		GetApiRoomDraftByEntityIdResponse,
		GetApiRoomDraftByEntityIdError,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/Room/draft/{entityId}',
		...options,
	});
};

export const putApiRoomDraftByEntityId = <ThrowOnError extends boolean = false>(
	options: Options<PutApiRoomDraftByEntityIdData, ThrowOnError>
) => {
	return (options.client ?? _heyApiClient).put<
		PutApiRoomDraftByEntityIdResponse,
		PutApiRoomDraftByEntityIdError,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/Room/draft/{entityId}',
		...options,
		headers: {
			'Content-Type': 'application/json',
			...options?.headers,
		},
	});
};

export const postApiRoomDraftByEntityIdCommit = <ThrowOnError extends boolean = false>(
	options: Options<PostApiRoomDraftByEntityIdCommitData, ThrowOnError>
) => {
	return (options.client ?? _heyApiClient).post<
		PostApiRoomDraftByEntityIdCommitResponse,
		PostApiRoomDraftByEntityIdCommitError,
		ThrowOnError
	>({
		responseType: 'text',
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/Room/draft/{entityId}/commit',
		...options,
	});
};

export const postApiRoomDraftValidate = <ThrowOnError extends boolean = false>(
	options?: Options<PostApiRoomDraftValidateData, ThrowOnError>
) => {
	return (options?.client ?? _heyApiClient).post<
		PostApiRoomDraftValidateResponse,
		PostApiRoomDraftValidateError,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/Room/draft/validate',
		...options,
		headers: {
			'Content-Type': 'application/json',
			...options?.headers,
		},
	});
};

export const postApiRoomDraftByEntityIdValidate = <ThrowOnError extends boolean = false>(
	options: Options<PostApiRoomDraftByEntityIdValidateData, ThrowOnError>
) => {
	return (options.client ?? _heyApiClient).post<
		PostApiRoomDraftByEntityIdValidateResponse,
		PostApiRoomDraftByEntityIdValidateError,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/Room/draft/{entityId}/validate',
		...options,
	});
};

export const postApiSchedulingFindSlots = <ThrowOnError extends boolean = false>(
	options?: Options<PostApiSchedulingFindSlotsData, ThrowOnError>
) => {
	return (options?.client ?? _heyApiClient).post<
		PostApiSchedulingFindSlotsResponse,
		unknown,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/Scheduling/find-slots',
		...options,
		headers: {
			'Content-Type': 'application/json',
			...options?.headers,
		},
	});
};

export const getApiSchedulingConstraintById = <ThrowOnError extends boolean = false>(
	options: Options<GetApiSchedulingConstraintByIdData, ThrowOnError>
) => {
	return (options.client ?? _heyApiClient).get<
		GetApiSchedulingConstraintByIdResponse,
		GetApiSchedulingConstraintByIdError,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/SchedulingConstraint/{id}',
		...options,
	});
};

export const patchApiSchedulingConstraintById = <ThrowOnError extends boolean = false>(
	options: Options<PatchApiSchedulingConstraintByIdData, ThrowOnError>
) => {
	return (options.client ?? _heyApiClient).patch<
		PatchApiSchedulingConstraintByIdResponse,
		PatchApiSchedulingConstraintByIdError,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/SchedulingConstraint/{id}',
		...options,
		headers: {
			'Content-Type': 'application/json',
			...options?.headers,
		},
	});
};

export const putApiSchedulingConstraintById = <ThrowOnError extends boolean = false>(
	options: Options<PutApiSchedulingConstraintByIdData, ThrowOnError>
) => {
	return (options.client ?? _heyApiClient).put<
		PutApiSchedulingConstraintByIdResponse,
		PutApiSchedulingConstraintByIdError,
		ThrowOnError
	>({
		responseType: 'text',
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/SchedulingConstraint/{id}',
		...options,
		headers: {
			'Content-Type': 'application/json',
			...options?.headers,
		},
	});
};

export const postApiSchedulingConstraintSearch = <ThrowOnError extends boolean = false>(
	options?: Options<PostApiSchedulingConstraintSearchData, ThrowOnError>
) => {
	return (options?.client ?? _heyApiClient).post<
		PostApiSchedulingConstraintSearchResponse,
		unknown,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/SchedulingConstraint/search',
		...options,
		headers: {
			'Content-Type': 'application/json',
			...options?.headers,
		},
	});
};

export const getApiSchedulingConstraint = <ThrowOnError extends boolean = false>(
	options?: Options<GetApiSchedulingConstraintData, ThrowOnError>
) => {
	return (options?.client ?? _heyApiClient).get<
		GetApiSchedulingConstraintResponse,
		unknown,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/SchedulingConstraint',
		...options,
	});
};

export const postApiSchedulingConstraint = <ThrowOnError extends boolean = false>(
	options?: Options<PostApiSchedulingConstraintData, ThrowOnError>
) => {
	return (options?.client ?? _heyApiClient).post<
		PostApiSchedulingConstraintResponse,
		PostApiSchedulingConstraintError,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/SchedulingConstraint',
		...options,
		headers: {
			'Content-Type': 'application/json',
			...options?.headers,
		},
	});
};

export const postApiSchedulingConstraintDraft = <ThrowOnError extends boolean = false>(
	options?: Options<PostApiSchedulingConstraintDraftData, ThrowOnError>
) => {
	return (options?.client ?? _heyApiClient).post<
		PostApiSchedulingConstraintDraftResponse,
		PostApiSchedulingConstraintDraftError,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/SchedulingConstraint/draft',
		...options,
		headers: {
			'Content-Type': 'application/json',
			...options?.headers,
		},
	});
};

export const getApiSchedulingConstraintDraftByEntityId = <ThrowOnError extends boolean = false>(
	options: Options<GetApiSchedulingConstraintDraftByEntityIdData, ThrowOnError>
) => {
	return (options.client ?? _heyApiClient).get<
		GetApiSchedulingConstraintDraftByEntityIdResponse,
		GetApiSchedulingConstraintDraftByEntityIdError,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/SchedulingConstraint/draft/{entityId}',
		...options,
	});
};

export const putApiSchedulingConstraintDraftByEntityId = <ThrowOnError extends boolean = false>(
	options: Options<PutApiSchedulingConstraintDraftByEntityIdData, ThrowOnError>
) => {
	return (options.client ?? _heyApiClient).put<
		PutApiSchedulingConstraintDraftByEntityIdResponse,
		PutApiSchedulingConstraintDraftByEntityIdError,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/SchedulingConstraint/draft/{entityId}',
		...options,
		headers: {
			'Content-Type': 'application/json',
			...options?.headers,
		},
	});
};

export const postApiSchedulingConstraintDraftByEntityIdCommit = <
	ThrowOnError extends boolean = false,
>(
	options: Options<PostApiSchedulingConstraintDraftByEntityIdCommitData, ThrowOnError>
) => {
	return (options.client ?? _heyApiClient).post<
		PostApiSchedulingConstraintDraftByEntityIdCommitResponse,
		PostApiSchedulingConstraintDraftByEntityIdCommitError,
		ThrowOnError
	>({
		responseType: 'text',
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/SchedulingConstraint/draft/{entityId}/commit',
		...options,
	});
};

export const postApiSchedulingConstraintDraftValidate = <ThrowOnError extends boolean = false>(
	options?: Options<PostApiSchedulingConstraintDraftValidateData, ThrowOnError>
) => {
	return (options?.client ?? _heyApiClient).post<
		PostApiSchedulingConstraintDraftValidateResponse,
		PostApiSchedulingConstraintDraftValidateError,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/SchedulingConstraint/draft/validate',
		...options,
		headers: {
			'Content-Type': 'application/json',
			...options?.headers,
		},
	});
};

export const postApiSchedulingConstraintDraftByEntityIdValidate = <
	ThrowOnError extends boolean = false,
>(
	options: Options<PostApiSchedulingConstraintDraftByEntityIdValidateData, ThrowOnError>
) => {
	return (options.client ?? _heyApiClient).post<
		PostApiSchedulingConstraintDraftByEntityIdValidateResponse,
		PostApiSchedulingConstraintDraftByEntityIdValidateError,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/SchedulingConstraint/draft/{entityId}/validate',
		...options,
	});
};

export const getApiStudyById = <ThrowOnError extends boolean = false>(
	options: Options<GetApiStudyByIdData, ThrowOnError>
) => {
	return (options.client ?? _heyApiClient).get<
		GetApiStudyByIdResponse,
		GetApiStudyByIdError,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/Study/{id}',
		...options,
	});
};

export const patchApiStudyById = <ThrowOnError extends boolean = false>(
	options: Options<PatchApiStudyByIdData, ThrowOnError>
) => {
	return (options.client ?? _heyApiClient).patch<
		PatchApiStudyByIdResponse,
		PatchApiStudyByIdError,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/Study/{id}',
		...options,
		headers: {
			'Content-Type': 'application/json',
			...options?.headers,
		},
	});
};

export const putApiStudyById = <ThrowOnError extends boolean = false>(
	options: Options<PutApiStudyByIdData, ThrowOnError>
) => {
	return (options.client ?? _heyApiClient).put<
		PutApiStudyByIdResponse,
		PutApiStudyByIdError,
		ThrowOnError
	>({
		responseType: 'text',
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/Study/{id}',
		...options,
		headers: {
			'Content-Type': 'application/json',
			...options?.headers,
		},
	});
};

export const postApiStudySearch = <ThrowOnError extends boolean = false>(
	options?: Options<PostApiStudySearchData, ThrowOnError>
) => {
	return (options?.client ?? _heyApiClient).post<PostApiStudySearchResponse, unknown, ThrowOnError>(
		{
			responseType: 'text',
			security: [
				{
					scheme: 'bearer',
					type: 'http',
				},
			],
			url: '/api/Study/search',
			...options,
			headers: {
				'Content-Type': 'application/json',
				...options?.headers,
			},
		}
	);
};

export const getApiStudy = <ThrowOnError extends boolean = false>(
	options?: Options<GetApiStudyData, ThrowOnError>
) => {
	return (options?.client ?? _heyApiClient).get<GetApiStudyResponse, unknown, ThrowOnError>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/Study',
		...options,
	});
};

export const postApiStudy = <ThrowOnError extends boolean = false>(
	options?: Options<PostApiStudyData, ThrowOnError>
) => {
	return (options?.client ?? _heyApiClient).post<
		PostApiStudyResponse,
		PostApiStudyError,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/Study',
		...options,
		headers: {
			'Content-Type': 'application/json',
			...options?.headers,
		},
	});
};

export const postApiStudyDraft = <ThrowOnError extends boolean = false>(
	options?: Options<PostApiStudyDraftData, ThrowOnError>
) => {
	return (options?.client ?? _heyApiClient).post<
		PostApiStudyDraftResponse,
		PostApiStudyDraftError,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/Study/draft',
		...options,
		headers: {
			'Content-Type': 'application/json',
			...options?.headers,
		},
	});
};

export const getApiStudyDraftByEntityId = <ThrowOnError extends boolean = false>(
	options: Options<GetApiStudyDraftByEntityIdData, ThrowOnError>
) => {
	return (options.client ?? _heyApiClient).get<
		GetApiStudyDraftByEntityIdResponse,
		GetApiStudyDraftByEntityIdError,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/Study/draft/{entityId}',
		...options,
	});
};

export const putApiStudyDraftByEntityId = <ThrowOnError extends boolean = false>(
	options: Options<PutApiStudyDraftByEntityIdData, ThrowOnError>
) => {
	return (options.client ?? _heyApiClient).put<
		PutApiStudyDraftByEntityIdResponse,
		PutApiStudyDraftByEntityIdError,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/Study/draft/{entityId}',
		...options,
		headers: {
			'Content-Type': 'application/json',
			...options?.headers,
		},
	});
};

export const postApiStudyDraftByEntityIdCommit = <ThrowOnError extends boolean = false>(
	options: Options<PostApiStudyDraftByEntityIdCommitData, ThrowOnError>
) => {
	return (options.client ?? _heyApiClient).post<
		PostApiStudyDraftByEntityIdCommitResponse,
		PostApiStudyDraftByEntityIdCommitError,
		ThrowOnError
	>({
		responseType: 'text',
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/Study/draft/{entityId}/commit',
		...options,
	});
};

export const postApiStudyDraftValidate = <ThrowOnError extends boolean = false>(
	options?: Options<PostApiStudyDraftValidateData, ThrowOnError>
) => {
	return (options?.client ?? _heyApiClient).post<
		PostApiStudyDraftValidateResponse,
		PostApiStudyDraftValidateError,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/Study/draft/validate',
		...options,
		headers: {
			'Content-Type': 'application/json',
			...options?.headers,
		},
	});
};

export const postApiStudyDraftByEntityIdValidate = <ThrowOnError extends boolean = false>(
	options: Options<PostApiStudyDraftByEntityIdValidateData, ThrowOnError>
) => {
	return (options.client ?? _heyApiClient).post<
		PostApiStudyDraftByEntityIdValidateResponse,
		PostApiStudyDraftByEntityIdValidateError,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/Study/draft/{entityId}/validate',
		...options,
	});
};

export const getApiTechnicianById = <ThrowOnError extends boolean = false>(
	options: Options<GetApiTechnicianByIdData, ThrowOnError>
) => {
	return (options.client ?? _heyApiClient).get<
		GetApiTechnicianByIdResponse,
		GetApiTechnicianByIdError,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/Technician/{id}',
		...options,
	});
};

export const patchApiTechnicianById = <ThrowOnError extends boolean = false>(
	options: Options<PatchApiTechnicianByIdData, ThrowOnError>
) => {
	return (options.client ?? _heyApiClient).patch<
		PatchApiTechnicianByIdResponse,
		PatchApiTechnicianByIdError,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/Technician/{id}',
		...options,
		headers: {
			'Content-Type': 'application/json',
			...options?.headers,
		},
	});
};

export const putApiTechnicianById = <ThrowOnError extends boolean = false>(
	options: Options<PutApiTechnicianByIdData, ThrowOnError>
) => {
	return (options.client ?? _heyApiClient).put<
		PutApiTechnicianByIdResponse,
		PutApiTechnicianByIdError,
		ThrowOnError
	>({
		responseType: 'text',
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/Technician/{id}',
		...options,
		headers: {
			'Content-Type': 'application/json',
			...options?.headers,
		},
	});
};

export const postApiTechnicianSearch = <ThrowOnError extends boolean = false>(
	options?: Options<PostApiTechnicianSearchData, ThrowOnError>
) => {
	return (options?.client ?? _heyApiClient).post<
		PostApiTechnicianSearchResponse,
		unknown,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/Technician/search',
		...options,
		headers: {
			'Content-Type': 'application/json',
			...options?.headers,
		},
	});
};

export const getApiTechnician = <ThrowOnError extends boolean = false>(
	options?: Options<GetApiTechnicianData, ThrowOnError>
) => {
	return (options?.client ?? _heyApiClient).get<GetApiTechnicianResponse, unknown, ThrowOnError>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/Technician',
		...options,
	});
};

export const postApiTechnician = <ThrowOnError extends boolean = false>(
	options?: Options<PostApiTechnicianData, ThrowOnError>
) => {
	return (options?.client ?? _heyApiClient).post<
		PostApiTechnicianResponse,
		PostApiTechnicianError,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/Technician',
		...options,
		headers: {
			'Content-Type': 'application/json',
			...options?.headers,
		},
	});
};

export const postApiTechnicianDraft = <ThrowOnError extends boolean = false>(
	options?: Options<PostApiTechnicianDraftData, ThrowOnError>
) => {
	return (options?.client ?? _heyApiClient).post<
		PostApiTechnicianDraftResponse,
		PostApiTechnicianDraftError,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/Technician/draft',
		...options,
		headers: {
			'Content-Type': 'application/json',
			...options?.headers,
		},
	});
};

export const getApiTechnicianDraftByEntityId = <ThrowOnError extends boolean = false>(
	options: Options<GetApiTechnicianDraftByEntityIdData, ThrowOnError>
) => {
	return (options.client ?? _heyApiClient).get<
		GetApiTechnicianDraftByEntityIdResponse,
		GetApiTechnicianDraftByEntityIdError,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/Technician/draft/{entityId}',
		...options,
	});
};

export const putApiTechnicianDraftByEntityId = <ThrowOnError extends boolean = false>(
	options: Options<PutApiTechnicianDraftByEntityIdData, ThrowOnError>
) => {
	return (options.client ?? _heyApiClient).put<
		PutApiTechnicianDraftByEntityIdResponse,
		PutApiTechnicianDraftByEntityIdError,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/Technician/draft/{entityId}',
		...options,
		headers: {
			'Content-Type': 'application/json',
			...options?.headers,
		},
	});
};

export const postApiTechnicianDraftByEntityIdCommit = <ThrowOnError extends boolean = false>(
	options: Options<PostApiTechnicianDraftByEntityIdCommitData, ThrowOnError>
) => {
	return (options.client ?? _heyApiClient).post<
		PostApiTechnicianDraftByEntityIdCommitResponse,
		PostApiTechnicianDraftByEntityIdCommitError,
		ThrowOnError
	>({
		responseType: 'text',
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/Technician/draft/{entityId}/commit',
		...options,
	});
};

export const postApiTechnicianDraftValidate = <ThrowOnError extends boolean = false>(
	options?: Options<PostApiTechnicianDraftValidateData, ThrowOnError>
) => {
	return (options?.client ?? _heyApiClient).post<
		PostApiTechnicianDraftValidateResponse,
		PostApiTechnicianDraftValidateError,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/Technician/draft/validate',
		...options,
		headers: {
			'Content-Type': 'application/json',
			...options?.headers,
		},
	});
};

export const postApiTechnicianDraftByEntityIdValidate = <ThrowOnError extends boolean = false>(
	options: Options<PostApiTechnicianDraftByEntityIdValidateData, ThrowOnError>
) => {
	return (options.client ?? _heyApiClient).post<
		PostApiTechnicianDraftByEntityIdValidateResponse,
		PostApiTechnicianDraftByEntityIdValidateError,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/Technician/draft/{entityId}/validate',
		...options,
	});
};

export const getApiTechnicianAppointmentById = <ThrowOnError extends boolean = false>(
	options: Options<GetApiTechnicianAppointmentByIdData, ThrowOnError>
) => {
	return (options.client ?? _heyApiClient).get<
		GetApiTechnicianAppointmentByIdResponse,
		GetApiTechnicianAppointmentByIdError,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/TechnicianAppointment/{id}',
		...options,
	});
};

export const patchApiTechnicianAppointmentById = <ThrowOnError extends boolean = false>(
	options: Options<PatchApiTechnicianAppointmentByIdData, ThrowOnError>
) => {
	return (options.client ?? _heyApiClient).patch<
		PatchApiTechnicianAppointmentByIdResponse,
		PatchApiTechnicianAppointmentByIdError,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/TechnicianAppointment/{id}',
		...options,
		headers: {
			'Content-Type': 'application/json',
			...options?.headers,
		},
	});
};

export const putApiTechnicianAppointmentById = <ThrowOnError extends boolean = false>(
	options: Options<PutApiTechnicianAppointmentByIdData, ThrowOnError>
) => {
	return (options.client ?? _heyApiClient).put<
		PutApiTechnicianAppointmentByIdResponse,
		PutApiTechnicianAppointmentByIdError,
		ThrowOnError
	>({
		responseType: 'text',
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/TechnicianAppointment/{id}',
		...options,
		headers: {
			'Content-Type': 'application/json',
			...options?.headers,
		},
	});
};

export const postApiTechnicianAppointmentSearch = <ThrowOnError extends boolean = false>(
	options?: Options<PostApiTechnicianAppointmentSearchData, ThrowOnError>
) => {
	return (options?.client ?? _heyApiClient).post<
		PostApiTechnicianAppointmentSearchResponse,
		unknown,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/TechnicianAppointment/search',
		...options,
		headers: {
			'Content-Type': 'application/json',
			...options?.headers,
		},
	});
};

export const getApiTechnicianAppointment = <ThrowOnError extends boolean = false>(
	options?: Options<GetApiTechnicianAppointmentData, ThrowOnError>
) => {
	return (options?.client ?? _heyApiClient).get<
		GetApiTechnicianAppointmentResponse,
		unknown,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/TechnicianAppointment',
		...options,
	});
};

export const postApiTechnicianAppointment = <ThrowOnError extends boolean = false>(
	options?: Options<PostApiTechnicianAppointmentData, ThrowOnError>
) => {
	return (options?.client ?? _heyApiClient).post<
		PostApiTechnicianAppointmentResponse,
		PostApiTechnicianAppointmentError,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/TechnicianAppointment',
		...options,
		headers: {
			'Content-Type': 'application/json',
			...options?.headers,
		},
	});
};

export const postApiTechnicianAppointmentDraft = <ThrowOnError extends boolean = false>(
	options?: Options<PostApiTechnicianAppointmentDraftData, ThrowOnError>
) => {
	return (options?.client ?? _heyApiClient).post<
		PostApiTechnicianAppointmentDraftResponse,
		PostApiTechnicianAppointmentDraftError,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/TechnicianAppointment/draft',
		...options,
		headers: {
			'Content-Type': 'application/json',
			...options?.headers,
		},
	});
};

export const getApiTechnicianAppointmentDraftByEntityId = <ThrowOnError extends boolean = false>(
	options: Options<GetApiTechnicianAppointmentDraftByEntityIdData, ThrowOnError>
) => {
	return (options.client ?? _heyApiClient).get<
		GetApiTechnicianAppointmentDraftByEntityIdResponse,
		GetApiTechnicianAppointmentDraftByEntityIdError,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/TechnicianAppointment/draft/{entityId}',
		...options,
	});
};

export const putApiTechnicianAppointmentDraftByEntityId = <ThrowOnError extends boolean = false>(
	options: Options<PutApiTechnicianAppointmentDraftByEntityIdData, ThrowOnError>
) => {
	return (options.client ?? _heyApiClient).put<
		PutApiTechnicianAppointmentDraftByEntityIdResponse,
		PutApiTechnicianAppointmentDraftByEntityIdError,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/TechnicianAppointment/draft/{entityId}',
		...options,
		headers: {
			'Content-Type': 'application/json',
			...options?.headers,
		},
	});
};

export const postApiTechnicianAppointmentDraftByEntityIdCommit = <
	ThrowOnError extends boolean = false,
>(
	options: Options<PostApiTechnicianAppointmentDraftByEntityIdCommitData, ThrowOnError>
) => {
	return (options.client ?? _heyApiClient).post<
		PostApiTechnicianAppointmentDraftByEntityIdCommitResponse,
		PostApiTechnicianAppointmentDraftByEntityIdCommitError,
		ThrowOnError
	>({
		responseType: 'text',
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/TechnicianAppointment/draft/{entityId}/commit',
		...options,
	});
};

export const postApiTechnicianAppointmentDraftValidate = <ThrowOnError extends boolean = false>(
	options?: Options<PostApiTechnicianAppointmentDraftValidateData, ThrowOnError>
) => {
	return (options?.client ?? _heyApiClient).post<
		PostApiTechnicianAppointmentDraftValidateResponse,
		PostApiTechnicianAppointmentDraftValidateError,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/TechnicianAppointment/draft/validate',
		...options,
		headers: {
			'Content-Type': 'application/json',
			...options?.headers,
		},
	});
};

export const postApiTechnicianAppointmentDraftByEntityIdValidate = <
	ThrowOnError extends boolean = false,
>(
	options: Options<PostApiTechnicianAppointmentDraftByEntityIdValidateData, ThrowOnError>
) => {
	return (options.client ?? _heyApiClient).post<
		PostApiTechnicianAppointmentDraftByEntityIdValidateResponse,
		PostApiTechnicianAppointmentDraftByEntityIdValidateError,
		ThrowOnError
	>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/TechnicianAppointment/draft/{entityId}/validate',
		...options,
	});
};

export const getApiWorkflowFlows = <ThrowOnError extends boolean = false>(
	options?: Options<GetApiWorkflowFlowsData, ThrowOnError>
) => {
	return (options?.client ?? _heyApiClient).get<unknown, unknown, ThrowOnError>({
		responseType: 'text',
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/Workflow/flows',
		...options,
	});
};

export const getApiWorkflowMyTasks = <ThrowOnError extends boolean = false>(
	options?: Options<GetApiWorkflowMyTasksData, ThrowOnError>
) => {
	return (options?.client ?? _heyApiClient).get<unknown, unknown, ThrowOnError>({
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/Workflow/myTasks',
		...options,
	});
};

export const getApiWorkflowProfile = <ThrowOnError extends boolean = false>(
	options?: Options<GetApiWorkflowProfileData, ThrowOnError>
) => {
	return (options?.client ?? _heyApiClient).get<unknown, unknown, ThrowOnError>({
		security: [
			{
				scheme: 'bearer',
				type: 'http',
			},
		],
		url: '/api/Workflow/profile',
		...options,
	});
};
