import MenuItem from '@components/menu/menu-item';
import SecondaryMenu from '@components/menu/secondary-menu';
import { User } from 'lucide-react';
import useOrder from '../hooks/use-order';
import { Status } from '@config/status';
import { OrderState, StepNames } from '../types';

const menuItems = [
	{
		stateKey: 'AddStudy',
		title: 'Study Information',
		value: 'study',
		icon: User,
		size: 'medium',
		path: '/study',
	},
	{
		stateKey: 'AddCareLocation',
		title: 'Care Location',
		value: 'care-location',
		icon: User,
		size: 'medium',
		path: '/care-location',
	},
	{
		stateKey: 'AddPhysicians',
		title: 'Physicians',
		value: 'physicians',
		icon: User,
		size: 'medium',
		path: '/physicians',
	},
	{
		stateKey: 'ReviewAndSubmitOrder',
		title: 'Review and Submit',
		value: 'confirmation',
		icon: User,
		size: 'medium',
		path: '/confirmation',
	},
] as const;

interface OrderCreateMenuProps {
	patientId: string;
	orderId: string;
	activePath: string;
	onClick: (path: string) => void;
}

export default function OrderCreateMenu({ orderId, activePath, onClick }: OrderCreateMenuProps) {
	const { orderData } = useOrder({ orderId });

	const getProgress = () => {
		if (!orderData) {
			return 0;
		}
		const { data } = orderData;
		const orderState = (data?._state as unknown as OrderState) ?? {};
		const flowState = orderState.flowState ?? {};
		const progress = flowState.progress ?? 0;
		return progress;
	};

	const getStepStatus = (stepName: StepNames) => {
		if (!orderData) {
			return Status.NotStarted;
		}
		const { data } = orderData;
		const patientState = (data?._state as unknown as OrderState) ?? {};
		const stepState = patientState.stepState ?? {};
		const stepStatus = stepState[stepName] ?? 'NotStarted';
		switch (stepStatus) {
			case 'Complete':
				return Status.Success;
			case 'NotStarted':
				return Status.NotStarted;
			case 'InProgress':
				return Status.Process;
			case 'Error':
				return Status.Error;
			case 'Warning':
				return Status.Warning;
			default:
				return Status.NotStarted;
		}
	};

	return (
		<SecondaryMenu
			headerProps={{
				title: 'Order',
				subtitle: 'Subtitle',
				icon: User,
				type: 'Workflow',
				progress: getProgress(),
				color: 'success',
			}}>
			{menuItems.map((item) => (
				<MenuItem
					key={item.value}
					title={item.title}
					value={item.value}
					icon={item.icon}
					size={item.size}
					status={getStepStatus(item.stateKey)}
					selected={activePath.includes(item.path)}
					onClick={() => {
						onClick(item.path);
					}}
				/>
			))}
		</SecondaryMenu>
	);
}
