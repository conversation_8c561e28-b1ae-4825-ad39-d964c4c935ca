// This file is auto-generated by @hey-api/openapi-ts

export type EthosModelCareLocationQ = {
    [key: string]: never;
};

export type EthosModelDraftQ = {
    [key: string]: never;
};

export type EthosModelEntityType = 'EditRecord' | 'FileMetadata' | 'WorkflowDraftTransition' | 'WorkflowEntityLink' | 'WorkflowInstance' | 'WorkflowTransition' | 'Draft' | 'Note' | 'Address' | 'Insurance' | 'InsuranceHolderData' | 'PhoneNumberWithUseData' | 'Patient' | 'PatientGuardian' | 'PatientAppointment' | 'PatientAppointmentConfirmation' | 'PersonalContactDetail' | 'PersonalEmail' | 'PersonalAddress' | 'PersonalPhoneNumber' | 'PersonalEmergencyContact' | 'OrganizationContactDetail' | 'OrganizationEmail' | 'OrganizationAddress' | 'OrganizationPhoneNumber' | 'Order' | 'Study' | 'Provider' | 'CareLocation' | 'Room' | 'Equipment' | 'Physician' | 'PhysicianCareLocationRelation' | 'Technician' | 'TechnicianRole' | 'TechnicianShiftPreference' | 'TechnicianCareLocationRelation' | 'TechnicianAppointment' | 'InsuranceVerification' | 'SchedulingConstraint';

export type EthosModelEquipmentQ = {
    [key: string]: never;
};

export type EthosModelFileStatus = 'PendingUpload' | 'Uploading' | 'ValidationFailed' | 'UploadFailed' | 'Processing' | 'ProcessingFailed' | 'Ready' | 'Deleted';

export type EthosModelInsuranceQ = {
    [key: string]: never;
};

export type EthosModelInsuranceVerificationQ = {
    [key: string]: never;
};

export type EthosModelNoteQ = {
    [key: string]: never;
};

export type EthosModelOrderQ = {
    [key: string]: never;
};

export type EthosModelPatientAppointmentConfirmationQ = {
    [key: string]: never;
};

export type EthosModelPatientAppointmentQ = {
    [key: string]: never;
};

export type EthosModelPatientQ = {
    [key: string]: never;
};

export type EthosModelPhysicianQ = {
    [key: string]: never;
};

export type EthosModelProviderQ = {
    [key: string]: never;
};

export type EthosModelQueryDto1_AllEthosModelCareLocationQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null = EthosModelQueryDto1EthosModelCareLocationQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null;

export type EthosModelQueryDto1_AllEthosModelDraftQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null = EthosModelQueryDto1EthosModelDraftQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null;

export type EthosModelQueryDto1_AllEthosModelEquipmentQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null = EthosModelQueryDto1EthosModelEquipmentQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null;

export type EthosModelQueryDto1_AllEthosModelInsuranceQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null = EthosModelQueryDto1EthosModelInsuranceQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null;

export type EthosModelQueryDto1_AllEthosModelInsuranceVerificationQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null = EthosModelQueryDto1EthosModelInsuranceVerificationQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null;

export type EthosModelQueryDto1_AllEthosModelNoteQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null = EthosModelQueryDto1EthosModelNoteQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null;

export type EthosModelQueryDto1_AllEthosModelOrderQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null = EthosModelQueryDto1EthosModelOrderQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null;

export type EthosModelQueryDto1_AllEthosModelPatientAppointmentConfirmationQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null = EthosModelQueryDto1EthosModelPatientAppointmentConfirmationQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null;

export type EthosModelQueryDto1_AllEthosModelPatientAppointmentQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null = EthosModelQueryDto1EthosModelPatientAppointmentQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null;

export type EthosModelQueryDto1_AllEthosModelPatientQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null = EthosModelQueryDto1EthosModelPatientQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null;

export type EthosModelQueryDto1_AllEthosModelPhysicianQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null = EthosModelQueryDto1EthosModelPhysicianQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null;

export type EthosModelQueryDto1_AllEthosModelProviderQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null = EthosModelQueryDto1EthosModelProviderQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null;

export type EthosModelQueryDto1_AllEthosModelRoomQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null = EthosModelQueryDto1EthosModelRoomQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null;

export type EthosModelQueryDto1_AllEthosModelSchedulingSchedulingConstraintQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null = EthosModelQueryDto1EthosModelSchedulingSchedulingConstraintQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null;

export type EthosModelQueryDto1_AllEthosModelStudyQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null = EthosModelQueryDto1EthosModelStudyQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null;

export type EthosModelQueryDto1_AllEthosModelTechnicianAppointmentQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null = EthosModelQueryDto1EthosModelTechnicianAppointmentQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null;

export type EthosModelQueryDto1_AllEthosModelTechnicianQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null = EthosModelQueryDto1EthosModelTechnicianQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null;

export type EthosModelQueryDto1_AndEthosModelCareLocationQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null = EthosModelQueryDto1EthosModelCareLocationQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null & {
    exprs?: Array<EthosModelQueryDto1_AllEthosModelCareLocationQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_LiteralEthosModelCareLocationQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_NotEthosModelCareLocationQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_AndEthosModelCareLocationQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_OrEthosModelCareLocationQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null>;
};

export type EthosModelQueryDto1_AndEthosModelDraftQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null = EthosModelQueryDto1EthosModelDraftQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null & {
    exprs?: Array<EthosModelQueryDto1_AllEthosModelDraftQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_LiteralEthosModelDraftQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_NotEthosModelDraftQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_AndEthosModelDraftQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_OrEthosModelDraftQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null>;
};

export type EthosModelQueryDto1_AndEthosModelEquipmentQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null = EthosModelQueryDto1EthosModelEquipmentQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null & {
    exprs?: Array<EthosModelQueryDto1_AllEthosModelEquipmentQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_LiteralEthosModelEquipmentQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_NotEthosModelEquipmentQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_AndEthosModelEquipmentQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_OrEthosModelEquipmentQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null>;
};

export type EthosModelQueryDto1_AndEthosModelInsuranceQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null = EthosModelQueryDto1EthosModelInsuranceQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null & {
    exprs?: Array<EthosModelQueryDto1_AllEthosModelInsuranceQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_LiteralEthosModelInsuranceQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_NotEthosModelInsuranceQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_AndEthosModelInsuranceQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_OrEthosModelInsuranceQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null>;
};

export type EthosModelQueryDto1_AndEthosModelInsuranceVerificationQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null = EthosModelQueryDto1EthosModelInsuranceVerificationQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null & {
    exprs?: Array<EthosModelQueryDto1_AllEthosModelInsuranceVerificationQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_LiteralEthosModelInsuranceVerificationQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_NotEthosModelInsuranceVerificationQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_AndEthosModelInsuranceVerificationQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_OrEthosModelInsuranceVerificationQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null>;
};

export type EthosModelQueryDto1_AndEthosModelNoteQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null = EthosModelQueryDto1EthosModelNoteQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null & {
    exprs?: Array<EthosModelQueryDto1_AllEthosModelNoteQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_LiteralEthosModelNoteQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_NotEthosModelNoteQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_AndEthosModelNoteQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_OrEthosModelNoteQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null>;
};

export type EthosModelQueryDto1_AndEthosModelOrderQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null = EthosModelQueryDto1EthosModelOrderQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null & {
    exprs?: Array<EthosModelQueryDto1_AllEthosModelOrderQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_LiteralEthosModelOrderQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_NotEthosModelOrderQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_AndEthosModelOrderQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_OrEthosModelOrderQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null>;
};

export type EthosModelQueryDto1_AndEthosModelPatientAppointmentConfirmationQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null = EthosModelQueryDto1EthosModelPatientAppointmentConfirmationQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null & {
    exprs?: Array<EthosModelQueryDto1_AllEthosModelPatientAppointmentConfirmationQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_LiteralEthosModelPatientAppointmentConfirmationQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_NotEthosModelPatientAppointmentConfirmationQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_AndEthosModelPatientAppointmentConfirmationQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_OrEthosModelPatientAppointmentConfirmationQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null>;
};

export type EthosModelQueryDto1_AndEthosModelPatientAppointmentQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null = EthosModelQueryDto1EthosModelPatientAppointmentQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null & {
    exprs?: Array<EthosModelQueryDto1_AllEthosModelPatientAppointmentQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_LiteralEthosModelPatientAppointmentQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_NotEthosModelPatientAppointmentQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_AndEthosModelPatientAppointmentQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_OrEthosModelPatientAppointmentQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null>;
};

export type EthosModelQueryDto1_AndEthosModelPatientQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null = EthosModelQueryDto1EthosModelPatientQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null & {
    exprs?: Array<EthosModelQueryDto1_AllEthosModelPatientQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_LiteralEthosModelPatientQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_NotEthosModelPatientQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_AndEthosModelPatientQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_OrEthosModelPatientQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null>;
};

export type EthosModelQueryDto1_AndEthosModelPhysicianQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null = EthosModelQueryDto1EthosModelPhysicianQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null & {
    exprs?: Array<EthosModelQueryDto1_AllEthosModelPhysicianQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_LiteralEthosModelPhysicianQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_NotEthosModelPhysicianQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_AndEthosModelPhysicianQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_OrEthosModelPhysicianQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null>;
};

export type EthosModelQueryDto1_AndEthosModelProviderQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null = EthosModelQueryDto1EthosModelProviderQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null & {
    exprs?: Array<EthosModelQueryDto1_AllEthosModelProviderQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_LiteralEthosModelProviderQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_NotEthosModelProviderQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_AndEthosModelProviderQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_OrEthosModelProviderQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null>;
};

export type EthosModelQueryDto1_AndEthosModelRoomQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null = EthosModelQueryDto1EthosModelRoomQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null & {
    exprs?: Array<EthosModelQueryDto1_AllEthosModelRoomQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_LiteralEthosModelRoomQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_NotEthosModelRoomQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_AndEthosModelRoomQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_OrEthosModelRoomQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null>;
};

export type EthosModelQueryDto1_AndEthosModelSchedulingSchedulingConstraintQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null = EthosModelQueryDto1EthosModelSchedulingSchedulingConstraintQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null & {
    exprs?: Array<EthosModelQueryDto1_AllEthosModelSchedulingSchedulingConstraintQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_LiteralEthosModelSchedulingSchedulingConstraintQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_NotEthosModelSchedulingSchedulingConstraintQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_AndEthosModelSchedulingSchedulingConstraintQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_OrEthosModelSchedulingSchedulingConstraintQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null>;
};

export type EthosModelQueryDto1_AndEthosModelStudyQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null = EthosModelQueryDto1EthosModelStudyQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null & {
    exprs?: Array<EthosModelQueryDto1_AllEthosModelStudyQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_LiteralEthosModelStudyQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_NotEthosModelStudyQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_AndEthosModelStudyQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_OrEthosModelStudyQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null>;
};

export type EthosModelQueryDto1_AndEthosModelTechnicianAppointmentQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null = EthosModelQueryDto1EthosModelTechnicianAppointmentQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null & {
    exprs?: Array<EthosModelQueryDto1_AllEthosModelTechnicianAppointmentQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_LiteralEthosModelTechnicianAppointmentQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_NotEthosModelTechnicianAppointmentQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_AndEthosModelTechnicianAppointmentQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_OrEthosModelTechnicianAppointmentQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null>;
};

export type EthosModelQueryDto1_AndEthosModelTechnicianQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null = EthosModelQueryDto1EthosModelTechnicianQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null & {
    exprs?: Array<EthosModelQueryDto1_AllEthosModelTechnicianQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_LiteralEthosModelTechnicianQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_NotEthosModelTechnicianQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_AndEthosModelTechnicianQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_OrEthosModelTechnicianQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null>;
};

export type EthosModelQueryDto1_LiteralEthosModelCareLocationQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null = EthosModelQueryDto1EthosModelCareLocationQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null & {
    value?: EthosModelCareLocationQ | null;
};

export type EthosModelQueryDto1_LiteralEthosModelDraftQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null = EthosModelQueryDto1EthosModelDraftQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null & {
    value?: EthosModelDraftQ | null;
};

export type EthosModelQueryDto1_LiteralEthosModelEquipmentQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null = EthosModelQueryDto1EthosModelEquipmentQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null & {
    value?: EthosModelEquipmentQ | null;
};

export type EthosModelQueryDto1_LiteralEthosModelInsuranceQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null = EthosModelQueryDto1EthosModelInsuranceQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null & {
    value?: EthosModelInsuranceQ | null;
};

export type EthosModelQueryDto1_LiteralEthosModelInsuranceVerificationQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null = EthosModelQueryDto1EthosModelInsuranceVerificationQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null & {
    value?: EthosModelInsuranceVerificationQ | null;
};

export type EthosModelQueryDto1_LiteralEthosModelNoteQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null = EthosModelQueryDto1EthosModelNoteQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null & {
    value?: EthosModelNoteQ | null;
};

export type EthosModelQueryDto1_LiteralEthosModelOrderQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null = EthosModelQueryDto1EthosModelOrderQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null & {
    value?: EthosModelOrderQ | null;
};

export type EthosModelQueryDto1_LiteralEthosModelPatientAppointmentConfirmationQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null = EthosModelQueryDto1EthosModelPatientAppointmentConfirmationQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null & {
    value?: EthosModelPatientAppointmentConfirmationQ | null;
};

export type EthosModelQueryDto1_LiteralEthosModelPatientAppointmentQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null = EthosModelQueryDto1EthosModelPatientAppointmentQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null & {
    value?: EthosModelPatientAppointmentQ | null;
};

export type EthosModelQueryDto1_LiteralEthosModelPatientQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null = EthosModelQueryDto1EthosModelPatientQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null & {
    value?: EthosModelPatientQ | null;
};

export type EthosModelQueryDto1_LiteralEthosModelPhysicianQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null = EthosModelQueryDto1EthosModelPhysicianQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null & {
    value?: EthosModelPhysicianQ | null;
};

export type EthosModelQueryDto1_LiteralEthosModelProviderQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null = EthosModelQueryDto1EthosModelProviderQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null & {
    value?: EthosModelProviderQ | null;
};

export type EthosModelQueryDto1_LiteralEthosModelRoomQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null = EthosModelQueryDto1EthosModelRoomQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null & {
    value?: EthosModelRoomQ | null;
};

export type EthosModelQueryDto1_LiteralEthosModelSchedulingSchedulingConstraintQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null = EthosModelQueryDto1EthosModelSchedulingSchedulingConstraintQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null & {
    value?: EthosModelSchedulingSchedulingConstraintQ | null;
};

export type EthosModelQueryDto1_LiteralEthosModelStudyQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null = EthosModelQueryDto1EthosModelStudyQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null & {
    value?: EthosModelStudyQ | null;
};

export type EthosModelQueryDto1_LiteralEthosModelTechnicianAppointmentQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null = EthosModelQueryDto1EthosModelTechnicianAppointmentQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null & {
    value?: EthosModelTechnicianAppointmentQ | null;
};

export type EthosModelQueryDto1_LiteralEthosModelTechnicianQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null = EthosModelQueryDto1EthosModelTechnicianQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null & {
    value?: EthosModelTechnicianQ | null;
};

export type EthosModelQueryDto1_NotEthosModelCareLocationQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null = EthosModelQueryDto1EthosModelCareLocationQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null & {
    expr?: EthosModelQueryDto1_AllEthosModelCareLocationQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_LiteralEthosModelCareLocationQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_NotEthosModelCareLocationQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_AndEthosModelCareLocationQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_OrEthosModelCareLocationQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null;
};

export type EthosModelQueryDto1_NotEthosModelDraftQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null = EthosModelQueryDto1EthosModelDraftQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null & {
    expr?: EthosModelQueryDto1_AllEthosModelDraftQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_LiteralEthosModelDraftQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_NotEthosModelDraftQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_AndEthosModelDraftQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_OrEthosModelDraftQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null;
};

export type EthosModelQueryDto1_NotEthosModelEquipmentQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null = EthosModelQueryDto1EthosModelEquipmentQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null & {
    expr?: EthosModelQueryDto1_AllEthosModelEquipmentQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_LiteralEthosModelEquipmentQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_NotEthosModelEquipmentQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_AndEthosModelEquipmentQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_OrEthosModelEquipmentQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null;
};

export type EthosModelQueryDto1_NotEthosModelInsuranceQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null = EthosModelQueryDto1EthosModelInsuranceQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null & {
    expr?: EthosModelQueryDto1_AllEthosModelInsuranceQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_LiteralEthosModelInsuranceQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_NotEthosModelInsuranceQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_AndEthosModelInsuranceQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_OrEthosModelInsuranceQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null;
};

export type EthosModelQueryDto1_NotEthosModelInsuranceVerificationQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null = EthosModelQueryDto1EthosModelInsuranceVerificationQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null & {
    expr?: EthosModelQueryDto1_AllEthosModelInsuranceVerificationQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_LiteralEthosModelInsuranceVerificationQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_NotEthosModelInsuranceVerificationQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_AndEthosModelInsuranceVerificationQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_OrEthosModelInsuranceVerificationQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null;
};

export type EthosModelQueryDto1_NotEthosModelNoteQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null = EthosModelQueryDto1EthosModelNoteQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null & {
    expr?: EthosModelQueryDto1_AllEthosModelNoteQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_LiteralEthosModelNoteQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_NotEthosModelNoteQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_AndEthosModelNoteQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_OrEthosModelNoteQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null;
};

export type EthosModelQueryDto1_NotEthosModelOrderQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null = EthosModelQueryDto1EthosModelOrderQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null & {
    expr?: EthosModelQueryDto1_AllEthosModelOrderQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_LiteralEthosModelOrderQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_NotEthosModelOrderQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_AndEthosModelOrderQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_OrEthosModelOrderQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null;
};

export type EthosModelQueryDto1_NotEthosModelPatientAppointmentConfirmationQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null = EthosModelQueryDto1EthosModelPatientAppointmentConfirmationQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null & {
    expr?: EthosModelQueryDto1_AllEthosModelPatientAppointmentConfirmationQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_LiteralEthosModelPatientAppointmentConfirmationQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_NotEthosModelPatientAppointmentConfirmationQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_AndEthosModelPatientAppointmentConfirmationQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_OrEthosModelPatientAppointmentConfirmationQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null;
};

export type EthosModelQueryDto1_NotEthosModelPatientAppointmentQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null = EthosModelQueryDto1EthosModelPatientAppointmentQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null & {
    expr?: EthosModelQueryDto1_AllEthosModelPatientAppointmentQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_LiteralEthosModelPatientAppointmentQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_NotEthosModelPatientAppointmentQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_AndEthosModelPatientAppointmentQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_OrEthosModelPatientAppointmentQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null;
};

export type EthosModelQueryDto1_NotEthosModelPatientQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null = EthosModelQueryDto1EthosModelPatientQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null & {
    expr?: EthosModelQueryDto1_AllEthosModelPatientQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_LiteralEthosModelPatientQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_NotEthosModelPatientQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_AndEthosModelPatientQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_OrEthosModelPatientQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null;
};

export type EthosModelQueryDto1_NotEthosModelPhysicianQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null = EthosModelQueryDto1EthosModelPhysicianQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null & {
    expr?: EthosModelQueryDto1_AllEthosModelPhysicianQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_LiteralEthosModelPhysicianQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_NotEthosModelPhysicianQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_AndEthosModelPhysicianQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_OrEthosModelPhysicianQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null;
};

export type EthosModelQueryDto1_NotEthosModelProviderQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null = EthosModelQueryDto1EthosModelProviderQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null & {
    expr?: EthosModelQueryDto1_AllEthosModelProviderQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_LiteralEthosModelProviderQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_NotEthosModelProviderQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_AndEthosModelProviderQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_OrEthosModelProviderQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null;
};

export type EthosModelQueryDto1_NotEthosModelRoomQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null = EthosModelQueryDto1EthosModelRoomQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null & {
    expr?: EthosModelQueryDto1_AllEthosModelRoomQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_LiteralEthosModelRoomQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_NotEthosModelRoomQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_AndEthosModelRoomQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_OrEthosModelRoomQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null;
};

export type EthosModelQueryDto1_NotEthosModelSchedulingSchedulingConstraintQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null = EthosModelQueryDto1EthosModelSchedulingSchedulingConstraintQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null & {
    expr?: EthosModelQueryDto1_AllEthosModelSchedulingSchedulingConstraintQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_LiteralEthosModelSchedulingSchedulingConstraintQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_NotEthosModelSchedulingSchedulingConstraintQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_AndEthosModelSchedulingSchedulingConstraintQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_OrEthosModelSchedulingSchedulingConstraintQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null;
};

export type EthosModelQueryDto1_NotEthosModelStudyQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null = EthosModelQueryDto1EthosModelStudyQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null & {
    expr?: EthosModelQueryDto1_AllEthosModelStudyQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_LiteralEthosModelStudyQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_NotEthosModelStudyQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_AndEthosModelStudyQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_OrEthosModelStudyQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null;
};

export type EthosModelQueryDto1_NotEthosModelTechnicianAppointmentQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null = EthosModelQueryDto1EthosModelTechnicianAppointmentQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null & {
    expr?: EthosModelQueryDto1_AllEthosModelTechnicianAppointmentQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_LiteralEthosModelTechnicianAppointmentQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_NotEthosModelTechnicianAppointmentQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_AndEthosModelTechnicianAppointmentQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_OrEthosModelTechnicianAppointmentQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null;
};

export type EthosModelQueryDto1_NotEthosModelTechnicianQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null = EthosModelQueryDto1EthosModelTechnicianQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null & {
    expr?: EthosModelQueryDto1_AllEthosModelTechnicianQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_LiteralEthosModelTechnicianQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_NotEthosModelTechnicianQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_AndEthosModelTechnicianQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_OrEthosModelTechnicianQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null;
};

export type EthosModelQueryDto1_OrEthosModelCareLocationQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null = EthosModelQueryDto1EthosModelCareLocationQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null & {
    exprs?: Array<EthosModelQueryDto1_AllEthosModelCareLocationQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_LiteralEthosModelCareLocationQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_NotEthosModelCareLocationQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_AndEthosModelCareLocationQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_OrEthosModelCareLocationQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null>;
};

export type EthosModelQueryDto1_OrEthosModelDraftQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null = EthosModelQueryDto1EthosModelDraftQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null & {
    exprs?: Array<EthosModelQueryDto1_AllEthosModelDraftQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_LiteralEthosModelDraftQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_NotEthosModelDraftQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_AndEthosModelDraftQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_OrEthosModelDraftQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null>;
};

export type EthosModelQueryDto1_OrEthosModelEquipmentQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null = EthosModelQueryDto1EthosModelEquipmentQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null & {
    exprs?: Array<EthosModelQueryDto1_AllEthosModelEquipmentQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_LiteralEthosModelEquipmentQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_NotEthosModelEquipmentQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_AndEthosModelEquipmentQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_OrEthosModelEquipmentQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null>;
};

export type EthosModelQueryDto1_OrEthosModelInsuranceQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null = EthosModelQueryDto1EthosModelInsuranceQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null & {
    exprs?: Array<EthosModelQueryDto1_AllEthosModelInsuranceQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_LiteralEthosModelInsuranceQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_NotEthosModelInsuranceQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_AndEthosModelInsuranceQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_OrEthosModelInsuranceQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null>;
};

export type EthosModelQueryDto1_OrEthosModelInsuranceVerificationQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null = EthosModelQueryDto1EthosModelInsuranceVerificationQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null & {
    exprs?: Array<EthosModelQueryDto1_AllEthosModelInsuranceVerificationQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_LiteralEthosModelInsuranceVerificationQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_NotEthosModelInsuranceVerificationQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_AndEthosModelInsuranceVerificationQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_OrEthosModelInsuranceVerificationQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null>;
};

export type EthosModelQueryDto1_OrEthosModelNoteQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null = EthosModelQueryDto1EthosModelNoteQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null & {
    exprs?: Array<EthosModelQueryDto1_AllEthosModelNoteQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_LiteralEthosModelNoteQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_NotEthosModelNoteQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_AndEthosModelNoteQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_OrEthosModelNoteQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null>;
};

export type EthosModelQueryDto1_OrEthosModelOrderQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null = EthosModelQueryDto1EthosModelOrderQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null & {
    exprs?: Array<EthosModelQueryDto1_AllEthosModelOrderQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_LiteralEthosModelOrderQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_NotEthosModelOrderQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_AndEthosModelOrderQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_OrEthosModelOrderQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null>;
};

export type EthosModelQueryDto1_OrEthosModelPatientAppointmentConfirmationQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null = EthosModelQueryDto1EthosModelPatientAppointmentConfirmationQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null & {
    exprs?: Array<EthosModelQueryDto1_AllEthosModelPatientAppointmentConfirmationQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_LiteralEthosModelPatientAppointmentConfirmationQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_NotEthosModelPatientAppointmentConfirmationQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_AndEthosModelPatientAppointmentConfirmationQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_OrEthosModelPatientAppointmentConfirmationQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null>;
};

export type EthosModelQueryDto1_OrEthosModelPatientAppointmentQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null = EthosModelQueryDto1EthosModelPatientAppointmentQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null & {
    exprs?: Array<EthosModelQueryDto1_AllEthosModelPatientAppointmentQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_LiteralEthosModelPatientAppointmentQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_NotEthosModelPatientAppointmentQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_AndEthosModelPatientAppointmentQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_OrEthosModelPatientAppointmentQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null>;
};

export type EthosModelQueryDto1_OrEthosModelPatientQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null = EthosModelQueryDto1EthosModelPatientQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null & {
    exprs?: Array<EthosModelQueryDto1_AllEthosModelPatientQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_LiteralEthosModelPatientQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_NotEthosModelPatientQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_AndEthosModelPatientQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_OrEthosModelPatientQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null>;
};

export type EthosModelQueryDto1_OrEthosModelPhysicianQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null = EthosModelQueryDto1EthosModelPhysicianQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null & {
    exprs?: Array<EthosModelQueryDto1_AllEthosModelPhysicianQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_LiteralEthosModelPhysicianQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_NotEthosModelPhysicianQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_AndEthosModelPhysicianQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_OrEthosModelPhysicianQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null>;
};

export type EthosModelQueryDto1_OrEthosModelProviderQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null = EthosModelQueryDto1EthosModelProviderQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null & {
    exprs?: Array<EthosModelQueryDto1_AllEthosModelProviderQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_LiteralEthosModelProviderQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_NotEthosModelProviderQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_AndEthosModelProviderQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_OrEthosModelProviderQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null>;
};

export type EthosModelQueryDto1_OrEthosModelRoomQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null = EthosModelQueryDto1EthosModelRoomQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null & {
    exprs?: Array<EthosModelQueryDto1_AllEthosModelRoomQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_LiteralEthosModelRoomQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_NotEthosModelRoomQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_AndEthosModelRoomQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_OrEthosModelRoomQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null>;
};

export type EthosModelQueryDto1_OrEthosModelSchedulingSchedulingConstraintQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null = EthosModelQueryDto1EthosModelSchedulingSchedulingConstraintQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null & {
    exprs?: Array<EthosModelQueryDto1_AllEthosModelSchedulingSchedulingConstraintQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_LiteralEthosModelSchedulingSchedulingConstraintQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_NotEthosModelSchedulingSchedulingConstraintQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_AndEthosModelSchedulingSchedulingConstraintQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_OrEthosModelSchedulingSchedulingConstraintQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null>;
};

export type EthosModelQueryDto1_OrEthosModelStudyQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null = EthosModelQueryDto1EthosModelStudyQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null & {
    exprs?: Array<EthosModelQueryDto1_AllEthosModelStudyQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_LiteralEthosModelStudyQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_NotEthosModelStudyQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_AndEthosModelStudyQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_OrEthosModelStudyQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null>;
};

export type EthosModelQueryDto1_OrEthosModelTechnicianAppointmentQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null = EthosModelQueryDto1EthosModelTechnicianAppointmentQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null & {
    exprs?: Array<EthosModelQueryDto1_AllEthosModelTechnicianAppointmentQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_LiteralEthosModelTechnicianAppointmentQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_NotEthosModelTechnicianAppointmentQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_AndEthosModelTechnicianAppointmentQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_OrEthosModelTechnicianAppointmentQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null>;
};

export type EthosModelQueryDto1_OrEthosModelTechnicianQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null = EthosModelQueryDto1EthosModelTechnicianQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null & {
    exprs?: Array<EthosModelQueryDto1_AllEthosModelTechnicianQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_LiteralEthosModelTechnicianQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_NotEthosModelTechnicianQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_AndEthosModelTechnicianQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_OrEthosModelTechnicianQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null>;
};

export type EthosModelQueryDto1EthosModelCareLocationQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null = {
    [key: string]: never;
};

export type EthosModelQueryDto1EthosModelDraftQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null = {
    [key: string]: never;
};

export type EthosModelQueryDto1EthosModelEquipmentQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null = {
    [key: string]: never;
};

export type EthosModelQueryDto1EthosModelInsuranceQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null = {
    [key: string]: never;
};

export type EthosModelQueryDto1EthosModelInsuranceVerificationQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null = {
    [key: string]: never;
};

export type EthosModelQueryDto1EthosModelNoteQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null = {
    [key: string]: never;
};

export type EthosModelQueryDto1EthosModelOrderQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null = {
    [key: string]: never;
};

export type EthosModelQueryDto1EthosModelPatientAppointmentConfirmationQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null = {
    [key: string]: never;
};

export type EthosModelQueryDto1EthosModelPatientAppointmentQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null = {
    [key: string]: never;
};

export type EthosModelQueryDto1EthosModelPatientQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null = {
    [key: string]: never;
};

export type EthosModelQueryDto1EthosModelPhysicianQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null = {
    [key: string]: never;
};

export type EthosModelQueryDto1EthosModelProviderQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null = {
    [key: string]: never;
};

export type EthosModelQueryDto1EthosModelRoomQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null = {
    [key: string]: never;
};

export type EthosModelQueryDto1EthosModelSchedulingSchedulingConstraintQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null = {
    [key: string]: never;
};

export type EthosModelQueryDto1EthosModelStudyQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null = {
    [key: string]: never;
};

export type EthosModelQueryDto1EthosModelTechnicianAppointmentQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null = {
    [key: string]: never;
};

export type EthosModelQueryDto1EthosModelTechnicianQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null = {
    [key: string]: never;
};

export type EthosModelRoomQ = {
    [key: string]: never;
};

export type EthosModelSchedulingExpr = {
    [key: string]: never;
};

export type EthosModelSchedulingSchedulingConstraintQ = {
    [key: string]: never;
};

export type EthosModelStudyQ = {
    [key: string]: never;
};

export type EthosModelTechnicianAppointmentQ = {
    [key: string]: never;
};

export type EthosModelTechnicianQ = {
    [key: string]: never;
};

export type EthosModelTypesAddress = {
    line1?: string;
    line2?: string | null;
    city?: string;
    state?: number;
    postalCode?: string;
    country?: number;
};

export type EthosModelTypesAddressWithUse = {
    address?: EthosModelTypesAddress;
    use?: number;
};

export type EthosModelTypesAddressWithUseType = {
    use?: number;
    type?: number;
    address?: EthosModelTypesAddress;
};

export type EthosModelTypesDemographics = {
    dateOfBirth?: string;
    gender?: number;
    birthSex?: number;
    maritalStatus?: number;
    race?: number;
    ethnicity?: number;
};

export type EthosModelTypesEmailWithUse = {
    email?: string;
    use?: number;
};

export type EthosModelTypesGuardianBasicInformation = {
    guardianType?: number;
    prefix?: number | null;
    firstName?: string;
    middleName?: string | null;
    lastName?: string;
    suffix?: number | null;
};

export type EthosModelTypesInsurance = {
    insuranceCarrier?: number;
    planType?: number;
    insuranceId?: string | null;
    policyId?: string;
    groupNumber?: string;
    memberId?: string;
    insuranceHolder?: EthosModelTypesInsuranceHolder | null;
    phoneNumber?: EthosModelTypesPhoneNumberWithUse | null;
    email?: EthosModelTypesEmailWithUse | null;
    address?: EthosModelTypesAddressWithUse | null;
};

export type EthosModelTypesInsuranceHolder = {
    name?: string;
    dateOfBirth?: string;
    relationship?: number;
};

export type EthosModelTypesPhoneNumberWithUse = {
    type?: number;
    phoneNumber?: string;
    use?: number;
    allowsSMS?: boolean | null;
    allowsVoice?: boolean | null;
    allowsCommunication?: boolean | null;
    extension?: string | null;
};

export type EthosModelTypesPhysicalMeasurements = {
    heightInches?: number;
    weightPounds?: number;
    neckSize?: number;
    bmi?: number;
};

export type EthosUtilitiesIssue = {
    paths?: Array<string>;
    message?: string;
    issueId?: string | null;
    data?: {
        [key: string]: SystemTextJsonNodesJsonNode;
    } | null;
};

export type EthosUtilitiesPaginationPagedResponse1EthosWorkflowsApiCareLocationDto_EthosWorkflows_Version_1000_Culture_neutral_PublicKeyToken_null = {
    items?: Array<EthosWorkflowsApiCareLocationDto>;
    offset?: number;
    limit?: number;
    totalCount?: number;
    previous?: string | null;
    next?: string | null;
};

export type EthosUtilitiesPaginationPagedResponse1EthosWorkflowsApiDraftDto_EthosWorkflows_Version_1000_Culture_neutral_PublicKeyToken_null = {
    items?: Array<EthosWorkflowsApiDraftDto>;
    offset?: number;
    limit?: number;
    totalCount?: number;
    previous?: string | null;
    next?: string | null;
};

export type EthosUtilitiesPaginationPagedResponse1EthosWorkflowsApiEquipmentDto_EthosWorkflows_Version_1000_Culture_neutral_PublicKeyToken_null = {
    items?: Array<EthosWorkflowsApiEquipmentDto>;
    offset?: number;
    limit?: number;
    totalCount?: number;
    previous?: string | null;
    next?: string | null;
};

export type EthosUtilitiesPaginationPagedResponse1EthosWorkflowsApiInsuranceOutputDto_EthosWorkflows_Version_1000_Culture_neutral_PublicKeyToken_null = {
    items?: Array<EthosWorkflowsApiInsuranceOutputDto>;
    offset?: number;
    limit?: number;
    totalCount?: number;
    previous?: string | null;
    next?: string | null;
};

export type EthosUtilitiesPaginationPagedResponse1EthosWorkflowsApiNoteDto_EthosWorkflows_Version_1000_Culture_neutral_PublicKeyToken_null = {
    items?: Array<EthosWorkflowsApiNoteDto>;
    offset?: number;
    limit?: number;
    totalCount?: number;
    previous?: string | null;
    next?: string | null;
};

export type EthosUtilitiesPaginationPagedResponse1EthosWorkflowsApiOrderDto_EthosWorkflows_Version_1000_Culture_neutral_PublicKeyToken_null = {
    items?: Array<EthosWorkflowsApiOrderDto>;
    offset?: number;
    limit?: number;
    totalCount?: number;
    previous?: string | null;
    next?: string | null;
};

export type EthosUtilitiesPaginationPagedResponse1EthosWorkflowsApiPatientAppointmentConfirmationDto_EthosWorkflows_Version_1000_Culture_neutral_PublicKeyToken_null = {
    items?: Array<EthosWorkflowsApiPatientAppointmentConfirmationDto>;
    offset?: number;
    limit?: number;
    totalCount?: number;
    previous?: string | null;
    next?: string | null;
};

export type EthosUtilitiesPaginationPagedResponse1EthosWorkflowsApiPatientAppointmentDto_EthosWorkflows_Version_1000_Culture_neutral_PublicKeyToken_null = {
    items?: Array<EthosWorkflowsApiPatientAppointmentDto>;
    offset?: number;
    limit?: number;
    totalCount?: number;
    previous?: string | null;
    next?: string | null;
};

export type EthosUtilitiesPaginationPagedResponse1EthosWorkflowsApiPatientDto_EthosWorkflows_Version_1000_Culture_neutral_PublicKeyToken_null = {
    items?: Array<EthosWorkflowsApiPatientDto>;
    offset?: number;
    limit?: number;
    totalCount?: number;
    previous?: string | null;
    next?: string | null;
};

export type EthosUtilitiesPaginationPagedResponse1EthosWorkflowsApiPhysicianDto_EthosWorkflows_Version_1000_Culture_neutral_PublicKeyToken_null = {
    items?: Array<EthosWorkflowsApiPhysicianDto>;
    offset?: number;
    limit?: number;
    totalCount?: number;
    previous?: string | null;
    next?: string | null;
};

export type EthosUtilitiesPaginationPagedResponse1EthosWorkflowsApiProviderDto_EthosWorkflows_Version_1000_Culture_neutral_PublicKeyToken_null = {
    items?: Array<EthosWorkflowsApiProviderDto>;
    offset?: number;
    limit?: number;
    totalCount?: number;
    previous?: string | null;
    next?: string | null;
};

export type EthosUtilitiesPaginationPagedResponse1EthosWorkflowsApiRoomDto_EthosWorkflows_Version_1000_Culture_neutral_PublicKeyToken_null = {
    items?: Array<EthosWorkflowsApiRoomDto>;
    offset?: number;
    limit?: number;
    totalCount?: number;
    previous?: string | null;
    next?: string | null;
};

export type EthosUtilitiesPaginationPagedResponse1EthosWorkflowsApiSchedulingConstraintDto_EthosWorkflows_Version_1000_Culture_neutral_PublicKeyToken_null = {
    items?: Array<EthosWorkflowsApiSchedulingConstraintDto>;
    offset?: number;
    limit?: number;
    totalCount?: number;
    previous?: string | null;
    next?: string | null;
};

export type EthosUtilitiesPaginationPagedResponse1EthosWorkflowsApiStudyDto_EthosWorkflows_Version_1000_Culture_neutral_PublicKeyToken_null = {
    items?: Array<EthosWorkflowsApiStudyDto>;
    offset?: number;
    limit?: number;
    totalCount?: number;
    previous?: string | null;
    next?: string | null;
};

export type EthosUtilitiesPaginationPagedResponse1EthosWorkflowsApiTechnicianAppointmentDto_EthosWorkflows_Version_1000_Culture_neutral_PublicKeyToken_null = {
    items?: Array<EthosWorkflowsApiTechnicianAppointmentDto>;
    offset?: number;
    limit?: number;
    totalCount?: number;
    previous?: string | null;
    next?: string | null;
};

export type EthosUtilitiesPaginationPagedResponse1EthosWorkflowsApiTechnicianDto_EthosWorkflows_Version_1000_Culture_neutral_PublicKeyToken_null = {
    items?: Array<EthosWorkflowsApiTechnicianDto>;
    offset?: number;
    limit?: number;
    totalCount?: number;
    previous?: string | null;
    next?: string | null;
};

export type EthosUtilitiesPaginationPagedResponse1EthosWorkflowsControllersInsuranceVerificationJobStatusDto_EthosWorkflows_Version_1000_Culture_neutral_PublicKeyToken_null = {
    items?: Array<EthosWorkflowsControllersInsuranceVerificationJobStatusDto>;
    offset?: number;
    limit?: number;
    totalCount?: number;
    previous?: string | null;
    next?: string | null;
};

export type EthosWorkflowsApiAddressDto = {
    line1: string;
    line2: string | null;
    city: string;
    state: number | null;
    postalCode: string;
    country: number;
};

export type EthosWorkflowsApiAddressWithUseDto = {
    address?: EthosWorkflowsApiAddressDto;
    use?: number;
};

export type EthosWorkflowsApiCareLocationDto = {
    id: string;
    name: string;
    parentServiceLocationId: string | null;
    parentProviderId: string | null;
    contactDetail: EthosWorkflowsApiOrganizationContactDetailDto | null;
    supportedEncounterTypes: Array<number> | null;
    supportedStudyTypes: Array<number> | null;
};

export type EthosWorkflowsApiCreateCareLocationDto = {
    name: string;
    parentServiceLocationId: string | null;
    parentProviderId: string | null;
    contactDetail: EthosWorkflowsApiOrganizationContactDetailDto | null;
    supportedEncounterTypes: Array<number> | null;
    supportedStudyTypes: Array<number> | null;
};

export type EthosWorkflowsApiCreateDraftDto = {
    entityType: EthosModelEntityType;
    entityId: string;
    data: {
        [key: string]: SystemTextJsonNodesJsonNode;
    };
};

export type EthosWorkflowsApiCreateEquipmentDto = {
    careLocationId: string | null;
    roomId: string | null;
    equipmentTypeId: number | null;
    equipmentData: {
        [key: string]: SystemTextJsonNodesJsonNode;
    };
};

export type EthosWorkflowsApiCreateInsuranceDto = {
    insuranceCarrier: number;
    insuranceId: string | null;
    policyId: string;
    groupNumber: string;
    memberId: string | null;
};

export type EthosWorkflowsApiCreateNoteDto = {
    entityType: EthosModelEntityType;
    entityId: string;
    content: string;
};

export type EthosWorkflowsApiCreateOrderDto = {
    patientId: string;
    careLocationId: string;
    primaryCarePhysicianId: string | null;
    referringPhysicianId: string | null;
    interpretingPhysicianId: string | null;
};

export type EthosWorkflowsApiCreatePatientAppointmentConfirmationDto = {
    appointmentId: string;
    confirmationTypeId: number;
    confirmationDate: string;
};

export type EthosWorkflowsApiCreatePatientAppointmentDto = {
    studyId?: string;
    roomId?: string;
    careLocationShiftId?: string;
    date?: string;
};

export type EthosWorkflowsApiCreatePatientInputDto = {
    patientInformation?: EthosWorkflowsApiPatientInformationDto | null;
    demographics?: EthosWorkflowsApiDemographicsDto | null;
    physicalMeasurements?: EthosWorkflowsApiPhysicalMeasurementsDto | null;
    contactInformation?: EthosWorkflowsApiPersonalContactDetailDto | null;
    insurances?: Array<EthosWorkflowsApiInsuranceDto> | null;
    guardians?: Array<EthosWorkflowsApiGuardianDto> | null;
    clinicalConsiderations?: Array<number> | null;
    schedulingPreferences?: EthosWorkflowsApiSchedulingPreferencesDto | null;
    additionalPatientNotes?: string | null;
    caregiverInformation?: string | null;
    identifiers?: Array<EthosWorkflowsApiIdentifierDto> | null;
};

export type EthosWorkflowsApiCreatePhysicianDto = {
    names: Array<EthosWorkflowsApiPersonNameDto>;
    demographics: EthosWorkflowsApiDemographicsDto | null;
    contactInformation: EthosWorkflowsApiPersonalContactDetailDto | null;
    careLocationIds: Array<string> | null;
    identifiers: Array<EthosWorkflowsApiIdentifierDto> | null;
};

export type EthosWorkflowsApiCreateProviderDto = {
    name?: string | null;
    parentProviderId?: string | null;
    identifiers?: Array<EthosWorkflowsApiIdentifierDto> | null;
    contactDetail?: EthosWorkflowsApiOrganizationContactDetailDto | null;
};

export type EthosWorkflowsApiCreateRoomDto = {
    name: string | null;
    careLocationId: string | null;
    supportedStudyTypes: Array<number> | null;
};

export type EthosWorkflowsApiCreateSchedulingConstraintDto = {
    name: string;
    description: string;
    expression: EthosModelSchedulingExpr;
    isHardConstraint: boolean;
};

export type EthosWorkflowsApiCreateStudyDto = {
    orderId: string;
    encounterType: number | null;
    studyType: number | null;
    studyAttributes: {
        [key: string]: SystemTextJsonNodesJsonNode;
    } | null;
    insurances: Array<string> | null;
};

export type EthosWorkflowsApiCreateTechnicianAppointmentDto = {
    studyId?: string;
    technicianId?: string;
    roomId?: string;
    careLocationShiftId?: string;
    date?: string;
};

export type EthosWorkflowsApiCreateTechnicianDto = {
    names: Array<EthosWorkflowsApiPersonNameDto> | null;
    demographics: EthosWorkflowsApiDemographicsDto | null;
    contactInformation: EthosWorkflowsApiPersonalContactDetailDto | null;
    qualifications: Array<EthosWorkflowsApiTechnicianQualificationDto> | null;
};

export type EthosWorkflowsApiDemographicsDto = {
    dateOfBirth: string | null;
    gender: number | null;
    birthSex: number | null;
    maritalStatus: number | null;
    race: number | null;
    ethnicity: number | null;
};

export type EthosWorkflowsApiDraftDto = {
    id: string;
    entityType: EthosModelEntityType;
    entityId: string;
    data: {
        [key: string]: SystemTextJsonNodesJsonNode;
    };
};

export type EthosWorkflowsApiEmailWithUseDto = {
    email?: string;
    use?: number;
};

export type EthosWorkflowsApiEquipmentDto = {
    id: string;
    careLocationId: string;
    roomId: string | null;
    equipmentTypeId: number;
    equipmentData: {
        [key: string]: SystemTextJsonNodesJsonNode;
    };
};

export type EthosWorkflowsApiGuardianBasicInformationDto = {
    guardianType?: number;
    prefix?: number | null;
    firstName?: string;
    middleName?: string | null;
    lastName?: string;
    suffix?: number | null;
};

export type EthosWorkflowsApiGuardianDto = {
    guardianDemographics?: {
        dateOfBirth: string
        gender: string
        idNumber: string
        relationShipToPatient: string
        ssn: string
    }
    emails?: Array<{
        value: string
        isPreferred: string
        use: number
    }>
    phoneNumbers?: Array<{
        value: string
        isPreferred: string
        use: number
    }>
    addresses?: Array<{
        address: {
            line1: string
            line2: string
            city: string
            state: string
            postalCode: string
        }
        type: number
        use: number
    }>
    guardianBasicInformation?: EthosWorkflowsApiGuardianBasicInformationDto;
};

export type EthosWorkflowsApiIdentifierDto = {
    system: string;
    value: string;
};

export type EthosWorkflowsApiInsuranceDto = {
    insuranceCarrier?: number;
    insuranceId?: string | null;
    policyId?: string;
    groupNumber?: string;
    memberId?: string;
    insuranceHolder?: EthosWorkflowsApiInsuranceHolderDto | null;
    phoneNumber?: EthosWorkflowsApiPhoneNumberWithUseDto | null;
    email?: EthosWorkflowsApiEmailWithUseDto | null;
    address?: EthosWorkflowsApiAddressWithUseDto | null;
};

export type EthosWorkflowsApiInsuranceHolderDto = {
    name: string;
    dateOfBirth: string;
    relationship: number;
};

export type EthosWorkflowsApiInsuranceOutputDto = {
    id: string;
    insuranceCarrier: number | null;
    insuranceId: string | null;
    policyId: string;
    groupNumber: string;
    memberId: string | null;
};

export type EthosWorkflowsApiIssueDto = {
    paths: Array<string>;
    message: string;
    issueId: string | null;
    data: {
        [key: string]: SystemTextJsonNodesJsonNode;
    } | null;
};

export type EthosWorkflowsApiLoginRequestDto = {
    username?: string;
    password?: string;
};

export type EthosWorkflowsApiNoteDto = {
    id: string;
    entityType: EthosModelEntityType;
    entityId: string;
    content: string;
};

export type EthosWorkflowsApiOrderDto = {
    id: string;
    patientId: string;
    careLocationId: string;
    primaryCarePhysicianId: string | null;
    referringPhysicianId: string | null;
    interpretingPhysicianId: string | null;
};

export type EthosWorkflowsApiOrganizationAddressDto = {
    use: number;
    type: number;
    address: EthosWorkflowsApiAddressDto;
};

export type EthosWorkflowsApiOrganizationContactDetailDto = {
    phoneNumbers: Array<EthosWorkflowsApiOrganizationPhoneNumberDto>;
    emails: Array<EthosWorkflowsApiOrganizationEmailDto>;
    addresses: Array<EthosWorkflowsApiOrganizationAddressDto>;
    contactPersons: Array<EthosWorkflowsApiOrganizationContactPersonDto>;
};

export type EthosWorkflowsApiOrganizationContactPersonDto = {
    name: EthosWorkflowsApiPersonNameDto;
    contactDetail: EthosWorkflowsApiPersonalContactDetailDto;
};

export type EthosWorkflowsApiOrganizationEmailDto = {
    email: string;
};

export type EthosWorkflowsApiOrganizationPhoneNumberDto = {
    phoneNumber: string;
};

export type EthosWorkflowsApiPatientAppointmentConfirmationDto = {
    id: string;
    appointmentId: string;
    confirmationTypeId: number;
    confirmationDate: string;
};

export type EthosWorkflowsApiPatientAppointmentDto = {
    id?: string;
    studyId?: string;
    roomId?: string;
    careLocationShiftId?: string;
    date?: string;
    createdAt?: string;
    updatedAt?: string;
    scheduledById?: string;
};

export type EthosWorkflowsApiPatientDto = {
    id: string;
    names: Array<EthosWorkflowsApiPersonNameDto>;
    identifiers: Array<EthosWorkflowsApiIdentifierDto>;
    demographics?: EthosWorkflowsApiDemographicsDto | null;
    physicalMeasurements?: EthosWorkflowsApiPhysicalMeasurementsDto | null;
    contactInformation?: EthosWorkflowsApiPersonalContactDetailDto | null;
    insurances?: Array<EthosWorkflowsApiInsuranceDto> | null;
    guardian?: EthosWorkflowsApiGuardianDto | null;
    schedulingPreferences?: EthosWorkflowsApiSchedulingPreferencesDto | null;
    clinicalConsiderations?: Array<number> | null;
    orderIds: Array<string>;
    lastStudyLocation: string | null;
    nextStudyDate?: string | null;
};

export type EthosWorkflowsApiPatientInformationDto = {
    prefix: number | null;
    suffix: number | null;
    ssn: string;
    firstName: string;
    middleName: string | null;
    lastName: string;
    mrn: string | null;
};

export type EthosWorkflowsApiPersonNameDto = {
    firstName: string;
    middleName: string | null;
    lastName: string;
    prefix: number | null;
    suffix: number | null;
};

export type EthosWorkflowsApiPersonalAddressDto = {
    use: number;
    type: number;
    address: EthosWorkflowsApiAddressDto;
};

export type EthosWorkflowsApiPersonalContactDetailDto = {
    phoneNumbers: Array<EthosWorkflowsApiPersonalPhoneNumberDto>;
    emails: Array<EthosWorkflowsApiPersonalEmailDto>;
    emergencyContacts: Array<EthosWorkflowsApiPersonalEmergencyContactDto>;
    addresses: Array<EthosWorkflowsApiPersonalAddressDto>;
};

export type EthosWorkflowsApiPersonalEmailDto = {
    use: number;
    value: string;
    isPreferred: boolean;
};

export type EthosWorkflowsApiPersonalEmergencyContactDto = {
    prefix: number | null;
    firstName: string;
    middleName: string | null;
    lastName: string;
    suffix: number | null;
    relationship: number;
    contactInformation: string;
};

export type EthosWorkflowsApiPersonalPhoneNumberDto = {
    type: number;
    value: string;
    preferredTime: number;
    allowsSms: boolean;
    allowsVoice: boolean;
    isPreferred: boolean;
};

export type EthosWorkflowsApiPhoneNumberWithUseDto = {
    phoneNumber?: string;
    use?: number;
    allowsSMS?: boolean | null;
    allowsVoice?: boolean | null;
    allowsCommunication?: boolean | null;
    extension?: string | null;
};

export type EthosWorkflowsApiPhysicalMeasurementsDto = {
    heightInches: number | null;
    weightPounds: number | null;
    neckSize: number | null;
    bmi: number | null;
};

export type EthosWorkflowsApiPhysicianDto = {
    id: string;
    names: Array<EthosWorkflowsApiPersonNameDto>;
    demographics: EthosWorkflowsApiDemographicsDto | null;
    contactInformation: EthosWorkflowsApiPersonalContactDetailDto | null;
    careLocationIds: Array<string>;
    identifiers: Array<EthosWorkflowsApiIdentifierDto>;
};

export type EthosWorkflowsApiProviderDto = {
    id: string;
    name: string;
    parentProviderId: string | null;
    identifiers: Array<EthosWorkflowsApiIdentifierDto>;
    contactDetail: EthosWorkflowsApiOrganizationContactDetailDto | null;
};

export type EthosWorkflowsApiRoomDto = {
    id: string;
    name: string | null;
    careLocationId: string;
    supportedStudyTypes: Array<number> | null;
};

export type EthosWorkflowsApiSchedulingConstraintDto = {
    id: string;
    name: string;
    description: string;
    expression: EthosModelSchedulingExpr;
    isHardConstraint: boolean;
};

export type EthosWorkflowsApiSchedulingPreferencesDto = {
    technicianPreference?: number | null;
    preferredDayOfWeek?: Array<number>;
};

export type EthosWorkflowsApiStudyDto = {
    id: string;
    orderId: string;
    encounterType: number | null;
    studyType: number | null;
    studyAttributes: {
        [key: string]: SystemTextJsonNodesJsonNode;
    } | null;
    insurances: Array<string> | null;
};

export type EthosWorkflowsApiTechnicianAppointmentDto = {
    id?: string;
    studyId?: string;
    technicianId?: string;
    roomId?: string;
    careLocationShiftId?: string;
    date?: string;
};

export type EthosWorkflowsApiTechnicianDto = {
    id: string;
    names: Array<EthosWorkflowsApiPersonNameDto>;
    demographics: EthosWorkflowsApiDemographicsDto | null;
    contactInformation: EthosWorkflowsApiPersonalContactDetailDto | null;
    qualifications: Array<EthosWorkflowsApiTechnicianQualificationDto>;
};

export type EthosWorkflowsApiTechnicianQualificationDto = {
    qualificationId: number;
    dateObtained: string | null;
    dateExpires: string | null;
};

export type EthosWorkflowsApiValidatedDraftDto = {
    id?: string;
    entityType?: EthosModelEntityType;
    entityId?: string;
    data?: {
        [key: string]: SystemTextJsonNodesJsonNode;
    };
    errors?: Array<EthosWorkflowsApiIssueDto> | null;
    warnings?: Array<EthosWorkflowsApiIssueDto> | null;
};

export type EthosWorkflowsControllersAvailableSlotDto = {
    date: string;
    roomId: string;
    careLocationShiftId: string;
    softConstraintViolations: Array<string>;
};

export type EthosWorkflowsControllersFindSlotsRequestDto = {
    studyId: string;
    careLocationId: string;
    startDate: string;
    endDate: string;
};

export type EthosWorkflowsControllersFindSlotsResponseDto = {
    availableSlots: Array<EthosWorkflowsControllersAvailableSlotDto>;
};

export type EthosWorkflowsControllersHealthReport = {
    status?: string;
    canConnectToDatabase?: boolean;
    canListPatients?: boolean;
    isFileStorageHealthy?: boolean;
    gitCommit?: string | null;
    gitBranch?: string | null;
    applicationVersion?: string | null;
    aspNetCoreEnvironment?: string | null;
    timestamp?: string;
    uptime?: string;
};

export type EthosWorkflowsControllersInsuranceVerificationJobStatusDto = {
    jobId?: string;
    studyId?: string;
    serviceId?: string;
    createdAt?: string;
    currentFineGrainedStateName?: string;
    currentFineGrainedStateDetails?: SystemTextJsonNodesJsonNode | null;
    currentCoarseState?: string;
    coarseStateMessage?: string | null;
    coarseStateSucceeded?: boolean | null;
    lastCoarseStateUpdate?: string | null;
    waitingUntil?: string | null;
    externalActionTimeoutTimestamp?: string | null;
    processingServiceInstanceId?: string | null;
    processingTimeoutTimestamp?: string | null;
};

export type EthosWorkflowsControllersInsuranceVerificationRequestDto = {
    studyId?: string;
    serviceId?: string;
};

export type EthosWorkflowsFilesFileStatusResponseDto = {
    fileId: string;
    status: EthosModelFileStatus;
    originalFileName: string;
    fileSize: number;
    mimeType: string;
    thumbnailUrl: string | null;
    failure: SystemTextJsonNodesJsonNode | null;
    uploadTimestamp: string;
    lastUpdateTimestamp: string;
    contextEntityType: EthosModelEntityType;
    contextEntityId: string;
    purpose: string;
    malwareScanPassed: boolean | null;
};

export type EthosWorkflowsFilesRequestUploadTokenDto = {
    contextEntityType: EthosModelEntityType;
    contextEntityId: string;
    purpose: string;
};

export type EthosWorkflowsFilesUploadResponseDto = {
    fileId: string;
    status: EthosModelFileStatus;
    originalFileName: string;
    fileSize: number;
    mimeType: string;
};

export type EthosWorkflowsFilesUploadTokenResponseDto = {
    uploadToken: string;
    uploadUrl: string;
};

export type EthosWorkflowsWorkflowAddNewOrderAddNewOrderState = {
    [key: string]: never;
};

export type EthosWorkflowsWorkflowAddNewOrderAddNewOrderState_AddedCareLocation = {
    studyPreferences?: EthosWorkflowsWorkflowAddNewOrderStudyPreferences;
    associatedInsurance?: Array<string>;
    careLocation?: string;
};

export type EthosWorkflowsWorkflowAddNewOrderAddNewOrderState_AddedPhysicians = {
    studyPreferences?: EthosWorkflowsWorkflowAddNewOrderStudyPreferences;
    associatedInsurance?: Array<string>;
    careLocation?: string;
    orderingPhysician?: string;
    interpretingPhysician?: string;
    referringPhysician?: string | null;
    primaryCarePhysician?: string | null;
};

export type EthosWorkflowsWorkflowAddNewOrderAddNewOrderState_AddedStudy = {
    studyPreferences?: EthosWorkflowsWorkflowAddNewOrderStudyPreferences;
    associatedInsurance?: Array<string>;
};

export type EthosWorkflowsWorkflowAddNewOrderAddNewOrderState_OrderSubmitted = {
    orderId?: string;
};

export type EthosWorkflowsWorkflowAddNewOrderIAddNewOrderRequest_AddCareLocation = {
    careLocation?: string;
};

export type EthosWorkflowsWorkflowAddNewOrderIAddNewOrderRequest_AddPhysicians = {
    orderingPhysician?: string;
    interpretingPhysician?: string;
    referringPhysician?: string | null;
    primaryCarePhysician?: string | null;
};

export type EthosWorkflowsWorkflowAddNewOrderIAddNewOrderRequest_AddStudy = {
    studyPreferences?: EthosWorkflowsWorkflowAddNewOrderStudyPreferences;
    associatedInsurance?: Array<string>;
};

export type EthosWorkflowsWorkflowAddNewOrderIAddNewOrderRequest_ReviewAndSubmitOrder = {
    [key: string]: never;
};

export type EthosWorkflowsWorkflowAddNewOrderStartAddNewOrderRequest = {
    patientId?: string;
};

export type EthosWorkflowsWorkflowAddNewOrderStartAddNewOrderResponse = {
    workflowId?: string;
    orderId?: string;
};

export type EthosWorkflowsWorkflowAddNewOrderStudyPreferences = {
    encounterType?: number;
    studyType?: number;
    studyAttributes?: SystemTextJsonNodesJsonNode;
};

export type EthosWorkflowsWorkflowAddNewPatientAddNewPatientState = {
    [key: string]: never;
};

export type EthosWorkflowsWorkflowAddNewPatientAddNewPatientState_AddedAddresses = {
    patientInformation?: EthosWorkflowsWorkflowAddNewPatientPatientInformation;
    demographics?: EthosModelTypesDemographics;
    physicalMeasurements?: EthosModelTypesPhysicalMeasurements;
    contactInformation?: EthosWorkflowsWorkflowAddNewPatientContactInformation;
    physicalAddresses?: Array<EthosModelTypesAddressWithUseType>;
    billingAddresses?: Array<EthosModelTypesAddressWithUseType>;
    deliveryAddresses?: Array<EthosModelTypesAddressWithUseType>;
};

export type EthosWorkflowsWorkflowAddNewPatientAddNewPatientState_AddedBasicInformation = {
    patientInformation?: EthosWorkflowsWorkflowAddNewPatientPatientInformation;
    demographics?: EthosModelTypesDemographics;
    physicalMeasurements?: EthosModelTypesPhysicalMeasurements;
};

export type EthosWorkflowsWorkflowAddNewPatientAddNewPatientState_AddedContacts = {
    patientInformation?: EthosWorkflowsWorkflowAddNewPatientPatientInformation;
    demographics?: EthosModelTypesDemographics;
    physicalMeasurements?: EthosModelTypesPhysicalMeasurements;
    contactInformation?: EthosWorkflowsWorkflowAddNewPatientContactInformation;
};

export type EthosWorkflowsWorkflowAddNewPatientAddNewPatientState_AddedGuardians = {
    patientInformation?: EthosWorkflowsWorkflowAddNewPatientPatientInformation;
    demographics?: EthosModelTypesDemographics;
    physicalMeasurements?: EthosModelTypesPhysicalMeasurements;
    contactInformation?: EthosWorkflowsWorkflowAddNewPatientContactInformation;
    physicalAddresses?: Array<EthosModelTypesAddressWithUseType>;
    billingAddresses?: Array<EthosModelTypesAddressWithUseType>;
    deliveryAddresses?: Array<EthosModelTypesAddressWithUseType>;
    insurances?: Array<EthosModelTypesInsurance>;
    guardians?: Array<EthosWorkflowsWorkflowAddNewPatientGuardian>;
};

export type EthosWorkflowsWorkflowAddNewPatientAddNewPatientState_AddedInsurances = {
    patientInformation?: EthosWorkflowsWorkflowAddNewPatientPatientInformation;
    demographics?: EthosModelTypesDemographics;
    physicalMeasurements?: EthosModelTypesPhysicalMeasurements;
    contactInformation?: EthosWorkflowsWorkflowAddNewPatientContactInformation;
    physicalAddresses?: Array<EthosModelTypesAddressWithUseType>;
    billingAddresses?: Array<EthosModelTypesAddressWithUseType>;
    deliveryAddresses?: Array<EthosModelTypesAddressWithUseType>;
    insurances?: Array<EthosModelTypesInsurance>;
};

export type EthosWorkflowsWorkflowAddNewPatientAddNewPatientState_Committed = {
    patientId?: string;
};

export type EthosWorkflowsWorkflowAddNewPatientContactInformation = {
    phoneNumbers?: Array<EthosWorkflowsWorkflowAddNewPatientPhoneNumberContact>;
    emails?: Array<EthosWorkflowsWorkflowAddNewPatientEmailContact>;
    emergencyContacts?: Array<EthosWorkflowsWorkflowAddNewPatientEmergencyContact>;
};

export type EthosWorkflowsWorkflowAddNewPatientEmailContact = {
    use?: number;
    value?: string;
    isPreferred?: boolean;
};

export type EthosWorkflowsWorkflowAddNewPatientEmergencyContact = {
    prefix?: number | null;
    firstName?: string;
    middleName?: string | null;
    lastName?: string;
    suffix?: number | null;
    relationship?: number;
    contactInformation?: string;
};

export type EthosWorkflowsWorkflowAddNewPatientGuardian = {
    guardianBasicInformation?: EthosModelTypesGuardianBasicInformation;
};

export type EthosWorkflowsWorkflowAddNewPatientIAddNewPatientRequest_AddAddresses = {
    physicalAddresses?: Array<EthosModelTypesAddressWithUseType>;
    billingAddresses?: Array<EthosModelTypesAddressWithUseType>;
    deliveryAddresses?: Array<EthosModelTypesAddressWithUseType>;
};

export type EthosWorkflowsWorkflowAddNewPatientIAddNewPatientRequest_AddBasicInformation = {
    patientInformation?: EthosWorkflowsWorkflowAddNewPatientPatientInformation;
    demographics?: EthosModelTypesDemographics;
    physicalMeasurements?: EthosModelTypesPhysicalMeasurements;
};

export type EthosWorkflowsWorkflowAddNewPatientIAddNewPatientRequest_AddClinicalInformation = {
    clinicalConsiderations?: Array<number>;
    schedulingPreferences?: EthosWorkflowsWorkflowAddNewPatientSchedulingPreferences;
    additionalPatientNotes?: string | null;
    caregiverInformation?: string | null;
};

export type EthosWorkflowsWorkflowAddNewPatientIAddNewPatientRequest_AddContacts = {
    contactInformation?: EthosWorkflowsWorkflowAddNewPatientContactInformation;
};

export type EthosWorkflowsWorkflowAddNewPatientIAddNewPatientRequest_AddGuardians = {
    guardians?: Array<EthosWorkflowsWorkflowAddNewPatientGuardian>;
};

export type EthosWorkflowsWorkflowAddNewPatientIAddNewPatientRequest_AddInsurances = {
    insurances?: Array<EthosModelTypesInsurance>;
};

export type EthosWorkflowsWorkflowAddNewPatientPatientInformation = {
    prefix?: number | null;
    firstName?: string;
    middleName?: string | null;
    lastName?: string;
    suffix?: number | null;
    ssn?: string;
    mrn?: string | null;
};

export type EthosWorkflowsWorkflowAddNewPatientPhoneNumberContact = {
    type?: number;
    value?: string;
    preferredTime?: number;
    allowsSMS?: boolean;
    allowsVoice?: boolean;
    isPreferred?: boolean;
};

export type EthosWorkflowsWorkflowAddNewPatientSchedulingPreferences = {
    technicianPreference?: number | null;
    preferredDaysOfWeek?: Array<number>;
};

export type EthosWorkflowsWorkflowAddNewPatientStartAddNewPatientRequest = {
    [key: string]: never;
};

export type EthosWorkflowsWorkflowAddNewPatientStartAddNewPatientResponse = {
    workflowId?: string;
    patientId?: string;
};

export type EthosWorkflowsWorkflowDraftRequest1EthosWorkflowsWorkflowAddNewOrderIAddNewOrderRequest_AddCareLocation_EthosWorkflows_Version_1000_Culture_neutral_PublicKeyToken_null = {
    instanceId?: string | null;
    inputData?: unknown;
};

export type EthosWorkflowsWorkflowDraftRequest1EthosWorkflowsWorkflowAddNewOrderIAddNewOrderRequest_AddPhysicians_EthosWorkflows_Version_1000_Culture_neutral_PublicKeyToken_null = {
    instanceId?: string | null;
    inputData?: unknown;
};

export type EthosWorkflowsWorkflowDraftRequest1EthosWorkflowsWorkflowAddNewOrderIAddNewOrderRequest_AddStudy_EthosWorkflows_Version_1000_Culture_neutral_PublicKeyToken_null = {
    instanceId?: string | null;
    inputData?: unknown;
};

export type EthosWorkflowsWorkflowDraftRequest1EthosWorkflowsWorkflowAddNewOrderIAddNewOrderRequest_ReviewAndSubmitOrder_EthosWorkflows_Version_1000_Culture_neutral_PublicKeyToken_null = {
    instanceId?: string | null;
    inputData?: unknown;
};

export type EthosWorkflowsWorkflowDraftRequest1EthosWorkflowsWorkflowAddNewPatientIAddNewPatientRequest_AddAddresses_EthosWorkflows_Version_1000_Culture_neutral_PublicKeyToken_null = {
    instanceId?: string | null;
    inputData?: unknown;
};

export type EthosWorkflowsWorkflowDraftRequest1EthosWorkflowsWorkflowAddNewPatientIAddNewPatientRequest_AddBasicInformation_EthosWorkflows_Version_1000_Culture_neutral_PublicKeyToken_null = {
    instanceId?: string | null;
    inputData?: unknown;
};

export type EthosWorkflowsWorkflowDraftRequest1EthosWorkflowsWorkflowAddNewPatientIAddNewPatientRequest_AddClinicalInformation_EthosWorkflows_Version_1000_Culture_neutral_PublicKeyToken_null = {
    instanceId?: string | null;
    inputData?: unknown;
};

export type EthosWorkflowsWorkflowDraftRequest1EthosWorkflowsWorkflowAddNewPatientIAddNewPatientRequest_AddContacts_EthosWorkflows_Version_1000_Culture_neutral_PublicKeyToken_null = {
    instanceId?: string | null;
    inputData?: unknown;
};

export type EthosWorkflowsWorkflowDraftRequest1EthosWorkflowsWorkflowAddNewPatientIAddNewPatientRequest_AddGuardians_EthosWorkflows_Version_1000_Culture_neutral_PublicKeyToken_null = {
    instanceId?: string | null;
    inputData?: unknown;
};

export type EthosWorkflowsWorkflowDraftRequest1EthosWorkflowsWorkflowAddNewPatientIAddNewPatientRequest_AddInsurances_EthosWorkflows_Version_1000_Culture_neutral_PublicKeyToken_null = {
    instanceId?: string | null;
    inputData?: unknown;
};

export type EthosWorkflowsWorkflowValidationExpr = {
    [key: string]: never;
};

export type EthosWorkflowsWorkflowValidationResult = {
    isValid?: boolean;
    warning?: Array<EthosUtilitiesIssue>;
    error?: Array<EthosUtilitiesIssue>;
};

export type EthosWorkflowsWorkflowValidationRule = {
    name?: string;
    expr?: EthosWorkflowsWorkflowValidationExpr;
    message?: string;
};

export type EthosWorkflowsWorkflowWorkflowRequest1EthosWorkflowsWorkflowAddNewOrderIAddNewOrderRequest_AddCareLocation_EthosWorkflows_Version_1000_Culture_neutral_PublicKeyToken_null = {
    instanceId?: string | null;
    inputData?: EthosWorkflowsWorkflowAddNewOrderIAddNewOrderRequest_AddCareLocation | null;
};

export type EthosWorkflowsWorkflowWorkflowRequest1EthosWorkflowsWorkflowAddNewOrderIAddNewOrderRequest_AddPhysicians_EthosWorkflows_Version_1000_Culture_neutral_PublicKeyToken_null = {
    instanceId?: string | null;
    inputData?: EthosWorkflowsWorkflowAddNewOrderIAddNewOrderRequest_AddPhysicians | null;
};

export type EthosWorkflowsWorkflowWorkflowRequest1EthosWorkflowsWorkflowAddNewOrderIAddNewOrderRequest_AddStudy_EthosWorkflows_Version_1000_Culture_neutral_PublicKeyToken_null = {
    instanceId?: string | null;
    inputData?: EthosWorkflowsWorkflowAddNewOrderIAddNewOrderRequest_AddStudy | null;
};

export type EthosWorkflowsWorkflowWorkflowRequest1EthosWorkflowsWorkflowAddNewOrderIAddNewOrderRequest_ReviewAndSubmitOrder_EthosWorkflows_Version_1000_Culture_neutral_PublicKeyToken_null = {
    instanceId?: string | null;
    inputData?: EthosWorkflowsWorkflowAddNewOrderIAddNewOrderRequest_ReviewAndSubmitOrder | null;
};

export type EthosWorkflowsWorkflowWorkflowRequest1EthosWorkflowsWorkflowAddNewPatientIAddNewPatientRequest_AddAddresses_EthosWorkflows_Version_1000_Culture_neutral_PublicKeyToken_null = {
    instanceId?: string | null;
    inputData?: EthosWorkflowsWorkflowAddNewPatientIAddNewPatientRequest_AddAddresses | null;
};

export type EthosWorkflowsWorkflowWorkflowRequest1EthosWorkflowsWorkflowAddNewPatientIAddNewPatientRequest_AddBasicInformation_EthosWorkflows_Version_1000_Culture_neutral_PublicKeyToken_null = {
    instanceId?: string | null;
    inputData?: EthosWorkflowsWorkflowAddNewPatientIAddNewPatientRequest_AddBasicInformation | null;
};

export type EthosWorkflowsWorkflowWorkflowRequest1EthosWorkflowsWorkflowAddNewPatientIAddNewPatientRequest_AddClinicalInformation_EthosWorkflows_Version_1000_Culture_neutral_PublicKeyToken_null = {
    instanceId?: string | null;
    inputData?: EthosWorkflowsWorkflowAddNewPatientIAddNewPatientRequest_AddClinicalInformation | null;
};

export type EthosWorkflowsWorkflowWorkflowRequest1EthosWorkflowsWorkflowAddNewPatientIAddNewPatientRequest_AddContacts_EthosWorkflows_Version_1000_Culture_neutral_PublicKeyToken_null = {
    instanceId?: string | null;
    inputData?: EthosWorkflowsWorkflowAddNewPatientIAddNewPatientRequest_AddContacts | null;
};

export type EthosWorkflowsWorkflowWorkflowRequest1EthosWorkflowsWorkflowAddNewPatientIAddNewPatientRequest_AddGuardians_EthosWorkflows_Version_1000_Culture_neutral_PublicKeyToken_null = {
    instanceId?: string | null;
    inputData?: EthosWorkflowsWorkflowAddNewPatientIAddNewPatientRequest_AddGuardians | null;
};

export type EthosWorkflowsWorkflowWorkflowRequest1EthosWorkflowsWorkflowAddNewPatientIAddNewPatientRequest_AddInsurances_EthosWorkflows_Version_1000_Culture_neutral_PublicKeyToken_null = {
    instanceId?: string | null;
    inputData?: EthosWorkflowsWorkflowAddNewPatientIAddNewPatientRequest_AddInsurances | null;
};

export type EthosWorkflowsWorkflowWorkflowResponse1EthosWorkflowsWorkflowAddNewOrderAddNewOrderState_AddedCareLocation_EthosWorkflows_Version_1000_Culture_neutral_PublicKeyToken_null = {
    instanceId?: string;
    outputData?: EthosWorkflowsWorkflowAddNewOrderAddNewOrderState_AddedCareLocation | null;
};

export type EthosWorkflowsWorkflowWorkflowResponse1EthosWorkflowsWorkflowAddNewOrderAddNewOrderState_AddedPhysicians_EthosWorkflows_Version_1000_Culture_neutral_PublicKeyToken_null = {
    instanceId?: string;
    outputData?: EthosWorkflowsWorkflowAddNewOrderAddNewOrderState_AddedPhysicians | null;
};

export type EthosWorkflowsWorkflowWorkflowResponse1EthosWorkflowsWorkflowAddNewOrderAddNewOrderState_AddedStudy_EthosWorkflows_Version_1000_Culture_neutral_PublicKeyToken_null = {
    instanceId?: string;
    outputData?: EthosWorkflowsWorkflowAddNewOrderAddNewOrderState_AddedStudy | null;
};

export type EthosWorkflowsWorkflowWorkflowResponse1EthosWorkflowsWorkflowAddNewOrderAddNewOrderState_OrderSubmitted_EthosWorkflows_Version_1000_Culture_neutral_PublicKeyToken_null = {
    instanceId?: string;
    outputData?: EthosWorkflowsWorkflowAddNewOrderAddNewOrderState_OrderSubmitted | null;
};

export type EthosWorkflowsWorkflowWorkflowResponse1EthosWorkflowsWorkflowAddNewPatientAddNewPatientState_AddedAddresses_EthosWorkflows_Version_1000_Culture_neutral_PublicKeyToken_null = {
    instanceId?: string;
    outputData?: EthosWorkflowsWorkflowAddNewPatientAddNewPatientState_AddedAddresses | null;
};

export type EthosWorkflowsWorkflowWorkflowResponse1EthosWorkflowsWorkflowAddNewPatientAddNewPatientState_AddedBasicInformation_EthosWorkflows_Version_1000_Culture_neutral_PublicKeyToken_null = {
    instanceId?: string;
    outputData?: EthosWorkflowsWorkflowAddNewPatientAddNewPatientState_AddedBasicInformation | null;
};

export type EthosWorkflowsWorkflowWorkflowResponse1EthosWorkflowsWorkflowAddNewPatientAddNewPatientState_AddedContacts_EthosWorkflows_Version_1000_Culture_neutral_PublicKeyToken_null = {
    instanceId?: string;
    outputData?: EthosWorkflowsWorkflowAddNewPatientAddNewPatientState_AddedContacts | null;
};

export type EthosWorkflowsWorkflowWorkflowResponse1EthosWorkflowsWorkflowAddNewPatientAddNewPatientState_AddedGuardians_EthosWorkflows_Version_1000_Culture_neutral_PublicKeyToken_null = {
    instanceId?: string;
    outputData?: EthosWorkflowsWorkflowAddNewPatientAddNewPatientState_AddedGuardians | null;
};

export type EthosWorkflowsWorkflowWorkflowResponse1EthosWorkflowsWorkflowAddNewPatientAddNewPatientState_AddedInsurances_EthosWorkflows_Version_1000_Culture_neutral_PublicKeyToken_null = {
    instanceId?: string;
    outputData?: EthosWorkflowsWorkflowAddNewPatientAddNewPatientState_AddedInsurances | null;
};

export type EthosWorkflowsWorkflowWorkflowResponse1EthosWorkflowsWorkflowAddNewPatientAddNewPatientState_Committed_EthosWorkflows_Version_1000_Culture_neutral_PublicKeyToken_null = {
    instanceId?: string;
    outputData?: EthosWorkflowsWorkflowAddNewPatientAddNewPatientState_Committed | null;
};

export type MicrosoftAspNetCoreMvcProblemDetails = {
    type?: string | null;
    title?: string | null;
    status?: number | null;
    detail?: string | null;
    instance?: string | null;
    [key: string]: unknown | (string | null) | (string | null) | (number | null) | (string | null) | (string | null) | undefined;
};

export type SystemTextJsonNodesJsonNode = {
    options?: SystemTextJsonNodesJsonNodeOptions | null;
    parent?: SystemTextJsonNodesJsonNode | null;
    root?: SystemTextJsonNodesJsonNode;
};

export type SystemTextJsonNodesJsonNodeOptions = {
    propertyNameCaseInsensitive?: boolean;
};

export type GetApiAddNewOrderValidationRulesData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/AddNewOrder/validation-rules';
};

export type GetApiAddNewOrderValidationRulesResponses = {
    /**
     * OK
     */
    200: {
        [key: string]: Array<EthosWorkflowsWorkflowValidationRule>;
    };
};

export type GetApiAddNewOrderValidationRulesResponse = GetApiAddNewOrderValidationRulesResponses[keyof GetApiAddNewOrderValidationRulesResponses];

export type GetApiAddNewOrderStateByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/AddNewOrder/state/{id}';
};

export type GetApiAddNewOrderStateByIdResponses = {
    /**
     * OK
     */
    200: EthosWorkflowsWorkflowAddNewOrderAddNewOrderState;
};

export type GetApiAddNewOrderStateByIdResponse = GetApiAddNewOrderStateByIdResponses[keyof GetApiAddNewOrderStateByIdResponses];

export type PostApiAddNewOrderStartData = {
    body?: EthosWorkflowsWorkflowAddNewOrderStartAddNewOrderRequest;
    path?: never;
    query?: never;
    url: '/api/AddNewOrder/start';
};

export type PostApiAddNewOrderStartResponses = {
    /**
     * OK
     */
    200: EthosWorkflowsWorkflowAddNewOrderStartAddNewOrderResponse;
};

export type PostApiAddNewOrderStartResponse = PostApiAddNewOrderStartResponses[keyof PostApiAddNewOrderStartResponses];

export type PostApiAddNewOrderRewindData = {
    body?: never;
    path?: never;
    query?: {
        id?: string;
        stateName?: string;
    };
    url: '/api/AddNewOrder/rewind';
};

export type PostApiAddNewOrderRewindResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type PostApiAddNewOrderListData = {
    body?: {
        [key: string]: string;
    };
    path?: never;
    query?: never;
    url: '/api/AddNewOrder/list';
};

export type PostApiAddNewOrderListResponses = {
    /**
     * OK
     */
    200: Array<string>;
};

export type PostApiAddNewOrderListResponse = PostApiAddNewOrderListResponses[keyof PostApiAddNewOrderListResponses];

export type PostApiAddNewOrderAddStudyData = {
    body?: EthosWorkflowsWorkflowWorkflowRequest1EthosWorkflowsWorkflowAddNewOrderIAddNewOrderRequest_AddStudy_EthosWorkflows_Version_1000_Culture_neutral_PublicKeyToken_null;
    path?: never;
    query?: never;
    url: '/api/AddNewOrder/add-study';
};

export type PostApiAddNewOrderAddStudyResponses = {
    /**
     * OK
     */
    200: EthosWorkflowsWorkflowWorkflowResponse1EthosWorkflowsWorkflowAddNewOrderAddNewOrderState_AddedStudy_EthosWorkflows_Version_1000_Culture_neutral_PublicKeyToken_null;
};

export type PostApiAddNewOrderAddStudyResponse = PostApiAddNewOrderAddStudyResponses[keyof PostApiAddNewOrderAddStudyResponses];

export type PostApiAddNewOrderAddStudyDraftData = {
    body?: EthosWorkflowsWorkflowDraftRequest1EthosWorkflowsWorkflowAddNewOrderIAddNewOrderRequest_AddStudy_EthosWorkflows_Version_1000_Culture_neutral_PublicKeyToken_null;
    path?: never;
    query?: never;
    url: '/api/AddNewOrder/add-study/draft';
};

export type PostApiAddNewOrderAddStudyDraftResponses = {
    /**
     * OK
     */
    200: string;
};

export type PostApiAddNewOrderAddStudyDraftResponse = PostApiAddNewOrderAddStudyDraftResponses[keyof PostApiAddNewOrderAddStudyDraftResponses];

export type PostApiAddNewOrderAddStudyValidateData = {
    body?: EthosWorkflowsWorkflowWorkflowRequest1EthosWorkflowsWorkflowAddNewOrderIAddNewOrderRequest_AddStudy_EthosWorkflows_Version_1000_Culture_neutral_PublicKeyToken_null;
    path?: never;
    query?: never;
    url: '/api/AddNewOrder/add-study/validate';
};

export type PostApiAddNewOrderAddStudyValidateResponses = {
    /**
     * OK
     */
    200: EthosWorkflowsWorkflowValidationResult;
};

export type PostApiAddNewOrderAddStudyValidateResponse = PostApiAddNewOrderAddStudyValidateResponses[keyof PostApiAddNewOrderAddStudyValidateResponses];

export type PostApiAddNewOrderAddCareLocationData = {
    body?: EthosWorkflowsWorkflowWorkflowRequest1EthosWorkflowsWorkflowAddNewOrderIAddNewOrderRequest_AddCareLocation_EthosWorkflows_Version_1000_Culture_neutral_PublicKeyToken_null;
    path?: never;
    query?: never;
    url: '/api/AddNewOrder/add-care-location';
};

export type PostApiAddNewOrderAddCareLocationResponses = {
    /**
     * OK
     */
    200: EthosWorkflowsWorkflowWorkflowResponse1EthosWorkflowsWorkflowAddNewOrderAddNewOrderState_AddedCareLocation_EthosWorkflows_Version_1000_Culture_neutral_PublicKeyToken_null;
};

export type PostApiAddNewOrderAddCareLocationResponse = PostApiAddNewOrderAddCareLocationResponses[keyof PostApiAddNewOrderAddCareLocationResponses];

export type PostApiAddNewOrderAddCareLocationDraftData = {
    body?: EthosWorkflowsWorkflowDraftRequest1EthosWorkflowsWorkflowAddNewOrderIAddNewOrderRequest_AddCareLocation_EthosWorkflows_Version_1000_Culture_neutral_PublicKeyToken_null;
    path?: never;
    query?: never;
    url: '/api/AddNewOrder/add-care-location/draft';
};

export type PostApiAddNewOrderAddCareLocationDraftResponses = {
    /**
     * OK
     */
    200: string;
};

export type PostApiAddNewOrderAddCareLocationDraftResponse = PostApiAddNewOrderAddCareLocationDraftResponses[keyof PostApiAddNewOrderAddCareLocationDraftResponses];

export type PostApiAddNewOrderAddCareLocationValidateData = {
    body?: EthosWorkflowsWorkflowWorkflowRequest1EthosWorkflowsWorkflowAddNewOrderIAddNewOrderRequest_AddCareLocation_EthosWorkflows_Version_1000_Culture_neutral_PublicKeyToken_null;
    path?: never;
    query?: never;
    url: '/api/AddNewOrder/add-care-location/validate';
};

export type PostApiAddNewOrderAddCareLocationValidateResponses = {
    /**
     * OK
     */
    200: EthosWorkflowsWorkflowValidationResult;
};

export type PostApiAddNewOrderAddCareLocationValidateResponse = PostApiAddNewOrderAddCareLocationValidateResponses[keyof PostApiAddNewOrderAddCareLocationValidateResponses];

export type PostApiAddNewOrderAddPhysiciansData = {
    body?: EthosWorkflowsWorkflowWorkflowRequest1EthosWorkflowsWorkflowAddNewOrderIAddNewOrderRequest_AddPhysicians_EthosWorkflows_Version_1000_Culture_neutral_PublicKeyToken_null;
    path?: never;
    query?: never;
    url: '/api/AddNewOrder/add-physicians';
};

export type PostApiAddNewOrderAddPhysiciansResponses = {
    /**
     * OK
     */
    200: EthosWorkflowsWorkflowWorkflowResponse1EthosWorkflowsWorkflowAddNewOrderAddNewOrderState_AddedPhysicians_EthosWorkflows_Version_1000_Culture_neutral_PublicKeyToken_null;
};

export type PostApiAddNewOrderAddPhysiciansResponse = PostApiAddNewOrderAddPhysiciansResponses[keyof PostApiAddNewOrderAddPhysiciansResponses];

export type PostApiAddNewOrderAddPhysiciansDraftData = {
    body?: EthosWorkflowsWorkflowDraftRequest1EthosWorkflowsWorkflowAddNewOrderIAddNewOrderRequest_AddPhysicians_EthosWorkflows_Version_1000_Culture_neutral_PublicKeyToken_null;
    path?: never;
    query?: never;
    url: '/api/AddNewOrder/add-physicians/draft';
};

export type PostApiAddNewOrderAddPhysiciansDraftResponses = {
    /**
     * OK
     */
    200: string;
};

export type PostApiAddNewOrderAddPhysiciansDraftResponse = PostApiAddNewOrderAddPhysiciansDraftResponses[keyof PostApiAddNewOrderAddPhysiciansDraftResponses];

export type PostApiAddNewOrderAddPhysiciansValidateData = {
    body?: EthosWorkflowsWorkflowWorkflowRequest1EthosWorkflowsWorkflowAddNewOrderIAddNewOrderRequest_AddPhysicians_EthosWorkflows_Version_1000_Culture_neutral_PublicKeyToken_null;
    path?: never;
    query?: never;
    url: '/api/AddNewOrder/add-physicians/validate';
};

export type PostApiAddNewOrderAddPhysiciansValidateResponses = {
    /**
     * OK
     */
    200: EthosWorkflowsWorkflowValidationResult;
};

export type PostApiAddNewOrderAddPhysiciansValidateResponse = PostApiAddNewOrderAddPhysiciansValidateResponses[keyof PostApiAddNewOrderAddPhysiciansValidateResponses];

export type PostApiAddNewOrderReviewAndSubmitOrderData = {
    body?: EthosWorkflowsWorkflowWorkflowRequest1EthosWorkflowsWorkflowAddNewOrderIAddNewOrderRequest_ReviewAndSubmitOrder_EthosWorkflows_Version_1000_Culture_neutral_PublicKeyToken_null;
    path?: never;
    query?: never;
    url: '/api/AddNewOrder/review-and-submit-order';
};

export type PostApiAddNewOrderReviewAndSubmitOrderResponses = {
    /**
     * OK
     */
    200: EthosWorkflowsWorkflowWorkflowResponse1EthosWorkflowsWorkflowAddNewOrderAddNewOrderState_OrderSubmitted_EthosWorkflows_Version_1000_Culture_neutral_PublicKeyToken_null;
};

export type PostApiAddNewOrderReviewAndSubmitOrderResponse = PostApiAddNewOrderReviewAndSubmitOrderResponses[keyof PostApiAddNewOrderReviewAndSubmitOrderResponses];

export type PostApiAddNewOrderReviewAndSubmitOrderDraftData = {
    body?: EthosWorkflowsWorkflowDraftRequest1EthosWorkflowsWorkflowAddNewOrderIAddNewOrderRequest_ReviewAndSubmitOrder_EthosWorkflows_Version_1000_Culture_neutral_PublicKeyToken_null;
    path?: never;
    query?: never;
    url: '/api/AddNewOrder/review-and-submit-order/draft';
};

export type PostApiAddNewOrderReviewAndSubmitOrderDraftResponses = {
    /**
     * OK
     */
    200: string;
};

export type PostApiAddNewOrderReviewAndSubmitOrderDraftResponse = PostApiAddNewOrderReviewAndSubmitOrderDraftResponses[keyof PostApiAddNewOrderReviewAndSubmitOrderDraftResponses];

export type PostApiAddNewOrderReviewAndSubmitOrderValidateData = {
    body?: EthosWorkflowsWorkflowWorkflowRequest1EthosWorkflowsWorkflowAddNewOrderIAddNewOrderRequest_ReviewAndSubmitOrder_EthosWorkflows_Version_1000_Culture_neutral_PublicKeyToken_null;
    path?: never;
    query?: never;
    url: '/api/AddNewOrder/review-and-submit-order/validate';
};

export type PostApiAddNewOrderReviewAndSubmitOrderValidateResponses = {
    /**
     * OK
     */
    200: EthosWorkflowsWorkflowValidationResult;
};

export type PostApiAddNewOrderReviewAndSubmitOrderValidateResponse = PostApiAddNewOrderReviewAndSubmitOrderValidateResponses[keyof PostApiAddNewOrderReviewAndSubmitOrderValidateResponses];

export type GetApiAddNewPatientValidationRulesData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/AddNewPatient/validation-rules';
};

export type GetApiAddNewPatientValidationRulesResponses = {
    /**
     * OK
     */
    200: {
        [key: string]: Array<EthosWorkflowsWorkflowValidationRule>;
    };
};

export type GetApiAddNewPatientValidationRulesResponse = GetApiAddNewPatientValidationRulesResponses[keyof GetApiAddNewPatientValidationRulesResponses];

export type GetApiAddNewPatientStateByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/AddNewPatient/state/{id}';
};

export type GetApiAddNewPatientStateByIdResponses = {
    /**
     * OK
     */
    200: EthosWorkflowsWorkflowAddNewPatientAddNewPatientState;
};

export type GetApiAddNewPatientStateByIdResponse = GetApiAddNewPatientStateByIdResponses[keyof GetApiAddNewPatientStateByIdResponses];

export type PostApiAddNewPatientStartData = {
    body?: EthosWorkflowsWorkflowAddNewPatientStartAddNewPatientRequest;
    path?: never;
    query?: never;
    url: '/api/AddNewPatient/start';
};

export type PostApiAddNewPatientStartResponses = {
    /**
     * OK
     */
    200: EthosWorkflowsWorkflowAddNewPatientStartAddNewPatientResponse;
};

export type PostApiAddNewPatientStartResponse = PostApiAddNewPatientStartResponses[keyof PostApiAddNewPatientStartResponses];

export type PostApiAddNewPatientRewindData = {
    body?: never;
    path?: never;
    query?: {
        id?: string;
        stateName?: string;
    };
    url: '/api/AddNewPatient/rewind';
};

export type PostApiAddNewPatientRewindResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type PostApiAddNewPatientListData = {
    body?: {
        [key: string]: string;
    };
    path?: never;
    query?: never;
    url: '/api/AddNewPatient/list';
};

export type PostApiAddNewPatientListResponses = {
    /**
     * OK
     */
    200: Array<string>;
};

export type PostApiAddNewPatientListResponse = PostApiAddNewPatientListResponses[keyof PostApiAddNewPatientListResponses];

export type PostApiAddNewPatientAddBasicInformationData = {
    body?: EthosWorkflowsWorkflowWorkflowRequest1EthosWorkflowsWorkflowAddNewPatientIAddNewPatientRequest_AddBasicInformation_EthosWorkflows_Version_1000_Culture_neutral_PublicKeyToken_null;
    path?: never;
    query?: never;
    url: '/api/AddNewPatient/add-basic-information';
};

export type PostApiAddNewPatientAddBasicInformationResponses = {
    /**
     * OK
     */
    200: EthosWorkflowsWorkflowWorkflowResponse1EthosWorkflowsWorkflowAddNewPatientAddNewPatientState_AddedBasicInformation_EthosWorkflows_Version_1000_Culture_neutral_PublicKeyToken_null;
};

export type PostApiAddNewPatientAddBasicInformationResponse = PostApiAddNewPatientAddBasicInformationResponses[keyof PostApiAddNewPatientAddBasicInformationResponses];

export type PostApiAddNewPatientAddBasicInformationDraftData = {
    body?: EthosWorkflowsWorkflowDraftRequest1EthosWorkflowsWorkflowAddNewPatientIAddNewPatientRequest_AddBasicInformation_EthosWorkflows_Version_1000_Culture_neutral_PublicKeyToken_null;
    path?: never;
    query?: never;
    url: '/api/AddNewPatient/add-basic-information/draft';
};

export type PostApiAddNewPatientAddBasicInformationDraftResponses = {
    /**
     * OK
     */
    200: string;
};

export type PostApiAddNewPatientAddBasicInformationDraftResponse = PostApiAddNewPatientAddBasicInformationDraftResponses[keyof PostApiAddNewPatientAddBasicInformationDraftResponses];

export type PostApiAddNewPatientAddBasicInformationValidateData = {
    body?: EthosWorkflowsWorkflowWorkflowRequest1EthosWorkflowsWorkflowAddNewPatientIAddNewPatientRequest_AddBasicInformation_EthosWorkflows_Version_1000_Culture_neutral_PublicKeyToken_null;
    path?: never;
    query?: never;
    url: '/api/AddNewPatient/add-basic-information/validate';
};

export type PostApiAddNewPatientAddBasicInformationValidateResponses = {
    /**
     * OK
     */
    200: EthosWorkflowsWorkflowValidationResult;
};

export type PostApiAddNewPatientAddBasicInformationValidateResponse = PostApiAddNewPatientAddBasicInformationValidateResponses[keyof PostApiAddNewPatientAddBasicInformationValidateResponses];

export type PostApiAddNewPatientAddContactsData = {
    body?: EthosWorkflowsWorkflowWorkflowRequest1EthosWorkflowsWorkflowAddNewPatientIAddNewPatientRequest_AddContacts_EthosWorkflows_Version_1000_Culture_neutral_PublicKeyToken_null;
    path?: never;
    query?: never;
    url: '/api/AddNewPatient/add-contacts';
};

export type PostApiAddNewPatientAddContactsResponses = {
    /**
     * OK
     */
    200: EthosWorkflowsWorkflowWorkflowResponse1EthosWorkflowsWorkflowAddNewPatientAddNewPatientState_AddedContacts_EthosWorkflows_Version_1000_Culture_neutral_PublicKeyToken_null;
};

export type PostApiAddNewPatientAddContactsResponse = PostApiAddNewPatientAddContactsResponses[keyof PostApiAddNewPatientAddContactsResponses];

export type PostApiAddNewPatientAddContactsDraftData = {
    body?: EthosWorkflowsWorkflowDraftRequest1EthosWorkflowsWorkflowAddNewPatientIAddNewPatientRequest_AddContacts_EthosWorkflows_Version_1000_Culture_neutral_PublicKeyToken_null;
    path?: never;
    query?: never;
    url: '/api/AddNewPatient/add-contacts/draft';
};

export type PostApiAddNewPatientAddContactsDraftResponses = {
    /**
     * OK
     */
    200: string;
};

export type PostApiAddNewPatientAddContactsDraftResponse = PostApiAddNewPatientAddContactsDraftResponses[keyof PostApiAddNewPatientAddContactsDraftResponses];

export type PostApiAddNewPatientAddContactsValidateData = {
    body?: EthosWorkflowsWorkflowWorkflowRequest1EthosWorkflowsWorkflowAddNewPatientIAddNewPatientRequest_AddContacts_EthosWorkflows_Version_1000_Culture_neutral_PublicKeyToken_null;
    path?: never;
    query?: never;
    url: '/api/AddNewPatient/add-contacts/validate';
};

export type PostApiAddNewPatientAddContactsValidateResponses = {
    /**
     * OK
     */
    200: EthosWorkflowsWorkflowValidationResult;
};

export type PostApiAddNewPatientAddContactsValidateResponse = PostApiAddNewPatientAddContactsValidateResponses[keyof PostApiAddNewPatientAddContactsValidateResponses];

export type PostApiAddNewPatientAddAddressesData = {
    body?: EthosWorkflowsWorkflowWorkflowRequest1EthosWorkflowsWorkflowAddNewPatientIAddNewPatientRequest_AddAddresses_EthosWorkflows_Version_1000_Culture_neutral_PublicKeyToken_null;
    path?: never;
    query?: never;
    url: '/api/AddNewPatient/add-addresses';
};

export type PostApiAddNewPatientAddAddressesResponses = {
    /**
     * OK
     */
    200: EthosWorkflowsWorkflowWorkflowResponse1EthosWorkflowsWorkflowAddNewPatientAddNewPatientState_AddedAddresses_EthosWorkflows_Version_1000_Culture_neutral_PublicKeyToken_null;
};

export type PostApiAddNewPatientAddAddressesResponse = PostApiAddNewPatientAddAddressesResponses[keyof PostApiAddNewPatientAddAddressesResponses];

export type PostApiAddNewPatientAddAddressesDraftData = {
    body?: EthosWorkflowsWorkflowDraftRequest1EthosWorkflowsWorkflowAddNewPatientIAddNewPatientRequest_AddAddresses_EthosWorkflows_Version_1000_Culture_neutral_PublicKeyToken_null;
    path?: never;
    query?: never;
    url: '/api/AddNewPatient/add-addresses/draft';
};

export type PostApiAddNewPatientAddAddressesDraftResponses = {
    /**
     * OK
     */
    200: string;
};

export type PostApiAddNewPatientAddAddressesDraftResponse = PostApiAddNewPatientAddAddressesDraftResponses[keyof PostApiAddNewPatientAddAddressesDraftResponses];

export type PostApiAddNewPatientAddAddressesValidateData = {
    body?: EthosWorkflowsWorkflowWorkflowRequest1EthosWorkflowsWorkflowAddNewPatientIAddNewPatientRequest_AddAddresses_EthosWorkflows_Version_1000_Culture_neutral_PublicKeyToken_null;
    path?: never;
    query?: never;
    url: '/api/AddNewPatient/add-addresses/validate';
};

export type PostApiAddNewPatientAddAddressesValidateResponses = {
    /**
     * OK
     */
    200: EthosWorkflowsWorkflowValidationResult;
};

export type PostApiAddNewPatientAddAddressesValidateResponse = PostApiAddNewPatientAddAddressesValidateResponses[keyof PostApiAddNewPatientAddAddressesValidateResponses];

export type PostApiAddNewPatientAddInsurancesData = {
    body?: EthosWorkflowsWorkflowWorkflowRequest1EthosWorkflowsWorkflowAddNewPatientIAddNewPatientRequest_AddInsurances_EthosWorkflows_Version_1000_Culture_neutral_PublicKeyToken_null;
    path?: never;
    query?: never;
    url: '/api/AddNewPatient/add-insurances';
};

export type PostApiAddNewPatientAddInsurancesResponses = {
    /**
     * OK
     */
    200: EthosWorkflowsWorkflowWorkflowResponse1EthosWorkflowsWorkflowAddNewPatientAddNewPatientState_AddedInsurances_EthosWorkflows_Version_1000_Culture_neutral_PublicKeyToken_null;
};

export type PostApiAddNewPatientAddInsurancesResponse = PostApiAddNewPatientAddInsurancesResponses[keyof PostApiAddNewPatientAddInsurancesResponses];

export type PostApiAddNewPatientAddInsurancesDraftData = {
    body?: EthosWorkflowsWorkflowDraftRequest1EthosWorkflowsWorkflowAddNewPatientIAddNewPatientRequest_AddInsurances_EthosWorkflows_Version_1000_Culture_neutral_PublicKeyToken_null;
    path?: never;
    query?: never;
    url: '/api/AddNewPatient/add-insurances/draft';
};

export type PostApiAddNewPatientAddInsurancesDraftResponses = {
    /**
     * OK
     */
    200: string;
};

export type PostApiAddNewPatientAddInsurancesDraftResponse = PostApiAddNewPatientAddInsurancesDraftResponses[keyof PostApiAddNewPatientAddInsurancesDraftResponses];

export type PostApiAddNewPatientAddInsurancesValidateData = {
    body?: EthosWorkflowsWorkflowWorkflowRequest1EthosWorkflowsWorkflowAddNewPatientIAddNewPatientRequest_AddInsurances_EthosWorkflows_Version_1000_Culture_neutral_PublicKeyToken_null;
    path?: never;
    query?: never;
    url: '/api/AddNewPatient/add-insurances/validate';
};

export type PostApiAddNewPatientAddInsurancesValidateResponses = {
    /**
     * OK
     */
    200: EthosWorkflowsWorkflowValidationResult;
};

export type PostApiAddNewPatientAddInsurancesValidateResponse = PostApiAddNewPatientAddInsurancesValidateResponses[keyof PostApiAddNewPatientAddInsurancesValidateResponses];

export type PostApiAddNewPatientAddGuardiansData = {
    body?: EthosWorkflowsWorkflowWorkflowRequest1EthosWorkflowsWorkflowAddNewPatientIAddNewPatientRequest_AddGuardians_EthosWorkflows_Version_1000_Culture_neutral_PublicKeyToken_null;
    path?: never;
    query?: never;
    url: '/api/AddNewPatient/add-guardians';
};

export type PostApiAddNewPatientAddGuardiansResponses = {
    /**
     * OK
     */
    200: EthosWorkflowsWorkflowWorkflowResponse1EthosWorkflowsWorkflowAddNewPatientAddNewPatientState_AddedGuardians_EthosWorkflows_Version_1000_Culture_neutral_PublicKeyToken_null;
};

export type PostApiAddNewPatientAddGuardiansResponse = PostApiAddNewPatientAddGuardiansResponses[keyof PostApiAddNewPatientAddGuardiansResponses];

export type PostApiAddNewPatientAddGuardiansDraftData = {
    body?: EthosWorkflowsWorkflowDraftRequest1EthosWorkflowsWorkflowAddNewPatientIAddNewPatientRequest_AddGuardians_EthosWorkflows_Version_1000_Culture_neutral_PublicKeyToken_null;
    path?: never;
    query?: never;
    url: '/api/AddNewPatient/add-guardians/draft';
};

export type PostApiAddNewPatientAddGuardiansDraftResponses = {
    /**
     * OK
     */
    200: string;
};

export type PostApiAddNewPatientAddGuardiansDraftResponse = PostApiAddNewPatientAddGuardiansDraftResponses[keyof PostApiAddNewPatientAddGuardiansDraftResponses];

export type PostApiAddNewPatientAddGuardiansValidateData = {
    body?: EthosWorkflowsWorkflowWorkflowRequest1EthosWorkflowsWorkflowAddNewPatientIAddNewPatientRequest_AddGuardians_EthosWorkflows_Version_1000_Culture_neutral_PublicKeyToken_null;
    path?: never;
    query?: never;
    url: '/api/AddNewPatient/add-guardians/validate';
};

export type PostApiAddNewPatientAddGuardiansValidateResponses = {
    /**
     * OK
     */
    200: EthosWorkflowsWorkflowValidationResult;
};

export type PostApiAddNewPatientAddGuardiansValidateResponse = PostApiAddNewPatientAddGuardiansValidateResponses[keyof PostApiAddNewPatientAddGuardiansValidateResponses];

export type PostApiAddNewPatientAddClinicalInformationData = {
    body?: EthosWorkflowsWorkflowWorkflowRequest1EthosWorkflowsWorkflowAddNewPatientIAddNewPatientRequest_AddClinicalInformation_EthosWorkflows_Version_1000_Culture_neutral_PublicKeyToken_null;
    path?: never;
    query?: never;
    url: '/api/AddNewPatient/add-clinical-information';
};

export type PostApiAddNewPatientAddClinicalInformationResponses = {
    /**
     * OK
     */
    200: EthosWorkflowsWorkflowWorkflowResponse1EthosWorkflowsWorkflowAddNewPatientAddNewPatientState_Committed_EthosWorkflows_Version_1000_Culture_neutral_PublicKeyToken_null;
};

export type PostApiAddNewPatientAddClinicalInformationResponse = PostApiAddNewPatientAddClinicalInformationResponses[keyof PostApiAddNewPatientAddClinicalInformationResponses];

export type PostApiAddNewPatientAddClinicalInformationDraftData = {
    body?: EthosWorkflowsWorkflowDraftRequest1EthosWorkflowsWorkflowAddNewPatientIAddNewPatientRequest_AddClinicalInformation_EthosWorkflows_Version_1000_Culture_neutral_PublicKeyToken_null;
    path?: never;
    query?: never;
    url: '/api/AddNewPatient/add-clinical-information/draft';
};

export type PostApiAddNewPatientAddClinicalInformationDraftResponses = {
    /**
     * OK
     */
    200: string;
};

export type PostApiAddNewPatientAddClinicalInformationDraftResponse = PostApiAddNewPatientAddClinicalInformationDraftResponses[keyof PostApiAddNewPatientAddClinicalInformationDraftResponses];

export type PostApiAddNewPatientAddClinicalInformationValidateData = {
    body?: EthosWorkflowsWorkflowWorkflowRequest1EthosWorkflowsWorkflowAddNewPatientIAddNewPatientRequest_AddClinicalInformation_EthosWorkflows_Version_1000_Culture_neutral_PublicKeyToken_null;
    path?: never;
    query?: never;
    url: '/api/AddNewPatient/add-clinical-information/validate';
};

export type PostApiAddNewPatientAddClinicalInformationValidateResponses = {
    /**
     * OK
     */
    200: EthosWorkflowsWorkflowValidationResult;
};

export type PostApiAddNewPatientAddClinicalInformationValidateResponse = PostApiAddNewPatientAddClinicalInformationValidateResponses[keyof PostApiAddNewPatientAddClinicalInformationValidateResponses];

export type GetApiCareLocationByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/CareLocation/{id}';
};

export type GetApiCareLocationByIdErrors = {
    /**
     * Not Found
     */
    404: MicrosoftAspNetCoreMvcProblemDetails;
};

export type GetApiCareLocationByIdError = GetApiCareLocationByIdErrors[keyof GetApiCareLocationByIdErrors];

export type GetApiCareLocationByIdResponses = {
    /**
     * OK
     */
    200: EthosWorkflowsApiCareLocationDto;
};

export type GetApiCareLocationByIdResponse = GetApiCareLocationByIdResponses[keyof GetApiCareLocationByIdResponses];

export type PatchApiCareLocationByIdData = {
    body?: EthosWorkflowsApiCreateCareLocationDto;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/CareLocation/{id}';
};

export type PatchApiCareLocationByIdErrors = {
    /**
     * Bad Request
     */
    400: MicrosoftAspNetCoreMvcProblemDetails;
    /**
     * Not Found
     */
    404: MicrosoftAspNetCoreMvcProblemDetails;
    /**
     * Precondition Failed
     */
    412: MicrosoftAspNetCoreMvcProblemDetails;
};

export type PatchApiCareLocationByIdError = PatchApiCareLocationByIdErrors[keyof PatchApiCareLocationByIdErrors];

export type PatchApiCareLocationByIdResponses = {
    /**
     * OK
     */
    200: EthosWorkflowsApiCareLocationDto;
};

export type PatchApiCareLocationByIdResponse = PatchApiCareLocationByIdResponses[keyof PatchApiCareLocationByIdResponses];

export type PutApiCareLocationByIdData = {
    body?: EthosWorkflowsApiCreateCareLocationDto;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/CareLocation/{id}';
};

export type PutApiCareLocationByIdErrors = {
    /**
     * Bad Request
     */
    400: MicrosoftAspNetCoreMvcProblemDetails;
    /**
     * Not Found
     */
    404: MicrosoftAspNetCoreMvcProblemDetails;
    /**
     * Precondition Failed
     */
    412: MicrosoftAspNetCoreMvcProblemDetails;
};

export type PutApiCareLocationByIdError = PutApiCareLocationByIdErrors[keyof PutApiCareLocationByIdErrors];

export type PutApiCareLocationByIdResponses = {
    /**
     * OK
     */
    200: EthosWorkflowsApiCareLocationDto;
    /**
     * Created
     */
    201: EthosWorkflowsApiCareLocationDto;
};

export type PutApiCareLocationByIdResponse = PutApiCareLocationByIdResponses[keyof PutApiCareLocationByIdResponses];

export type PostApiCareLocationSearchData = {
    body?: EthosModelQueryDto1_AllEthosModelCareLocationQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_LiteralEthosModelCareLocationQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_NotEthosModelCareLocationQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_AndEthosModelCareLocationQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_OrEthosModelCareLocationQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null;
    path?: never;
    query?: {
        offset?: number;
        limit?: number;
    };
    url: '/api/CareLocation/search';
};

export type PostApiCareLocationSearchResponses = {
    /**
     * OK
     */
    200: EthosUtilitiesPaginationPagedResponse1EthosWorkflowsApiCareLocationDto_EthosWorkflows_Version_1000_Culture_neutral_PublicKeyToken_null;
};

export type PostApiCareLocationSearchResponse = PostApiCareLocationSearchResponses[keyof PostApiCareLocationSearchResponses];

export type GetApiCareLocationData = {
    body?: never;
    path?: never;
    query?: {
        queryBase64?: string;
        offset?: number;
        limit?: number;
    };
    url: '/api/CareLocation';
};

export type GetApiCareLocationResponses = {
    /**
     * OK
     */
    200: EthosUtilitiesPaginationPagedResponse1EthosWorkflowsApiCareLocationDto_EthosWorkflows_Version_1000_Culture_neutral_PublicKeyToken_null;
};

export type GetApiCareLocationResponse = GetApiCareLocationResponses[keyof GetApiCareLocationResponses];

export type PostApiCareLocationData = {
    body?: EthosWorkflowsApiCreateCareLocationDto;
    path?: never;
    query?: never;
    url: '/api/CareLocation';
};

export type PostApiCareLocationErrors = {
    /**
     * Bad Request
     */
    400: MicrosoftAspNetCoreMvcProblemDetails;
};

export type PostApiCareLocationError = PostApiCareLocationErrors[keyof PostApiCareLocationErrors];

export type PostApiCareLocationResponses = {
    /**
     * Created
     */
    201: EthosWorkflowsApiCareLocationDto;
};

export type PostApiCareLocationResponse = PostApiCareLocationResponses[keyof PostApiCareLocationResponses];

export type PostApiCareLocationDraftData = {
    body?: {
        [key: string]: SystemTextJsonNodesJsonNode;
    };
    path?: never;
    query?: {
        entityId?: string;
    };
    url: '/api/CareLocation/draft';
};

export type PostApiCareLocationDraftErrors = {
    /**
     * Bad Request
     */
    400: MicrosoftAspNetCoreMvcProblemDetails;
};

export type PostApiCareLocationDraftError = PostApiCareLocationDraftErrors[keyof PostApiCareLocationDraftErrors];

export type PostApiCareLocationDraftResponses = {
    /**
     * Created
     */
    201: EthosWorkflowsApiValidatedDraftDto;
};

export type PostApiCareLocationDraftResponse = PostApiCareLocationDraftResponses[keyof PostApiCareLocationDraftResponses];

export type GetApiCareLocationDraftByEntityIdData = {
    body?: never;
    path: {
        entityId: string;
    };
    query?: never;
    url: '/api/CareLocation/draft/{entityId}';
};

export type GetApiCareLocationDraftByEntityIdErrors = {
    /**
     * Not Found
     */
    404: MicrosoftAspNetCoreMvcProblemDetails;
};

export type GetApiCareLocationDraftByEntityIdError = GetApiCareLocationDraftByEntityIdErrors[keyof GetApiCareLocationDraftByEntityIdErrors];

export type GetApiCareLocationDraftByEntityIdResponses = {
    /**
     * OK
     */
    200: EthosWorkflowsApiDraftDto;
};

export type GetApiCareLocationDraftByEntityIdResponse = GetApiCareLocationDraftByEntityIdResponses[keyof GetApiCareLocationDraftByEntityIdResponses];

export type PutApiCareLocationDraftByEntityIdData = {
    body?: {
        [key: string]: SystemTextJsonNodesJsonNode;
    };
    path: {
        entityId: string;
    };
    query?: never;
    url: '/api/CareLocation/draft/{entityId}';
};

export type PutApiCareLocationDraftByEntityIdErrors = {
    /**
     * Bad Request
     */
    400: MicrosoftAspNetCoreMvcProblemDetails;
    /**
     * Not Found
     */
    404: MicrosoftAspNetCoreMvcProblemDetails;
    /**
     * Precondition Failed
     */
    412: MicrosoftAspNetCoreMvcProblemDetails;
};

export type PutApiCareLocationDraftByEntityIdError = PutApiCareLocationDraftByEntityIdErrors[keyof PutApiCareLocationDraftByEntityIdErrors];

export type PutApiCareLocationDraftByEntityIdResponses = {
    /**
     * OK
     */
    200: EthosWorkflowsApiValidatedDraftDto;
};

export type PutApiCareLocationDraftByEntityIdResponse = PutApiCareLocationDraftByEntityIdResponses[keyof PutApiCareLocationDraftByEntityIdResponses];

export type PostApiCareLocationDraftByEntityIdCommitData = {
    body?: never;
    path: {
        entityId: string;
    };
    query?: never;
    url: '/api/CareLocation/draft/{entityId}/commit';
};

export type PostApiCareLocationDraftByEntityIdCommitErrors = {
    /**
     * Bad Request
     */
    400: MicrosoftAspNetCoreMvcProblemDetails;
    /**
     * Not Found
     */
    404: MicrosoftAspNetCoreMvcProblemDetails;
    /**
     * Precondition Failed
     */
    412: MicrosoftAspNetCoreMvcProblemDetails;
};

export type PostApiCareLocationDraftByEntityIdCommitError = PostApiCareLocationDraftByEntityIdCommitErrors[keyof PostApiCareLocationDraftByEntityIdCommitErrors];

export type PostApiCareLocationDraftByEntityIdCommitResponses = {
    /**
     * OK
     */
    200: EthosWorkflowsApiCareLocationDto;
    /**
     * Created
     */
    201: EthosWorkflowsApiCareLocationDto;
};

export type PostApiCareLocationDraftByEntityIdCommitResponse = PostApiCareLocationDraftByEntityIdCommitResponses[keyof PostApiCareLocationDraftByEntityIdCommitResponses];

export type PostApiCareLocationDraftValidateData = {
    body?: {
        [key: string]: SystemTextJsonNodesJsonNode;
    };
    path?: never;
    query?: never;
    url: '/api/CareLocation/draft/validate';
};

export type PostApiCareLocationDraftValidateErrors = {
    /**
     * Bad Request
     */
    400: MicrosoftAspNetCoreMvcProblemDetails;
};

export type PostApiCareLocationDraftValidateError = PostApiCareLocationDraftValidateErrors[keyof PostApiCareLocationDraftValidateErrors];

export type PostApiCareLocationDraftValidateResponses = {
    /**
     * OK
     */
    200: EthosWorkflowsApiValidatedDraftDto;
};

export type PostApiCareLocationDraftValidateResponse = PostApiCareLocationDraftValidateResponses[keyof PostApiCareLocationDraftValidateResponses];

export type PostApiCareLocationDraftByEntityIdValidateData = {
    body?: never;
    path: {
        entityId: string;
    };
    query?: never;
    url: '/api/CareLocation/draft/{entityId}/validate';
};

export type PostApiCareLocationDraftByEntityIdValidateErrors = {
    /**
     * Bad Request
     */
    400: MicrosoftAspNetCoreMvcProblemDetails;
    /**
     * Not Found
     */
    404: MicrosoftAspNetCoreMvcProblemDetails;
};

export type PostApiCareLocationDraftByEntityIdValidateError = PostApiCareLocationDraftByEntityIdValidateErrors[keyof PostApiCareLocationDraftByEntityIdValidateErrors];

export type PostApiCareLocationDraftByEntityIdValidateResponses = {
    /**
     * OK
     */
    200: EthosWorkflowsApiValidatedDraftDto;
};

export type PostApiCareLocationDraftByEntityIdValidateResponse = PostApiCareLocationDraftByEntityIdValidateResponses[keyof PostApiCareLocationDraftByEntityIdValidateResponses];

export type GetApiDraftByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/Draft/{id}';
};

export type GetApiDraftByIdErrors = {
    /**
     * Not Found
     */
    404: MicrosoftAspNetCoreMvcProblemDetails;
};

export type GetApiDraftByIdError = GetApiDraftByIdErrors[keyof GetApiDraftByIdErrors];

export type GetApiDraftByIdResponses = {
    /**
     * OK
     */
    200: EthosWorkflowsApiDraftDto;
};

export type GetApiDraftByIdResponse = GetApiDraftByIdResponses[keyof GetApiDraftByIdResponses];

export type PatchApiDraftByIdData = {
    body?: EthosWorkflowsApiCreateDraftDto;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/Draft/{id}';
};

export type PatchApiDraftByIdErrors = {
    /**
     * Bad Request
     */
    400: MicrosoftAspNetCoreMvcProblemDetails;
    /**
     * Not Found
     */
    404: MicrosoftAspNetCoreMvcProblemDetails;
    /**
     * Precondition Failed
     */
    412: MicrosoftAspNetCoreMvcProblemDetails;
};

export type PatchApiDraftByIdError = PatchApiDraftByIdErrors[keyof PatchApiDraftByIdErrors];

export type PatchApiDraftByIdResponses = {
    /**
     * OK
     */
    200: EthosWorkflowsApiDraftDto;
};

export type PatchApiDraftByIdResponse = PatchApiDraftByIdResponses[keyof PatchApiDraftByIdResponses];

export type PutApiDraftByIdData = {
    body?: EthosWorkflowsApiCreateDraftDto;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/Draft/{id}';
};

export type PutApiDraftByIdErrors = {
    /**
     * Bad Request
     */
    400: MicrosoftAspNetCoreMvcProblemDetails;
    /**
     * Not Found
     */
    404: MicrosoftAspNetCoreMvcProblemDetails;
    /**
     * Precondition Failed
     */
    412: MicrosoftAspNetCoreMvcProblemDetails;
};

export type PutApiDraftByIdError = PutApiDraftByIdErrors[keyof PutApiDraftByIdErrors];

export type PutApiDraftByIdResponses = {
    /**
     * OK
     */
    200: EthosWorkflowsApiDraftDto;
    /**
     * Created
     */
    201: EthosWorkflowsApiDraftDto;
};

export type PutApiDraftByIdResponse = PutApiDraftByIdResponses[keyof PutApiDraftByIdResponses];

export type PostApiDraftSearchData = {
    body?: EthosModelQueryDto1_AllEthosModelDraftQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_LiteralEthosModelDraftQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_NotEthosModelDraftQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_AndEthosModelDraftQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_OrEthosModelDraftQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null;
    path?: never;
    query?: {
        offset?: number;
        limit?: number;
    };
    url: '/api/Draft/search';
};

export type PostApiDraftSearchResponses = {
    /**
     * OK
     */
    200: EthosUtilitiesPaginationPagedResponse1EthosWorkflowsApiDraftDto_EthosWorkflows_Version_1000_Culture_neutral_PublicKeyToken_null;
};

export type PostApiDraftSearchResponse = PostApiDraftSearchResponses[keyof PostApiDraftSearchResponses];

export type GetApiDraftData = {
    body?: never;
    path?: never;
    query?: {
        queryBase64?: string;
        offset?: number;
        limit?: number;
    };
    url: '/api/Draft';
};

export type GetApiDraftResponses = {
    /**
     * OK
     */
    200: EthosUtilitiesPaginationPagedResponse1EthosWorkflowsApiDraftDto_EthosWorkflows_Version_1000_Culture_neutral_PublicKeyToken_null;
};

export type GetApiDraftResponse = GetApiDraftResponses[keyof GetApiDraftResponses];

export type PostApiDraftData = {
    body?: EthosWorkflowsApiCreateDraftDto;
    path?: never;
    query?: never;
    url: '/api/Draft';
};

export type PostApiDraftErrors = {
    /**
     * Bad Request
     */
    400: MicrosoftAspNetCoreMvcProblemDetails;
};

export type PostApiDraftError = PostApiDraftErrors[keyof PostApiDraftErrors];

export type PostApiDraftResponses = {
    /**
     * Created
     */
    201: EthosWorkflowsApiDraftDto;
};

export type PostApiDraftResponse = PostApiDraftResponses[keyof PostApiDraftResponses];

export type PostApiDraftDraftData = {
    body?: {
        [key: string]: SystemTextJsonNodesJsonNode;
    };
    path?: never;
    query?: {
        entityId?: string;
    };
    url: '/api/Draft/draft';
};

export type PostApiDraftDraftErrors = {
    /**
     * Bad Request
     */
    400: MicrosoftAspNetCoreMvcProblemDetails;
};

export type PostApiDraftDraftError = PostApiDraftDraftErrors[keyof PostApiDraftDraftErrors];

export type PostApiDraftDraftResponses = {
    /**
     * Created
     */
    201: EthosWorkflowsApiValidatedDraftDto;
};

export type PostApiDraftDraftResponse = PostApiDraftDraftResponses[keyof PostApiDraftDraftResponses];

export type GetApiDraftDraftByEntityIdData = {
    body?: never;
    path: {
        entityId: string;
    };
    query?: never;
    url: '/api/Draft/draft/{entityId}';
};

export type GetApiDraftDraftByEntityIdErrors = {
    /**
     * Not Found
     */
    404: MicrosoftAspNetCoreMvcProblemDetails;
};

export type GetApiDraftDraftByEntityIdError = GetApiDraftDraftByEntityIdErrors[keyof GetApiDraftDraftByEntityIdErrors];

export type GetApiDraftDraftByEntityIdResponses = {
    /**
     * OK
     */
    200: EthosWorkflowsApiDraftDto;
};

export type GetApiDraftDraftByEntityIdResponse = GetApiDraftDraftByEntityIdResponses[keyof GetApiDraftDraftByEntityIdResponses];

export type PutApiDraftDraftByEntityIdData = {
    body?: {
        [key: string]: SystemTextJsonNodesJsonNode;
    };
    path: {
        entityId: string;
    };
    query?: never;
    url: '/api/Draft/draft/{entityId}';
};

export type PutApiDraftDraftByEntityIdErrors = {
    /**
     * Bad Request
     */
    400: MicrosoftAspNetCoreMvcProblemDetails;
    /**
     * Not Found
     */
    404: MicrosoftAspNetCoreMvcProblemDetails;
    /**
     * Precondition Failed
     */
    412: MicrosoftAspNetCoreMvcProblemDetails;
};

export type PutApiDraftDraftByEntityIdError = PutApiDraftDraftByEntityIdErrors[keyof PutApiDraftDraftByEntityIdErrors];

export type PutApiDraftDraftByEntityIdResponses = {
    /**
     * OK
     */
    200: EthosWorkflowsApiValidatedDraftDto;
};

export type PutApiDraftDraftByEntityIdResponse = PutApiDraftDraftByEntityIdResponses[keyof PutApiDraftDraftByEntityIdResponses];

export type PostApiDraftDraftByEntityIdCommitData = {
    body?: never;
    path: {
        entityId: string;
    };
    query?: never;
    url: '/api/Draft/draft/{entityId}/commit';
};

export type PostApiDraftDraftByEntityIdCommitErrors = {
    /**
     * Bad Request
     */
    400: MicrosoftAspNetCoreMvcProblemDetails;
    /**
     * Not Found
     */
    404: MicrosoftAspNetCoreMvcProblemDetails;
    /**
     * Precondition Failed
     */
    412: MicrosoftAspNetCoreMvcProblemDetails;
};

export type PostApiDraftDraftByEntityIdCommitError = PostApiDraftDraftByEntityIdCommitErrors[keyof PostApiDraftDraftByEntityIdCommitErrors];

export type PostApiDraftDraftByEntityIdCommitResponses = {
    /**
     * OK
     */
    200: EthosWorkflowsApiDraftDto;
    /**
     * Created
     */
    201: EthosWorkflowsApiDraftDto;
};

export type PostApiDraftDraftByEntityIdCommitResponse = PostApiDraftDraftByEntityIdCommitResponses[keyof PostApiDraftDraftByEntityIdCommitResponses];

export type PostApiDraftDraftValidateData = {
    body?: {
        [key: string]: SystemTextJsonNodesJsonNode;
    };
    path?: never;
    query?: never;
    url: '/api/Draft/draft/validate';
};

export type PostApiDraftDraftValidateErrors = {
    /**
     * Bad Request
     */
    400: MicrosoftAspNetCoreMvcProblemDetails;
};

export type PostApiDraftDraftValidateError = PostApiDraftDraftValidateErrors[keyof PostApiDraftDraftValidateErrors];

export type PostApiDraftDraftValidateResponses = {
    /**
     * OK
     */
    200: EthosWorkflowsApiValidatedDraftDto;
};

export type PostApiDraftDraftValidateResponse = PostApiDraftDraftValidateResponses[keyof PostApiDraftDraftValidateResponses];

export type PostApiDraftDraftByEntityIdValidateData = {
    body?: never;
    path: {
        entityId: string;
    };
    query?: never;
    url: '/api/Draft/draft/{entityId}/validate';
};

export type PostApiDraftDraftByEntityIdValidateErrors = {
    /**
     * Bad Request
     */
    400: MicrosoftAspNetCoreMvcProblemDetails;
    /**
     * Not Found
     */
    404: MicrosoftAspNetCoreMvcProblemDetails;
};

export type PostApiDraftDraftByEntityIdValidateError = PostApiDraftDraftByEntityIdValidateErrors[keyof PostApiDraftDraftByEntityIdValidateErrors];

export type PostApiDraftDraftByEntityIdValidateResponses = {
    /**
     * OK
     */
    200: EthosWorkflowsApiValidatedDraftDto;
};

export type PostApiDraftDraftByEntityIdValidateResponse = PostApiDraftDraftByEntityIdValidateResponses[keyof PostApiDraftDraftByEntityIdValidateResponses];

export type GetApiEquipmentByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/Equipment/{id}';
};

export type GetApiEquipmentByIdErrors = {
    /**
     * Not Found
     */
    404: MicrosoftAspNetCoreMvcProblemDetails;
};

export type GetApiEquipmentByIdError = GetApiEquipmentByIdErrors[keyof GetApiEquipmentByIdErrors];

export type GetApiEquipmentByIdResponses = {
    /**
     * OK
     */
    200: EthosWorkflowsApiEquipmentDto;
};

export type GetApiEquipmentByIdResponse = GetApiEquipmentByIdResponses[keyof GetApiEquipmentByIdResponses];

export type PatchApiEquipmentByIdData = {
    body?: EthosWorkflowsApiCreateEquipmentDto;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/Equipment/{id}';
};

export type PatchApiEquipmentByIdErrors = {
    /**
     * Bad Request
     */
    400: MicrosoftAspNetCoreMvcProblemDetails;
    /**
     * Not Found
     */
    404: MicrosoftAspNetCoreMvcProblemDetails;
    /**
     * Precondition Failed
     */
    412: MicrosoftAspNetCoreMvcProblemDetails;
};

export type PatchApiEquipmentByIdError = PatchApiEquipmentByIdErrors[keyof PatchApiEquipmentByIdErrors];

export type PatchApiEquipmentByIdResponses = {
    /**
     * OK
     */
    200: EthosWorkflowsApiEquipmentDto;
};

export type PatchApiEquipmentByIdResponse = PatchApiEquipmentByIdResponses[keyof PatchApiEquipmentByIdResponses];

export type PutApiEquipmentByIdData = {
    body?: EthosWorkflowsApiCreateEquipmentDto;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/Equipment/{id}';
};

export type PutApiEquipmentByIdErrors = {
    /**
     * Bad Request
     */
    400: MicrosoftAspNetCoreMvcProblemDetails;
    /**
     * Not Found
     */
    404: MicrosoftAspNetCoreMvcProblemDetails;
    /**
     * Precondition Failed
     */
    412: MicrosoftAspNetCoreMvcProblemDetails;
};

export type PutApiEquipmentByIdError = PutApiEquipmentByIdErrors[keyof PutApiEquipmentByIdErrors];

export type PutApiEquipmentByIdResponses = {
    /**
     * OK
     */
    200: EthosWorkflowsApiEquipmentDto;
    /**
     * Created
     */
    201: EthosWorkflowsApiEquipmentDto;
};

export type PutApiEquipmentByIdResponse = PutApiEquipmentByIdResponses[keyof PutApiEquipmentByIdResponses];

export type PostApiEquipmentSearchData = {
    body?: EthosModelQueryDto1_AllEthosModelEquipmentQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_LiteralEthosModelEquipmentQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_NotEthosModelEquipmentQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_AndEthosModelEquipmentQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_OrEthosModelEquipmentQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null;
    path?: never;
    query?: {
        offset?: number;
        limit?: number;
    };
    url: '/api/Equipment/search';
};

export type PostApiEquipmentSearchResponses = {
    /**
     * OK
     */
    200: EthosUtilitiesPaginationPagedResponse1EthosWorkflowsApiEquipmentDto_EthosWorkflows_Version_1000_Culture_neutral_PublicKeyToken_null;
};

export type PostApiEquipmentSearchResponse = PostApiEquipmentSearchResponses[keyof PostApiEquipmentSearchResponses];

export type GetApiEquipmentData = {
    body?: never;
    path?: never;
    query?: {
        queryBase64?: string;
        offset?: number;
        limit?: number;
    };
    url: '/api/Equipment';
};

export type GetApiEquipmentResponses = {
    /**
     * OK
     */
    200: EthosUtilitiesPaginationPagedResponse1EthosWorkflowsApiEquipmentDto_EthosWorkflows_Version_1000_Culture_neutral_PublicKeyToken_null;
};

export type GetApiEquipmentResponse = GetApiEquipmentResponses[keyof GetApiEquipmentResponses];

export type PostApiEquipmentData = {
    body?: EthosWorkflowsApiCreateEquipmentDto;
    path?: never;
    query?: never;
    url: '/api/Equipment';
};

export type PostApiEquipmentErrors = {
    /**
     * Bad Request
     */
    400: MicrosoftAspNetCoreMvcProblemDetails;
};

export type PostApiEquipmentError = PostApiEquipmentErrors[keyof PostApiEquipmentErrors];

export type PostApiEquipmentResponses = {
    /**
     * Created
     */
    201: EthosWorkflowsApiEquipmentDto;
};

export type PostApiEquipmentResponse = PostApiEquipmentResponses[keyof PostApiEquipmentResponses];

export type PostApiEquipmentDraftData = {
    body?: {
        [key: string]: SystemTextJsonNodesJsonNode;
    };
    path?: never;
    query?: {
        entityId?: string;
    };
    url: '/api/Equipment/draft';
};

export type PostApiEquipmentDraftErrors = {
    /**
     * Bad Request
     */
    400: MicrosoftAspNetCoreMvcProblemDetails;
};

export type PostApiEquipmentDraftError = PostApiEquipmentDraftErrors[keyof PostApiEquipmentDraftErrors];

export type PostApiEquipmentDraftResponses = {
    /**
     * Created
     */
    201: EthosWorkflowsApiValidatedDraftDto;
};

export type PostApiEquipmentDraftResponse = PostApiEquipmentDraftResponses[keyof PostApiEquipmentDraftResponses];

export type GetApiEquipmentDraftByEntityIdData = {
    body?: never;
    path: {
        entityId: string;
    };
    query?: never;
    url: '/api/Equipment/draft/{entityId}';
};

export type GetApiEquipmentDraftByEntityIdErrors = {
    /**
     * Not Found
     */
    404: MicrosoftAspNetCoreMvcProblemDetails;
};

export type GetApiEquipmentDraftByEntityIdError = GetApiEquipmentDraftByEntityIdErrors[keyof GetApiEquipmentDraftByEntityIdErrors];

export type GetApiEquipmentDraftByEntityIdResponses = {
    /**
     * OK
     */
    200: EthosWorkflowsApiDraftDto;
};

export type GetApiEquipmentDraftByEntityIdResponse = GetApiEquipmentDraftByEntityIdResponses[keyof GetApiEquipmentDraftByEntityIdResponses];

export type PutApiEquipmentDraftByEntityIdData = {
    body?: {
        [key: string]: SystemTextJsonNodesJsonNode;
    };
    path: {
        entityId: string;
    };
    query?: never;
    url: '/api/Equipment/draft/{entityId}';
};

export type PutApiEquipmentDraftByEntityIdErrors = {
    /**
     * Bad Request
     */
    400: MicrosoftAspNetCoreMvcProblemDetails;
    /**
     * Not Found
     */
    404: MicrosoftAspNetCoreMvcProblemDetails;
    /**
     * Precondition Failed
     */
    412: MicrosoftAspNetCoreMvcProblemDetails;
};

export type PutApiEquipmentDraftByEntityIdError = PutApiEquipmentDraftByEntityIdErrors[keyof PutApiEquipmentDraftByEntityIdErrors];

export type PutApiEquipmentDraftByEntityIdResponses = {
    /**
     * OK
     */
    200: EthosWorkflowsApiValidatedDraftDto;
};

export type PutApiEquipmentDraftByEntityIdResponse = PutApiEquipmentDraftByEntityIdResponses[keyof PutApiEquipmentDraftByEntityIdResponses];

export type PostApiEquipmentDraftByEntityIdCommitData = {
    body?: never;
    path: {
        entityId: string;
    };
    query?: never;
    url: '/api/Equipment/draft/{entityId}/commit';
};

export type PostApiEquipmentDraftByEntityIdCommitErrors = {
    /**
     * Bad Request
     */
    400: MicrosoftAspNetCoreMvcProblemDetails;
    /**
     * Not Found
     */
    404: MicrosoftAspNetCoreMvcProblemDetails;
    /**
     * Precondition Failed
     */
    412: MicrosoftAspNetCoreMvcProblemDetails;
};

export type PostApiEquipmentDraftByEntityIdCommitError = PostApiEquipmentDraftByEntityIdCommitErrors[keyof PostApiEquipmentDraftByEntityIdCommitErrors];

export type PostApiEquipmentDraftByEntityIdCommitResponses = {
    /**
     * OK
     */
    200: EthosWorkflowsApiEquipmentDto;
    /**
     * Created
     */
    201: EthosWorkflowsApiEquipmentDto;
};

export type PostApiEquipmentDraftByEntityIdCommitResponse = PostApiEquipmentDraftByEntityIdCommitResponses[keyof PostApiEquipmentDraftByEntityIdCommitResponses];

export type PostApiEquipmentDraftValidateData = {
    body?: {
        [key: string]: SystemTextJsonNodesJsonNode;
    };
    path?: never;
    query?: never;
    url: '/api/Equipment/draft/validate';
};

export type PostApiEquipmentDraftValidateErrors = {
    /**
     * Bad Request
     */
    400: MicrosoftAspNetCoreMvcProblemDetails;
};

export type PostApiEquipmentDraftValidateError = PostApiEquipmentDraftValidateErrors[keyof PostApiEquipmentDraftValidateErrors];

export type PostApiEquipmentDraftValidateResponses = {
    /**
     * OK
     */
    200: EthosWorkflowsApiValidatedDraftDto;
};

export type PostApiEquipmentDraftValidateResponse = PostApiEquipmentDraftValidateResponses[keyof PostApiEquipmentDraftValidateResponses];

export type PostApiEquipmentDraftByEntityIdValidateData = {
    body?: never;
    path: {
        entityId: string;
    };
    query?: never;
    url: '/api/Equipment/draft/{entityId}/validate';
};

export type PostApiEquipmentDraftByEntityIdValidateErrors = {
    /**
     * Bad Request
     */
    400: MicrosoftAspNetCoreMvcProblemDetails;
    /**
     * Not Found
     */
    404: MicrosoftAspNetCoreMvcProblemDetails;
};

export type PostApiEquipmentDraftByEntityIdValidateError = PostApiEquipmentDraftByEntityIdValidateErrors[keyof PostApiEquipmentDraftByEntityIdValidateErrors];

export type PostApiEquipmentDraftByEntityIdValidateResponses = {
    /**
     * OK
     */
    200: EthosWorkflowsApiValidatedDraftDto;
};

export type PostApiEquipmentDraftByEntityIdValidateResponse = PostApiEquipmentDraftByEntityIdValidateResponses[keyof PostApiEquipmentDraftByEntityIdValidateResponses];

export type PostApiFileRequestUploadTokenData = {
    body?: EthosWorkflowsFilesRequestUploadTokenDto;
    path?: never;
    query?: never;
    url: '/api/File/request-upload-token';
};

export type PostApiFileRequestUploadTokenErrors = {
    /**
     * Bad Request
     */
    400: MicrosoftAspNetCoreMvcProblemDetails;
    /**
     * Forbidden
     */
    403: MicrosoftAspNetCoreMvcProblemDetails;
};

export type PostApiFileRequestUploadTokenError = PostApiFileRequestUploadTokenErrors[keyof PostApiFileRequestUploadTokenErrors];

export type PostApiFileRequestUploadTokenResponses = {
    /**
     * OK
     */
    200: EthosWorkflowsFilesUploadTokenResponseDto;
};

export type PostApiFileRequestUploadTokenResponse = PostApiFileRequestUploadTokenResponses[keyof PostApiFileRequestUploadTokenResponses];

export type PostApiFileUploadData = {
    body?: never;
    headers: {
        'Upload-Token': string;
    };
    path?: never;
    query?: never;
    url: '/api/File/upload';
};

export type PostApiFileUploadErrors = {
    /**
     * Bad Request
     */
    400: MicrosoftAspNetCoreMvcProblemDetails;
    /**
     * Unauthorized
     */
    401: MicrosoftAspNetCoreMvcProblemDetails;
    /**
     * Content Too Large
     */
    413: MicrosoftAspNetCoreMvcProblemDetails;
    /**
     * Unsupported Media Type
     */
    415: MicrosoftAspNetCoreMvcProblemDetails;
};

export type PostApiFileUploadError = PostApiFileUploadErrors[keyof PostApiFileUploadErrors];

export type PostApiFileUploadResponses = {
    /**
     * Accepted
     */
    202: EthosWorkflowsFilesUploadResponseDto;
};

export type PostApiFileUploadResponse = PostApiFileUploadResponses[keyof PostApiFileUploadResponses];

export type GetApiFileStatusByFileIdData = {
    body?: never;
    path: {
        fileId: string;
    };
    query?: never;
    url: '/api/File/status/{fileId}';
};

export type GetApiFileStatusByFileIdErrors = {
    /**
     * Not Found
     */
    404: MicrosoftAspNetCoreMvcProblemDetails;
};

export type GetApiFileStatusByFileIdError = GetApiFileStatusByFileIdErrors[keyof GetApiFileStatusByFileIdErrors];

export type GetApiFileStatusByFileIdResponses = {
    /**
     * OK
     */
    200: EthosWorkflowsFilesFileStatusResponseDto;
};

export type GetApiFileStatusByFileIdResponse = GetApiFileStatusByFileIdResponses[keyof GetApiFileStatusByFileIdResponses];

export type GetApiHealthData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/Health';
};

export type GetApiHealthResponses = {
    /**
     * OK
     */
    200: EthosWorkflowsControllersHealthReport;
};

export type GetApiHealthResponse = GetApiHealthResponses[keyof GetApiHealthResponses];

export type GetApiInsuranceByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/Insurance/{id}';
};

export type GetApiInsuranceByIdErrors = {
    /**
     * Not Found
     */
    404: MicrosoftAspNetCoreMvcProblemDetails;
};

export type GetApiInsuranceByIdError = GetApiInsuranceByIdErrors[keyof GetApiInsuranceByIdErrors];

export type GetApiInsuranceByIdResponses = {
    /**
     * OK
     */
    200: EthosWorkflowsApiInsuranceOutputDto;
};

export type GetApiInsuranceByIdResponse = GetApiInsuranceByIdResponses[keyof GetApiInsuranceByIdResponses];

export type PatchApiInsuranceByIdData = {
    body?: EthosWorkflowsApiCreateInsuranceDto;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/Insurance/{id}';
};

export type PatchApiInsuranceByIdErrors = {
    /**
     * Bad Request
     */
    400: MicrosoftAspNetCoreMvcProblemDetails;
    /**
     * Not Found
     */
    404: MicrosoftAspNetCoreMvcProblemDetails;
    /**
     * Precondition Failed
     */
    412: MicrosoftAspNetCoreMvcProblemDetails;
};

export type PatchApiInsuranceByIdError = PatchApiInsuranceByIdErrors[keyof PatchApiInsuranceByIdErrors];

export type PatchApiInsuranceByIdResponses = {
    /**
     * OK
     */
    200: EthosWorkflowsApiInsuranceOutputDto;
};

export type PatchApiInsuranceByIdResponse = PatchApiInsuranceByIdResponses[keyof PatchApiInsuranceByIdResponses];

export type PutApiInsuranceByIdData = {
    body?: EthosWorkflowsApiCreateInsuranceDto;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/Insurance/{id}';
};

export type PutApiInsuranceByIdErrors = {
    /**
     * Bad Request
     */
    400: MicrosoftAspNetCoreMvcProblemDetails;
    /**
     * Not Found
     */
    404: MicrosoftAspNetCoreMvcProblemDetails;
    /**
     * Precondition Failed
     */
    412: MicrosoftAspNetCoreMvcProblemDetails;
};

export type PutApiInsuranceByIdError = PutApiInsuranceByIdErrors[keyof PutApiInsuranceByIdErrors];

export type PutApiInsuranceByIdResponses = {
    /**
     * OK
     */
    200: EthosWorkflowsApiInsuranceOutputDto;
    /**
     * Created
     */
    201: EthosWorkflowsApiInsuranceOutputDto;
};

export type PutApiInsuranceByIdResponse = PutApiInsuranceByIdResponses[keyof PutApiInsuranceByIdResponses];

export type PostApiInsuranceSearchData = {
    body?: EthosModelQueryDto1_AllEthosModelInsuranceQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_LiteralEthosModelInsuranceQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_NotEthosModelInsuranceQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_AndEthosModelInsuranceQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_OrEthosModelInsuranceQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null;
    path?: never;
    query?: {
        offset?: number;
        limit?: number;
    };
    url: '/api/Insurance/search';
};

export type PostApiInsuranceSearchResponses = {
    /**
     * OK
     */
    200: EthosUtilitiesPaginationPagedResponse1EthosWorkflowsApiInsuranceOutputDto_EthosWorkflows_Version_1000_Culture_neutral_PublicKeyToken_null;
};

export type PostApiInsuranceSearchResponse = PostApiInsuranceSearchResponses[keyof PostApiInsuranceSearchResponses];

export type GetApiInsuranceData = {
    body?: never;
    path?: never;
    query?: {
        queryBase64?: string;
        offset?: number;
        limit?: number;
    };
    url: '/api/Insurance';
};

export type GetApiInsuranceResponses = {
    /**
     * OK
     */
    200: EthosUtilitiesPaginationPagedResponse1EthosWorkflowsApiInsuranceOutputDto_EthosWorkflows_Version_1000_Culture_neutral_PublicKeyToken_null;
};

export type GetApiInsuranceResponse = GetApiInsuranceResponses[keyof GetApiInsuranceResponses];

export type PostApiInsuranceData = {
    body?: EthosWorkflowsApiCreateInsuranceDto;
    path?: never;
    query?: never;
    url: '/api/Insurance';
};

export type PostApiInsuranceErrors = {
    /**
     * Bad Request
     */
    400: MicrosoftAspNetCoreMvcProblemDetails;
};

export type PostApiInsuranceError = PostApiInsuranceErrors[keyof PostApiInsuranceErrors];

export type PostApiInsuranceResponses = {
    /**
     * Created
     */
    201: EthosWorkflowsApiInsuranceOutputDto;
};

export type PostApiInsuranceResponse = PostApiInsuranceResponses[keyof PostApiInsuranceResponses];

export type PostApiInsuranceDraftData = {
    body?: {
        [key: string]: SystemTextJsonNodesJsonNode;
    };
    path?: never;
    query?: {
        entityId?: string;
    };
    url: '/api/Insurance/draft';
};

export type PostApiInsuranceDraftErrors = {
    /**
     * Bad Request
     */
    400: MicrosoftAspNetCoreMvcProblemDetails;
};

export type PostApiInsuranceDraftError = PostApiInsuranceDraftErrors[keyof PostApiInsuranceDraftErrors];

export type PostApiInsuranceDraftResponses = {
    /**
     * Created
     */
    201: EthosWorkflowsApiValidatedDraftDto;
};

export type PostApiInsuranceDraftResponse = PostApiInsuranceDraftResponses[keyof PostApiInsuranceDraftResponses];

export type GetApiInsuranceDraftByEntityIdData = {
    body?: never;
    path: {
        entityId: string;
    };
    query?: never;
    url: '/api/Insurance/draft/{entityId}';
};

export type GetApiInsuranceDraftByEntityIdErrors = {
    /**
     * Not Found
     */
    404: MicrosoftAspNetCoreMvcProblemDetails;
};

export type GetApiInsuranceDraftByEntityIdError = GetApiInsuranceDraftByEntityIdErrors[keyof GetApiInsuranceDraftByEntityIdErrors];

export type GetApiInsuranceDraftByEntityIdResponses = {
    /**
     * OK
     */
    200: EthosWorkflowsApiDraftDto;
};

export type GetApiInsuranceDraftByEntityIdResponse = GetApiInsuranceDraftByEntityIdResponses[keyof GetApiInsuranceDraftByEntityIdResponses];

export type PutApiInsuranceDraftByEntityIdData = {
    body?: {
        [key: string]: SystemTextJsonNodesJsonNode;
    };
    path: {
        entityId: string;
    };
    query?: never;
    url: '/api/Insurance/draft/{entityId}';
};

export type PutApiInsuranceDraftByEntityIdErrors = {
    /**
     * Bad Request
     */
    400: MicrosoftAspNetCoreMvcProblemDetails;
    /**
     * Not Found
     */
    404: MicrosoftAspNetCoreMvcProblemDetails;
    /**
     * Precondition Failed
     */
    412: MicrosoftAspNetCoreMvcProblemDetails;
};

export type PutApiInsuranceDraftByEntityIdError = PutApiInsuranceDraftByEntityIdErrors[keyof PutApiInsuranceDraftByEntityIdErrors];

export type PutApiInsuranceDraftByEntityIdResponses = {
    /**
     * OK
     */
    200: EthosWorkflowsApiValidatedDraftDto;
};

export type PutApiInsuranceDraftByEntityIdResponse = PutApiInsuranceDraftByEntityIdResponses[keyof PutApiInsuranceDraftByEntityIdResponses];

export type PostApiInsuranceDraftByEntityIdCommitData = {
    body?: never;
    path: {
        entityId: string;
    };
    query?: never;
    url: '/api/Insurance/draft/{entityId}/commit';
};

export type PostApiInsuranceDraftByEntityIdCommitErrors = {
    /**
     * Bad Request
     */
    400: MicrosoftAspNetCoreMvcProblemDetails;
    /**
     * Not Found
     */
    404: MicrosoftAspNetCoreMvcProblemDetails;
    /**
     * Precondition Failed
     */
    412: MicrosoftAspNetCoreMvcProblemDetails;
};

export type PostApiInsuranceDraftByEntityIdCommitError = PostApiInsuranceDraftByEntityIdCommitErrors[keyof PostApiInsuranceDraftByEntityIdCommitErrors];

export type PostApiInsuranceDraftByEntityIdCommitResponses = {
    /**
     * OK
     */
    200: EthosWorkflowsApiInsuranceOutputDto;
    /**
     * Created
     */
    201: EthosWorkflowsApiInsuranceOutputDto;
};

export type PostApiInsuranceDraftByEntityIdCommitResponse = PostApiInsuranceDraftByEntityIdCommitResponses[keyof PostApiInsuranceDraftByEntityIdCommitResponses];

export type PostApiInsuranceDraftValidateData = {
    body?: {
        [key: string]: SystemTextJsonNodesJsonNode;
    };
    path?: never;
    query?: never;
    url: '/api/Insurance/draft/validate';
};

export type PostApiInsuranceDraftValidateErrors = {
    /**
     * Bad Request
     */
    400: MicrosoftAspNetCoreMvcProblemDetails;
};

export type PostApiInsuranceDraftValidateError = PostApiInsuranceDraftValidateErrors[keyof PostApiInsuranceDraftValidateErrors];

export type PostApiInsuranceDraftValidateResponses = {
    /**
     * OK
     */
    200: EthosWorkflowsApiValidatedDraftDto;
};

export type PostApiInsuranceDraftValidateResponse = PostApiInsuranceDraftValidateResponses[keyof PostApiInsuranceDraftValidateResponses];

export type PostApiInsuranceDraftByEntityIdValidateData = {
    body?: never;
    path: {
        entityId: string;
    };
    query?: never;
    url: '/api/Insurance/draft/{entityId}/validate';
};

export type PostApiInsuranceDraftByEntityIdValidateErrors = {
    /**
     * Bad Request
     */
    400: MicrosoftAspNetCoreMvcProblemDetails;
    /**
     * Not Found
     */
    404: MicrosoftAspNetCoreMvcProblemDetails;
};

export type PostApiInsuranceDraftByEntityIdValidateError = PostApiInsuranceDraftByEntityIdValidateErrors[keyof PostApiInsuranceDraftByEntityIdValidateErrors];

export type PostApiInsuranceDraftByEntityIdValidateResponses = {
    /**
     * OK
     */
    200: EthosWorkflowsApiValidatedDraftDto;
};

export type PostApiInsuranceDraftByEntityIdValidateResponse = PostApiInsuranceDraftByEntityIdValidateResponses[keyof PostApiInsuranceDraftByEntityIdValidateResponses];

export type PostApiInsuranceVerificationStartData = {
    body?: EthosWorkflowsControllersInsuranceVerificationRequestDto;
    path?: never;
    query?: never;
    url: '/api/InsuranceVerification/start';
};

export type PostApiInsuranceVerificationStartErrors = {
    /**
     * Bad Request
     */
    400: MicrosoftAspNetCoreMvcProblemDetails;
    /**
     * Unauthorized
     */
    401: MicrosoftAspNetCoreMvcProblemDetails;
    /**
     * Internal Server Error
     */
    500: unknown;
};

export type PostApiInsuranceVerificationStartError = PostApiInsuranceVerificationStartErrors[keyof PostApiInsuranceVerificationStartErrors];

export type PostApiInsuranceVerificationStartResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetApiInsuranceVerificationStatusByJobIdData = {
    body?: never;
    path: {
        jobId: string;
    };
    query?: never;
    url: '/api/InsuranceVerification/status/{jobId}';
};

export type GetApiInsuranceVerificationStatusByJobIdErrors = {
    /**
     * Not Found
     */
    404: MicrosoftAspNetCoreMvcProblemDetails;
    /**
     * Internal Server Error
     */
    500: MicrosoftAspNetCoreMvcProblemDetails;
};

export type GetApiInsuranceVerificationStatusByJobIdError = GetApiInsuranceVerificationStatusByJobIdErrors[keyof GetApiInsuranceVerificationStatusByJobIdErrors];

export type GetApiInsuranceVerificationStatusByJobIdResponses = {
    /**
     * OK
     */
    200: EthosWorkflowsControllersInsuranceVerificationJobStatusDto;
};

export type GetApiInsuranceVerificationStatusByJobIdResponse = GetApiInsuranceVerificationStatusByJobIdResponses[keyof GetApiInsuranceVerificationStatusByJobIdResponses];

export type PostApiInsuranceVerificationSearchData = {
    body?: EthosModelQueryDto1_AllEthosModelInsuranceVerificationQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_LiteralEthosModelInsuranceVerificationQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_NotEthosModelInsuranceVerificationQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_AndEthosModelInsuranceVerificationQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_OrEthosModelInsuranceVerificationQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null;
    path?: never;
    query?: {
        offset?: number;
        limit?: number;
    };
    url: '/api/InsuranceVerification/search';
};

export type PostApiInsuranceVerificationSearchErrors = {
    /**
     * Bad Request
     */
    400: MicrosoftAspNetCoreMvcProblemDetails;
    /**
     * Internal Server Error
     */
    500: MicrosoftAspNetCoreMvcProblemDetails;
};

export type PostApiInsuranceVerificationSearchError = PostApiInsuranceVerificationSearchErrors[keyof PostApiInsuranceVerificationSearchErrors];

export type PostApiInsuranceVerificationSearchResponses = {
    /**
     * OK
     */
    200: EthosUtilitiesPaginationPagedResponse1EthosWorkflowsControllersInsuranceVerificationJobStatusDto_EthosWorkflows_Version_1000_Culture_neutral_PublicKeyToken_null;
};

export type PostApiInsuranceVerificationSearchResponse = PostApiInsuranceVerificationSearchResponses[keyof PostApiInsuranceVerificationSearchResponses];

export type PostApiInsuranceVerificationFastauthWebhookData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/InsuranceVerification/fastauth-webhook';
};

export type PostApiInsuranceVerificationFastauthWebhookErrors = {
    /**
     * Bad Request
     */
    400: MicrosoftAspNetCoreMvcProblemDetails;
    /**
     * Unauthorized
     */
    401: MicrosoftAspNetCoreMvcProblemDetails;
    /**
     * Internal Server Error
     */
    500: unknown;
};

export type PostApiInsuranceVerificationFastauthWebhookError = PostApiInsuranceVerificationFastauthWebhookErrors[keyof PostApiInsuranceVerificationFastauthWebhookErrors];

export type PostApiInsuranceVerificationFastauthWebhookResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type PostApiLoginData = {
    body?: EthosWorkflowsApiLoginRequestDto;
    path?: never;
    query?: never;
    url: '/api/Login';
};

export type PostApiLoginResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetApiLoginCheckData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/Login/check';
};

export type GetApiLoginCheckResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type PostApiMockDbDeleteData = {
    body?: Array<string>;
    path?: never;
    query?: never;
    url: '/api/MockDb/delete';
};

export type PostApiMockDbDeleteResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetApiMockDbResetData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/MockDb/reset';
};

export type GetApiMockDbResetResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetApiNoteByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/Note/{id}';
};

export type GetApiNoteByIdErrors = {
    /**
     * Not Found
     */
    404: MicrosoftAspNetCoreMvcProblemDetails;
};

export type GetApiNoteByIdError = GetApiNoteByIdErrors[keyof GetApiNoteByIdErrors];

export type GetApiNoteByIdResponses = {
    /**
     * OK
     */
    200: EthosWorkflowsApiNoteDto;
};

export type GetApiNoteByIdResponse = GetApiNoteByIdResponses[keyof GetApiNoteByIdResponses];

export type PatchApiNoteByIdData = {
    body?: EthosWorkflowsApiCreateNoteDto;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/Note/{id}';
};

export type PatchApiNoteByIdErrors = {
    /**
     * Bad Request
     */
    400: MicrosoftAspNetCoreMvcProblemDetails;
    /**
     * Not Found
     */
    404: MicrosoftAspNetCoreMvcProblemDetails;
    /**
     * Precondition Failed
     */
    412: MicrosoftAspNetCoreMvcProblemDetails;
};

export type PatchApiNoteByIdError = PatchApiNoteByIdErrors[keyof PatchApiNoteByIdErrors];

export type PatchApiNoteByIdResponses = {
    /**
     * OK
     */
    200: EthosWorkflowsApiNoteDto;
};

export type PatchApiNoteByIdResponse = PatchApiNoteByIdResponses[keyof PatchApiNoteByIdResponses];

export type PutApiNoteByIdData = {
    body?: EthosWorkflowsApiCreateNoteDto;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/Note/{id}';
};

export type PutApiNoteByIdErrors = {
    /**
     * Bad Request
     */
    400: MicrosoftAspNetCoreMvcProblemDetails;
    /**
     * Not Found
     */
    404: MicrosoftAspNetCoreMvcProblemDetails;
    /**
     * Precondition Failed
     */
    412: MicrosoftAspNetCoreMvcProblemDetails;
};

export type PutApiNoteByIdError = PutApiNoteByIdErrors[keyof PutApiNoteByIdErrors];

export type PutApiNoteByIdResponses = {
    /**
     * OK
     */
    200: EthosWorkflowsApiNoteDto;
    /**
     * Created
     */
    201: EthosWorkflowsApiNoteDto;
};

export type PutApiNoteByIdResponse = PutApiNoteByIdResponses[keyof PutApiNoteByIdResponses];

export type PostApiNoteSearchData = {
    body?: EthosModelQueryDto1_AllEthosModelNoteQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_LiteralEthosModelNoteQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_NotEthosModelNoteQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_AndEthosModelNoteQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_OrEthosModelNoteQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null;
    path?: never;
    query?: {
        offset?: number;
        limit?: number;
    };
    url: '/api/Note/search';
};

export type PostApiNoteSearchResponses = {
    /**
     * OK
     */
    200: EthosUtilitiesPaginationPagedResponse1EthosWorkflowsApiNoteDto_EthosWorkflows_Version_1000_Culture_neutral_PublicKeyToken_null;
};

export type PostApiNoteSearchResponse = PostApiNoteSearchResponses[keyof PostApiNoteSearchResponses];

export type GetApiNoteData = {
    body?: never;
    path?: never;
    query?: {
        queryBase64?: string;
        offset?: number;
        limit?: number;
    };
    url: '/api/Note';
};

export type GetApiNoteResponses = {
    /**
     * OK
     */
    200: EthosUtilitiesPaginationPagedResponse1EthosWorkflowsApiNoteDto_EthosWorkflows_Version_1000_Culture_neutral_PublicKeyToken_null;
};

export type GetApiNoteResponse = GetApiNoteResponses[keyof GetApiNoteResponses];

export type PostApiNoteData = {
    body?: EthosWorkflowsApiCreateNoteDto;
    path?: never;
    query?: never;
    url: '/api/Note';
};

export type PostApiNoteErrors = {
    /**
     * Bad Request
     */
    400: MicrosoftAspNetCoreMvcProblemDetails;
};

export type PostApiNoteError = PostApiNoteErrors[keyof PostApiNoteErrors];

export type PostApiNoteResponses = {
    /**
     * Created
     */
    201: EthosWorkflowsApiNoteDto;
};

export type PostApiNoteResponse = PostApiNoteResponses[keyof PostApiNoteResponses];

export type PostApiNoteDraftData = {
    body?: {
        [key: string]: SystemTextJsonNodesJsonNode;
    };
    path?: never;
    query?: {
        entityId?: string;
    };
    url: '/api/Note/draft';
};

export type PostApiNoteDraftErrors = {
    /**
     * Bad Request
     */
    400: MicrosoftAspNetCoreMvcProblemDetails;
};

export type PostApiNoteDraftError = PostApiNoteDraftErrors[keyof PostApiNoteDraftErrors];

export type PostApiNoteDraftResponses = {
    /**
     * Created
     */
    201: EthosWorkflowsApiValidatedDraftDto;
};

export type PostApiNoteDraftResponse = PostApiNoteDraftResponses[keyof PostApiNoteDraftResponses];

export type GetApiNoteDraftByEntityIdData = {
    body?: never;
    path: {
        entityId: string;
    };
    query?: never;
    url: '/api/Note/draft/{entityId}';
};

export type GetApiNoteDraftByEntityIdErrors = {
    /**
     * Not Found
     */
    404: MicrosoftAspNetCoreMvcProblemDetails;
};

export type GetApiNoteDraftByEntityIdError = GetApiNoteDraftByEntityIdErrors[keyof GetApiNoteDraftByEntityIdErrors];

export type GetApiNoteDraftByEntityIdResponses = {
    /**
     * OK
     */
    200: EthosWorkflowsApiDraftDto;
};

export type GetApiNoteDraftByEntityIdResponse = GetApiNoteDraftByEntityIdResponses[keyof GetApiNoteDraftByEntityIdResponses];

export type PutApiNoteDraftByEntityIdData = {
    body?: {
        [key: string]: SystemTextJsonNodesJsonNode;
    };
    path: {
        entityId: string;
    };
    query?: never;
    url: '/api/Note/draft/{entityId}';
};

export type PutApiNoteDraftByEntityIdErrors = {
    /**
     * Bad Request
     */
    400: MicrosoftAspNetCoreMvcProblemDetails;
    /**
     * Not Found
     */
    404: MicrosoftAspNetCoreMvcProblemDetails;
    /**
     * Precondition Failed
     */
    412: MicrosoftAspNetCoreMvcProblemDetails;
};

export type PutApiNoteDraftByEntityIdError = PutApiNoteDraftByEntityIdErrors[keyof PutApiNoteDraftByEntityIdErrors];

export type PutApiNoteDraftByEntityIdResponses = {
    /**
     * OK
     */
    200: EthosWorkflowsApiValidatedDraftDto;
};

export type PutApiNoteDraftByEntityIdResponse = PutApiNoteDraftByEntityIdResponses[keyof PutApiNoteDraftByEntityIdResponses];

export type PostApiNoteDraftByEntityIdCommitData = {
    body?: never;
    path: {
        entityId: string;
    };
    query?: never;
    url: '/api/Note/draft/{entityId}/commit';
};

export type PostApiNoteDraftByEntityIdCommitErrors = {
    /**
     * Bad Request
     */
    400: MicrosoftAspNetCoreMvcProblemDetails;
    /**
     * Not Found
     */
    404: MicrosoftAspNetCoreMvcProblemDetails;
    /**
     * Precondition Failed
     */
    412: MicrosoftAspNetCoreMvcProblemDetails;
};

export type PostApiNoteDraftByEntityIdCommitError = PostApiNoteDraftByEntityIdCommitErrors[keyof PostApiNoteDraftByEntityIdCommitErrors];

export type PostApiNoteDraftByEntityIdCommitResponses = {
    /**
     * OK
     */
    200: EthosWorkflowsApiNoteDto;
    /**
     * Created
     */
    201: EthosWorkflowsApiNoteDto;
};

export type PostApiNoteDraftByEntityIdCommitResponse = PostApiNoteDraftByEntityIdCommitResponses[keyof PostApiNoteDraftByEntityIdCommitResponses];

export type PostApiNoteDraftValidateData = {
    body?: {
        [key: string]: SystemTextJsonNodesJsonNode;
    };
    path?: never;
    query?: never;
    url: '/api/Note/draft/validate';
};

export type PostApiNoteDraftValidateErrors = {
    /**
     * Bad Request
     */
    400: MicrosoftAspNetCoreMvcProblemDetails;
};

export type PostApiNoteDraftValidateError = PostApiNoteDraftValidateErrors[keyof PostApiNoteDraftValidateErrors];

export type PostApiNoteDraftValidateResponses = {
    /**
     * OK
     */
    200: EthosWorkflowsApiValidatedDraftDto;
};

export type PostApiNoteDraftValidateResponse = PostApiNoteDraftValidateResponses[keyof PostApiNoteDraftValidateResponses];

export type PostApiNoteDraftByEntityIdValidateData = {
    body?: never;
    path: {
        entityId: string;
    };
    query?: never;
    url: '/api/Note/draft/{entityId}/validate';
};

export type PostApiNoteDraftByEntityIdValidateErrors = {
    /**
     * Bad Request
     */
    400: MicrosoftAspNetCoreMvcProblemDetails;
    /**
     * Not Found
     */
    404: MicrosoftAspNetCoreMvcProblemDetails;
};

export type PostApiNoteDraftByEntityIdValidateError = PostApiNoteDraftByEntityIdValidateErrors[keyof PostApiNoteDraftByEntityIdValidateErrors];

export type PostApiNoteDraftByEntityIdValidateResponses = {
    /**
     * OK
     */
    200: EthosWorkflowsApiValidatedDraftDto;
};

export type PostApiNoteDraftByEntityIdValidateResponse = PostApiNoteDraftByEntityIdValidateResponses[keyof PostApiNoteDraftByEntityIdValidateResponses];

export type GetApiOrderByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/Order/{id}';
};

export type GetApiOrderByIdErrors = {
    /**
     * Not Found
     */
    404: MicrosoftAspNetCoreMvcProblemDetails;
};

export type GetApiOrderByIdError = GetApiOrderByIdErrors[keyof GetApiOrderByIdErrors];

export type GetApiOrderByIdResponses = {
    /**
     * OK
     */
    200: EthosWorkflowsApiOrderDto;
};

export type GetApiOrderByIdResponse = GetApiOrderByIdResponses[keyof GetApiOrderByIdResponses];

export type PatchApiOrderByIdData = {
    body?: EthosWorkflowsApiCreateOrderDto;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/Order/{id}';
};

export type PatchApiOrderByIdErrors = {
    /**
     * Bad Request
     */
    400: MicrosoftAspNetCoreMvcProblemDetails;
    /**
     * Not Found
     */
    404: MicrosoftAspNetCoreMvcProblemDetails;
    /**
     * Precondition Failed
     */
    412: MicrosoftAspNetCoreMvcProblemDetails;
};

export type PatchApiOrderByIdError = PatchApiOrderByIdErrors[keyof PatchApiOrderByIdErrors];

export type PatchApiOrderByIdResponses = {
    /**
     * OK
     */
    200: EthosWorkflowsApiOrderDto;
};

export type PatchApiOrderByIdResponse = PatchApiOrderByIdResponses[keyof PatchApiOrderByIdResponses];

export type PutApiOrderByIdData = {
    body?: EthosWorkflowsApiCreateOrderDto;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/Order/{id}';
};

export type PutApiOrderByIdErrors = {
    /**
     * Bad Request
     */
    400: MicrosoftAspNetCoreMvcProblemDetails;
    /**
     * Not Found
     */
    404: MicrosoftAspNetCoreMvcProblemDetails;
    /**
     * Precondition Failed
     */
    412: MicrosoftAspNetCoreMvcProblemDetails;
};

export type PutApiOrderByIdError = PutApiOrderByIdErrors[keyof PutApiOrderByIdErrors];

export type PutApiOrderByIdResponses = {
    /**
     * OK
     */
    200: EthosWorkflowsApiOrderDto;
    /**
     * Created
     */
    201: EthosWorkflowsApiOrderDto;
};

export type PutApiOrderByIdResponse = PutApiOrderByIdResponses[keyof PutApiOrderByIdResponses];

export type PostApiOrderSearchData = {
    body?: EthosModelQueryDto1_AllEthosModelOrderQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_LiteralEthosModelOrderQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_NotEthosModelOrderQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_AndEthosModelOrderQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_OrEthosModelOrderQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null;
    path?: never;
    query?: {
        offset?: number;
        limit?: number;
    };
    url: '/api/Order/search';
};

export type PostApiOrderSearchResponses = {
    /**
     * OK
     */
    200: EthosUtilitiesPaginationPagedResponse1EthosWorkflowsApiOrderDto_EthosWorkflows_Version_1000_Culture_neutral_PublicKeyToken_null;
};

export type PostApiOrderSearchResponse = PostApiOrderSearchResponses[keyof PostApiOrderSearchResponses];

export type GetApiOrderData = {
    body?: never;
    path?: never;
    query?: {
        queryBase64?: string;
        offset?: number;
        limit?: number;
    };
    url: '/api/Order';
};

export type GetApiOrderResponses = {
    /**
     * OK
     */
    200: EthosUtilitiesPaginationPagedResponse1EthosWorkflowsApiOrderDto_EthosWorkflows_Version_1000_Culture_neutral_PublicKeyToken_null;
};

export type GetApiOrderResponse = GetApiOrderResponses[keyof GetApiOrderResponses];

export type PostApiOrderData = {
    body?: EthosWorkflowsApiCreateOrderDto;
    path?: never;
    query?: never;
    url: '/api/Order';
};

export type PostApiOrderErrors = {
    /**
     * Bad Request
     */
    400: MicrosoftAspNetCoreMvcProblemDetails;
};

export type PostApiOrderError = PostApiOrderErrors[keyof PostApiOrderErrors];

export type PostApiOrderResponses = {
    /**
     * Created
     */
    201: EthosWorkflowsApiOrderDto;
};

export type PostApiOrderResponse = PostApiOrderResponses[keyof PostApiOrderResponses];

export type PostApiOrderDraftData = {
    body?: {
        [key: string]: SystemTextJsonNodesJsonNode;
    };
    path?: never;
    query?: {
        entityId?: string;
    };
    url: '/api/Order/draft';
};

export type PostApiOrderDraftErrors = {
    /**
     * Bad Request
     */
    400: MicrosoftAspNetCoreMvcProblemDetails;
};

export type PostApiOrderDraftError = PostApiOrderDraftErrors[keyof PostApiOrderDraftErrors];

export type PostApiOrderDraftResponses = {
    /**
     * Created
     */
    201: EthosWorkflowsApiValidatedDraftDto;
};

export type PostApiOrderDraftResponse = PostApiOrderDraftResponses[keyof PostApiOrderDraftResponses];

export type GetApiOrderDraftByEntityIdData = {
    body?: never;
    path: {
        entityId: string;
    };
    query?: never;
    url: '/api/Order/draft/{entityId}';
};

export type GetApiOrderDraftByEntityIdErrors = {
    /**
     * Not Found
     */
    404: MicrosoftAspNetCoreMvcProblemDetails;
};

export type GetApiOrderDraftByEntityIdError = GetApiOrderDraftByEntityIdErrors[keyof GetApiOrderDraftByEntityIdErrors];

export type GetApiOrderDraftByEntityIdResponses = {
    /**
     * OK
     */
    200: EthosWorkflowsApiDraftDto;
};

export type GetApiOrderDraftByEntityIdResponse = GetApiOrderDraftByEntityIdResponses[keyof GetApiOrderDraftByEntityIdResponses];

export type PutApiOrderDraftByEntityIdData = {
    body?: {
        [key: string]: SystemTextJsonNodesJsonNode;
    };
    path: {
        entityId: string;
    };
    query?: never;
    url: '/api/Order/draft/{entityId}';
};

export type PutApiOrderDraftByEntityIdErrors = {
    /**
     * Bad Request
     */
    400: MicrosoftAspNetCoreMvcProblemDetails;
    /**
     * Not Found
     */
    404: MicrosoftAspNetCoreMvcProblemDetails;
    /**
     * Precondition Failed
     */
    412: MicrosoftAspNetCoreMvcProblemDetails;
};

export type PutApiOrderDraftByEntityIdError = PutApiOrderDraftByEntityIdErrors[keyof PutApiOrderDraftByEntityIdErrors];

export type PutApiOrderDraftByEntityIdResponses = {
    /**
     * OK
     */
    200: EthosWorkflowsApiValidatedDraftDto;
};

export type PutApiOrderDraftByEntityIdResponse = PutApiOrderDraftByEntityIdResponses[keyof PutApiOrderDraftByEntityIdResponses];

export type PostApiOrderDraftByEntityIdCommitData = {
    body?: never;
    path: {
        entityId: string;
    };
    query?: never;
    url: '/api/Order/draft/{entityId}/commit';
};

export type PostApiOrderDraftByEntityIdCommitErrors = {
    /**
     * Bad Request
     */
    400: MicrosoftAspNetCoreMvcProblemDetails;
    /**
     * Not Found
     */
    404: MicrosoftAspNetCoreMvcProblemDetails;
    /**
     * Precondition Failed
     */
    412: MicrosoftAspNetCoreMvcProblemDetails;
};

export type PostApiOrderDraftByEntityIdCommitError = PostApiOrderDraftByEntityIdCommitErrors[keyof PostApiOrderDraftByEntityIdCommitErrors];

export type PostApiOrderDraftByEntityIdCommitResponses = {
    /**
     * OK
     */
    200: EthosWorkflowsApiOrderDto;
    /**
     * Created
     */
    201: EthosWorkflowsApiOrderDto;
};

export type PostApiOrderDraftByEntityIdCommitResponse = PostApiOrderDraftByEntityIdCommitResponses[keyof PostApiOrderDraftByEntityIdCommitResponses];

export type PostApiOrderDraftValidateData = {
    body?: {
        [key: string]: SystemTextJsonNodesJsonNode;
    };
    path?: never;
    query?: never;
    url: '/api/Order/draft/validate';
};

export type PostApiOrderDraftValidateErrors = {
    /**
     * Bad Request
     */
    400: MicrosoftAspNetCoreMvcProblemDetails;
};

export type PostApiOrderDraftValidateError = PostApiOrderDraftValidateErrors[keyof PostApiOrderDraftValidateErrors];

export type PostApiOrderDraftValidateResponses = {
    /**
     * OK
     */
    200: EthosWorkflowsApiValidatedDraftDto;
};

export type PostApiOrderDraftValidateResponse = PostApiOrderDraftValidateResponses[keyof PostApiOrderDraftValidateResponses];

export type PostApiOrderDraftByEntityIdValidateData = {
    body?: never;
    path: {
        entityId: string;
    };
    query?: never;
    url: '/api/Order/draft/{entityId}/validate';
};

export type PostApiOrderDraftByEntityIdValidateErrors = {
    /**
     * Bad Request
     */
    400: MicrosoftAspNetCoreMvcProblemDetails;
    /**
     * Not Found
     */
    404: MicrosoftAspNetCoreMvcProblemDetails;
};

export type PostApiOrderDraftByEntityIdValidateError = PostApiOrderDraftByEntityIdValidateErrors[keyof PostApiOrderDraftByEntityIdValidateErrors];

export type PostApiOrderDraftByEntityIdValidateResponses = {
    /**
     * OK
     */
    200: EthosWorkflowsApiValidatedDraftDto;
};

export type PostApiOrderDraftByEntityIdValidateResponse = PostApiOrderDraftByEntityIdValidateResponses[keyof PostApiOrderDraftByEntityIdValidateResponses];

export type GetApiPatientByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/Patient/{id}';
};

export type GetApiPatientByIdErrors = {
    /**
     * Not Found
     */
    404: MicrosoftAspNetCoreMvcProblemDetails;
};

export type GetApiPatientByIdError = GetApiPatientByIdErrors[keyof GetApiPatientByIdErrors];

export type GetApiPatientByIdResponses = {
    /**
     * OK
     */
    200: EthosWorkflowsApiPatientDto;
};

export type GetApiPatientByIdResponse = GetApiPatientByIdResponses[keyof GetApiPatientByIdResponses];

export type PatchApiPatientByIdData = {
    body?: EthosWorkflowsApiCreatePatientInputDto;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/Patient/{id}';
};

export type PatchApiPatientByIdErrors = {
    /**
     * Bad Request
     */
    400: MicrosoftAspNetCoreMvcProblemDetails;
    /**
     * Not Found
     */
    404: MicrosoftAspNetCoreMvcProblemDetails;
    /**
     * Precondition Failed
     */
    412: MicrosoftAspNetCoreMvcProblemDetails;
};

export type PatchApiPatientByIdError = PatchApiPatientByIdErrors[keyof PatchApiPatientByIdErrors];

export type PatchApiPatientByIdResponses = {
    /**
     * OK
     */
    200: EthosWorkflowsApiPatientDto;
};

export type PatchApiPatientByIdResponse = PatchApiPatientByIdResponses[keyof PatchApiPatientByIdResponses];

export type PutApiPatientByIdData = {
    body?: EthosWorkflowsApiCreatePatientInputDto;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/Patient/{id}';
};

export type PutApiPatientByIdErrors = {
    /**
     * Bad Request
     */
    400: MicrosoftAspNetCoreMvcProblemDetails;
    /**
     * Not Found
     */
    404: MicrosoftAspNetCoreMvcProblemDetails;
    /**
     * Precondition Failed
     */
    412: MicrosoftAspNetCoreMvcProblemDetails;
};

export type PutApiPatientByIdError = PutApiPatientByIdErrors[keyof PutApiPatientByIdErrors];

export type PutApiPatientByIdResponses = {
    /**
     * OK
     */
    200: EthosWorkflowsApiPatientDto;
    /**
     * Created
     */
    201: EthosWorkflowsApiPatientDto;
};

export type PutApiPatientByIdResponse = PutApiPatientByIdResponses[keyof PutApiPatientByIdResponses];

export type PostApiPatientSearchData = {
    body?: EthosModelQueryDto1_AllEthosModelPatientQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_LiteralEthosModelPatientQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_NotEthosModelPatientQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_AndEthosModelPatientQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_OrEthosModelPatientQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null;
    path?: never;
    query?: {
        offset?: number;
        limit?: number;
    };
    url: '/api/Patient/search';
};

export type PostApiPatientSearchResponses = {
    /**
     * OK
     */
    200: EthosUtilitiesPaginationPagedResponse1EthosWorkflowsApiPatientDto_EthosWorkflows_Version_1000_Culture_neutral_PublicKeyToken_null;
};

export type PostApiPatientSearchResponse = PostApiPatientSearchResponses[keyof PostApiPatientSearchResponses];

export type GetApiPatientData = {
    body?: never;
    path?: never;
    query?: {
        queryBase64?: string;
        offset?: number;
        limit?: number;
    };
    url: '/api/Patient';
};

export type GetApiPatientResponses = {
    /**
     * OK
     */
    200: EthosUtilitiesPaginationPagedResponse1EthosWorkflowsApiPatientDto_EthosWorkflows_Version_1000_Culture_neutral_PublicKeyToken_null;
};

export type GetApiPatientResponse = GetApiPatientResponses[keyof GetApiPatientResponses];

export type PostApiPatientData = {
    body?: EthosWorkflowsApiCreatePatientInputDto;
    path?: never;
    query?: never;
    url: '/api/Patient';
};

export type PostApiPatientErrors = {
    /**
     * Bad Request
     */
    400: MicrosoftAspNetCoreMvcProblemDetails;
};

export type PostApiPatientError = PostApiPatientErrors[keyof PostApiPatientErrors];

export type PostApiPatientResponses = {
    /**
     * Created
     */
    201: EthosWorkflowsApiPatientDto;
};

export type PostApiPatientResponse = PostApiPatientResponses[keyof PostApiPatientResponses];

export type PostApiPatientDraftData = {
    body?: {
        [key: string]: SystemTextJsonNodesJsonNode;
    };
    path?: never;
    query?: {
        entityId?: string;
    };
    url: '/api/Patient/draft';
};

export type PostApiPatientDraftErrors = {
    /**
     * Bad Request
     */
    400: MicrosoftAspNetCoreMvcProblemDetails;
};

export type PostApiPatientDraftError = PostApiPatientDraftErrors[keyof PostApiPatientDraftErrors];

export type PostApiPatientDraftResponses = {
    /**
     * Created
     */
    201: EthosWorkflowsApiValidatedDraftDto;
};

export type PostApiPatientDraftResponse = PostApiPatientDraftResponses[keyof PostApiPatientDraftResponses];

export type GetApiPatientDraftByEntityIdData = {
    body?: never;
    path: {
        entityId: string;
    };
    query?: never;
    url: '/api/Patient/draft/{entityId}';
};

export type GetApiPatientDraftByEntityIdErrors = {
    /**
     * Not Found
     */
    404: MicrosoftAspNetCoreMvcProblemDetails;
};

export type GetApiPatientDraftByEntityIdError = GetApiPatientDraftByEntityIdErrors[keyof GetApiPatientDraftByEntityIdErrors];

export type GetApiPatientDraftByEntityIdResponses = {
    /**
     * OK
     */
    200: EthosWorkflowsApiDraftDto;
};

export type GetApiPatientDraftByEntityIdResponse = GetApiPatientDraftByEntityIdResponses[keyof GetApiPatientDraftByEntityIdResponses];

export type PutApiPatientDraftByEntityIdData = {
    body?: {
        [key: string]: SystemTextJsonNodesJsonNode;
    };
    path: {
        entityId: string;
    };
    query?: never;
    url: '/api/Patient/draft/{entityId}';
};

export type PutApiPatientDraftByEntityIdErrors = {
    /**
     * Bad Request
     */
    400: MicrosoftAspNetCoreMvcProblemDetails;
    /**
     * Not Found
     */
    404: MicrosoftAspNetCoreMvcProblemDetails;
    /**
     * Precondition Failed
     */
    412: MicrosoftAspNetCoreMvcProblemDetails;
};

export type PutApiPatientDraftByEntityIdError = PutApiPatientDraftByEntityIdErrors[keyof PutApiPatientDraftByEntityIdErrors];

export type PutApiPatientDraftByEntityIdResponses = {
    /**
     * OK
     */
    200: EthosWorkflowsApiValidatedDraftDto;
};

export type PutApiPatientDraftByEntityIdResponse = PutApiPatientDraftByEntityIdResponses[keyof PutApiPatientDraftByEntityIdResponses];

export type PostApiPatientDraftByEntityIdCommitData = {
    body?: never;
    path: {
        entityId: string;
    };
    query?: never;
    url: '/api/Patient/draft/{entityId}/commit';
};

export type PostApiPatientDraftByEntityIdCommitErrors = {
    /**
     * Bad Request
     */
    400: MicrosoftAspNetCoreMvcProblemDetails;
    /**
     * Not Found
     */
    404: MicrosoftAspNetCoreMvcProblemDetails;
    /**
     * Precondition Failed
     */
    412: MicrosoftAspNetCoreMvcProblemDetails;
};

export type PostApiPatientDraftByEntityIdCommitError = PostApiPatientDraftByEntityIdCommitErrors[keyof PostApiPatientDraftByEntityIdCommitErrors];

export type PostApiPatientDraftByEntityIdCommitResponses = {
    /**
     * OK
     */
    200: EthosWorkflowsApiPatientDto;
    /**
     * Created
     */
    201: EthosWorkflowsApiPatientDto;
};

export type PostApiPatientDraftByEntityIdCommitResponse = PostApiPatientDraftByEntityIdCommitResponses[keyof PostApiPatientDraftByEntityIdCommitResponses];

export type PostApiPatientDraftValidateData = {
    body?: {
        [key: string]: SystemTextJsonNodesJsonNode;
    };
    path?: never;
    query?: never;
    url: '/api/Patient/draft/validate';
};

export type PostApiPatientDraftValidateErrors = {
    /**
     * Bad Request
     */
    400: MicrosoftAspNetCoreMvcProblemDetails;
};

export type PostApiPatientDraftValidateError = PostApiPatientDraftValidateErrors[keyof PostApiPatientDraftValidateErrors];

export type PostApiPatientDraftValidateResponses = {
    /**
     * OK
     */
    200: EthosWorkflowsApiValidatedDraftDto;
};

export type PostApiPatientDraftValidateResponse = PostApiPatientDraftValidateResponses[keyof PostApiPatientDraftValidateResponses];

export type PostApiPatientDraftByEntityIdValidateData = {
    body?: never;
    path: {
        entityId: string;
    };
    query?: never;
    url: '/api/Patient/draft/{entityId}/validate';
};

export type PostApiPatientDraftByEntityIdValidateErrors = {
    /**
     * Bad Request
     */
    400: MicrosoftAspNetCoreMvcProblemDetails;
    /**
     * Not Found
     */
    404: MicrosoftAspNetCoreMvcProblemDetails;
};

export type PostApiPatientDraftByEntityIdValidateError = PostApiPatientDraftByEntityIdValidateErrors[keyof PostApiPatientDraftByEntityIdValidateErrors];

export type PostApiPatientDraftByEntityIdValidateResponses = {
    /**
     * OK
     */
    200: EthosWorkflowsApiValidatedDraftDto;
};

export type PostApiPatientDraftByEntityIdValidateResponse = PostApiPatientDraftByEntityIdValidateResponses[keyof PostApiPatientDraftByEntityIdValidateResponses];

export type GetApiPatientAppointmentByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/PatientAppointment/{id}';
};

export type GetApiPatientAppointmentByIdErrors = {
    /**
     * Not Found
     */
    404: MicrosoftAspNetCoreMvcProblemDetails;
};

export type GetApiPatientAppointmentByIdError = GetApiPatientAppointmentByIdErrors[keyof GetApiPatientAppointmentByIdErrors];

export type GetApiPatientAppointmentByIdResponses = {
    /**
     * OK
     */
    200: EthosWorkflowsApiPatientAppointmentDto;
};

export type GetApiPatientAppointmentByIdResponse = GetApiPatientAppointmentByIdResponses[keyof GetApiPatientAppointmentByIdResponses];

export type PatchApiPatientAppointmentByIdData = {
    body?: EthosWorkflowsApiCreatePatientAppointmentDto;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/PatientAppointment/{id}';
};

export type PatchApiPatientAppointmentByIdErrors = {
    /**
     * Bad Request
     */
    400: MicrosoftAspNetCoreMvcProblemDetails;
    /**
     * Not Found
     */
    404: MicrosoftAspNetCoreMvcProblemDetails;
    /**
     * Precondition Failed
     */
    412: MicrosoftAspNetCoreMvcProblemDetails;
};

export type PatchApiPatientAppointmentByIdError = PatchApiPatientAppointmentByIdErrors[keyof PatchApiPatientAppointmentByIdErrors];

export type PatchApiPatientAppointmentByIdResponses = {
    /**
     * OK
     */
    200: EthosWorkflowsApiPatientAppointmentDto;
};

export type PatchApiPatientAppointmentByIdResponse = PatchApiPatientAppointmentByIdResponses[keyof PatchApiPatientAppointmentByIdResponses];

export type PutApiPatientAppointmentByIdData = {
    body?: EthosWorkflowsApiCreatePatientAppointmentDto;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/PatientAppointment/{id}';
};

export type PutApiPatientAppointmentByIdErrors = {
    /**
     * Bad Request
     */
    400: MicrosoftAspNetCoreMvcProblemDetails;
    /**
     * Not Found
     */
    404: MicrosoftAspNetCoreMvcProblemDetails;
    /**
     * Precondition Failed
     */
    412: MicrosoftAspNetCoreMvcProblemDetails;
};

export type PutApiPatientAppointmentByIdError = PutApiPatientAppointmentByIdErrors[keyof PutApiPatientAppointmentByIdErrors];

export type PutApiPatientAppointmentByIdResponses = {
    /**
     * OK
     */
    200: EthosWorkflowsApiPatientAppointmentDto;
    /**
     * Created
     */
    201: EthosWorkflowsApiPatientAppointmentDto;
};

export type PutApiPatientAppointmentByIdResponse = PutApiPatientAppointmentByIdResponses[keyof PutApiPatientAppointmentByIdResponses];

export type PostApiPatientAppointmentSearchData = {
    body?: EthosModelQueryDto1_AllEthosModelPatientAppointmentQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_LiteralEthosModelPatientAppointmentQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_NotEthosModelPatientAppointmentQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_AndEthosModelPatientAppointmentQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_OrEthosModelPatientAppointmentQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null;
    path?: never;
    query?: {
        offset?: number;
        limit?: number;
    };
    url: '/api/PatientAppointment/search';
};

export type PostApiPatientAppointmentSearchResponses = {
    /**
     * OK
     */
    200: EthosUtilitiesPaginationPagedResponse1EthosWorkflowsApiPatientAppointmentDto_EthosWorkflows_Version_1000_Culture_neutral_PublicKeyToken_null;
};

export type PostApiPatientAppointmentSearchResponse = PostApiPatientAppointmentSearchResponses[keyof PostApiPatientAppointmentSearchResponses];

export type GetApiPatientAppointmentData = {
    body?: never;
    path?: never;
    query?: {
        queryBase64?: string;
        offset?: number;
        limit?: number;
    };
    url: '/api/PatientAppointment';
};

export type GetApiPatientAppointmentResponses = {
    /**
     * OK
     */
    200: EthosUtilitiesPaginationPagedResponse1EthosWorkflowsApiPatientAppointmentDto_EthosWorkflows_Version_1000_Culture_neutral_PublicKeyToken_null;
};

export type GetApiPatientAppointmentResponse = GetApiPatientAppointmentResponses[keyof GetApiPatientAppointmentResponses];

export type PostApiPatientAppointmentData = {
    body?: EthosWorkflowsApiCreatePatientAppointmentDto;
    path?: never;
    query?: never;
    url: '/api/PatientAppointment';
};

export type PostApiPatientAppointmentErrors = {
    /**
     * Bad Request
     */
    400: MicrosoftAspNetCoreMvcProblemDetails;
};

export type PostApiPatientAppointmentError = PostApiPatientAppointmentErrors[keyof PostApiPatientAppointmentErrors];

export type PostApiPatientAppointmentResponses = {
    /**
     * Created
     */
    201: EthosWorkflowsApiPatientAppointmentDto;
};

export type PostApiPatientAppointmentResponse = PostApiPatientAppointmentResponses[keyof PostApiPatientAppointmentResponses];

export type PostApiPatientAppointmentDraftData = {
    body?: {
        [key: string]: SystemTextJsonNodesJsonNode;
    };
    path?: never;
    query?: {
        entityId?: string;
    };
    url: '/api/PatientAppointment/draft';
};

export type PostApiPatientAppointmentDraftErrors = {
    /**
     * Bad Request
     */
    400: MicrosoftAspNetCoreMvcProblemDetails;
};

export type PostApiPatientAppointmentDraftError = PostApiPatientAppointmentDraftErrors[keyof PostApiPatientAppointmentDraftErrors];

export type PostApiPatientAppointmentDraftResponses = {
    /**
     * Created
     */
    201: EthosWorkflowsApiValidatedDraftDto;
};

export type PostApiPatientAppointmentDraftResponse = PostApiPatientAppointmentDraftResponses[keyof PostApiPatientAppointmentDraftResponses];

export type GetApiPatientAppointmentDraftByEntityIdData = {
    body?: never;
    path: {
        entityId: string;
    };
    query?: never;
    url: '/api/PatientAppointment/draft/{entityId}';
};

export type GetApiPatientAppointmentDraftByEntityIdErrors = {
    /**
     * Not Found
     */
    404: MicrosoftAspNetCoreMvcProblemDetails;
};

export type GetApiPatientAppointmentDraftByEntityIdError = GetApiPatientAppointmentDraftByEntityIdErrors[keyof GetApiPatientAppointmentDraftByEntityIdErrors];

export type GetApiPatientAppointmentDraftByEntityIdResponses = {
    /**
     * OK
     */
    200: EthosWorkflowsApiDraftDto;
};

export type GetApiPatientAppointmentDraftByEntityIdResponse = GetApiPatientAppointmentDraftByEntityIdResponses[keyof GetApiPatientAppointmentDraftByEntityIdResponses];

export type PutApiPatientAppointmentDraftByEntityIdData = {
    body?: {
        [key: string]: SystemTextJsonNodesJsonNode;
    };
    path: {
        entityId: string;
    };
    query?: never;
    url: '/api/PatientAppointment/draft/{entityId}';
};

export type PutApiPatientAppointmentDraftByEntityIdErrors = {
    /**
     * Bad Request
     */
    400: MicrosoftAspNetCoreMvcProblemDetails;
    /**
     * Not Found
     */
    404: MicrosoftAspNetCoreMvcProblemDetails;
    /**
     * Precondition Failed
     */
    412: MicrosoftAspNetCoreMvcProblemDetails;
};

export type PutApiPatientAppointmentDraftByEntityIdError = PutApiPatientAppointmentDraftByEntityIdErrors[keyof PutApiPatientAppointmentDraftByEntityIdErrors];

export type PutApiPatientAppointmentDraftByEntityIdResponses = {
    /**
     * OK
     */
    200: EthosWorkflowsApiValidatedDraftDto;
};

export type PutApiPatientAppointmentDraftByEntityIdResponse = PutApiPatientAppointmentDraftByEntityIdResponses[keyof PutApiPatientAppointmentDraftByEntityIdResponses];

export type PostApiPatientAppointmentDraftByEntityIdCommitData = {
    body?: never;
    path: {
        entityId: string;
    };
    query?: never;
    url: '/api/PatientAppointment/draft/{entityId}/commit';
};

export type PostApiPatientAppointmentDraftByEntityIdCommitErrors = {
    /**
     * Bad Request
     */
    400: MicrosoftAspNetCoreMvcProblemDetails;
    /**
     * Not Found
     */
    404: MicrosoftAspNetCoreMvcProblemDetails;
    /**
     * Precondition Failed
     */
    412: MicrosoftAspNetCoreMvcProblemDetails;
};

export type PostApiPatientAppointmentDraftByEntityIdCommitError = PostApiPatientAppointmentDraftByEntityIdCommitErrors[keyof PostApiPatientAppointmentDraftByEntityIdCommitErrors];

export type PostApiPatientAppointmentDraftByEntityIdCommitResponses = {
    /**
     * OK
     */
    200: EthosWorkflowsApiPatientAppointmentDto;
    /**
     * Created
     */
    201: EthosWorkflowsApiPatientAppointmentDto;
};

export type PostApiPatientAppointmentDraftByEntityIdCommitResponse = PostApiPatientAppointmentDraftByEntityIdCommitResponses[keyof PostApiPatientAppointmentDraftByEntityIdCommitResponses];

export type PostApiPatientAppointmentDraftValidateData = {
    body?: {
        [key: string]: SystemTextJsonNodesJsonNode;
    };
    path?: never;
    query?: never;
    url: '/api/PatientAppointment/draft/validate';
};

export type PostApiPatientAppointmentDraftValidateErrors = {
    /**
     * Bad Request
     */
    400: MicrosoftAspNetCoreMvcProblemDetails;
};

export type PostApiPatientAppointmentDraftValidateError = PostApiPatientAppointmentDraftValidateErrors[keyof PostApiPatientAppointmentDraftValidateErrors];

export type PostApiPatientAppointmentDraftValidateResponses = {
    /**
     * OK
     */
    200: EthosWorkflowsApiValidatedDraftDto;
};

export type PostApiPatientAppointmentDraftValidateResponse = PostApiPatientAppointmentDraftValidateResponses[keyof PostApiPatientAppointmentDraftValidateResponses];

export type PostApiPatientAppointmentDraftByEntityIdValidateData = {
    body?: never;
    path: {
        entityId: string;
    };
    query?: never;
    url: '/api/PatientAppointment/draft/{entityId}/validate';
};

export type PostApiPatientAppointmentDraftByEntityIdValidateErrors = {
    /**
     * Bad Request
     */
    400: MicrosoftAspNetCoreMvcProblemDetails;
    /**
     * Not Found
     */
    404: MicrosoftAspNetCoreMvcProblemDetails;
};

export type PostApiPatientAppointmentDraftByEntityIdValidateError = PostApiPatientAppointmentDraftByEntityIdValidateErrors[keyof PostApiPatientAppointmentDraftByEntityIdValidateErrors];

export type PostApiPatientAppointmentDraftByEntityIdValidateResponses = {
    /**
     * OK
     */
    200: EthosWorkflowsApiValidatedDraftDto;
};

export type PostApiPatientAppointmentDraftByEntityIdValidateResponse = PostApiPatientAppointmentDraftByEntityIdValidateResponses[keyof PostApiPatientAppointmentDraftByEntityIdValidateResponses];

export type GetApiPatientAppointmentConfirmationByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/PatientAppointmentConfirmation/{id}';
};

export type GetApiPatientAppointmentConfirmationByIdErrors = {
    /**
     * Not Found
     */
    404: MicrosoftAspNetCoreMvcProblemDetails;
};

export type GetApiPatientAppointmentConfirmationByIdError = GetApiPatientAppointmentConfirmationByIdErrors[keyof GetApiPatientAppointmentConfirmationByIdErrors];

export type GetApiPatientAppointmentConfirmationByIdResponses = {
    /**
     * OK
     */
    200: EthosWorkflowsApiPatientAppointmentConfirmationDto;
};

export type GetApiPatientAppointmentConfirmationByIdResponse = GetApiPatientAppointmentConfirmationByIdResponses[keyof GetApiPatientAppointmentConfirmationByIdResponses];

export type PatchApiPatientAppointmentConfirmationByIdData = {
    body?: EthosWorkflowsApiCreatePatientAppointmentConfirmationDto;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/PatientAppointmentConfirmation/{id}';
};

export type PatchApiPatientAppointmentConfirmationByIdErrors = {
    /**
     * Bad Request
     */
    400: MicrosoftAspNetCoreMvcProblemDetails;
    /**
     * Not Found
     */
    404: MicrosoftAspNetCoreMvcProblemDetails;
    /**
     * Precondition Failed
     */
    412: MicrosoftAspNetCoreMvcProblemDetails;
};

export type PatchApiPatientAppointmentConfirmationByIdError = PatchApiPatientAppointmentConfirmationByIdErrors[keyof PatchApiPatientAppointmentConfirmationByIdErrors];

export type PatchApiPatientAppointmentConfirmationByIdResponses = {
    /**
     * OK
     */
    200: EthosWorkflowsApiPatientAppointmentConfirmationDto;
};

export type PatchApiPatientAppointmentConfirmationByIdResponse = PatchApiPatientAppointmentConfirmationByIdResponses[keyof PatchApiPatientAppointmentConfirmationByIdResponses];

export type PutApiPatientAppointmentConfirmationByIdData = {
    body?: EthosWorkflowsApiCreatePatientAppointmentConfirmationDto;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/PatientAppointmentConfirmation/{id}';
};

export type PutApiPatientAppointmentConfirmationByIdErrors = {
    /**
     * Bad Request
     */
    400: MicrosoftAspNetCoreMvcProblemDetails;
    /**
     * Not Found
     */
    404: MicrosoftAspNetCoreMvcProblemDetails;
    /**
     * Precondition Failed
     */
    412: MicrosoftAspNetCoreMvcProblemDetails;
};

export type PutApiPatientAppointmentConfirmationByIdError = PutApiPatientAppointmentConfirmationByIdErrors[keyof PutApiPatientAppointmentConfirmationByIdErrors];

export type PutApiPatientAppointmentConfirmationByIdResponses = {
    /**
     * OK
     */
    200: EthosWorkflowsApiPatientAppointmentConfirmationDto;
    /**
     * Created
     */
    201: EthosWorkflowsApiPatientAppointmentConfirmationDto;
};

export type PutApiPatientAppointmentConfirmationByIdResponse = PutApiPatientAppointmentConfirmationByIdResponses[keyof PutApiPatientAppointmentConfirmationByIdResponses];

export type PostApiPatientAppointmentConfirmationSearchData = {
    body?: EthosModelQueryDto1_AllEthosModelPatientAppointmentConfirmationQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_LiteralEthosModelPatientAppointmentConfirmationQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_NotEthosModelPatientAppointmentConfirmationQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_AndEthosModelPatientAppointmentConfirmationQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_OrEthosModelPatientAppointmentConfirmationQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null;
    path?: never;
    query?: {
        offset?: number;
        limit?: number;
    };
    url: '/api/PatientAppointmentConfirmation/search';
};

export type PostApiPatientAppointmentConfirmationSearchResponses = {
    /**
     * OK
     */
    200: EthosUtilitiesPaginationPagedResponse1EthosWorkflowsApiPatientAppointmentConfirmationDto_EthosWorkflows_Version_1000_Culture_neutral_PublicKeyToken_null;
};

export type PostApiPatientAppointmentConfirmationSearchResponse = PostApiPatientAppointmentConfirmationSearchResponses[keyof PostApiPatientAppointmentConfirmationSearchResponses];

export type GetApiPatientAppointmentConfirmationData = {
    body?: never;
    path?: never;
    query?: {
        queryBase64?: string;
        offset?: number;
        limit?: number;
    };
    url: '/api/PatientAppointmentConfirmation';
};

export type GetApiPatientAppointmentConfirmationResponses = {
    /**
     * OK
     */
    200: EthosUtilitiesPaginationPagedResponse1EthosWorkflowsApiPatientAppointmentConfirmationDto_EthosWorkflows_Version_1000_Culture_neutral_PublicKeyToken_null;
};

export type GetApiPatientAppointmentConfirmationResponse = GetApiPatientAppointmentConfirmationResponses[keyof GetApiPatientAppointmentConfirmationResponses];

export type PostApiPatientAppointmentConfirmationData = {
    body?: EthosWorkflowsApiCreatePatientAppointmentConfirmationDto;
    path?: never;
    query?: never;
    url: '/api/PatientAppointmentConfirmation';
};

export type PostApiPatientAppointmentConfirmationErrors = {
    /**
     * Bad Request
     */
    400: MicrosoftAspNetCoreMvcProblemDetails;
};

export type PostApiPatientAppointmentConfirmationError = PostApiPatientAppointmentConfirmationErrors[keyof PostApiPatientAppointmentConfirmationErrors];

export type PostApiPatientAppointmentConfirmationResponses = {
    /**
     * Created
     */
    201: EthosWorkflowsApiPatientAppointmentConfirmationDto;
};

export type PostApiPatientAppointmentConfirmationResponse = PostApiPatientAppointmentConfirmationResponses[keyof PostApiPatientAppointmentConfirmationResponses];

export type PostApiPatientAppointmentConfirmationDraftData = {
    body?: {
        [key: string]: SystemTextJsonNodesJsonNode;
    };
    path?: never;
    query?: {
        entityId?: string;
    };
    url: '/api/PatientAppointmentConfirmation/draft';
};

export type PostApiPatientAppointmentConfirmationDraftErrors = {
    /**
     * Bad Request
     */
    400: MicrosoftAspNetCoreMvcProblemDetails;
};

export type PostApiPatientAppointmentConfirmationDraftError = PostApiPatientAppointmentConfirmationDraftErrors[keyof PostApiPatientAppointmentConfirmationDraftErrors];

export type PostApiPatientAppointmentConfirmationDraftResponses = {
    /**
     * Created
     */
    201: EthosWorkflowsApiValidatedDraftDto;
};

export type PostApiPatientAppointmentConfirmationDraftResponse = PostApiPatientAppointmentConfirmationDraftResponses[keyof PostApiPatientAppointmentConfirmationDraftResponses];

export type GetApiPatientAppointmentConfirmationDraftByEntityIdData = {
    body?: never;
    path: {
        entityId: string;
    };
    query?: never;
    url: '/api/PatientAppointmentConfirmation/draft/{entityId}';
};

export type GetApiPatientAppointmentConfirmationDraftByEntityIdErrors = {
    /**
     * Not Found
     */
    404: MicrosoftAspNetCoreMvcProblemDetails;
};

export type GetApiPatientAppointmentConfirmationDraftByEntityIdError = GetApiPatientAppointmentConfirmationDraftByEntityIdErrors[keyof GetApiPatientAppointmentConfirmationDraftByEntityIdErrors];

export type GetApiPatientAppointmentConfirmationDraftByEntityIdResponses = {
    /**
     * OK
     */
    200: EthosWorkflowsApiDraftDto;
};

export type GetApiPatientAppointmentConfirmationDraftByEntityIdResponse = GetApiPatientAppointmentConfirmationDraftByEntityIdResponses[keyof GetApiPatientAppointmentConfirmationDraftByEntityIdResponses];

export type PutApiPatientAppointmentConfirmationDraftByEntityIdData = {
    body?: {
        [key: string]: SystemTextJsonNodesJsonNode;
    };
    path: {
        entityId: string;
    };
    query?: never;
    url: '/api/PatientAppointmentConfirmation/draft/{entityId}';
};

export type PutApiPatientAppointmentConfirmationDraftByEntityIdErrors = {
    /**
     * Bad Request
     */
    400: MicrosoftAspNetCoreMvcProblemDetails;
    /**
     * Not Found
     */
    404: MicrosoftAspNetCoreMvcProblemDetails;
    /**
     * Precondition Failed
     */
    412: MicrosoftAspNetCoreMvcProblemDetails;
};

export type PutApiPatientAppointmentConfirmationDraftByEntityIdError = PutApiPatientAppointmentConfirmationDraftByEntityIdErrors[keyof PutApiPatientAppointmentConfirmationDraftByEntityIdErrors];

export type PutApiPatientAppointmentConfirmationDraftByEntityIdResponses = {
    /**
     * OK
     */
    200: EthosWorkflowsApiValidatedDraftDto;
};

export type PutApiPatientAppointmentConfirmationDraftByEntityIdResponse = PutApiPatientAppointmentConfirmationDraftByEntityIdResponses[keyof PutApiPatientAppointmentConfirmationDraftByEntityIdResponses];

export type PostApiPatientAppointmentConfirmationDraftByEntityIdCommitData = {
    body?: never;
    path: {
        entityId: string;
    };
    query?: never;
    url: '/api/PatientAppointmentConfirmation/draft/{entityId}/commit';
};

export type PostApiPatientAppointmentConfirmationDraftByEntityIdCommitErrors = {
    /**
     * Bad Request
     */
    400: MicrosoftAspNetCoreMvcProblemDetails;
    /**
     * Not Found
     */
    404: MicrosoftAspNetCoreMvcProblemDetails;
    /**
     * Precondition Failed
     */
    412: MicrosoftAspNetCoreMvcProblemDetails;
};

export type PostApiPatientAppointmentConfirmationDraftByEntityIdCommitError = PostApiPatientAppointmentConfirmationDraftByEntityIdCommitErrors[keyof PostApiPatientAppointmentConfirmationDraftByEntityIdCommitErrors];

export type PostApiPatientAppointmentConfirmationDraftByEntityIdCommitResponses = {
    /**
     * OK
     */
    200: EthosWorkflowsApiPatientAppointmentConfirmationDto;
    /**
     * Created
     */
    201: EthosWorkflowsApiPatientAppointmentConfirmationDto;
};

export type PostApiPatientAppointmentConfirmationDraftByEntityIdCommitResponse = PostApiPatientAppointmentConfirmationDraftByEntityIdCommitResponses[keyof PostApiPatientAppointmentConfirmationDraftByEntityIdCommitResponses];

export type PostApiPatientAppointmentConfirmationDraftValidateData = {
    body?: {
        [key: string]: SystemTextJsonNodesJsonNode;
    };
    path?: never;
    query?: never;
    url: '/api/PatientAppointmentConfirmation/draft/validate';
};

export type PostApiPatientAppointmentConfirmationDraftValidateErrors = {
    /**
     * Bad Request
     */
    400: MicrosoftAspNetCoreMvcProblemDetails;
};

export type PostApiPatientAppointmentConfirmationDraftValidateError = PostApiPatientAppointmentConfirmationDraftValidateErrors[keyof PostApiPatientAppointmentConfirmationDraftValidateErrors];

export type PostApiPatientAppointmentConfirmationDraftValidateResponses = {
    /**
     * OK
     */
    200: EthosWorkflowsApiValidatedDraftDto;
};

export type PostApiPatientAppointmentConfirmationDraftValidateResponse = PostApiPatientAppointmentConfirmationDraftValidateResponses[keyof PostApiPatientAppointmentConfirmationDraftValidateResponses];

export type PostApiPatientAppointmentConfirmationDraftByEntityIdValidateData = {
    body?: never;
    path: {
        entityId: string;
    };
    query?: never;
    url: '/api/PatientAppointmentConfirmation/draft/{entityId}/validate';
};

export type PostApiPatientAppointmentConfirmationDraftByEntityIdValidateErrors = {
    /**
     * Bad Request
     */
    400: MicrosoftAspNetCoreMvcProblemDetails;
    /**
     * Not Found
     */
    404: MicrosoftAspNetCoreMvcProblemDetails;
};

export type PostApiPatientAppointmentConfirmationDraftByEntityIdValidateError = PostApiPatientAppointmentConfirmationDraftByEntityIdValidateErrors[keyof PostApiPatientAppointmentConfirmationDraftByEntityIdValidateErrors];

export type PostApiPatientAppointmentConfirmationDraftByEntityIdValidateResponses = {
    /**
     * OK
     */
    200: EthosWorkflowsApiValidatedDraftDto;
};

export type PostApiPatientAppointmentConfirmationDraftByEntityIdValidateResponse = PostApiPatientAppointmentConfirmationDraftByEntityIdValidateResponses[keyof PostApiPatientAppointmentConfirmationDraftByEntityIdValidateResponses];

export type GetApiPhysicianByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/Physician/{id}';
};

export type GetApiPhysicianByIdErrors = {
    /**
     * Not Found
     */
    404: MicrosoftAspNetCoreMvcProblemDetails;
};

export type GetApiPhysicianByIdError = GetApiPhysicianByIdErrors[keyof GetApiPhysicianByIdErrors];

export type GetApiPhysicianByIdResponses = {
    /**
     * OK
     */
    200: EthosWorkflowsApiPhysicianDto;
};

export type GetApiPhysicianByIdResponse = GetApiPhysicianByIdResponses[keyof GetApiPhysicianByIdResponses];

export type PatchApiPhysicianByIdData = {
    body?: EthosWorkflowsApiCreatePhysicianDto;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/Physician/{id}';
};

export type PatchApiPhysicianByIdErrors = {
    /**
     * Bad Request
     */
    400: MicrosoftAspNetCoreMvcProblemDetails;
    /**
     * Not Found
     */
    404: MicrosoftAspNetCoreMvcProblemDetails;
    /**
     * Precondition Failed
     */
    412: MicrosoftAspNetCoreMvcProblemDetails;
};

export type PatchApiPhysicianByIdError = PatchApiPhysicianByIdErrors[keyof PatchApiPhysicianByIdErrors];

export type PatchApiPhysicianByIdResponses = {
    /**
     * OK
     */
    200: EthosWorkflowsApiPhysicianDto;
};

export type PatchApiPhysicianByIdResponse = PatchApiPhysicianByIdResponses[keyof PatchApiPhysicianByIdResponses];

export type PutApiPhysicianByIdData = {
    body?: EthosWorkflowsApiCreatePhysicianDto;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/Physician/{id}';
};

export type PutApiPhysicianByIdErrors = {
    /**
     * Bad Request
     */
    400: MicrosoftAspNetCoreMvcProblemDetails;
    /**
     * Not Found
     */
    404: MicrosoftAspNetCoreMvcProblemDetails;
    /**
     * Precondition Failed
     */
    412: MicrosoftAspNetCoreMvcProblemDetails;
};

export type PutApiPhysicianByIdError = PutApiPhysicianByIdErrors[keyof PutApiPhysicianByIdErrors];

export type PutApiPhysicianByIdResponses = {
    /**
     * OK
     */
    200: EthosWorkflowsApiPhysicianDto;
    /**
     * Created
     */
    201: EthosWorkflowsApiPhysicianDto;
};

export type PutApiPhysicianByIdResponse = PutApiPhysicianByIdResponses[keyof PutApiPhysicianByIdResponses];

export type PostApiPhysicianSearchData = {
    body?: EthosModelQueryDto1_AllEthosModelPhysicianQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_LiteralEthosModelPhysicianQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_NotEthosModelPhysicianQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_AndEthosModelPhysicianQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_OrEthosModelPhysicianQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null;
    path?: never;
    query?: {
        offset?: number;
        limit?: number;
    };
    url: '/api/Physician/search';
};

export type PostApiPhysicianSearchResponses = {
    /**
     * OK
     */
    200: EthosUtilitiesPaginationPagedResponse1EthosWorkflowsApiPhysicianDto_EthosWorkflows_Version_1000_Culture_neutral_PublicKeyToken_null;
};

export type PostApiPhysicianSearchResponse = PostApiPhysicianSearchResponses[keyof PostApiPhysicianSearchResponses];

export type GetApiPhysicianData = {
    body?: never;
    path?: never;
    query?: {
        queryBase64?: string;
        offset?: number;
        limit?: number;
    };
    url: '/api/Physician';
};

export type GetApiPhysicianResponses = {
    /**
     * OK
     */
    200: EthosUtilitiesPaginationPagedResponse1EthosWorkflowsApiPhysicianDto_EthosWorkflows_Version_1000_Culture_neutral_PublicKeyToken_null;
};

export type GetApiPhysicianResponse = GetApiPhysicianResponses[keyof GetApiPhysicianResponses];

export type PostApiPhysicianData = {
    body?: EthosWorkflowsApiCreatePhysicianDto;
    path?: never;
    query?: never;
    url: '/api/Physician';
};

export type PostApiPhysicianErrors = {
    /**
     * Bad Request
     */
    400: MicrosoftAspNetCoreMvcProblemDetails;
};

export type PostApiPhysicianError = PostApiPhysicianErrors[keyof PostApiPhysicianErrors];

export type PostApiPhysicianResponses = {
    /**
     * Created
     */
    201: EthosWorkflowsApiPhysicianDto;
};

export type PostApiPhysicianResponse = PostApiPhysicianResponses[keyof PostApiPhysicianResponses];

export type PostApiPhysicianDraftData = {
    body?: {
        [key: string]: SystemTextJsonNodesJsonNode;
    };
    path?: never;
    query?: {
        entityId?: string;
    };
    url: '/api/Physician/draft';
};

export type PostApiPhysicianDraftErrors = {
    /**
     * Bad Request
     */
    400: MicrosoftAspNetCoreMvcProblemDetails;
};

export type PostApiPhysicianDraftError = PostApiPhysicianDraftErrors[keyof PostApiPhysicianDraftErrors];

export type PostApiPhysicianDraftResponses = {
    /**
     * Created
     */
    201: EthosWorkflowsApiValidatedDraftDto;
};

export type PostApiPhysicianDraftResponse = PostApiPhysicianDraftResponses[keyof PostApiPhysicianDraftResponses];

export type GetApiPhysicianDraftByEntityIdData = {
    body?: never;
    path: {
        entityId: string;
    };
    query?: never;
    url: '/api/Physician/draft/{entityId}';
};

export type GetApiPhysicianDraftByEntityIdErrors = {
    /**
     * Not Found
     */
    404: MicrosoftAspNetCoreMvcProblemDetails;
};

export type GetApiPhysicianDraftByEntityIdError = GetApiPhysicianDraftByEntityIdErrors[keyof GetApiPhysicianDraftByEntityIdErrors];

export type GetApiPhysicianDraftByEntityIdResponses = {
    /**
     * OK
     */
    200: EthosWorkflowsApiDraftDto;
};

export type GetApiPhysicianDraftByEntityIdResponse = GetApiPhysicianDraftByEntityIdResponses[keyof GetApiPhysicianDraftByEntityIdResponses];

export type PutApiPhysicianDraftByEntityIdData = {
    body?: {
        [key: string]: SystemTextJsonNodesJsonNode;
    };
    path: {
        entityId: string;
    };
    query?: never;
    url: '/api/Physician/draft/{entityId}';
};

export type PutApiPhysicianDraftByEntityIdErrors = {
    /**
     * Bad Request
     */
    400: MicrosoftAspNetCoreMvcProblemDetails;
    /**
     * Not Found
     */
    404: MicrosoftAspNetCoreMvcProblemDetails;
    /**
     * Precondition Failed
     */
    412: MicrosoftAspNetCoreMvcProblemDetails;
};

export type PutApiPhysicianDraftByEntityIdError = PutApiPhysicianDraftByEntityIdErrors[keyof PutApiPhysicianDraftByEntityIdErrors];

export type PutApiPhysicianDraftByEntityIdResponses = {
    /**
     * OK
     */
    200: EthosWorkflowsApiValidatedDraftDto;
};

export type PutApiPhysicianDraftByEntityIdResponse = PutApiPhysicianDraftByEntityIdResponses[keyof PutApiPhysicianDraftByEntityIdResponses];

export type PostApiPhysicianDraftByEntityIdCommitData = {
    body?: never;
    path: {
        entityId: string;
    };
    query?: never;
    url: '/api/Physician/draft/{entityId}/commit';
};

export type PostApiPhysicianDraftByEntityIdCommitErrors = {
    /**
     * Bad Request
     */
    400: MicrosoftAspNetCoreMvcProblemDetails;
    /**
     * Not Found
     */
    404: MicrosoftAspNetCoreMvcProblemDetails;
    /**
     * Precondition Failed
     */
    412: MicrosoftAspNetCoreMvcProblemDetails;
};

export type PostApiPhysicianDraftByEntityIdCommitError = PostApiPhysicianDraftByEntityIdCommitErrors[keyof PostApiPhysicianDraftByEntityIdCommitErrors];

export type PostApiPhysicianDraftByEntityIdCommitResponses = {
    /**
     * OK
     */
    200: EthosWorkflowsApiPhysicianDto;
    /**
     * Created
     */
    201: EthosWorkflowsApiPhysicianDto;
};

export type PostApiPhysicianDraftByEntityIdCommitResponse = PostApiPhysicianDraftByEntityIdCommitResponses[keyof PostApiPhysicianDraftByEntityIdCommitResponses];

export type PostApiPhysicianDraftValidateData = {
    body?: {
        [key: string]: SystemTextJsonNodesJsonNode;
    };
    path?: never;
    query?: never;
    url: '/api/Physician/draft/validate';
};

export type PostApiPhysicianDraftValidateErrors = {
    /**
     * Bad Request
     */
    400: MicrosoftAspNetCoreMvcProblemDetails;
};

export type PostApiPhysicianDraftValidateError = PostApiPhysicianDraftValidateErrors[keyof PostApiPhysicianDraftValidateErrors];

export type PostApiPhysicianDraftValidateResponses = {
    /**
     * OK
     */
    200: EthosWorkflowsApiValidatedDraftDto;
};

export type PostApiPhysicianDraftValidateResponse = PostApiPhysicianDraftValidateResponses[keyof PostApiPhysicianDraftValidateResponses];

export type PostApiPhysicianDraftByEntityIdValidateData = {
    body?: never;
    path: {
        entityId: string;
    };
    query?: never;
    url: '/api/Physician/draft/{entityId}/validate';
};

export type PostApiPhysicianDraftByEntityIdValidateErrors = {
    /**
     * Bad Request
     */
    400: MicrosoftAspNetCoreMvcProblemDetails;
    /**
     * Not Found
     */
    404: MicrosoftAspNetCoreMvcProblemDetails;
};

export type PostApiPhysicianDraftByEntityIdValidateError = PostApiPhysicianDraftByEntityIdValidateErrors[keyof PostApiPhysicianDraftByEntityIdValidateErrors];

export type PostApiPhysicianDraftByEntityIdValidateResponses = {
    /**
     * OK
     */
    200: EthosWorkflowsApiValidatedDraftDto;
};

export type PostApiPhysicianDraftByEntityIdValidateResponse = PostApiPhysicianDraftByEntityIdValidateResponses[keyof PostApiPhysicianDraftByEntityIdValidateResponses];

export type GetApiProviderByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/Provider/{id}';
};

export type GetApiProviderByIdErrors = {
    /**
     * Not Found
     */
    404: MicrosoftAspNetCoreMvcProblemDetails;
};

export type GetApiProviderByIdError = GetApiProviderByIdErrors[keyof GetApiProviderByIdErrors];

export type GetApiProviderByIdResponses = {
    /**
     * OK
     */
    200: EthosWorkflowsApiProviderDto;
};

export type GetApiProviderByIdResponse = GetApiProviderByIdResponses[keyof GetApiProviderByIdResponses];

export type PatchApiProviderByIdData = {
    body?: EthosWorkflowsApiCreateProviderDto;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/Provider/{id}';
};

export type PatchApiProviderByIdErrors = {
    /**
     * Bad Request
     */
    400: MicrosoftAspNetCoreMvcProblemDetails;
    /**
     * Not Found
     */
    404: MicrosoftAspNetCoreMvcProblemDetails;
    /**
     * Precondition Failed
     */
    412: MicrosoftAspNetCoreMvcProblemDetails;
};

export type PatchApiProviderByIdError = PatchApiProviderByIdErrors[keyof PatchApiProviderByIdErrors];

export type PatchApiProviderByIdResponses = {
    /**
     * OK
     */
    200: EthosWorkflowsApiProviderDto;
};

export type PatchApiProviderByIdResponse = PatchApiProviderByIdResponses[keyof PatchApiProviderByIdResponses];

export type PutApiProviderByIdData = {
    body?: EthosWorkflowsApiCreateProviderDto;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/Provider/{id}';
};

export type PutApiProviderByIdErrors = {
    /**
     * Bad Request
     */
    400: MicrosoftAspNetCoreMvcProblemDetails;
    /**
     * Not Found
     */
    404: MicrosoftAspNetCoreMvcProblemDetails;
    /**
     * Precondition Failed
     */
    412: MicrosoftAspNetCoreMvcProblemDetails;
};

export type PutApiProviderByIdError = PutApiProviderByIdErrors[keyof PutApiProviderByIdErrors];

export type PutApiProviderByIdResponses = {
    /**
     * OK
     */
    200: EthosWorkflowsApiProviderDto;
    /**
     * Created
     */
    201: EthosWorkflowsApiProviderDto;
};

export type PutApiProviderByIdResponse = PutApiProviderByIdResponses[keyof PutApiProviderByIdResponses];

export type PostApiProviderSearchData = {
    body?: EthosModelQueryDto1_AllEthosModelProviderQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_LiteralEthosModelProviderQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_NotEthosModelProviderQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_AndEthosModelProviderQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_OrEthosModelProviderQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null;
    path?: never;
    query?: {
        offset?: number;
        limit?: number;
    };
    url: '/api/Provider/search';
};

export type PostApiProviderSearchResponses = {
    /**
     * OK
     */
    200: EthosUtilitiesPaginationPagedResponse1EthosWorkflowsApiProviderDto_EthosWorkflows_Version_1000_Culture_neutral_PublicKeyToken_null;
};

export type PostApiProviderSearchResponse = PostApiProviderSearchResponses[keyof PostApiProviderSearchResponses];

export type GetApiProviderData = {
    body?: never;
    path?: never;
    query?: {
        queryBase64?: string;
        offset?: number;
        limit?: number;
    };
    url: '/api/Provider';
};

export type GetApiProviderResponses = {
    /**
     * OK
     */
    200: EthosUtilitiesPaginationPagedResponse1EthosWorkflowsApiProviderDto_EthosWorkflows_Version_1000_Culture_neutral_PublicKeyToken_null;
};

export type GetApiProviderResponse = GetApiProviderResponses[keyof GetApiProviderResponses];

export type PostApiProviderData = {
    body?: EthosWorkflowsApiCreateProviderDto;
    path?: never;
    query?: never;
    url: '/api/Provider';
};

export type PostApiProviderErrors = {
    /**
     * Bad Request
     */
    400: MicrosoftAspNetCoreMvcProblemDetails;
};

export type PostApiProviderError = PostApiProviderErrors[keyof PostApiProviderErrors];

export type PostApiProviderResponses = {
    /**
     * Created
     */
    201: EthosWorkflowsApiProviderDto;
};

export type PostApiProviderResponse = PostApiProviderResponses[keyof PostApiProviderResponses];

export type PostApiProviderDraftData = {
    body?: {
        [key: string]: SystemTextJsonNodesJsonNode;
    };
    path?: never;
    query?: {
        entityId?: string;
    };
    url: '/api/Provider/draft';
};

export type PostApiProviderDraftErrors = {
    /**
     * Bad Request
     */
    400: MicrosoftAspNetCoreMvcProblemDetails;
};

export type PostApiProviderDraftError = PostApiProviderDraftErrors[keyof PostApiProviderDraftErrors];

export type PostApiProviderDraftResponses = {
    /**
     * Created
     */
    201: EthosWorkflowsApiValidatedDraftDto;
};

export type PostApiProviderDraftResponse = PostApiProviderDraftResponses[keyof PostApiProviderDraftResponses];

export type GetApiProviderDraftByEntityIdData = {
    body?: never;
    path: {
        entityId: string;
    };
    query?: never;
    url: '/api/Provider/draft/{entityId}';
};

export type GetApiProviderDraftByEntityIdErrors = {
    /**
     * Not Found
     */
    404: MicrosoftAspNetCoreMvcProblemDetails;
};

export type GetApiProviderDraftByEntityIdError = GetApiProviderDraftByEntityIdErrors[keyof GetApiProviderDraftByEntityIdErrors];

export type GetApiProviderDraftByEntityIdResponses = {
    /**
     * OK
     */
    200: EthosWorkflowsApiDraftDto;
};

export type GetApiProviderDraftByEntityIdResponse = GetApiProviderDraftByEntityIdResponses[keyof GetApiProviderDraftByEntityIdResponses];

export type PutApiProviderDraftByEntityIdData = {
    body?: {
        [key: string]: SystemTextJsonNodesJsonNode;
    };
    path: {
        entityId: string;
    };
    query?: never;
    url: '/api/Provider/draft/{entityId}';
};

export type PutApiProviderDraftByEntityIdErrors = {
    /**
     * Bad Request
     */
    400: MicrosoftAspNetCoreMvcProblemDetails;
    /**
     * Not Found
     */
    404: MicrosoftAspNetCoreMvcProblemDetails;
    /**
     * Precondition Failed
     */
    412: MicrosoftAspNetCoreMvcProblemDetails;
};

export type PutApiProviderDraftByEntityIdError = PutApiProviderDraftByEntityIdErrors[keyof PutApiProviderDraftByEntityIdErrors];

export type PutApiProviderDraftByEntityIdResponses = {
    /**
     * OK
     */
    200: EthosWorkflowsApiValidatedDraftDto;
};

export type PutApiProviderDraftByEntityIdResponse = PutApiProviderDraftByEntityIdResponses[keyof PutApiProviderDraftByEntityIdResponses];

export type PostApiProviderDraftByEntityIdCommitData = {
    body?: never;
    path: {
        entityId: string;
    };
    query?: never;
    url: '/api/Provider/draft/{entityId}/commit';
};

export type PostApiProviderDraftByEntityIdCommitErrors = {
    /**
     * Bad Request
     */
    400: MicrosoftAspNetCoreMvcProblemDetails;
    /**
     * Not Found
     */
    404: MicrosoftAspNetCoreMvcProblemDetails;
    /**
     * Precondition Failed
     */
    412: MicrosoftAspNetCoreMvcProblemDetails;
};

export type PostApiProviderDraftByEntityIdCommitError = PostApiProviderDraftByEntityIdCommitErrors[keyof PostApiProviderDraftByEntityIdCommitErrors];

export type PostApiProviderDraftByEntityIdCommitResponses = {
    /**
     * OK
     */
    200: EthosWorkflowsApiProviderDto;
    /**
     * Created
     */
    201: EthosWorkflowsApiProviderDto;
};

export type PostApiProviderDraftByEntityIdCommitResponse = PostApiProviderDraftByEntityIdCommitResponses[keyof PostApiProviderDraftByEntityIdCommitResponses];

export type PostApiProviderDraftValidateData = {
    body?: {
        [key: string]: SystemTextJsonNodesJsonNode;
    };
    path?: never;
    query?: never;
    url: '/api/Provider/draft/validate';
};

export type PostApiProviderDraftValidateErrors = {
    /**
     * Bad Request
     */
    400: MicrosoftAspNetCoreMvcProblemDetails;
};

export type PostApiProviderDraftValidateError = PostApiProviderDraftValidateErrors[keyof PostApiProviderDraftValidateErrors];

export type PostApiProviderDraftValidateResponses = {
    /**
     * OK
     */
    200: EthosWorkflowsApiValidatedDraftDto;
};

export type PostApiProviderDraftValidateResponse = PostApiProviderDraftValidateResponses[keyof PostApiProviderDraftValidateResponses];

export type PostApiProviderDraftByEntityIdValidateData = {
    body?: never;
    path: {
        entityId: string;
    };
    query?: never;
    url: '/api/Provider/draft/{entityId}/validate';
};

export type PostApiProviderDraftByEntityIdValidateErrors = {
    /**
     * Bad Request
     */
    400: MicrosoftAspNetCoreMvcProblemDetails;
    /**
     * Not Found
     */
    404: MicrosoftAspNetCoreMvcProblemDetails;
};

export type PostApiProviderDraftByEntityIdValidateError = PostApiProviderDraftByEntityIdValidateErrors[keyof PostApiProviderDraftByEntityIdValidateErrors];

export type PostApiProviderDraftByEntityIdValidateResponses = {
    /**
     * OK
     */
    200: EthosWorkflowsApiValidatedDraftDto;
};

export type PostApiProviderDraftByEntityIdValidateResponse = PostApiProviderDraftByEntityIdValidateResponses[keyof PostApiProviderDraftByEntityIdValidateResponses];

export type GetPyapiZipData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/pyapi.zip';
};

export type GetPyapiZipResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetTsapiZipData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/tsapi.zip';
};

export type GetTsapiZipResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetApiRoomByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/Room/{id}';
};

export type GetApiRoomByIdErrors = {
    /**
     * Not Found
     */
    404: MicrosoftAspNetCoreMvcProblemDetails;
};

export type GetApiRoomByIdError = GetApiRoomByIdErrors[keyof GetApiRoomByIdErrors];

export type GetApiRoomByIdResponses = {
    /**
     * OK
     */
    200: EthosWorkflowsApiRoomDto;
};

export type GetApiRoomByIdResponse = GetApiRoomByIdResponses[keyof GetApiRoomByIdResponses];

export type PatchApiRoomByIdData = {
    body?: EthosWorkflowsApiCreateRoomDto;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/Room/{id}';
};

export type PatchApiRoomByIdErrors = {
    /**
     * Bad Request
     */
    400: MicrosoftAspNetCoreMvcProblemDetails;
    /**
     * Not Found
     */
    404: MicrosoftAspNetCoreMvcProblemDetails;
    /**
     * Precondition Failed
     */
    412: MicrosoftAspNetCoreMvcProblemDetails;
};

export type PatchApiRoomByIdError = PatchApiRoomByIdErrors[keyof PatchApiRoomByIdErrors];

export type PatchApiRoomByIdResponses = {
    /**
     * OK
     */
    200: EthosWorkflowsApiRoomDto;
};

export type PatchApiRoomByIdResponse = PatchApiRoomByIdResponses[keyof PatchApiRoomByIdResponses];

export type PutApiRoomByIdData = {
    body?: EthosWorkflowsApiCreateRoomDto;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/Room/{id}';
};

export type PutApiRoomByIdErrors = {
    /**
     * Bad Request
     */
    400: MicrosoftAspNetCoreMvcProblemDetails;
    /**
     * Not Found
     */
    404: MicrosoftAspNetCoreMvcProblemDetails;
    /**
     * Precondition Failed
     */
    412: MicrosoftAspNetCoreMvcProblemDetails;
};

export type PutApiRoomByIdError = PutApiRoomByIdErrors[keyof PutApiRoomByIdErrors];

export type PutApiRoomByIdResponses = {
    /**
     * OK
     */
    200: EthosWorkflowsApiRoomDto;
    /**
     * Created
     */
    201: EthosWorkflowsApiRoomDto;
};

export type PutApiRoomByIdResponse = PutApiRoomByIdResponses[keyof PutApiRoomByIdResponses];

export type PostApiRoomSearchData = {
    body?: EthosModelQueryDto1_AllEthosModelRoomQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_LiteralEthosModelRoomQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_NotEthosModelRoomQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_AndEthosModelRoomQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_OrEthosModelRoomQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null;
    path?: never;
    query?: {
        offset?: number;
        limit?: number;
    };
    url: '/api/Room/search';
};

export type PostApiRoomSearchResponses = {
    /**
     * OK
     */
    200: EthosUtilitiesPaginationPagedResponse1EthosWorkflowsApiRoomDto_EthosWorkflows_Version_1000_Culture_neutral_PublicKeyToken_null;
};

export type PostApiRoomSearchResponse = PostApiRoomSearchResponses[keyof PostApiRoomSearchResponses];

export type GetApiRoomData = {
    body?: never;
    path?: never;
    query?: {
        queryBase64?: string;
        offset?: number;
        limit?: number;
    };
    url: '/api/Room';
};

export type GetApiRoomResponses = {
    /**
     * OK
     */
    200: EthosUtilitiesPaginationPagedResponse1EthosWorkflowsApiRoomDto_EthosWorkflows_Version_1000_Culture_neutral_PublicKeyToken_null;
};

export type GetApiRoomResponse = GetApiRoomResponses[keyof GetApiRoomResponses];

export type PostApiRoomData = {
    body?: EthosWorkflowsApiCreateRoomDto;
    path?: never;
    query?: never;
    url: '/api/Room';
};

export type PostApiRoomErrors = {
    /**
     * Bad Request
     */
    400: MicrosoftAspNetCoreMvcProblemDetails;
};

export type PostApiRoomError = PostApiRoomErrors[keyof PostApiRoomErrors];

export type PostApiRoomResponses = {
    /**
     * Created
     */
    201: EthosWorkflowsApiRoomDto;
};

export type PostApiRoomResponse = PostApiRoomResponses[keyof PostApiRoomResponses];

export type PostApiRoomDraftData = {
    body?: {
        [key: string]: SystemTextJsonNodesJsonNode;
    };
    path?: never;
    query?: {
        entityId?: string;
    };
    url: '/api/Room/draft';
};

export type PostApiRoomDraftErrors = {
    /**
     * Bad Request
     */
    400: MicrosoftAspNetCoreMvcProblemDetails;
};

export type PostApiRoomDraftError = PostApiRoomDraftErrors[keyof PostApiRoomDraftErrors];

export type PostApiRoomDraftResponses = {
    /**
     * Created
     */
    201: EthosWorkflowsApiValidatedDraftDto;
};

export type PostApiRoomDraftResponse = PostApiRoomDraftResponses[keyof PostApiRoomDraftResponses];

export type GetApiRoomDraftByEntityIdData = {
    body?: never;
    path: {
        entityId: string;
    };
    query?: never;
    url: '/api/Room/draft/{entityId}';
};

export type GetApiRoomDraftByEntityIdErrors = {
    /**
     * Not Found
     */
    404: MicrosoftAspNetCoreMvcProblemDetails;
};

export type GetApiRoomDraftByEntityIdError = GetApiRoomDraftByEntityIdErrors[keyof GetApiRoomDraftByEntityIdErrors];

export type GetApiRoomDraftByEntityIdResponses = {
    /**
     * OK
     */
    200: EthosWorkflowsApiDraftDto;
};

export type GetApiRoomDraftByEntityIdResponse = GetApiRoomDraftByEntityIdResponses[keyof GetApiRoomDraftByEntityIdResponses];

export type PutApiRoomDraftByEntityIdData = {
    body?: {
        [key: string]: SystemTextJsonNodesJsonNode;
    };
    path: {
        entityId: string;
    };
    query?: never;
    url: '/api/Room/draft/{entityId}';
};

export type PutApiRoomDraftByEntityIdErrors = {
    /**
     * Bad Request
     */
    400: MicrosoftAspNetCoreMvcProblemDetails;
    /**
     * Not Found
     */
    404: MicrosoftAspNetCoreMvcProblemDetails;
    /**
     * Precondition Failed
     */
    412: MicrosoftAspNetCoreMvcProblemDetails;
};

export type PutApiRoomDraftByEntityIdError = PutApiRoomDraftByEntityIdErrors[keyof PutApiRoomDraftByEntityIdErrors];

export type PutApiRoomDraftByEntityIdResponses = {
    /**
     * OK
     */
    200: EthosWorkflowsApiValidatedDraftDto;
};

export type PutApiRoomDraftByEntityIdResponse = PutApiRoomDraftByEntityIdResponses[keyof PutApiRoomDraftByEntityIdResponses];

export type PostApiRoomDraftByEntityIdCommitData = {
    body?: never;
    path: {
        entityId: string;
    };
    query?: never;
    url: '/api/Room/draft/{entityId}/commit';
};

export type PostApiRoomDraftByEntityIdCommitErrors = {
    /**
     * Bad Request
     */
    400: MicrosoftAspNetCoreMvcProblemDetails;
    /**
     * Not Found
     */
    404: MicrosoftAspNetCoreMvcProblemDetails;
    /**
     * Precondition Failed
     */
    412: MicrosoftAspNetCoreMvcProblemDetails;
};

export type PostApiRoomDraftByEntityIdCommitError = PostApiRoomDraftByEntityIdCommitErrors[keyof PostApiRoomDraftByEntityIdCommitErrors];

export type PostApiRoomDraftByEntityIdCommitResponses = {
    /**
     * OK
     */
    200: EthosWorkflowsApiRoomDto;
    /**
     * Created
     */
    201: EthosWorkflowsApiRoomDto;
};

export type PostApiRoomDraftByEntityIdCommitResponse = PostApiRoomDraftByEntityIdCommitResponses[keyof PostApiRoomDraftByEntityIdCommitResponses];

export type PostApiRoomDraftValidateData = {
    body?: {
        [key: string]: SystemTextJsonNodesJsonNode;
    };
    path?: never;
    query?: never;
    url: '/api/Room/draft/validate';
};

export type PostApiRoomDraftValidateErrors = {
    /**
     * Bad Request
     */
    400: MicrosoftAspNetCoreMvcProblemDetails;
};

export type PostApiRoomDraftValidateError = PostApiRoomDraftValidateErrors[keyof PostApiRoomDraftValidateErrors];

export type PostApiRoomDraftValidateResponses = {
    /**
     * OK
     */
    200: EthosWorkflowsApiValidatedDraftDto;
};

export type PostApiRoomDraftValidateResponse = PostApiRoomDraftValidateResponses[keyof PostApiRoomDraftValidateResponses];

export type PostApiRoomDraftByEntityIdValidateData = {
    body?: never;
    path: {
        entityId: string;
    };
    query?: never;
    url: '/api/Room/draft/{entityId}/validate';
};

export type PostApiRoomDraftByEntityIdValidateErrors = {
    /**
     * Bad Request
     */
    400: MicrosoftAspNetCoreMvcProblemDetails;
    /**
     * Not Found
     */
    404: MicrosoftAspNetCoreMvcProblemDetails;
};

export type PostApiRoomDraftByEntityIdValidateError = PostApiRoomDraftByEntityIdValidateErrors[keyof PostApiRoomDraftByEntityIdValidateErrors];

export type PostApiRoomDraftByEntityIdValidateResponses = {
    /**
     * OK
     */
    200: EthosWorkflowsApiValidatedDraftDto;
};

export type PostApiRoomDraftByEntityIdValidateResponse = PostApiRoomDraftByEntityIdValidateResponses[keyof PostApiRoomDraftByEntityIdValidateResponses];

export type PostApiSchedulingFindSlotsData = {
    body?: EthosWorkflowsControllersFindSlotsRequestDto;
    path?: never;
    query?: never;
    url: '/api/Scheduling/find-slots';
};

export type PostApiSchedulingFindSlotsResponses = {
    /**
     * OK
     */
    200: EthosWorkflowsControllersFindSlotsResponseDto;
};

export type PostApiSchedulingFindSlotsResponse = PostApiSchedulingFindSlotsResponses[keyof PostApiSchedulingFindSlotsResponses];

export type GetApiSchedulingConstraintByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/SchedulingConstraint/{id}';
};

export type GetApiSchedulingConstraintByIdErrors = {
    /**
     * Not Found
     */
    404: MicrosoftAspNetCoreMvcProblemDetails;
};

export type GetApiSchedulingConstraintByIdError = GetApiSchedulingConstraintByIdErrors[keyof GetApiSchedulingConstraintByIdErrors];

export type GetApiSchedulingConstraintByIdResponses = {
    /**
     * OK
     */
    200: EthosWorkflowsApiSchedulingConstraintDto;
};

export type GetApiSchedulingConstraintByIdResponse = GetApiSchedulingConstraintByIdResponses[keyof GetApiSchedulingConstraintByIdResponses];

export type PatchApiSchedulingConstraintByIdData = {
    body?: EthosWorkflowsApiCreateSchedulingConstraintDto;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/SchedulingConstraint/{id}';
};

export type PatchApiSchedulingConstraintByIdErrors = {
    /**
     * Bad Request
     */
    400: MicrosoftAspNetCoreMvcProblemDetails;
    /**
     * Not Found
     */
    404: MicrosoftAspNetCoreMvcProblemDetails;
    /**
     * Precondition Failed
     */
    412: MicrosoftAspNetCoreMvcProblemDetails;
};

export type PatchApiSchedulingConstraintByIdError = PatchApiSchedulingConstraintByIdErrors[keyof PatchApiSchedulingConstraintByIdErrors];

export type PatchApiSchedulingConstraintByIdResponses = {
    /**
     * OK
     */
    200: EthosWorkflowsApiSchedulingConstraintDto;
};

export type PatchApiSchedulingConstraintByIdResponse = PatchApiSchedulingConstraintByIdResponses[keyof PatchApiSchedulingConstraintByIdResponses];

export type PutApiSchedulingConstraintByIdData = {
    body?: EthosWorkflowsApiCreateSchedulingConstraintDto;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/SchedulingConstraint/{id}';
};

export type PutApiSchedulingConstraintByIdErrors = {
    /**
     * Bad Request
     */
    400: MicrosoftAspNetCoreMvcProblemDetails;
    /**
     * Not Found
     */
    404: MicrosoftAspNetCoreMvcProblemDetails;
    /**
     * Precondition Failed
     */
    412: MicrosoftAspNetCoreMvcProblemDetails;
};

export type PutApiSchedulingConstraintByIdError = PutApiSchedulingConstraintByIdErrors[keyof PutApiSchedulingConstraintByIdErrors];

export type PutApiSchedulingConstraintByIdResponses = {
    /**
     * OK
     */
    200: EthosWorkflowsApiSchedulingConstraintDto;
    /**
     * Created
     */
    201: EthosWorkflowsApiSchedulingConstraintDto;
};

export type PutApiSchedulingConstraintByIdResponse = PutApiSchedulingConstraintByIdResponses[keyof PutApiSchedulingConstraintByIdResponses];

export type PostApiSchedulingConstraintSearchData = {
    body?: EthosModelQueryDto1_AllEthosModelSchedulingSchedulingConstraintQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_LiteralEthosModelSchedulingSchedulingConstraintQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_NotEthosModelSchedulingSchedulingConstraintQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_AndEthosModelSchedulingSchedulingConstraintQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_OrEthosModelSchedulingSchedulingConstraintQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null;
    path?: never;
    query?: {
        offset?: number;
        limit?: number;
    };
    url: '/api/SchedulingConstraint/search';
};

export type PostApiSchedulingConstraintSearchResponses = {
    /**
     * OK
     */
    200: EthosUtilitiesPaginationPagedResponse1EthosWorkflowsApiSchedulingConstraintDto_EthosWorkflows_Version_1000_Culture_neutral_PublicKeyToken_null;
};

export type PostApiSchedulingConstraintSearchResponse = PostApiSchedulingConstraintSearchResponses[keyof PostApiSchedulingConstraintSearchResponses];

export type GetApiSchedulingConstraintData = {
    body?: never;
    path?: never;
    query?: {
        queryBase64?: string;
        offset?: number;
        limit?: number;
    };
    url: '/api/SchedulingConstraint';
};

export type GetApiSchedulingConstraintResponses = {
    /**
     * OK
     */
    200: EthosUtilitiesPaginationPagedResponse1EthosWorkflowsApiSchedulingConstraintDto_EthosWorkflows_Version_1000_Culture_neutral_PublicKeyToken_null;
};

export type GetApiSchedulingConstraintResponse = GetApiSchedulingConstraintResponses[keyof GetApiSchedulingConstraintResponses];

export type PostApiSchedulingConstraintData = {
    body?: EthosWorkflowsApiCreateSchedulingConstraintDto;
    path?: never;
    query?: never;
    url: '/api/SchedulingConstraint';
};

export type PostApiSchedulingConstraintErrors = {
    /**
     * Bad Request
     */
    400: MicrosoftAspNetCoreMvcProblemDetails;
};

export type PostApiSchedulingConstraintError = PostApiSchedulingConstraintErrors[keyof PostApiSchedulingConstraintErrors];

export type PostApiSchedulingConstraintResponses = {
    /**
     * Created
     */
    201: EthosWorkflowsApiSchedulingConstraintDto;
};

export type PostApiSchedulingConstraintResponse = PostApiSchedulingConstraintResponses[keyof PostApiSchedulingConstraintResponses];

export type PostApiSchedulingConstraintDraftData = {
    body?: {
        [key: string]: SystemTextJsonNodesJsonNode;
    };
    path?: never;
    query?: {
        entityId?: string;
    };
    url: '/api/SchedulingConstraint/draft';
};

export type PostApiSchedulingConstraintDraftErrors = {
    /**
     * Bad Request
     */
    400: MicrosoftAspNetCoreMvcProblemDetails;
};

export type PostApiSchedulingConstraintDraftError = PostApiSchedulingConstraintDraftErrors[keyof PostApiSchedulingConstraintDraftErrors];

export type PostApiSchedulingConstraintDraftResponses = {
    /**
     * Created
     */
    201: EthosWorkflowsApiValidatedDraftDto;
};

export type PostApiSchedulingConstraintDraftResponse = PostApiSchedulingConstraintDraftResponses[keyof PostApiSchedulingConstraintDraftResponses];

export type GetApiSchedulingConstraintDraftByEntityIdData = {
    body?: never;
    path: {
        entityId: string;
    };
    query?: never;
    url: '/api/SchedulingConstraint/draft/{entityId}';
};

export type GetApiSchedulingConstraintDraftByEntityIdErrors = {
    /**
     * Not Found
     */
    404: MicrosoftAspNetCoreMvcProblemDetails;
};

export type GetApiSchedulingConstraintDraftByEntityIdError = GetApiSchedulingConstraintDraftByEntityIdErrors[keyof GetApiSchedulingConstraintDraftByEntityIdErrors];

export type GetApiSchedulingConstraintDraftByEntityIdResponses = {
    /**
     * OK
     */
    200: EthosWorkflowsApiDraftDto;
};

export type GetApiSchedulingConstraintDraftByEntityIdResponse = GetApiSchedulingConstraintDraftByEntityIdResponses[keyof GetApiSchedulingConstraintDraftByEntityIdResponses];

export type PutApiSchedulingConstraintDraftByEntityIdData = {
    body?: {
        [key: string]: SystemTextJsonNodesJsonNode;
    };
    path: {
        entityId: string;
    };
    query?: never;
    url: '/api/SchedulingConstraint/draft/{entityId}';
};

export type PutApiSchedulingConstraintDraftByEntityIdErrors = {
    /**
     * Bad Request
     */
    400: MicrosoftAspNetCoreMvcProblemDetails;
    /**
     * Not Found
     */
    404: MicrosoftAspNetCoreMvcProblemDetails;
    /**
     * Precondition Failed
     */
    412: MicrosoftAspNetCoreMvcProblemDetails;
};

export type PutApiSchedulingConstraintDraftByEntityIdError = PutApiSchedulingConstraintDraftByEntityIdErrors[keyof PutApiSchedulingConstraintDraftByEntityIdErrors];

export type PutApiSchedulingConstraintDraftByEntityIdResponses = {
    /**
     * OK
     */
    200: EthosWorkflowsApiValidatedDraftDto;
};

export type PutApiSchedulingConstraintDraftByEntityIdResponse = PutApiSchedulingConstraintDraftByEntityIdResponses[keyof PutApiSchedulingConstraintDraftByEntityIdResponses];

export type PostApiSchedulingConstraintDraftByEntityIdCommitData = {
    body?: never;
    path: {
        entityId: string;
    };
    query?: never;
    url: '/api/SchedulingConstraint/draft/{entityId}/commit';
};

export type PostApiSchedulingConstraintDraftByEntityIdCommitErrors = {
    /**
     * Bad Request
     */
    400: MicrosoftAspNetCoreMvcProblemDetails;
    /**
     * Not Found
     */
    404: MicrosoftAspNetCoreMvcProblemDetails;
    /**
     * Precondition Failed
     */
    412: MicrosoftAspNetCoreMvcProblemDetails;
};

export type PostApiSchedulingConstraintDraftByEntityIdCommitError = PostApiSchedulingConstraintDraftByEntityIdCommitErrors[keyof PostApiSchedulingConstraintDraftByEntityIdCommitErrors];

export type PostApiSchedulingConstraintDraftByEntityIdCommitResponses = {
    /**
     * OK
     */
    200: EthosWorkflowsApiSchedulingConstraintDto;
    /**
     * Created
     */
    201: EthosWorkflowsApiSchedulingConstraintDto;
};

export type PostApiSchedulingConstraintDraftByEntityIdCommitResponse = PostApiSchedulingConstraintDraftByEntityIdCommitResponses[keyof PostApiSchedulingConstraintDraftByEntityIdCommitResponses];

export type PostApiSchedulingConstraintDraftValidateData = {
    body?: {
        [key: string]: SystemTextJsonNodesJsonNode;
    };
    path?: never;
    query?: never;
    url: '/api/SchedulingConstraint/draft/validate';
};

export type PostApiSchedulingConstraintDraftValidateErrors = {
    /**
     * Bad Request
     */
    400: MicrosoftAspNetCoreMvcProblemDetails;
};

export type PostApiSchedulingConstraintDraftValidateError = PostApiSchedulingConstraintDraftValidateErrors[keyof PostApiSchedulingConstraintDraftValidateErrors];

export type PostApiSchedulingConstraintDraftValidateResponses = {
    /**
     * OK
     */
    200: EthosWorkflowsApiValidatedDraftDto;
};

export type PostApiSchedulingConstraintDraftValidateResponse = PostApiSchedulingConstraintDraftValidateResponses[keyof PostApiSchedulingConstraintDraftValidateResponses];

export type PostApiSchedulingConstraintDraftByEntityIdValidateData = {
    body?: never;
    path: {
        entityId: string;
    };
    query?: never;
    url: '/api/SchedulingConstraint/draft/{entityId}/validate';
};

export type PostApiSchedulingConstraintDraftByEntityIdValidateErrors = {
    /**
     * Bad Request
     */
    400: MicrosoftAspNetCoreMvcProblemDetails;
    /**
     * Not Found
     */
    404: MicrosoftAspNetCoreMvcProblemDetails;
};

export type PostApiSchedulingConstraintDraftByEntityIdValidateError = PostApiSchedulingConstraintDraftByEntityIdValidateErrors[keyof PostApiSchedulingConstraintDraftByEntityIdValidateErrors];

export type PostApiSchedulingConstraintDraftByEntityIdValidateResponses = {
    /**
     * OK
     */
    200: EthosWorkflowsApiValidatedDraftDto;
};

export type PostApiSchedulingConstraintDraftByEntityIdValidateResponse = PostApiSchedulingConstraintDraftByEntityIdValidateResponses[keyof PostApiSchedulingConstraintDraftByEntityIdValidateResponses];

export type GetApiStudyByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/Study/{id}';
};

export type GetApiStudyByIdErrors = {
    /**
     * Not Found
     */
    404: MicrosoftAspNetCoreMvcProblemDetails;
};

export type GetApiStudyByIdError = GetApiStudyByIdErrors[keyof GetApiStudyByIdErrors];

export type GetApiStudyByIdResponses = {
    /**
     * OK
     */
    200: EthosWorkflowsApiStudyDto;
};

export type GetApiStudyByIdResponse = GetApiStudyByIdResponses[keyof GetApiStudyByIdResponses];

export type PatchApiStudyByIdData = {
    body?: EthosWorkflowsApiCreateStudyDto;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/Study/{id}';
};

export type PatchApiStudyByIdErrors = {
    /**
     * Bad Request
     */
    400: MicrosoftAspNetCoreMvcProblemDetails;
    /**
     * Not Found
     */
    404: MicrosoftAspNetCoreMvcProblemDetails;
    /**
     * Precondition Failed
     */
    412: MicrosoftAspNetCoreMvcProblemDetails;
};

export type PatchApiStudyByIdError = PatchApiStudyByIdErrors[keyof PatchApiStudyByIdErrors];

export type PatchApiStudyByIdResponses = {
    /**
     * OK
     */
    200: EthosWorkflowsApiStudyDto;
};

export type PatchApiStudyByIdResponse = PatchApiStudyByIdResponses[keyof PatchApiStudyByIdResponses];

export type PutApiStudyByIdData = {
    body?: EthosWorkflowsApiCreateStudyDto;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/Study/{id}';
};

export type PutApiStudyByIdErrors = {
    /**
     * Bad Request
     */
    400: MicrosoftAspNetCoreMvcProblemDetails;
    /**
     * Not Found
     */
    404: MicrosoftAspNetCoreMvcProblemDetails;
    /**
     * Precondition Failed
     */
    412: MicrosoftAspNetCoreMvcProblemDetails;
};

export type PutApiStudyByIdError = PutApiStudyByIdErrors[keyof PutApiStudyByIdErrors];

export type PutApiStudyByIdResponses = {
    /**
     * OK
     */
    200: EthosWorkflowsApiStudyDto;
    /**
     * Created
     */
    201: EthosWorkflowsApiStudyDto;
};

export type PutApiStudyByIdResponse = PutApiStudyByIdResponses[keyof PutApiStudyByIdResponses];

export type PostApiStudySearchData = {
    body?: EthosModelQueryDto1_AllEthosModelStudyQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_LiteralEthosModelStudyQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_NotEthosModelStudyQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_AndEthosModelStudyQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_OrEthosModelStudyQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null;
    path?: never;
    query?: {
        offset?: number;
        limit?: number;
    };
    url: '/api/Study/search';
};

export type PostApiStudySearchResponses = {
    /**
     * OK
     */
    200: EthosUtilitiesPaginationPagedResponse1EthosWorkflowsApiStudyDto_EthosWorkflows_Version_1000_Culture_neutral_PublicKeyToken_null;
};

export type PostApiStudySearchResponse = PostApiStudySearchResponses[keyof PostApiStudySearchResponses];

export type GetApiStudyData = {
    body?: never;
    path?: never;
    query?: {
        queryBase64?: string;
        offset?: number;
        limit?: number;
    };
    url: '/api/Study';
};

export type GetApiStudyResponses = {
    /**
     * OK
     */
    200: EthosUtilitiesPaginationPagedResponse1EthosWorkflowsApiStudyDto_EthosWorkflows_Version_1000_Culture_neutral_PublicKeyToken_null;
};

export type GetApiStudyResponse = GetApiStudyResponses[keyof GetApiStudyResponses];

export type PostApiStudyData = {
    body?: EthosWorkflowsApiCreateStudyDto;
    path?: never;
    query?: never;
    url: '/api/Study';
};

export type PostApiStudyErrors = {
    /**
     * Bad Request
     */
    400: MicrosoftAspNetCoreMvcProblemDetails;
};

export type PostApiStudyError = PostApiStudyErrors[keyof PostApiStudyErrors];

export type PostApiStudyResponses = {
    /**
     * Created
     */
    201: EthosWorkflowsApiStudyDto;
};

export type PostApiStudyResponse = PostApiStudyResponses[keyof PostApiStudyResponses];

export type PostApiStudyDraftData = {
    body?: {
        [key: string]: SystemTextJsonNodesJsonNode;
    };
    path?: never;
    query?: {
        entityId?: string;
    };
    url: '/api/Study/draft';
};

export type PostApiStudyDraftErrors = {
    /**
     * Bad Request
     */
    400: MicrosoftAspNetCoreMvcProblemDetails;
};

export type PostApiStudyDraftError = PostApiStudyDraftErrors[keyof PostApiStudyDraftErrors];

export type PostApiStudyDraftResponses = {
    /**
     * Created
     */
    201: EthosWorkflowsApiValidatedDraftDto;
};

export type PostApiStudyDraftResponse = PostApiStudyDraftResponses[keyof PostApiStudyDraftResponses];

export type GetApiStudyDraftByEntityIdData = {
    body?: never;
    path: {
        entityId: string;
    };
    query?: never;
    url: '/api/Study/draft/{entityId}';
};

export type GetApiStudyDraftByEntityIdErrors = {
    /**
     * Not Found
     */
    404: MicrosoftAspNetCoreMvcProblemDetails;
};

export type GetApiStudyDraftByEntityIdError = GetApiStudyDraftByEntityIdErrors[keyof GetApiStudyDraftByEntityIdErrors];

export type GetApiStudyDraftByEntityIdResponses = {
    /**
     * OK
     */
    200: EthosWorkflowsApiDraftDto;
};

export type GetApiStudyDraftByEntityIdResponse = GetApiStudyDraftByEntityIdResponses[keyof GetApiStudyDraftByEntityIdResponses];

export type PutApiStudyDraftByEntityIdData = {
    body?: {
        [key: string]: SystemTextJsonNodesJsonNode;
    };
    path: {
        entityId: string;
    };
    query?: never;
    url: '/api/Study/draft/{entityId}';
};

export type PutApiStudyDraftByEntityIdErrors = {
    /**
     * Bad Request
     */
    400: MicrosoftAspNetCoreMvcProblemDetails;
    /**
     * Not Found
     */
    404: MicrosoftAspNetCoreMvcProblemDetails;
    /**
     * Precondition Failed
     */
    412: MicrosoftAspNetCoreMvcProblemDetails;
};

export type PutApiStudyDraftByEntityIdError = PutApiStudyDraftByEntityIdErrors[keyof PutApiStudyDraftByEntityIdErrors];

export type PutApiStudyDraftByEntityIdResponses = {
    /**
     * OK
     */
    200: EthosWorkflowsApiValidatedDraftDto;
};

export type PutApiStudyDraftByEntityIdResponse = PutApiStudyDraftByEntityIdResponses[keyof PutApiStudyDraftByEntityIdResponses];

export type PostApiStudyDraftByEntityIdCommitData = {
    body?: never;
    path: {
        entityId: string;
    };
    query?: never;
    url: '/api/Study/draft/{entityId}/commit';
};

export type PostApiStudyDraftByEntityIdCommitErrors = {
    /**
     * Bad Request
     */
    400: MicrosoftAspNetCoreMvcProblemDetails;
    /**
     * Not Found
     */
    404: MicrosoftAspNetCoreMvcProblemDetails;
    /**
     * Precondition Failed
     */
    412: MicrosoftAspNetCoreMvcProblemDetails;
};

export type PostApiStudyDraftByEntityIdCommitError = PostApiStudyDraftByEntityIdCommitErrors[keyof PostApiStudyDraftByEntityIdCommitErrors];

export type PostApiStudyDraftByEntityIdCommitResponses = {
    /**
     * OK
     */
    200: EthosWorkflowsApiStudyDto;
    /**
     * Created
     */
    201: EthosWorkflowsApiStudyDto;
};

export type PostApiStudyDraftByEntityIdCommitResponse = PostApiStudyDraftByEntityIdCommitResponses[keyof PostApiStudyDraftByEntityIdCommitResponses];

export type PostApiStudyDraftValidateData = {
    body?: {
        [key: string]: SystemTextJsonNodesJsonNode;
    };
    path?: never;
    query?: never;
    url: '/api/Study/draft/validate';
};

export type PostApiStudyDraftValidateErrors = {
    /**
     * Bad Request
     */
    400: MicrosoftAspNetCoreMvcProblemDetails;
};

export type PostApiStudyDraftValidateError = PostApiStudyDraftValidateErrors[keyof PostApiStudyDraftValidateErrors];

export type PostApiStudyDraftValidateResponses = {
    /**
     * OK
     */
    200: EthosWorkflowsApiValidatedDraftDto;
};

export type PostApiStudyDraftValidateResponse = PostApiStudyDraftValidateResponses[keyof PostApiStudyDraftValidateResponses];

export type PostApiStudyDraftByEntityIdValidateData = {
    body?: never;
    path: {
        entityId: string;
    };
    query?: never;
    url: '/api/Study/draft/{entityId}/validate';
};

export type PostApiStudyDraftByEntityIdValidateErrors = {
    /**
     * Bad Request
     */
    400: MicrosoftAspNetCoreMvcProblemDetails;
    /**
     * Not Found
     */
    404: MicrosoftAspNetCoreMvcProblemDetails;
};

export type PostApiStudyDraftByEntityIdValidateError = PostApiStudyDraftByEntityIdValidateErrors[keyof PostApiStudyDraftByEntityIdValidateErrors];

export type PostApiStudyDraftByEntityIdValidateResponses = {
    /**
     * OK
     */
    200: EthosWorkflowsApiValidatedDraftDto;
};

export type PostApiStudyDraftByEntityIdValidateResponse = PostApiStudyDraftByEntityIdValidateResponses[keyof PostApiStudyDraftByEntityIdValidateResponses];

export type GetApiTechnicianByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/Technician/{id}';
};

export type GetApiTechnicianByIdErrors = {
    /**
     * Not Found
     */
    404: MicrosoftAspNetCoreMvcProblemDetails;
};

export type GetApiTechnicianByIdError = GetApiTechnicianByIdErrors[keyof GetApiTechnicianByIdErrors];

export type GetApiTechnicianByIdResponses = {
    /**
     * OK
     */
    200: EthosWorkflowsApiTechnicianDto;
};

export type GetApiTechnicianByIdResponse = GetApiTechnicianByIdResponses[keyof GetApiTechnicianByIdResponses];

export type PatchApiTechnicianByIdData = {
    body?: EthosWorkflowsApiCreateTechnicianDto;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/Technician/{id}';
};

export type PatchApiTechnicianByIdErrors = {
    /**
     * Bad Request
     */
    400: MicrosoftAspNetCoreMvcProblemDetails;
    /**
     * Not Found
     */
    404: MicrosoftAspNetCoreMvcProblemDetails;
    /**
     * Precondition Failed
     */
    412: MicrosoftAspNetCoreMvcProblemDetails;
};

export type PatchApiTechnicianByIdError = PatchApiTechnicianByIdErrors[keyof PatchApiTechnicianByIdErrors];

export type PatchApiTechnicianByIdResponses = {
    /**
     * OK
     */
    200: EthosWorkflowsApiTechnicianDto;
};

export type PatchApiTechnicianByIdResponse = PatchApiTechnicianByIdResponses[keyof PatchApiTechnicianByIdResponses];

export type PutApiTechnicianByIdData = {
    body?: EthosWorkflowsApiCreateTechnicianDto;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/Technician/{id}';
};

export type PutApiTechnicianByIdErrors = {
    /**
     * Bad Request
     */
    400: MicrosoftAspNetCoreMvcProblemDetails;
    /**
     * Not Found
     */
    404: MicrosoftAspNetCoreMvcProblemDetails;
    /**
     * Precondition Failed
     */
    412: MicrosoftAspNetCoreMvcProblemDetails;
};

export type PutApiTechnicianByIdError = PutApiTechnicianByIdErrors[keyof PutApiTechnicianByIdErrors];

export type PutApiTechnicianByIdResponses = {
    /**
     * OK
     */
    200: EthosWorkflowsApiTechnicianDto;
    /**
     * Created
     */
    201: EthosWorkflowsApiTechnicianDto;
};

export type PutApiTechnicianByIdResponse = PutApiTechnicianByIdResponses[keyof PutApiTechnicianByIdResponses];

export type PostApiTechnicianSearchData = {
    body?: EthosModelQueryDto1_AllEthosModelTechnicianQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_LiteralEthosModelTechnicianQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_NotEthosModelTechnicianQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_AndEthosModelTechnicianQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_OrEthosModelTechnicianQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null;
    path?: never;
    query?: {
        offset?: number;
        limit?: number;
    };
    url: '/api/Technician/search';
};

export type PostApiTechnicianSearchResponses = {
    /**
     * OK
     */
    200: EthosUtilitiesPaginationPagedResponse1EthosWorkflowsApiTechnicianDto_EthosWorkflows_Version_1000_Culture_neutral_PublicKeyToken_null;
};

export type PostApiTechnicianSearchResponse = PostApiTechnicianSearchResponses[keyof PostApiTechnicianSearchResponses];

export type GetApiTechnicianData = {
    body?: never;
    path?: never;
    query?: {
        queryBase64?: string;
        offset?: number;
        limit?: number;
    };
    url: '/api/Technician';
};

export type GetApiTechnicianResponses = {
    /**
     * OK
     */
    200: EthosUtilitiesPaginationPagedResponse1EthosWorkflowsApiTechnicianDto_EthosWorkflows_Version_1000_Culture_neutral_PublicKeyToken_null;
};

export type GetApiTechnicianResponse = GetApiTechnicianResponses[keyof GetApiTechnicianResponses];

export type PostApiTechnicianData = {
    body?: EthosWorkflowsApiCreateTechnicianDto;
    path?: never;
    query?: never;
    url: '/api/Technician';
};

export type PostApiTechnicianErrors = {
    /**
     * Bad Request
     */
    400: MicrosoftAspNetCoreMvcProblemDetails;
};

export type PostApiTechnicianError = PostApiTechnicianErrors[keyof PostApiTechnicianErrors];

export type PostApiTechnicianResponses = {
    /**
     * Created
     */
    201: EthosWorkflowsApiTechnicianDto;
};

export type PostApiTechnicianResponse = PostApiTechnicianResponses[keyof PostApiTechnicianResponses];

export type PostApiTechnicianDraftData = {
    body?: {
        [key: string]: SystemTextJsonNodesJsonNode;
    };
    path?: never;
    query?: {
        entityId?: string;
    };
    url: '/api/Technician/draft';
};

export type PostApiTechnicianDraftErrors = {
    /**
     * Bad Request
     */
    400: MicrosoftAspNetCoreMvcProblemDetails;
};

export type PostApiTechnicianDraftError = PostApiTechnicianDraftErrors[keyof PostApiTechnicianDraftErrors];

export type PostApiTechnicianDraftResponses = {
    /**
     * Created
     */
    201: EthosWorkflowsApiValidatedDraftDto;
};

export type PostApiTechnicianDraftResponse = PostApiTechnicianDraftResponses[keyof PostApiTechnicianDraftResponses];

export type GetApiTechnicianDraftByEntityIdData = {
    body?: never;
    path: {
        entityId: string;
    };
    query?: never;
    url: '/api/Technician/draft/{entityId}';
};

export type GetApiTechnicianDraftByEntityIdErrors = {
    /**
     * Not Found
     */
    404: MicrosoftAspNetCoreMvcProblemDetails;
};

export type GetApiTechnicianDraftByEntityIdError = GetApiTechnicianDraftByEntityIdErrors[keyof GetApiTechnicianDraftByEntityIdErrors];

export type GetApiTechnicianDraftByEntityIdResponses = {
    /**
     * OK
     */
    200: EthosWorkflowsApiDraftDto;
};

export type GetApiTechnicianDraftByEntityIdResponse = GetApiTechnicianDraftByEntityIdResponses[keyof GetApiTechnicianDraftByEntityIdResponses];

export type PutApiTechnicianDraftByEntityIdData = {
    body?: {
        [key: string]: SystemTextJsonNodesJsonNode;
    };
    path: {
        entityId: string;
    };
    query?: never;
    url: '/api/Technician/draft/{entityId}';
};

export type PutApiTechnicianDraftByEntityIdErrors = {
    /**
     * Bad Request
     */
    400: MicrosoftAspNetCoreMvcProblemDetails;
    /**
     * Not Found
     */
    404: MicrosoftAspNetCoreMvcProblemDetails;
    /**
     * Precondition Failed
     */
    412: MicrosoftAspNetCoreMvcProblemDetails;
};

export type PutApiTechnicianDraftByEntityIdError = PutApiTechnicianDraftByEntityIdErrors[keyof PutApiTechnicianDraftByEntityIdErrors];

export type PutApiTechnicianDraftByEntityIdResponses = {
    /**
     * OK
     */
    200: EthosWorkflowsApiValidatedDraftDto;
};

export type PutApiTechnicianDraftByEntityIdResponse = PutApiTechnicianDraftByEntityIdResponses[keyof PutApiTechnicianDraftByEntityIdResponses];

export type PostApiTechnicianDraftByEntityIdCommitData = {
    body?: never;
    path: {
        entityId: string;
    };
    query?: never;
    url: '/api/Technician/draft/{entityId}/commit';
};

export type PostApiTechnicianDraftByEntityIdCommitErrors = {
    /**
     * Bad Request
     */
    400: MicrosoftAspNetCoreMvcProblemDetails;
    /**
     * Not Found
     */
    404: MicrosoftAspNetCoreMvcProblemDetails;
    /**
     * Precondition Failed
     */
    412: MicrosoftAspNetCoreMvcProblemDetails;
};

export type PostApiTechnicianDraftByEntityIdCommitError = PostApiTechnicianDraftByEntityIdCommitErrors[keyof PostApiTechnicianDraftByEntityIdCommitErrors];

export type PostApiTechnicianDraftByEntityIdCommitResponses = {
    /**
     * OK
     */
    200: EthosWorkflowsApiTechnicianDto;
    /**
     * Created
     */
    201: EthosWorkflowsApiTechnicianDto;
};

export type PostApiTechnicianDraftByEntityIdCommitResponse = PostApiTechnicianDraftByEntityIdCommitResponses[keyof PostApiTechnicianDraftByEntityIdCommitResponses];

export type PostApiTechnicianDraftValidateData = {
    body?: {
        [key: string]: SystemTextJsonNodesJsonNode;
    };
    path?: never;
    query?: never;
    url: '/api/Technician/draft/validate';
};

export type PostApiTechnicianDraftValidateErrors = {
    /**
     * Bad Request
     */
    400: MicrosoftAspNetCoreMvcProblemDetails;
};

export type PostApiTechnicianDraftValidateError = PostApiTechnicianDraftValidateErrors[keyof PostApiTechnicianDraftValidateErrors];

export type PostApiTechnicianDraftValidateResponses = {
    /**
     * OK
     */
    200: EthosWorkflowsApiValidatedDraftDto;
};

export type PostApiTechnicianDraftValidateResponse = PostApiTechnicianDraftValidateResponses[keyof PostApiTechnicianDraftValidateResponses];

export type PostApiTechnicianDraftByEntityIdValidateData = {
    body?: never;
    path: {
        entityId: string;
    };
    query?: never;
    url: '/api/Technician/draft/{entityId}/validate';
};

export type PostApiTechnicianDraftByEntityIdValidateErrors = {
    /**
     * Bad Request
     */
    400: MicrosoftAspNetCoreMvcProblemDetails;
    /**
     * Not Found
     */
    404: MicrosoftAspNetCoreMvcProblemDetails;
};

export type PostApiTechnicianDraftByEntityIdValidateError = PostApiTechnicianDraftByEntityIdValidateErrors[keyof PostApiTechnicianDraftByEntityIdValidateErrors];

export type PostApiTechnicianDraftByEntityIdValidateResponses = {
    /**
     * OK
     */
    200: EthosWorkflowsApiValidatedDraftDto;
};

export type PostApiTechnicianDraftByEntityIdValidateResponse = PostApiTechnicianDraftByEntityIdValidateResponses[keyof PostApiTechnicianDraftByEntityIdValidateResponses];

export type GetApiTechnicianAppointmentByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/TechnicianAppointment/{id}';
};

export type GetApiTechnicianAppointmentByIdErrors = {
    /**
     * Not Found
     */
    404: MicrosoftAspNetCoreMvcProblemDetails;
};

export type GetApiTechnicianAppointmentByIdError = GetApiTechnicianAppointmentByIdErrors[keyof GetApiTechnicianAppointmentByIdErrors];

export type GetApiTechnicianAppointmentByIdResponses = {
    /**
     * OK
     */
    200: EthosWorkflowsApiTechnicianAppointmentDto;
};

export type GetApiTechnicianAppointmentByIdResponse = GetApiTechnicianAppointmentByIdResponses[keyof GetApiTechnicianAppointmentByIdResponses];

export type PatchApiTechnicianAppointmentByIdData = {
    body?: EthosWorkflowsApiCreateTechnicianAppointmentDto;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/TechnicianAppointment/{id}';
};

export type PatchApiTechnicianAppointmentByIdErrors = {
    /**
     * Bad Request
     */
    400: MicrosoftAspNetCoreMvcProblemDetails;
    /**
     * Not Found
     */
    404: MicrosoftAspNetCoreMvcProblemDetails;
    /**
     * Precondition Failed
     */
    412: MicrosoftAspNetCoreMvcProblemDetails;
};

export type PatchApiTechnicianAppointmentByIdError = PatchApiTechnicianAppointmentByIdErrors[keyof PatchApiTechnicianAppointmentByIdErrors];

export type PatchApiTechnicianAppointmentByIdResponses = {
    /**
     * OK
     */
    200: EthosWorkflowsApiTechnicianAppointmentDto;
};

export type PatchApiTechnicianAppointmentByIdResponse = PatchApiTechnicianAppointmentByIdResponses[keyof PatchApiTechnicianAppointmentByIdResponses];

export type PutApiTechnicianAppointmentByIdData = {
    body?: EthosWorkflowsApiCreateTechnicianAppointmentDto;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/TechnicianAppointment/{id}';
};

export type PutApiTechnicianAppointmentByIdErrors = {
    /**
     * Bad Request
     */
    400: MicrosoftAspNetCoreMvcProblemDetails;
    /**
     * Not Found
     */
    404: MicrosoftAspNetCoreMvcProblemDetails;
    /**
     * Precondition Failed
     */
    412: MicrosoftAspNetCoreMvcProblemDetails;
};

export type PutApiTechnicianAppointmentByIdError = PutApiTechnicianAppointmentByIdErrors[keyof PutApiTechnicianAppointmentByIdErrors];

export type PutApiTechnicianAppointmentByIdResponses = {
    /**
     * OK
     */
    200: EthosWorkflowsApiTechnicianAppointmentDto;
    /**
     * Created
     */
    201: EthosWorkflowsApiTechnicianAppointmentDto;
};

export type PutApiTechnicianAppointmentByIdResponse = PutApiTechnicianAppointmentByIdResponses[keyof PutApiTechnicianAppointmentByIdResponses];

export type PostApiTechnicianAppointmentSearchData = {
    body?: EthosModelQueryDto1_AllEthosModelTechnicianAppointmentQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_LiteralEthosModelTechnicianAppointmentQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_NotEthosModelTechnicianAppointmentQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_AndEthosModelTechnicianAppointmentQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null | EthosModelQueryDto1_OrEthosModelTechnicianAppointmentQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null;
    path?: never;
    query?: {
        offset?: number;
        limit?: number;
    };
    url: '/api/TechnicianAppointment/search';
};

export type PostApiTechnicianAppointmentSearchResponses = {
    /**
     * OK
     */
    200: EthosUtilitiesPaginationPagedResponse1EthosWorkflowsApiTechnicianAppointmentDto_EthosWorkflows_Version_1000_Culture_neutral_PublicKeyToken_null;
};

export type PostApiTechnicianAppointmentSearchResponse = PostApiTechnicianAppointmentSearchResponses[keyof PostApiTechnicianAppointmentSearchResponses];

export type GetApiTechnicianAppointmentData = {
    body?: never;
    path?: never;
    query?: {
        queryBase64?: string;
        offset?: number;
        limit?: number;
    };
    url: '/api/TechnicianAppointment';
};

export type GetApiTechnicianAppointmentResponses = {
    /**
     * OK
     */
    200: EthosUtilitiesPaginationPagedResponse1EthosWorkflowsApiTechnicianAppointmentDto_EthosWorkflows_Version_1000_Culture_neutral_PublicKeyToken_null;
};

export type GetApiTechnicianAppointmentResponse = GetApiTechnicianAppointmentResponses[keyof GetApiTechnicianAppointmentResponses];

export type PostApiTechnicianAppointmentData = {
    body?: EthosWorkflowsApiCreateTechnicianAppointmentDto;
    path?: never;
    query?: never;
    url: '/api/TechnicianAppointment';
};

export type PostApiTechnicianAppointmentErrors = {
    /**
     * Bad Request
     */
    400: MicrosoftAspNetCoreMvcProblemDetails;
};

export type PostApiTechnicianAppointmentError = PostApiTechnicianAppointmentErrors[keyof PostApiTechnicianAppointmentErrors];

export type PostApiTechnicianAppointmentResponses = {
    /**
     * Created
     */
    201: EthosWorkflowsApiTechnicianAppointmentDto;
};

export type PostApiTechnicianAppointmentResponse = PostApiTechnicianAppointmentResponses[keyof PostApiTechnicianAppointmentResponses];

export type PostApiTechnicianAppointmentDraftData = {
    body?: {
        [key: string]: SystemTextJsonNodesJsonNode;
    };
    path?: never;
    query?: {
        entityId?: string;
    };
    url: '/api/TechnicianAppointment/draft';
};

export type PostApiTechnicianAppointmentDraftErrors = {
    /**
     * Bad Request
     */
    400: MicrosoftAspNetCoreMvcProblemDetails;
};

export type PostApiTechnicianAppointmentDraftError = PostApiTechnicianAppointmentDraftErrors[keyof PostApiTechnicianAppointmentDraftErrors];

export type PostApiTechnicianAppointmentDraftResponses = {
    /**
     * Created
     */
    201: EthosWorkflowsApiValidatedDraftDto;
};

export type PostApiTechnicianAppointmentDraftResponse = PostApiTechnicianAppointmentDraftResponses[keyof PostApiTechnicianAppointmentDraftResponses];

export type GetApiTechnicianAppointmentDraftByEntityIdData = {
    body?: never;
    path: {
        entityId: string;
    };
    query?: never;
    url: '/api/TechnicianAppointment/draft/{entityId}';
};

export type GetApiTechnicianAppointmentDraftByEntityIdErrors = {
    /**
     * Not Found
     */
    404: MicrosoftAspNetCoreMvcProblemDetails;
};

export type GetApiTechnicianAppointmentDraftByEntityIdError = GetApiTechnicianAppointmentDraftByEntityIdErrors[keyof GetApiTechnicianAppointmentDraftByEntityIdErrors];

export type GetApiTechnicianAppointmentDraftByEntityIdResponses = {
    /**
     * OK
     */
    200: EthosWorkflowsApiDraftDto;
};

export type GetApiTechnicianAppointmentDraftByEntityIdResponse = GetApiTechnicianAppointmentDraftByEntityIdResponses[keyof GetApiTechnicianAppointmentDraftByEntityIdResponses];

export type PutApiTechnicianAppointmentDraftByEntityIdData = {
    body?: {
        [key: string]: SystemTextJsonNodesJsonNode;
    };
    path: {
        entityId: string;
    };
    query?: never;
    url: '/api/TechnicianAppointment/draft/{entityId}';
};

export type PutApiTechnicianAppointmentDraftByEntityIdErrors = {
    /**
     * Bad Request
     */
    400: MicrosoftAspNetCoreMvcProblemDetails;
    /**
     * Not Found
     */
    404: MicrosoftAspNetCoreMvcProblemDetails;
    /**
     * Precondition Failed
     */
    412: MicrosoftAspNetCoreMvcProblemDetails;
};

export type PutApiTechnicianAppointmentDraftByEntityIdError = PutApiTechnicianAppointmentDraftByEntityIdErrors[keyof PutApiTechnicianAppointmentDraftByEntityIdErrors];

export type PutApiTechnicianAppointmentDraftByEntityIdResponses = {
    /**
     * OK
     */
    200: EthosWorkflowsApiValidatedDraftDto;
};

export type PutApiTechnicianAppointmentDraftByEntityIdResponse = PutApiTechnicianAppointmentDraftByEntityIdResponses[keyof PutApiTechnicianAppointmentDraftByEntityIdResponses];

export type PostApiTechnicianAppointmentDraftByEntityIdCommitData = {
    body?: never;
    path: {
        entityId: string;
    };
    query?: never;
    url: '/api/TechnicianAppointment/draft/{entityId}/commit';
};

export type PostApiTechnicianAppointmentDraftByEntityIdCommitErrors = {
    /**
     * Bad Request
     */
    400: MicrosoftAspNetCoreMvcProblemDetails;
    /**
     * Not Found
     */
    404: MicrosoftAspNetCoreMvcProblemDetails;
    /**
     * Precondition Failed
     */
    412: MicrosoftAspNetCoreMvcProblemDetails;
};

export type PostApiTechnicianAppointmentDraftByEntityIdCommitError = PostApiTechnicianAppointmentDraftByEntityIdCommitErrors[keyof PostApiTechnicianAppointmentDraftByEntityIdCommitErrors];

export type PostApiTechnicianAppointmentDraftByEntityIdCommitResponses = {
    /**
     * OK
     */
    200: EthosWorkflowsApiTechnicianAppointmentDto;
    /**
     * Created
     */
    201: EthosWorkflowsApiTechnicianAppointmentDto;
};

export type PostApiTechnicianAppointmentDraftByEntityIdCommitResponse = PostApiTechnicianAppointmentDraftByEntityIdCommitResponses[keyof PostApiTechnicianAppointmentDraftByEntityIdCommitResponses];

export type PostApiTechnicianAppointmentDraftValidateData = {
    body?: {
        [key: string]: SystemTextJsonNodesJsonNode;
    };
    path?: never;
    query?: never;
    url: '/api/TechnicianAppointment/draft/validate';
};

export type PostApiTechnicianAppointmentDraftValidateErrors = {
    /**
     * Bad Request
     */
    400: MicrosoftAspNetCoreMvcProblemDetails;
};

export type PostApiTechnicianAppointmentDraftValidateError = PostApiTechnicianAppointmentDraftValidateErrors[keyof PostApiTechnicianAppointmentDraftValidateErrors];

export type PostApiTechnicianAppointmentDraftValidateResponses = {
    /**
     * OK
     */
    200: EthosWorkflowsApiValidatedDraftDto;
};

export type PostApiTechnicianAppointmentDraftValidateResponse = PostApiTechnicianAppointmentDraftValidateResponses[keyof PostApiTechnicianAppointmentDraftValidateResponses];

export type PostApiTechnicianAppointmentDraftByEntityIdValidateData = {
    body?: never;
    path: {
        entityId: string;
    };
    query?: never;
    url: '/api/TechnicianAppointment/draft/{entityId}/validate';
};

export type PostApiTechnicianAppointmentDraftByEntityIdValidateErrors = {
    /**
     * Bad Request
     */
    400: MicrosoftAspNetCoreMvcProblemDetails;
    /**
     * Not Found
     */
    404: MicrosoftAspNetCoreMvcProblemDetails;
};

export type PostApiTechnicianAppointmentDraftByEntityIdValidateError = PostApiTechnicianAppointmentDraftByEntityIdValidateErrors[keyof PostApiTechnicianAppointmentDraftByEntityIdValidateErrors];

export type PostApiTechnicianAppointmentDraftByEntityIdValidateResponses = {
    /**
     * OK
     */
    200: EthosWorkflowsApiValidatedDraftDto;
};

export type PostApiTechnicianAppointmentDraftByEntityIdValidateResponse = PostApiTechnicianAppointmentDraftByEntityIdValidateResponses[keyof PostApiTechnicianAppointmentDraftByEntityIdValidateResponses];

export type GetApiWorkflowFlowsData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/Workflow/flows';
};

export type GetApiWorkflowFlowsResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetApiWorkflowMyTasksData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/Workflow/myTasks';
};

export type GetApiWorkflowMyTasksResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetApiWorkflowProfileData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/Workflow/profile';
};

export type GetApiWorkflowProfileResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type ClientOptions = {
    baseURL: 'http://localhost:4000' | (string & {});
};