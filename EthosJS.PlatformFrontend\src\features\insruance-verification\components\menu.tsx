import MenuItem from '@components/menu/menu-item';
import SecondaryMenu from '@components/menu/secondary-menu';
import { ScrollText, ShieldCheck, User } from 'lucide-react';
import SelectionControls from './selection-controls';
import useInsuranceVerification from '../hooks/use-insurance-verification';

interface InsuranceVerificationMenuProps {
	patientId: string;
	orderId: string;
	studyId: string;
	activePath: string;
	onClick: (path: string) => void;
}

export default function InsuranceVerificationMenu({
	patientId,
	orderId,
	studyId,
	activePath,
	onClick,
}: InsuranceVerificationMenuProps) {
	const onChange = (orderId: string, studyId?: string) => {
		onClick(`/insurance-verification?orderId=${orderId}&studyId=${studyId}`);
	};

	const { verificationStatus } = useInsuranceVerification({
		studyId,
	});

	return (
		<SecondaryMenu
			headerProps={{
				title: 'Insurance Verification',
				subtitle: undefined,
				icon: User,
				type: 'Workflow',
				showIcon: false,
				color: 'success',
				progress: 0,
				description: 'Complete all the patient profile details.',
			}}
			topContainer={
				<SelectionControls
					patientId={patientId}
					orderId={orderId}
					studyId={studyId}
					onChange={onChange}
				/>
			}>
			<MenuItem
				title="Insurance Verification"
				value="insurance-verification"
				icon={ShieldCheck}
				size="medium"
				selected={activePath.includes('/insurance-verification')}
				onClick={() => {
					onClick('/insurance-verification');
				}}
			/>
			<MenuItem
				title="Prior Authorization"
				value="prior-authorization"
				icon={ScrollText}
				size="medium"
				selected={activePath.includes('/prior-authorization')}
				disabled={verificationStatus?.currentCoarseState !== 'VerificationSuccessfulAuthRequired'}
				onClick={() => {
					onClick('/prior-authorization');
				}}
			/>
		</SecondaryMenu>
	);
}
