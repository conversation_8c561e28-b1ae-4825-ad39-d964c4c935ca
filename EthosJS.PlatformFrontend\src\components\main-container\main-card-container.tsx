import React, { ReactElement, ReactNode } from 'react';
import {
	<PERSON><PERSON><PERSON><PERSON>on,
	Stack,
	Card<PERSON>ontent,
	useTheme,
	CardContentProps,
	CardActionsProps,
	IconButtonProps,
	Box,
	Typography,
	Chip,
} from '@mui/material';

import { Plus, Edit, X, ChevronDown, ChevronUp } from 'lucide-react';

import Card from '@components/card';
import <PERSON><PERSON>eader, { CardHeaderProps } from '@components/card-header';
import MainCardFooter, { MainCardFooterProps } from './main-card-footer';

import { lighten } from '@mui/material/styles';

type Emphasis = 'low' | 'medium' | 'high';
export type ColorKey = 'primary' | 'success' | 'error' | 'warning' | 'gray';
type PrimaryActionType = 'Edit' | 'Add' | 'Delete' | 'collapse' | 'none';
type SecondaryActionType = 'Buttons' | 'Dropdown' | 'Chips' | 'Selection' | 'none';

export interface MainCardContainerProps extends CardContentProps {
	emphasis?: Emphasis;
	color?: ColorKey;
	title?: string;
	icon?: CardHeaderProps['avatar'];
	headerSize?: CardHeaderProps['size'];
	containerSlot?: ReactNode;
	customAction?: ReactNode;
	// Primary action
	primaryActionType?: PrimaryActionType;

	onPrimaryAction?: IconButtonProps['onClick'];
	collapsed?: boolean;
	// Secondary actions - only one can be used
	secondaryActionType?: SecondaryActionType;

	// Buttons props
	buttonPrimaryLabel?: string;
	buttonSecondaryLabel?: string;
	onButtonPrimaryClick?: () => void;
	onButtonSecondaryClick?: () => void;

	// Dropdown props
	dropdownLabel?: string;
	onDropdownClick?: () => void;

	// Chips props
	chipLabels?: string[] | { label: string; icon: ReactElement }[];
	onChipClick?: (label: string) => void;

	// Selection props
	selectionSelected?: boolean;
	selectionLabel?: string;
	onSelectionChange?: (selected: boolean) => void;

	// Description
	descriptionSubheader?: string;
	descriptionText?: string;

	// Footer props
	footerProps?: CardActionsProps & Omit<MainCardFooterProps, 'backgroundColor'>;
	// Children for content
	children?: React.ReactNode;
}

const MainCardContainer: React.FC<MainCardContainerProps> = ({
	emphasis = 'low',
	color = 'primary',
	title = 'Header',
	headerSize = 'medium',
	containerSlot,
	customAction,
	icon,
	primaryActionType = 'none',
	onPrimaryAction,
	secondaryActionType = 'none',
	buttonPrimaryLabel = 'Primary',
	buttonSecondaryLabel = 'Secondary',
	onButtonPrimaryClick,
	onButtonSecondaryClick,
	dropdownLabel = 'Dropdown',
	onDropdownClick,
	chipLabels = [],
	onChipClick,
	selectionSelected = false,
	selectionLabel = 'Selected',
	onSelectionChange,
	descriptionSubheader,
	descriptionText,
	footerProps,
	children,
	collapsed,
	...rest
}) => {
	const theme = useTheme();

	const grayPalette = theme.palette.grey;

	const customGrayPalette = {
		main: grayPalette[400],
		dark: grayPalette[900],
	};

	const palette =
		color === 'gray' ? customGrayPalette : theme.palette[color] || theme.palette.primary;

	const headerColorMap = {
		low: lighten(palette.main, 0.8),
		medium: lighten(palette.main, 0.1),
		high: palette.dark,
	};

	const headerBackgroundColor = headerColorMap[emphasis];
	const footerBackgroundColor = lighten(palette.main, 0.9);
	const textColor = emphasis === 'medium' || emphasis === 'high' ? 'white' : palette.dark;

	const borderColor =
		color === 'gray'
			? emphasis === 'high'
				? customGrayPalette.main
				: lighten(customGrayPalette.main, 0.8)
			: emphasis === 'high'
				? palette.main
				: lighten(palette.main, 0.8);

	// Render primary action button
	const renderPrimaryAction = () => {
		if (primaryActionType === 'none') return null;

		let icon;
		switch (primaryActionType) {
			case 'Edit':
				icon = <Edit size={18} />;
				break;
			case 'Add':
				icon = <Plus size={18} />;
				break;
			case 'Delete':
				icon = <X size={18} />;
				break;
			case 'collapse':
				icon = collapsed ? <ChevronDown size={18} /> : <ChevronUp size={18} />;
				break;
			default:
				return null;
		}

		return (
			<IconButton
				size="small"
				sx={{
					color: textColor,
				}}
				onClick={onPrimaryAction}>
				{icon}
			</IconButton>
		);
	};

	// Render secondary action
	const renderSecondaryAction = () => {
		switch (secondaryActionType) {
			case 'Buttons':
				return (
					<Stack
						direction="row"
						spacing={1}>
						<IconButton
							size="small"
							sx={{
								color: textColor,
								border: '1px solid',
								borderColor: 'rgba(94, 53, 177, 0.5)',
								borderRadius: '4px',
								padding: '4px 8px',
								fontSize: '0.75rem',
							}}
							onClick={onButtonSecondaryClick}>
							{buttonSecondaryLabel}
						</IconButton>
						<IconButton
							size="small"
							sx={{
								color: textColor,
								border: '1px solid',
								borderColor: 'rgba(94, 53, 177, 0.5)',
								borderRadius: '4px',
								padding: '4px 8px',
								fontSize: '0.75rem',
								bgcolor: 'rgba(94, 53, 177, 0.1)',
							}}
							onClick={onButtonPrimaryClick}>
							{buttonPrimaryLabel}
						</IconButton>
					</Stack>
				);

			case 'Dropdown':
				return (
					<IconButton
						size="small"
						sx={{
							color: textColor,
							border: '1px solid',
							borderColor: 'rgba(0, 0, 0, 0.23)',
							borderRadius: '4px',
							padding: '4px 8px',
							fontSize: '0.75rem',
							display: 'flex',
							alignItems: 'center',
							gap: '4px',
						}}
						onClick={onDropdownClick}>
						{dropdownLabel}
						<Box
							component="span"
							sx={{
								width: 0,
								height: 0,
								borderLeft: '4px solid transparent',
								borderRight: '4px solid transparent',
								borderTop: '4px solid currentColor',
								ml: 0.5,
							}}
						/>
					</IconButton>
				);

			case 'Chips':
				return (
					<Stack
						direction="row"
						spacing={0.5}>
						{chipLabels.map((item, index) => {
							if (typeof item === 'string') {
								return (
									<Chip
										key={index}
										label={item}
										size="small"
										onClick={() => onChipClick?.(item)}
										sx={{
											height: '24px',
											fontSize: '0.75rem',
											bgcolor: color === 'gray' ? 'rgba(0, 0, 0, 0.08)' : `${color}.light`,
											'&:hover': {
												bgcolor: color === 'gray' ? 'rgba(0, 0, 0, 0.12)' : `${color}.main`,
											},
										}}
									/>
								);
							}
							return (
								<Chip
									key={index}
									label={item.label}
									icon={item.icon}
									size="small"
									variant="outlined"
									onClick={() => onChipClick?.(item.label)}
									sx={(theme) => ({
										height: '24px',
										fontSize: '0.75rem',
										//color: theme.palette.common.white,
										borderRadius: theme.shape.borderRadius,
										borderColor: color === 'gray' ? 'rgba(0, 0, 0, 0.23)' : `${color}.main`,
										//bgcolor: color === 'gray' ? 'rgba(0, 0, 0, 0.08)' : `${color}.light`,
										'&:hover': {
											bgcolor: color === 'gray' ? 'rgba(0, 0, 0, 0.12)' : `${color}.main`,
										},
										'& .MuiChip-icon': {
											//color: theme.palette.common.white,
										},
									})}
								/>
							);
						})}
					</Stack>
				);

			case 'Selection':
				return (
					<Box
						sx={{
							display: 'flex',
							alignItems: 'center',
							border: '1px solid',
							borderColor: color === 'gray' ? '#422F7D' : `${color}.main`,
							borderRadius: '15.5px',
							padding: '4px',
							cursor: 'pointer',
							height: '31px',
						}}
						onClick={() => onSelectionChange?.(!selectionSelected)}>
						{selectionSelected && (
							<Box
								sx={{
									width: '24px',
									height: '24px',
									borderRadius: '12px',
									bgcolor: color === 'gray' ? '#422F7D' : `${color}.main`,
									display: 'flex',
									alignItems: 'center',
									justifyContent: 'center',
									mr: 1,
								}}>
								<Box
									component="span"
									sx={{
										width: '10px',
										height: '5px',
										borderLeft: '2px solid white',
										borderBottom: '2px solid white',
										transform: 'rotate(-45deg)',
										position: 'relative',
										top: '-1px',
									}}
								/>
							</Box>
						)}
						<Typography
							variant="caption"
							sx={{
								px: 1,
								fontWeight: 500,
							}}>
							{selectionLabel}
						</Typography>
					</Box>
				);

			default:
				return null;
		}
	};

	// Render description if provided
	const renderDescription = () => {
		if (!descriptionSubheader && !descriptionText) return null;

		return (
			<Box
				sx={(theme) => ({
					px: 2,
					p: 1,
					borderBottom: '1px solid',
					borderColor: 'divider',
					backgroundColor: lighten(palette.main, 0.9),
					color: theme.palette.text.primary,
				})}>
				{descriptionSubheader && (
					<Typography
						variant="subtitle1"
						gutterBottom>
						{descriptionSubheader}
					</Typography>
				)}
				{descriptionText && <Typography variant="body2">{descriptionText}</Typography>}
			</Box>
		);
	};

	return (
		<Card sx={{ border: `1px solid ${borderColor}` }}>
			<CardHeader
				title={title}
				avatar={icon}
				size={headerSize}
				sx={{
					backgroundColor: headerBackgroundColor,
					color: textColor,
				}}
				action={
					<Stack
						direction="row"
						spacing={1}
						alignItems="center"
						sx={{ height: '100%' }}>
						{customAction}
						{renderSecondaryAction()}
						{renderPrimaryAction()}
					</Stack>
				}
			/>
			{renderDescription()}
			{containerSlot}
			{children ? (
				<CardContent
					sx={{
						borderTop:
							!descriptionSubheader && !descriptionText
								? `1px solid ${headerColorMap.low}`
								: 'none',
						borderBottom: `1px solid ${headerColorMap.low}`,
					}}
					{...rest}>
					{children}
				</CardContent>
			) : null}
			{Object.keys(footerProps || {}).length > 0 && (
				<MainCardFooter
					backgroundColor={footerBackgroundColor}
					color={color}
					{...footerProps}
				/>
			)}
		</Card>
	);
};

export default MainCardContainer;
