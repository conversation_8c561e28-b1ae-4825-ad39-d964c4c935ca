import {
	getApiInsuranceByIdOptions,
	getApiStudyByIdOptions,
} from '@client/workflows/@tanstack/react-query.gen';
import ChipSummary from '@components/chip-summary';
import MainCardContainer from '@components/main-container/main-card-container';
import { Typography } from '@mui/material';
import { useQueries, useQuery } from '@tanstack/react-query';
import { map, some } from 'lodash';

interface InsuranceSummaryProps {
	studyId: string;
}

export default function InsuranceSummary({ studyId }: InsuranceSummaryProps) {
	const { data: studyData } = useQuery({
		...getApiStudyByIdOptions({
			path: { id: studyId! },
			responseType: 'json',
		}),
		enabled: !!studyId,
	});

	const { insurances } = studyData ?? {};

	const { data: insuranceData, isPending: isFetchingInsuranceData } = useQueries({
		queries: map(insurances, (insurance) => ({
			...getApiInsuranceByIdOptions({
				path: { id: insurance! },
				responseType: 'json',
			}),
			enabled: !!insurance,
		})),
		combine: (results) => {
			return {
				data: map(results, (result) => result.data),
				isPending: some(results, (result) => result.isPending),
			};
		},
	});

	if (isFetchingInsuranceData) {
		return (
			<MainCardContainer
				title="Insurance Information"
				color="primary"
				emphasis="low">
				<Typography
					variant="body1"
					color="primary">
					Loading insurance data...
				</Typography>
			</MainCardContainer>
		);
	}

	if (!insuranceData) {
		return (
			<MainCardContainer
				title="Insurance Information"
				color="error"
				emphasis="low">
				<Typography
					variant="body1"
					color="error">
					Error loading insurance data
				</Typography>
			</MainCardContainer>
		);
	}

	return map(insuranceData, (insurance) => (
		<MainCardContainer
			title="Insurance Information"
			color="primary"
			emphasis="low">
			<ChipSummary
				items={[
					{
						label: 'Insurance ID',
						value: insurance?.id ? insurance?.id : '',
					},
					{
						label: 'Insurance Carrier',
						value: insurance?.insuranceCarrier ? insurance?.insuranceCarrier.toString() : '',
					},
					{
						label: 'Policy ID',
						value: insurance?.policyId ? insurance?.policyId : '',
					},
					{
						label: 'Group Number',
						value: insurance?.groupNumber ? insurance?.groupNumber : '',
					},
					{
						label: 'Member ID',
						value: insurance?.memberId ? insurance?.memberId : '',
					},
				]}
			/>
		</MainCardContainer>
	));
}
