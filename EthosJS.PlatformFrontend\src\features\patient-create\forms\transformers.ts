import { z } from 'zod';
import { AddressWithUseType, PatientBasicInformationData } from '../types/form-types';
import { EthosWorkflowsApiAddressDto, EthosWorkflowsApiAddressWithUseDto } from '@client/workflows';

const patientInformationTransformer = z
	.any()
	.transform((raw: PatientBasicInformationData['patientInformation']) => {
		if (!raw) {
			return raw;
		}
		return {
			...raw,
			middleName: raw.middleName === '' ? null : raw.middleName,
			mrn: raw.mrn === '' ? null : raw.mrn,
		};
	});

const demographicsTransformer = z
	.any()
	.transform((raw: PatientBasicInformationData['demographics']) => {
		return {
			...raw,
		};
	});

const physicalMeasurementsTransformer = z
	.any()
	.transform((raw: PatientBasicInformationData['physicalMeasurements']) => {
		if (!raw) {
			return raw;
		}
		return {
			...raw,
			bmi: raw.bmi ? Number(raw.bmi) : 0,
			heightInches: raw.heightInches ? Number(raw.heightInches) : 0,
			weightPounds: raw.weightPounds ? Number(raw.weightPounds) : 0,
			neckSize: raw.neckSize ? Number(raw.neckSize) : 0,
		};
	});

const patientBasicInformationSchema = z.object({
	patientInformation: patientInformationTransformer,
	demographics: demographicsTransformer,
	physicalMeasurements: physicalMeasurementsTransformer,
});

function patientBasicInformationTransformer<T>(data: T): PatientBasicInformationData {
	const { data: parsed } = patientBasicInformationSchema.safeParse(data);
	return parsed as PatientBasicInformationData;
}

const addressTrasnsformer = z.any().transform((raw: EthosWorkflowsApiAddressDto) => {
	return {
		...raw,
		line2: !raw.line2 ? null : raw.line2,
	};
});

const addressWithUseTypeTransformer = z.any().transform((raw: AddressWithUseType) => {
	return {
		...raw,
		address: addressTrasnsformer.parse(raw.address),
	};
});

const addressWithUseTransformer = z.any().transform((raw: EthosWorkflowsApiAddressWithUseDto) => {
	if (!raw.address) {
		return raw;
	}
	return {
		...raw,
		address: addressTrasnsformer.parse(raw.address),
	};
});

function addressesWithUseTypeTransformer(data: AddressWithUseType[]) {
	return data.map((item) => addressWithUseTypeTransformer.parse(item));
}

function addressesWithUseTransformer(data: EthosWorkflowsApiAddressWithUseDto) {
	return addressWithUseTransformer.parse(data);
}

export {
	patientBasicInformationSchema,
	patientBasicInformationTransformer,
	addressesWithUseTypeTransformer,
	addressesWithUseTransformer,
};
