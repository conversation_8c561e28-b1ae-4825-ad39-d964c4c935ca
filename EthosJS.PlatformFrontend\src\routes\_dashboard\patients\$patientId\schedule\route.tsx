import { Status } from '@components/left-menu';
import { Box } from '@mui/material';
import { createFileRoute, Outlet, useLocation } from '@tanstack/react-router';
import useScheduleStore from '@hooks/use-schedule-store';
import { useStore } from '@tanstack/react-store';
import SmartButtonOutlinedIcon from '@mui/icons-material/SmartButtonOutlined';
import PermPhoneMsgOutlinedIcon from '@mui/icons-material/PermPhoneMsgOutlined';
import { z } from 'zod';
import SchedulingMenu from '@features/scheduling/components/menu';
import Card from '@components/card';

const validateSearch = z.object({
	orderId: z.string().optional(),
	studyId: z.string().optional(),
});

export const Route = createFileRoute('/_dashboard/patients/$patientId/schedule')({
	component: RouteComponent,
	validateSearch,
});

export const scheduleSteps = [
	{
		name: 'Appointment Creation',
		key: 'AppointmentCreation',
		icon: SmartButtonOutlinedIcon,
		stepNumber: 1,
		status: Status.None,
		from: undefined,
		to: 'AppointmentCreated',
		data: {},
		navigationURL: `/patients/$patientId/schedule/appointment-creation/dashboard`,
	},
	{
		name: 'Follow-up Call',
		key: 'follow-up-call',
		icon: PermPhoneMsgOutlinedIcon,
		status: Status.None,
		from: undefined,
		stepNumber: 2,
		to: 'AppointmentCreated',
		data: {},
		navigationURL: `/patients/$patientId/schedule/follow-up-call`,
	},
];

function RouteComponent() {
	const { patientId } = Route.useParams();
	const { orderId, studyId } = Route.useSearch();
	const navigate = Route.useNavigate();
	const { pathname } = useLocation();

	const { store, actions } = useScheduleStore();

	const { steps } = useStore(store, (state) => state);

	const onNavigateStep = (selectedStep: (typeof scheduleSteps)[number]) => {
		const search = { orderId: orderId, studyId: studyId };
		navigate({
			to: selectedStep.navigationURL,
			params: { patientId },
			search,
		});
	};

	const onFieldChange = (fieldName: string, fieldValue: string) => {
		navigate({
			to: '/patients/$patientId/schedule/appointment-creation/dashboard',
			params: { patientId },
			search: (prev) => ({ ...prev, [fieldName]: fieldValue }),
		});
	};

	return (
		<Card
			sx={{
				flex: 1,
				minHeight: 0,
				display: 'flex',
				gap: 2,
				p: 2,
				pb: 0,
				borderRadius: 2,
				borderBottomLeftRadius: 0,
				borderBottomRightRadius: 0,
			}}>
			<SchedulingMenu
				patientId={patientId}
				orderId={orderId!}
				studyId={studyId!}
				activePath={pathname}
				onClick={(path) => {
					navigate({
						to: `/patients/$patientId/schedule${path}`,
						params: { patientId },
						search: { orderId, studyId },
					});
				}}
			/>
			<Box sx={styles.outletContent}>
				<Outlet />
			</Box>
		</Card>
	);
}

const styles = {
	cardContent: {
		overflow: 'auto',
		minHeight: 0,
		flex: 1,
		display: 'flex',
		gap: 2,
		height: '100%',
	},
	cardContentInner: { overflow: 'auto', height: '100%' },
	outletContent: { flex: 1, overflow: 'auto', height: '100%' },
};
