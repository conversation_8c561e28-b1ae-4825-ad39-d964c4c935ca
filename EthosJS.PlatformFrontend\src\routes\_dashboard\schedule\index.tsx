import {
  Add,
  Schedule,
  FilterAlt,
  Search,
  ScheduleOutlined,
} from "@mui/icons-material";
import {
  Box,
  Button,
  Chip,
  FormControl,
  InputAdornment,
  InputLabel,
  MenuItem,
  Popover,
  Select,
  Stack,
  TextField,
  Typography,
} from "@mui/material";
import { useQuery } from "@tanstack/react-query";
import { createFileRoute } from "@tanstack/react-router";
import { useEffect, useMemo, useRef, useState } from "react";
import LoadingComponent from "@components/loading-component";
import PageContainer from "@components/page-container";
import { debounce } from "lodash";
import { ArrowUpward } from "@mui/icons-material";
import {
  DataGridPro,
  GridColDef,
  GridFilterModel,
  useGridApiRef,
} from "@mui/x-data-grid-pro";
import { paginationQueryParams } from "@utils/query";
import { EthosWorkflowsApiOrderDto } from "@client/workflows";
import { postApiOrderSearchOptions } from "@client/workflows/@tanstack/react-query.gen";
import StyledCardFooter from "@components/card-footer";
import { PatientCreate, PatientRead } from "@auth/scopes";

// Schedule data type for display
interface ScheduleDisplayData {
  id: string;
  patientName: string;
  patientId: string;
  patientDOB: string;
  patientEmail: string;
  patientContact: string;
  orderId: string;
  studyType: string;
  physicianName: string;
  locationName: string;
  appointmentDate: string;
  appointmentTime: string;
  scheduledBy: string;
  createdDate: string;
  duration: string;
  locationPhone: string;
  locationAddress1: string;
  locationCityStateZip: string;
  status: string;
  note1: string;
  note2: string;
}

// Sample schedule data for demonstration
const sampleScheduleData: ScheduleDisplayData[] = [
  {
    id: "SCH-2024-001",
    patientName: "Sarah Johnson",
    patientId: "PAT-001",
    patientDOB: "1985-03-15",
    patientEmail: "<EMAIL>",
    patientContact: "(*************",
    orderId: "ORD-2024-001",
    studyType: "Sleep Study",
    physicianName: "Dr. Michael Chen",
    locationName: "Tukwila Sleep Center",
    appointmentDate: "October 22, 2025 at 10:36 AM",
    appointmentTime: "11:00 AM - 5:00 PM",
    scheduledBy: "John Scott",
    createdDate: "3/16/2025",
    duration: "6 hours",
    locationPhone: "(*************",
    locationAddress1: "12500 Tukwila International Blvd",
    locationCityStateZip: "Seattle, WA 98168",
    status: "Scheduled",
    note1: "Patient confirmed appointment",
    note2: "Bring insurance card",
  },
  {
    id: "SCH-2024-002",
    patientName: "Robert Martinez",
    patientId: "PAT-002",
    patientDOB: "1978-11-22",
    patientEmail: "<EMAIL>",
    patientContact: "(*************",
    orderId: "ORD-2024-002",
    studyType: "Home Sleep Test",
    physicianName: "Dr. Lisa Wang",
    locationName: "Seattle Sleep Clinic",
    appointmentDate: "November 15, 2025 at 2:15 PM",
    appointmentTime: "2:00 PM - 4:00 PM",
    scheduledBy: "Maria Garcia",
    createdDate: "3/18/2025",
    duration: "2 hours",
    locationPhone: "(*************",
    locationAddress1: "1500 Madison St",
    locationCityStateZip: "Seattle, WA 98104",
    status: "Confirmed",
    note1: "Equipment pickup required",
    note2: "Call 24hrs before",
  },
  {
    id: "SCH-2024-003",
    patientName: "Emily Davis",
    patientId: "PAT-003",
    patientDOB: "1992-07-08",
    patientEmail: "<EMAIL>",
    patientContact: "(*************",
    orderId: "ORD-2024-003",
    studyType: "MSLT",
    physicianName: "Dr. Sarah Thompson",
    locationName: "Bellevue Sleep Center",
    appointmentDate: "December 3, 2025 at 9:00 AM",
    appointmentTime: "9:00 AM - 3:00 PM",
    scheduledBy: "David Wilson",
    createdDate: "3/20/2025",
    duration: "6 hours",
    locationPhone: "(*************",
    locationAddress1: "2000 116th Ave NE",
    locationCityStateZip: "Bellevue, WA 98004",
    status: "Cancelled",
    note1: "Patient requested reschedule",
    note2: "Insurance authorization pending",
  },
  {
    id: "SCH-2024-004",
    patientName: "David Wilson",
    patientId: "PAT-004",
    patientDOB: "1965-12-03",
    patientEmail: "<EMAIL>",
    patientContact: "(*************",
    orderId: "ORD-2024-004",
    studyType: "CPAP Titration",
    physicianName: "Dr. Michael Chen",
    locationName: "Tukwila Sleep Center",
    appointmentDate: "January 10, 2026 at 8:30 PM",
    appointmentTime: "8:30 PM - 6:30 AM",
    scheduledBy: "Jennifer Lee",
    createdDate: "3/22/2025",
    duration: "10 hours",
    locationPhone: "(*************",
    locationAddress1: "12500 Tukwila International Blvd",
    locationCityStateZip: "Seattle, WA 98168",
    status: "Completed",
    note1: "Study completed successfully",
    note2: "Results available in 3-5 days",
  },
];

// Helper function to get schedule status chip color and style
const getScheduleStatusChipProps = (status: string) => {
  switch (status?.toLowerCase()) {
    case "scheduled":
      return { color: "primary" as const, variant: "filled" as const };
    case "confirmed":
      return { color: "success" as const, variant: "filled" as const };
    case "completed":
      return { color: "success" as const, variant: "outlined" as const };
    case "cancelled":
      return { color: "error" as const, variant: "filled" as const };
    default:
      return { color: "default" as const, variant: "filled" as const };
  }
};

const columns: GridColDef<ScheduleDisplayData>[] = [
  {
    field: "patientDetails",
    headerName: "Patient Details",
    flex: 1,
    minWidth: 200,
    sortable: true,
    renderHeader: () => (
      <Box
        sx={{
          display: "flex",
          fontWeight: "medium",
          alignItems: "center",
          gap: 0.5,
          fontSize: "0.875rem",
          padding: "16px",
        }}
      >
        Patient Details
      </Box>
    ),
    renderCell: (params) => {
      const row = params.row;

      return (
        <Box
          sx={{ height: "100%", fontSize: "0.875rem" }}
          color="black"
          padding={"16px"}
          display={"flex"}
          flexDirection={"column"}
          justifyContent={"center"}
        >
          <Typography
            variant="body2"
            sx={{ fontWeight: "bold", lineHeight: 1.2, mb: 0.5 }}
          >
            {row.patientName}
          </Typography>
          <Typography
            variant="body2"
            sx={{ fontWeight: "regular", lineHeight: 1.2, mb: 0.5 }}
          >
            {row.patientId} • DOB: {row.patientDOB}
          </Typography>
          <Typography
            variant="body2"
            sx={{ fontWeight: "regular", lineHeight: 1.2, mb: 0.5 }}
          >
            {row.patientEmail}
          </Typography>
          <Typography
            variant="body2"
            sx={{ fontWeight: "regular", lineHeight: 1.2 }}
          >
            {row.patientContact}
          </Typography>
        </Box>
      );
    },
  },
  {
    field: "studyDetails",
    headerName: "Study Details",
    flex: 1,
    minWidth: 200,
    renderHeader: () => (
      <Box
        sx={{
          display: "flex",
          fontWeight: "medium",
          alignItems: "center",
          gap: 0.5,
          fontSize: "0.875rem",
          padding: "16px",
        }}
      >
        Study Details
      </Box>
    ),
    renderCell: (params) => {
      const row = params.row;

      return (
        <Box
          sx={{ height: "100%", fontSize: "0.875rem" }}
          color="black"
          padding={"16px"}
          display={"flex"}
          flexDirection={"column"}
          justifyContent={"center"}
        >
          <Typography
            variant="body2"
            sx={{ fontWeight: "bold", lineHeight: 1.2, mb: 0.5 }}
          >
            Order ID: {row.orderId}
          </Typography>
          <Typography
            variant="body2"
            sx={{ fontWeight: "regular", lineHeight: 1.2, mb: 0.5 }}
          >
            {row.studyType}
          </Typography>
          <Typography
            variant="body2"
            sx={{ fontWeight: "regular", lineHeight: 1.2, mb: 0.5 }}
          >
            {row.physicianName}
          </Typography>
          <Typography
            variant="body2"
            sx={{ fontWeight: "regular", lineHeight: 1.2 }}
          >
            {row.locationName}
          </Typography>
        </Box>
      );
    },
  },
  {
    field: "appointmentDetails",
    headerName: "Appointment Details",
    flex: 1,
    minWidth: 200,
    renderHeader: () => (
      <Box
        sx={{
          display: "flex",
          fontWeight: "medium",
          alignItems: "center",
          gap: 0.5,
          fontSize: "0.875rem",
          padding: "16px",
        }}
      >
        Appointment Details
      </Box>
    ),
    renderCell: (params) => {
      const row = params.row;

      return (
        <Box
          sx={{ height: "100%", fontSize: "0.875rem" }}
          color="black"
          padding={"16px"}
          display={"flex"}
          flexDirection={"column"}
          justifyContent={"center"}
        >
          <Typography
            variant="body2"
            sx={{ fontWeight: "bold", lineHeight: 1.2, mb: 0.5 }}
          >
            {row.appointmentDate}
          </Typography>
          <Typography
            variant="body2"
            sx={{ fontWeight: "regular", lineHeight: 1.2, mb: 0.5 }}
          >
            Time: {row.appointmentTime}
          </Typography>
          <Typography
            variant="body2"
            sx={{ fontWeight: "regular", lineHeight: 1.2, mb: 0.5 }}
          >
            Scheduled By: {row.scheduledBy}
          </Typography>
          <Typography
            variant="body2"
            sx={{ fontWeight: "regular", lineHeight: 1.2, mb: 0.5 }}
          >
            Created: {row.createdDate}
          </Typography>
          <Typography
            variant="body2"
            sx={{ fontWeight: "regular", lineHeight: 1.2 }}
          >
            Duration: {row.duration}
          </Typography>
        </Box>
      );
    },
  },
  {
    field: "locationDetails",
    headerName: "Location Details",
    flex: 1,
    minWidth: 200,
    renderHeader: () => (
      <Box
        sx={{
          display: "flex",
          fontWeight: "medium",
          alignItems: "center",
          gap: 0.5,
          fontSize: "0.875rem",
          padding: "16px",
        }}
      >
        Location Details
      </Box>
    ),
    renderCell: (params) => {
      const row = params.row;

      return (
        <Box
          sx={{ height: "100%", fontSize: "0.875rem" }}
          color="black"
          padding={"16px"}
          display={"flex"}
          flexDirection={"column"}
          justifyContent={"center"}
        >
          <Typography
            variant="body2"
            sx={{ fontWeight: "bold", lineHeight: 1.2, mb: 0.5 }}
          >
            {row.locationName}
          </Typography>
          <Typography
            variant="body2"
            sx={{ fontWeight: "regular", lineHeight: 1.2, mb: 0.5 }}
          >
            Phone: {row.locationPhone}
          </Typography>
          <Typography
            variant="body2"
            sx={{ fontWeight: "regular", lineHeight: 1.2, mb: 0.5 }}
          >
            {row.locationAddress1}
          </Typography>
          <Typography
            variant="body2"
            sx={{ fontWeight: "regular", lineHeight: 1.2 }}
          >
            {row.locationCityStateZip}
          </Typography>
        </Box>
      );
    },
  },
  {
    field: "status",
    headerName: "Status",
    flex: 1,
    minWidth: 200,
    renderHeader: () => (
      <Box
        sx={{
          display: "flex",
          fontWeight: "medium",
          alignItems: "center",
          gap: 0.5,
          fontSize: "0.875rem",
          padding: "16px",
        }}
      >
        Status
      </Box>
    ),
    renderCell: (params) => {
      const row = params.row;
      const chipProps = getScheduleStatusChipProps(row.status);

      return (
        <Box
          sx={{ height: "100%", fontSize: "0.875rem" }}
          color="black"
          padding={"16px"}
          display={"flex"}
          flexDirection={"column"}
          justifyContent={"center"}
        >
          <Box sx={{ mb: 0.5 }}>
            <Chip
              label={row.status}
              size="small"
              {...chipProps}
              sx={{
                fontSize: "0.8125rem",
                fontWeight: "regular",
                borderRadius: 5,
              }}
            />
          </Box>
          <Typography
            variant="body2"
            sx={{ fontWeight: "regular", lineHeight: 1.2, mb: 0.5 }}
          >
            {row.note1}
          </Typography>
          <Typography
            variant="body2"
            sx={{ fontWeight: "regular", lineHeight: 1.2 }}
          >
            {row.note2}
          </Typography>
        </Box>
      );
    },
  },
];

interface SearchParams {
  Status: string;
}

interface SearchDeps extends Partial<SearchParams> {
  DateOfBirth?: string;
  StudyDate?: string;
}

export const Route = createFileRoute("/_dashboard/schedule/")({
  component: RouteComponent,
  pendingComponent: () => <LoadingComponent />,
  loader: ({ context: { queryClient } }) =>
    queryClient.ensureQueryData(
      postApiOrderSearchOptions({
        scopes: [PatientCreate.value, PatientRead.value],
        responseType: "json",
      })
    ),
});

interface FilterItem {
  column: string;
  operator: string;
  value: string;
}

function RouteComponent() {
  const navigate = Route.useNavigate();

  const [paginationModel, setPaginationModel] = useState({
    pageSize: 100,
    page: 0,
  });

  const [userSearchValue, setUserSearchValue] = useState("");
  const [searchValue, setSearchValue] = useState("");
  const debouncedSearch = useMemo(
    () =>
      debounce((value: string) => {
        setSearchValue(value);
      }, 300),
    []
  );

  const [searchParams, setSearchParams] = useState<SearchParams>({
    Status: "all",
  });

  const statusOptions = [
    { label: "All Status", value: "all" },
    { label: "Scheduled", value: "Scheduled" },
    { label: "Confirmed", value: "Confirmed" },
    { label: "Completed", value: "Completed" },
    { label: "Cancelled", value: "Cancelled" },
  ];

  // Build search parameters from current state
  const searchQueryParams = useMemo(() => {
    const params: any = {};

    // Add search value for schedule search
    if (searchValue) {
      // Search in patient name, appointment details, etc.
      params.searchTerm = searchValue;
    }

    // Add status filter if not "all"
    if (searchParams.Status && searchParams.Status !== "all") {
      params.Status = searchParams.Status;
    }

    return Object.keys(params).length > 0 ? params : undefined;
  }, [searchValue, searchParams.Status]);

  // For now, we'll use sample data instead of API
  // const { data, isFetching } = useQuery(
  //   postApiScheduleSearchOptions({
  //     query: {
  //       ...paginationQueryParams(paginationModel),
  //       ...searchQueryParams,
  //     },
  //     scopes: [PatientCreate.value, PatientRead.value],
  //     responseType: 'json',
  //   }),
  // )

  const rowCountRef = useRef(sampleScheduleData.length || 0);
  const rowCount = useMemo(() => {
    rowCountRef.current = sampleScheduleData.length;
    return rowCountRef.current;
  }, [sampleScheduleData.length]);

  const [filters, setFilters] = useState<FilterItem[]>([]);
  const [filterAnchorEl, setFilterAnchorEl] = useState<HTMLElement | null>(
    null
  );
  const [filterColumn, setFilterColumn] = useState("");
  const [filterOperator, setFilterOperator] = useState("contains");
  const [filterValue, setFilterValue] = useState("");

  const filterColumns = [
    { value: "patientDetails", label: "Patient Details" },
    { value: "studyDetails", label: "Study Details" },
    { value: "appointmentDetails", label: "Appointment Details" },
    { value: "locationDetails", label: "Location Details" },
    { value: "status", label: "Status" },
  ];

  const filterOperators = [
    { value: "contains", label: "Contains" },
    { value: "equals", label: "Equals" },
    { value: "startsWith", label: "Starts with" },
    { value: "endsWith", label: "Ends with" },
  ];

  const handleFilterClick = (event: React.MouseEvent<HTMLElement>) => {
    setFilterAnchorEl(event.currentTarget);
  };

  const handleFilterClose = () => {
    setFilterAnchorEl(null);
    setFilterColumn("");
    setFilterOperator("contains");
    setFilterValue("");
  };

  const handleAddFilter = () => {
    if (filterColumn && filterValue) {
      const newFilter: FilterItem = {
        column: filterColumn,
        operator: filterOperator,
        value: filterValue,
      };
      setFilters([...filters, newFilter]);
      handleFilterClose();
    }
  };

  const handleRemoveFilter = (index: number) => {
    setFilters(filters.filter((_, i) => i !== index));
  };

  const apiRef = useGridApiRef();

  useEffect(() => {
    if (apiRef.current) {
      apiRef.current.setRowSelectionModel([]);
    }
  }, []);

  return (
    <PageContainer
      title="Schedule"
      icon={ScheduleOutlined}
      actions={
        <Stack direction="row" alignItems="center" spacing={"16px"}>
          <TextField
            value={userSearchValue}
            onChange={(e) => {
              setUserSearchValue(e.target.value);
              debouncedSearch(e.target.value);
            }}
            variant="outlined"
            placeholder="Search"
            size="small"
            sx={{ width: "300px" }}
            slotProps={{
              input: {
                startAdornment: (
                  <InputAdornment position="start">
                    <Search />
                  </InputAdornment>
                ),
              },
            }}
          />

          <Button
            variant="outlined"
            startIcon={<FilterAlt />}
            onClick={handleFilterClick}
            sx={{ height: "40px", minWidth: "100px" }}
          >
            Filter
          </Button>

          <Popover
            open={Boolean(filterAnchorEl)}
            anchorEl={filterAnchorEl}
            onClose={handleFilterClose}
            anchorOrigin={{
              vertical: "bottom",
              horizontal: "left",
            }}
            transformOrigin={{
              vertical: "top",
              horizontal: "left",
            }}
          >
            <Box sx={{ p: 2, minWidth: 300 }}>
              <Stack spacing={2}>
                <FormControl fullWidth size="small">
                  <InputLabel>Column</InputLabel>
                  <Select
                    value={filterColumn}
                    label="Column"
                    onChange={(e) => setFilterColumn(e.target.value)}
                  >
                    {filterColumns.map((col) => (
                      <MenuItem key={col.value} value={col.value}>
                        {col.label}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>

                <FormControl fullWidth size="small">
                  <InputLabel>Operator</InputLabel>
                  <Select
                    value={filterOperator}
                    label="Operator"
                    onChange={(e) => setFilterOperator(e.target.value)}
                  >
                    {filterOperators.map((op) => (
                      <MenuItem key={op.value} value={op.value}>
                        {op.label}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>

                <TextField
                  fullWidth
                  size="small"
                  label="Value"
                  value={filterValue}
                  onChange={(e) => setFilterValue(e.target.value)}
                />

                <Stack direction="row" spacing={1} justifyContent="flex-end">
                  <Button variant="outlined" onClick={handleFilterClose}>
                    Cancel
                  </Button>
                  <Button variant="contained" onClick={handleAddFilter}>
                    Add Filter
                  </Button>
                </Stack>
              </Stack>
            </Box>
          </Popover>

          <FormControl size="small" sx={{ minWidth: 150 }}>
            <InputLabel>Status</InputLabel>
            <Select
              value={searchParams.Status}
              label="Status"
              onChange={(e) =>
                setSearchParams({ ...searchParams, Status: e.target.value })
              }
            >
              {statusOptions.map((option) => (
                <MenuItem key={option.value} value={option.value}>
                  {option.label}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Stack>
      }
    >
      {/* Filter Chips */}
      {filters.length > 0 && (
        <Box sx={{ mb: 2 }}>
          <Stack direction="row" spacing={1} flexWrap="wrap">
            {filters.map((filter, index) => (
              <Chip
                key={index}
                label={`${filter.column} ${filter.operator} "${filter.value}"`}
                onDelete={() => handleRemoveFilter(index)}
                variant="outlined"
                size="small"
              />
            ))}
          </Stack>
        </Box>
      )}

      {/* Data Grid */}
      <Box
        sx={{
          height: "calc(100vh - 300px)",
          width: "100%",
          "& .MuiDataGrid-root": {
            border: "none",
          },
          "& .MuiDataGrid-cell": {
            borderBottom: "none",
          },
          "& .MuiDataGrid-columnHeaders": {
            backgroundColor: "#f5f5f5",
            borderBottom: "none",
          },
          "& .MuiDataGrid-virtualScroller": {
            backgroundColor: "#ffffff",
          },
          "& .MuiDataGrid-footerContainer": {
            borderTop: "none",
            backgroundColor: "#f5f5f5",
          },
        }}
      >
        <DataGridPro
          className="custom-data-grid"
          loading={false}
          rows={sampleScheduleData}
          rowCount={sampleScheduleData.length}
          paginationMode="client"
          paginationModel={paginationModel}
          onPaginationModelChange={setPaginationModel}
          pageSizeOptions={[25, 50, 100]}
          columns={columns}
          apiRef={apiRef}
          getRowHeight={() => "auto"}
          sx={{
            "& .MuiDataGrid-row:hover": {
              backgroundColor: "#f5f5f5",
              cursor: "pointer",
            },
          }}
          slots={{
            footer: () => (
              <StyledCardFooter>
                <Typography variant="body2" color="text.secondary">
                  Showing {sampleScheduleData.length} schedule entries
                </Typography>
              </StyledCardFooter>
            ),
          }}
        />
      </Box>
    </PageContainer>
  );
}
