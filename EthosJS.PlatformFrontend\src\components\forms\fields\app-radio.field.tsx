
import { useFieldContext } from "@hooks/form-context";
import { FieldPropType } from "./FieldPropType";
import Card from "@components/card";
import CardHeader from "@components/card-header";
import {
   CardContent,
   Grid,
   GridProps,
   List,
   ListItem,
   Radio as MuiRadio,
   <PERSON>ack,
   Typography,
   Checkbox as Mui<PERSON>heckbox
} from "@mui/material";

type OptionTypes = {
   title: string
   description: string
   value: string | number
}

interface AppRadioFieldProps extends FieldPropType {
   options: Array<OptionTypes>
   isMulti?: boolean;
   onValueChange?: (value: string | string[]) => void;
}

const GRID_ITEM_PROPS: Partial<GridProps> = {
   sm: 12
}

export default function AppRadioField({
   label,
   options,
   isMulti = false,
   onValueChange
}: AppRadioFieldProps) {
   const field = useFieldContext<string | string[]>();

   const onSelect = (selectedItem: OptionTypes) => {
      if (isMulti) {
         const currentSelected = Array.isArray(field.state.value) ? field.state.value : [];
         const newSelected = currentSelected.includes(String(selectedItem.value))
            ? currentSelected.filter(value => value !== String(selectedItem.value))
            : [...currentSelected, String(selectedItem.value)];

         field.setValue(newSelected);
         onValueChange?.(newSelected);
      } else {
         const newValue = String(selectedItem.value);
         field.setValue(newValue);
         onValueChange?.(newValue);
      }
   }

   const isItemSelected = (value: OptionTypes['value']) => {
      if (isMulti) {
         return Array.isArray(field.state.value) && field.state.value.includes(String(value));
      }
      return field.state.value === String(value);
   }

   return (
      <Card>
         <CardHeader
            {...{
               title: label
            }}
         />
         <CardContent>
            <Grid component={List} container spacing={3}>
               {options?.map((option, index) => {
                  const isSelected = isItemSelected(option.value);
                  return (
                     <Grid
                        component={ListItem}
                        item
                        key={index}
                        onClick={() => onSelect(option)}
                        style={{
                           cursor: 'pointer',
                           justifyContent: 'space-between'
                        }}
                        {...GRID_ITEM_PROPS}
                     >
                        <Stack>
                           <Typography variant="h6" color="text.secondary">{option.title}</Typography>
                           <Typography variant="body2" color="text.secondary">{option.description}</Typography>
                        </Stack>
                        {isMulti ? (
                           <MuiCheckbox checked={isSelected} />
                        ) : (
                           <MuiRadio checked={isSelected} />
                        )}
                     </Grid>
                  )
               })}
            </Grid>
         </CardContent>
      </Card>
   );
}
