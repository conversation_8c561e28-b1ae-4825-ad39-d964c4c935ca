import { styled, useTheme } from '@mui/material/styles';
import { Box, Typography } from '@mui/material';
import { CheckIcon } from 'lucide-react';
import { useState, ReactNode } from 'react';

type color = 'default' | 'primary' | 'success' | 'warning' | 'error';
type Size = 'small' | 'medium';

export interface IBadge {
  status?: color;
  size?: Size;
  label?: string;
  isSelected?: boolean;
  selectable?: boolean;
  icon?: ReactNode;
  onSelect?: () => void;
}

const getColorTokens = (theme: any, status: color = 'default') => {
  const isDefault = status === 'default';

  const colorMap = {
    primary: theme.palette.primary.dark,
    success: theme.palette.success.main,
    warning: theme.palette.warning.main,
    error: theme.palette.error.main
  };

  const color = colorMap[status as keyof typeof colorMap] || theme.palette.grey[300];

  return {
    borderColor: isDefault ? theme.palette.grey[100] : color,
    backgroundColor: theme.palette.common.white,
    textColor: isDefault ? theme.palette.common.black : color,
    iconBg: isDefault ? theme.palette.grey[800] : color,
    iconColor: theme.palette.common.white
  };
};

const getSizeTokens = (size: Size = 'medium') => {
  const isSmall = size === 'small';
  return {
    padding: '4px 8px',
    fontSize: '0.8125rem',
    minWidth: isSmall ? 70 : 67,
    minHeight: isSmall ? 24 : 36,
    iconSize: isSmall ? 27 : 30
  };
};

const StyledChip = styled(Box)(({ theme, ...tokens }: any) => ({
  display: 'inline-flex',
  alignItems: 'center',
  justifyContent: 'center',
  gap: 4,
  borderRadius: '100px',
  cursor: 'pointer',
  userSelect: 'none',
  border: `1px solid ${tokens.borderColor}`,
  backgroundColor: tokens.backgroundColor,
  color: tokens.textColor,
  padding: tokens.padding,
  fontSize: tokens.fontSize,
  minWidth: tokens.minWidth,
  minHeight: tokens.minHeight
}));

const IconWrapper = styled('span')<{
  iconSize: number;
  iconBg: string;
  iconColor: string;
}>(({ iconSize, iconBg, iconColor }) => ({
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  minWidth: iconSize,
  minHeight: iconSize,
  borderRadius: '50%',
  backgroundColor: iconBg,
  color: iconColor
}));

const StyledLabel = styled(Typography)<{ isBold: boolean }>(({ isBold }) => ({
  fontWeight: isBold ? 600 : 400
}));

const Badge = ({
  status = 'default',
  size = 'medium',
  label,
  isSelected: isSelectedProp,
  selectable = false,
  icon,
  onSelect
}: IBadge) => {
  const theme = useTheme();
  const colors = getColorTokens(theme, status);
  const sizes = getSizeTokens(size);

  const [isSelectedLocal, setIsSelectedLocal] = useState<boolean>(false);
  const isSelected = isSelectedProp ?? isSelectedLocal;

  const displayLabel = label;
  const isBold = size === 'medium';

  const onToggleSelect = () => {
    if (selectable) {
      setIsSelectedLocal(!isSelected);
      onSelect?.();
    }
  };

  const iconNode = (
    <IconWrapper
      iconSize={sizes.iconSize}
      iconBg={colors.iconBg}
      iconColor={colors.iconColor}
    >
      <Box
        component="span"
        sx={{
          display: 'inline-flex',
          alignItems: 'center',
          justifyContent: 'center',
          width: sizes.iconSize * 0.75,
          height: sizes.iconSize * 0.75
        }}
      >
        {icon ?? <CheckIcon />}
      </Box>
    </IconWrapper>
  );

  return (
    <StyledChip {...colors} {...sizes} onClick={onToggleSelect}>
      {isSelected && selectable && iconNode}
      <StyledLabel isBold={isBold} variant="inherit">
        {displayLabel}
      </StyledLabel>
    </StyledChip>
  );
};

export default Badge;
