import { BasicField } from "@components/workflow/step-form-generator";
import { AppFormType } from "@hooks/app-form";
import { pascalToSpacedWords } from "@utils/generator-helper";
import { ValidationFunction } from "@workflows/ValidationFunction";
import { useMemo } from "react";

// Convert getBasicField to a HOC
export default function BasicFieldComponent({ form, fieldDef }: { form: AppFormType, fieldDef: BasicField }) {
    const { fieldType, name, label: rawLabel } = fieldDef as BasicField;

    const label = pascalToSpacedWords(rawLabel);

    // Create a unique identifier for this field that won't change between renders
    const fieldId = useMemo(() => `field-${name.replace(/\W/g, '-')}`, [name]);
    const validators = {
        onChange: ({ value }: { value: any }) => {
            const validatorFn = ValidationFunction.create(fieldDef.validation ?? []);
            const errors = validatorFn.validate(value);
            if (!errors || errors.length === 0) {
                return undefined;
            }
            // Filter out any invalid error objects and map to messages
            return errors
                .filter(error => error && error.type && error.message)
                .map(error => error.message)
                .filter(Boolean);
        }
    }

    if (fieldType === 'TimeRange') {
        return (
            <form.AppField
                name={name}
                validators={validators}
                children={(field) => (
                    <field.AppTimeRangeField label={label} />
                )}
            />
        );
    }
    if (fieldType === 'PhoneNumber') {
        return (
            <form.AppField
                name={name}
                validators={validators}
                children={(field) => (
                    <field.AppPhoneNumberField label={label} />
                )}
            />
        );
    }
    if (fieldType === 'SSN') {
        return (
            <form.AppField
                name={name}
                validators={validators}
                children={(field) => (
                    <field.AppSsnField label={label} />
                )}
            />
        );
    }

    if (fieldType === 'Email') {
        return (
            <form.AppField
                name={name}
                validators={validators}
                children={(field) => (
                    <field.AppEmailField label={label} />
                )}
            />
        );
    }

    if (fieldType === 'Date') {
        return (
            <form.AppField
                name={name}
                validators={validators}
                children={(field) => (
                    <field.AppDateField label={label} />
                )}
            />
        );
    }

    return (
        <form.AppField
            name={name}
            validators={validators}
            children={(field) => (<field.AppTextField label={label} id={fieldId} />)}
        />
    );
};
