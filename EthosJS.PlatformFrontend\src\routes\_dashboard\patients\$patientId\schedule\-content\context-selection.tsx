import { Button, Stack } from "@mui/material"

type Item = {
   name: string
   value: string
}

interface IContextSelection {
   items: Array<Item>
   onChange?: (selectedContext: Item) => void
   selected?: string
}

const ContextSelection = ({ selected, items = [], onChange }: IContextSelection) => {
   return (
      <Stack direction={'row'} gap={1}>
         {items.map((item, index) => {
            const isSelected = selected === item?.value;
            return (
               <Button
                  key={index.toString()}
                  onClick={() => onChange?.(item)}
                  variant={isSelected ? 'contained' : 'outlined'}
                  color="primary"
               >
                  {item.name}
               </Button>
            )
         })}
      </Stack>
   )
}

export default ContextSelection;