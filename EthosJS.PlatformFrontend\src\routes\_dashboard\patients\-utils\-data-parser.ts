import { EthosWorkflowsWorkflowAddNewOrderAddNewOrderState } from "@client/workflows";

const CARE_LOCATION_KEY = 'AddCareLocation'

export const parseCareLocation = (orderDetails: EthosWorkflowsWorkflowAddNewOrderAddNewOrderState | undefined | null) => {
   if (!orderDetails) return null;
   const careLocation = (orderDetails?.pastTransitions as Array<{ key: string, data: { careLocation: string } }>)
      ?.find((i) => i.key === CARE_LOCATION_KEY)?.data;
   return careLocation
}