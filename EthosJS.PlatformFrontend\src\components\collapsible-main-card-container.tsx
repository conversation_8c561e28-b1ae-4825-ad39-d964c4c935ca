import { useState } from "react";
import MainCardContainer, { MainCardContainerProps } from "./main-container/main-card-container"

interface ICollapsibleMainCardContainer {
   defaultCollapse?: boolean
   collapse?: boolean
   onToggleCollapse?: (collapsed: boolean) => boolean
   children?: React.ReactNode
   mainContainerProps?: MainCardContainerProps
}

const CollapsibleMainCardContainer = (props: ICollapsibleMainCardContainer) => {

   const {
      defaultCollapse = false,
      collapse: collapseProp,
      onToggleCollapse: onToggleCollapseProp,
      children,
      mainContainerProps = {}
   } = props;

   const [localOpen, setLocalOpen] = useState<boolean>(defaultCollapse);

   const collapse = collapseProp ?? localOpen;

   const onToggleCollapse = () => {
      setLocalOpen(!localOpen);
      onToggleCollapseProp?.(!localOpen)
   }

   return (
      <MainCardContainer
         primaryActionType="collapse"
         onPrimaryAction={onToggleCollapse}
         collapsed={collapse}
         {...mainContainerProps}
      >
         {collapse && children}
      </MainCardContainer>
   )
}

export default CollapsibleMainCardContainer