import { createFileRoute } from '@tanstack/react-router';
import Dashboard from '../../../../../../../features/scheduling/components/-dashboard';
import { z } from 'zod';

const validateSearch = z.object({
	orderId: z.string(),
	studyId: z.string().optional(),
});

export const Route = createFileRoute(
	'/_dashboard/patients/$patientId/schedule/appointment-creation/dashboard/'
)({
	component: RouteComponent,
	validateSearch,
});

function RouteComponent() {
	const { patientId } = Route.useParams();
	const search = Route.useSearch();
	const navigate = Route.useNavigate();

	return (
		<Dashboard
			onStartCreateAppoinment={() => {
				navigate({
					to: `/patients/$patientId/schedule/appointment-creation/dashboard/create`,
					params: { patientId },
					search,
				});
			}}
			studyId={search.studyId as string}
			patientId={patientId as string}
		/>
	);
}
