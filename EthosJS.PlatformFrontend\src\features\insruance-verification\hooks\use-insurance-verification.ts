import { getApiInsuranceVerificationStatusByJobIdOptions } from '@client/workflows/@tanstack/react-query.gen';
import { useQuery } from '@tanstack/react-query';
import { useEffect } from 'react';

export default function useInsuranceVerification({ studyId }: { studyId?: string }) {
	const jobId = localStorage.getItem(`insurance_verification_job_${studyId}`);

	const {
		data: verificationStatus,
		isLoading: isFetchingVerificationStatus,
		error: verificationStatusError,
	} = useQuery({
		...getApiInsuranceVerificationStatusByJobIdOptions({
			path: { jobId: jobId! },
			responseType: 'json',
		}),
		enabled: !!jobId,
		refetchInterval: 1000 * 5,
	});

	useEffect(() => {
		if (verificationStatus) {
			if (!studyId) return;
			const { currentCoarseState } = verificationStatus;
			if (currentCoarseState === 'Completed' || currentCoarseState === 'Error') {
				localStorage.removeItem(`insurance_verification_job_${studyId}`);
			}
		}
	}, [verificationStatus, studyId]);

	return {
		verificationStatus,
		isFetchingVerificationStatus,
		verificationStatusError,
	};
}
