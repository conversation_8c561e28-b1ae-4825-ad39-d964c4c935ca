import { useEffect, useState } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { getApiReferenceSetsKeysByIdOptions } from '@client/refdata/@tanstack/react-query.gen';
import { PatientCreate, PatientRead } from '@auth/scopes';
import { postApiInsuranceSearchOptions } from '@client/workflows/@tanstack/react-query.gen';
import { InsuranceQuery, Query } from '@utils/query-dsl';

interface ChipData {
  label: string;
  value: string;
}

export function useStudyAttributeChips(studyAttributeIds = []) {
  const [chipItems, setChipItems] = useState<ChipData[]>([]);
  const queryClient = useQueryClient();

  useEffect(() => {
    if (!studyAttributeIds.length) return;

    const fetchAll = async () => {
      try {
        const responses = await Promise.all(
          studyAttributeIds.map((id) => {
            const options = getApiReferenceSetsKeysByIdOptions({
              path: { id },
              query: { maxDepth: 3 },
              responseType: 'json',
              scopes: [PatientCreate.value, PatientRead.value]
            });

            return queryClient.fetchQuery({
              queryKey: options.queryKey,
              queryFn: options.queryFn
            });
          })
        );

        const items = responses.map((res) => ({
          label: '',
          value: res?.value?.values?.name || res?.value?.key?.value || 'Unknown'
        }));
        setChipItems(items);
      } catch (error) {
        console.error('Failed to fetch study attributes:', error);
      }
    };

    fetchAll();
  }, [studyAttributeIds, queryClient]);

  return chipItems;
}

export function useAssociatedInsuranceReview(insuranceIds: number[] = []) {
  const [chipItems, setChipItems] = useState<any[]>([]);
  const queryClient = useQueryClient();

  useEffect(() => {
    if (!insuranceIds.length) return;

    const fetchAll = async () => {
      try {
        const responses = await Promise.all(
          insuranceIds.map((id) => {
            const options = postApiInsuranceSearchOptions({
              scopes: [PatientCreate.value, PatientRead.value],
              responseType: 'json',
              body: Query.literal(InsuranceQuery.withId(id)) as any
            });

            return queryClient.fetchQuery({
              queryKey: options.queryKey,
              queryFn: options.queryFn
            });
          })
        );

        const items = responses.map((res: any) => {
          const insurance = res?.items?.[0];
          return insurance;
        });
        setChipItems(items);
      } catch (error) {
        console.error('Failed to fetch associated insurances:', error);
      }
    };

    fetchAll();
  }, [insuranceIds, queryClient]);

  return chipItems;
}

export function formatPhysiciansSummary(values: any = [], hasValues: boolean): Array<ChipData> {
  if (!hasValues || !values) {
    return [];
  }

  const fullName = values.names?.[0]
    ? `${values.names[0].firstName || ''} ${values.names[0].middleName || ''} ${values.names[0].lastName || ''}`.trim()
    : '';

  const summary: Array<ChipData> = [
    {
      label: 'Full Name',
      value: fullName
    },
    {
      label: 'NPI',
      value: values.npi || 'N/A'
    },
    {
      label: 'Specialty',
      value: values.specialty || 'N/A'
    }
  ];

  return summary;
}

export function getBasicSummaryItems(insurance: any): ChipData[] {
  return [
    insurance?.insuranceHolder?.name && {
      label: 'Policy Holder',
      value: insurance.insuranceHolder.name
    },
    insurance?.insuranceHolder?.dob && {
      label: "Policy Holder's DOB",
      value: insurance.insuranceHolder.dob
    },
    insurance?.insuranceHolder?.relationship && {
      label: 'Relationship to Patient',
      value: insurance.insuranceHolder.relationship
    },
    insurance?.memberId && {
      label: 'Member ID',
      value: `#${insurance.memberId}`
    },
    insurance?.policyId && {
      label: 'Policy ID',
      value: `#${insurance.policyId}`
    },
    insurance?.groupNumber && {
      label: 'Group Number',
      value: `#${insurance.groupNumber}`
    },
    insurance?.planType && {
      label: 'Plan Type',
      value: `#${insurance.planType}`
    },
    insurance?.insuranceId && {
      label: 'Insurance ID',
      value: insurance.insuranceId
    }
  ].filter(Boolean) as ChipData[];
}

export function getContactSummaryItems(insurance: any): ChipData[] {
  return [
    insurance?.phone && {
      label: 'Phone',
      value: insurance.phone
    },
    insurance?.fax && {
      label: 'Fax',
      value: insurance.fax
    },
    insurance?.email && {
      label: 'E-Mail',
      value: insurance.email
    }
  ].filter(Boolean) as ChipData[];
}

export function getAddressSummaryItems(insurance: any): ChipData[] {
  return [
    insurance?.po && {
      label: 'PO Box Address',
      value: insurance.po
    }
  ].filter(Boolean) as ChipData[];
}
