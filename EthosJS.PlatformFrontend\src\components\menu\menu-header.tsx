import { styled, Typography, Stack, Box } from '@mui/material';
import { ComponentType, SVGProps } from 'react';

const StyledHeader = styled(Box)(({ theme }) => ({
	flex: 1,
	display: 'flex',
	whiteSpace: 'pre',
	gap: theme.spacing(1.5),
}));

const IconContainer = styled(Box)(({ theme }) => ({
	display: 'flex',
	alignItems: 'center',
	justifyContent: 'center',
	width: 40,
	height: 40,
	borderRadius: theme.shape.borderRadius,
	backgroundColor: theme.palette.grey[200],
	color: theme.palette.text.primary,
	...theme.applyStyles('dark', {
		backgroundColor: theme.palette.grey[700],
		color: theme.palette.common.white,
	}),
}));

export interface MenuHeaderProps {
	title: string;
	subtitle: string | undefined;
	icon: ComponentType<SVGProps<SVGSVGElement>> | undefined;
	showIcon?: boolean;
}

export default function MenuHeader({
	title,
	subtitle,
	icon: Icon,
	showIcon = true,
}: MenuHeaderProps) {
	return (
		<StyledHeader>
			{showIcon ? (
				Icon ? (
					<IconContainer>
						<Icon
							style={{
								width: '1.5rem',
								height: '1.5rem',
								flexShrink: 0,
								color: 'inherit',
							}}
						/>
					</IconContainer>
				) : (
					<IconContainer>
						<Typography
							variant="h6"
							sx={{ fontWeight: 600, fontSize: '1.125rem' }}>
							{title.slice(0, 1)?.toUpperCase()}
						</Typography>
					</IconContainer>
				)
			) : null}
			<Stack
				maxHeight="40px"
				flex={1}
				justifyContent={'center'}
				gap={1}>
				<Typography sx={{ fontWeight: 600, fontSize: '1rem', lineHeight: 1 }}>{title}</Typography>
				{subtitle && (
					<Typography
						variant="asapCondensed"
						sx={{ fontWeight: 500, fontSize: '0.75rem', lineHeight: 'normal' }}>
						{subtitle}
					</Typography>
				)}
			</Stack>
		</StyledHeader>
	);
}
