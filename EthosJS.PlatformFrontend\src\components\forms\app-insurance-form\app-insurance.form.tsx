import MainCardContainer from '@components/main-container/main-card-container';
import { AppFormType, useAppForm } from '@hooks/app-form';
import { ReceiptOutlined } from '@mui/icons-material';
import { Grid2 as Grid, Box, InputAdornment } from '@mui/material';
import { useStore } from '@tanstack/react-form';
import { Lock } from 'lucide-react';
import { useEffect, useMemo, useState } from 'react';
import { getApiReferenceSetsValuesOptions } from '@client/refdata/@tanstack/react-query.gen';
import { useQuery } from '@tanstack/react-query';
import { find } from 'lodash';
import { ReferenceDataSetKeyValueDto } from '@client/refdata';
import { EthosWorkflowsApiInsuranceDto } from '@client/workflows';
import { insuranceFormOptions } from './utils';
import { ValidationErrors } from '@app-types/validation';
import { formHasErrors } from '@utils/forms';

interface InsuranceFormProps {
	onAdd?: (values: EthosWorkflowsApiInsuranceDto) => void;
	onCancel?: (shouldRemove: boolean) => void;
	onDelete?: () => void;
	onValidate: (data: EthosWorkflowsApiInsuranceDto) => Promise<ValidationErrors | undefined>;
	formValues: EthosWorkflowsApiInsuranceDto;
}

export default function InsuranceForm({
	onAdd,
	onCancel,
	onDelete,
	onValidate,
	formValues,
	patientName,
	dateOfBirth,
}: InsuranceFormProps & { patientName: string; dateOfBirth: string }) {
	const options = useMemo(() => insuranceFormOptions(formValues), [formValues]);
	const hasValues = JSON.stringify(formValues) !== JSON.stringify(options.defaultValues);

	const form = useAppForm({
		...options,
		validators: {
			onChangeAsyncDebounceMs: 500,
			onChangeAsync: async ({ value }) => {
				const res = await onValidate(value);
				return res;
			},
		},
		defaultState: {
			isDirty: hasValues,
			isPristine: !hasValues,
		},
	});

	const [isSelf, setIsSelf] = useState(false);

	const { values, isDirty, canSubmit } = useStore(form.store, ({ values, isDirty, canSubmit }) => ({
		values,
		isDirty,
		canSubmit,
	}));

	const { data: relationshipOptions } = useQuery({
		...getApiReferenceSetsValuesOptions({
			query: {
				setName: 'relationship',
				filter: 'relationshipType eq Insurance',
			},
			responseType: 'json',
		}),
	});

	useEffect(() => {
		if (relationshipOptions) {
			const reletionshipResponse = relationshipOptions as { items: ReferenceDataSetKeyValueDto[] };
			//@ts-expect-error Type Mismatch
			const selfOption = find(reletionshipResponse.items, (item) => item.values?.name === 'Self');
			if (selfOption) {
				form.setFieldValue('insuranceHolder.name', patientName);
				form.setFieldValue('insuranceHolder.dateOfBirth', dateOfBirth);
				setIsSelf(selfOption.id === values.insuranceHolder?.relationship);
			} else {
				setIsSelf(false);
			}
		}
	}, [dateOfBirth, form, patientName, relationshipOptions, values.insuranceHolder?.relationship]);

	return (
		<MainCardContainer
			emphasis="high"
			color={formHasErrors(form as AppFormType) ? 'error' : 'primary'}
			title="Add Insurance"
			icon={<ReceiptOutlined />}
			descriptionSubheader="* Indicates a required field"
			descriptionText="Enter the policy holder information and insurance details."
			footerProps={{
				primaryButton1: {
					label: hasValues ? 'Update' : 'Add',
					onClick: () => onAdd?.(values),
					disabled: !canSubmit || !isDirty,
				},
				primaryButton2: {
					label: 'Cancel',
					onClick: () => {
						form.reset();
						onCancel?.(!hasValues);
					},
				},
				secondaryButton1: hasValues
					? {
							label: 'Delete',
							onClick: onDelete,
						}
					: undefined,
			}}>
			<Grid
				container
				spacing={2}>
				{/* Insurance Holder Section */}
				<Grid size={12}>
					<Box sx={{ typography: 'subtitle1', mb: 1, color: 'primary.dark' }}>
						Insurance Holder Information
					</Box>
				</Grid>
				<Grid size={4}>
					<form.AppField name="insuranceHolder.relationship">
						{(field) => (
							<field.AppSelectField
								label="Relationship"
								required
								referenceDataSetName="relationship"
								referenceDataFilter="relationshipType eq Insurance"
								defaultReferenceValue="Self"
								data-testid="insurances.relationship"
							/>
						)}
					</form.AppField>
				</Grid>
				<Grid size={4}>
					<form.AppField
						name="insuranceHolder.name"
						children={(field) => (
							<field.AppTextField
								label="Full Name"
								required
								disabled={isSelf}
								data-testid="insurances.holderName"
							/>
						)}
					/>
				</Grid>
				<Grid size={4}>
					<form.AppField
						name="insuranceHolder.dateOfBirth"
						children={(field) => (
							<field.AppDateField
								label="Date of Birth"
								required
								disabled={isSelf}
							/>
						)}
					/>
				</Grid>

				{/* Insurance Information Section */}
				<Grid size={12}>
					<Box sx={{ typography: 'subtitle1', mb: 1, mt: 2, color: 'primary.dark' }}>
						Insurance Information
					</Box>
				</Grid>
				<Grid size={6}>
					<form.AppField name="insuranceCarrier">
						{(field) => (
							<field.AppAutocompleteField
								label="Insurance Carrier"
								referenceDataSetName="insuranceCarrier"
								required
								data-testid="insurances.carrier"
							/>
						)}
					</form.AppField>
				</Grid>
				{/* <Grid size={6}>
					<form.AppField name="planType">
						{(field) => (
							<field.AppAutocompleteField
								label="Plan Type"
								referenceDataSetName="insurancePlanType"
								required
								data-testid="insurances.planType"
							/>
						)}
					</form.AppField>
				</Grid> */}
				<Grid size={6}>
					<form.AppField
						name="insuranceId"
						children={(field) => (
							<field.AppTextField
								label="Insurance ID"
								required
								data-testid="insurances.insuranceId"
							/>
						)}
					/>
				</Grid>
				<Grid size={6}>
					<form.AppField
						name="policyId"
						children={(field) => (
							<field.AppTextField
								label="Policy ID"
								required
								data-testid="insurances.policyId"
							/>
						)}
					/>
				</Grid>
				<Grid size={6}>
					<form.AppField
						name="memberId"
						children={(field) => (
							<field.AppTextField
								label="Member ID"
								required
								data-testid="insurances.memberId"
							/>
						)}
					/>
				</Grid>
				<Grid size={6}>
					<form.AppField
						name="groupNumber"
						children={(field) => (
							<field.AppTextField
								label="Group Number"
								data-testid="insurances.groupNumber"
							/>
						)}
					/>
				</Grid>
				{/* Contact Information Section */}
				<Grid size={12}>
					<Box sx={{ typography: 'subtitle1', mb: 1, mt: 2, color: 'primary.dark' }}>
						Contact Information
					</Box>
				</Grid>
				<Grid size={3}>
					<form.AppField name="phoneNumber.use">
						{(field) => (
							<field.AppSelectField
								label="Phone Type"
								referenceDataSetName="phoneUse"
								defaultReferenceValue="Work"
								disabled
								required
								data-testid="insurances.phoneType"
								slotProps={{
									input: {
										endAdornment: (
											<InputAdornment position="end">
												<Lock />
											</InputAdornment>
										),
									},
								}}
							/>
						)}
					</form.AppField>
				</Grid>
				<Grid size={9}>
					<form.AppField
						name="phoneNumber.phoneNumber"
						children={(field) => (
							<field.AppPhoneNumberField
								label="Phone Number"
								required
								data-testid="insurances.phoneNumber"
							/>
						)}
					/>
				</Grid>
				<Grid size={3}>
					<form.AppField name="email.use">
						{(field) => (
							<field.AppSelectField
								label="Email Type"
								referenceDataSetName="emailUse"
								defaultReferenceValue="Work"
								data-testid="insurances.emailType"
								slotProps={{
									input: {
										endAdornment: (
											<InputAdornment position="end">
												<Lock />
											</InputAdornment>
										),
									},
								}}
								disabled
							/>
						)}
					</form.AppField>
				</Grid>
				<Grid size={9}>
					<form.AppField
						name="email.email"
						children={(field) => (
							<field.AppEmailField
								label="Email"
								required
								data-testid="insurances.email"
							/>
						)}
					/>
				</Grid>

				{/* Address Section - Moved to bottom */}
				<Grid size={12}>
					<Box sx={{ typography: 'subtitle1', mb: 1, mt: 2, color: 'primary.dark' }}>
						Address Information
					</Box>
				</Grid>

				<Grid size={4}>
					<form.AppField name="address.use">
						{(field) => (
							<field.AppSelectField
								label="Address Type"
								referenceDataSetName="addressType"
								data-testid="insurances.addressType"
							/>
						)}
					</form.AppField>
				</Grid>
				<Grid size={12}>
					<form.AppField
						name="address.address.line1"
						children={(field) => (
							<field.AppTextField
								label="Address Line 1"
								required
								fullWidth
								data-testid="insurances.addressLine1"
							/>
						)}
					/>
				</Grid>
				<Grid size={12}>
					<form.AppField
						name="address.address.line2"
						children={(field) => (
							<field.AppTextField
								label="Address Line 2"
								fullWidth
								data-testid="insurances.addressLine2"
							/>
						)}
					/>
				</Grid>
				<Grid size={4}>
					<form.AppField
						name="address.address.city"
						children={(field) => (
							<field.AppTextField
								label="City"
								required
								data-testid="insurances.city"
							/>
						)}
					/>
				</Grid>
				<Grid size={4}>
					<form.AppField name="address.address.state">
						{(field) => (
							<field.AppAutocompleteField
								label="State"
								referenceDataSetName="state"
								required
								data-testid="insurances.state"
							/>
						)}
					</form.AppField>
				</Grid>
				<Grid size={4}>
					<form.AppField
						name="address.address.postalCode"
						children={(field) => (
							<field.AppTextField
								label="Postal Code"
								required
								data-testid="insurances.postalCode"
							/>
						)}
					/>
				</Grid>
			</Grid>
		</MainCardContainer>
	);
}
