import MainCardContainer from '@components/main-container/main-card-container';
import { withForm } from '@hooks/app-form';
import { Grid2 as Grid } from '@mui/material';
import { formHasErrors } from '@utils/forms';
import { Users } from 'lucide-react';

const DemographicsForm = withForm({
	render: ({ form }) => {
		const hasErrors = formHasErrors(form, 'demographics');
		return (
			<MainCardContainer
				title="Demographics"
				icon={<Users />}
				color={hasErrors ? 'error' : 'primary'}
				emphasis={hasErrors ? 'high' : 'low'}>
				<Grid
					container
					spacing={2}>
					<Grid size={{ xs: 12, sm: 4 }}>
						<form.AppField
							name="demographics.dateOfBirth"
							children={(field) => (
								<field.AppDateField
									label="Date of Birth *"
									data-testid="demographics.dateOfBirth"
								/>
							)}
						/>
					</Grid>
					<Grid size={{ xs: 12, sm: 4 }}>
						<form.AppField
							name="demographics.birthSex"
							children={(field) => (
								<field.AppAutocompleteField
									label="Birth Sex"
									required
									referenceDataSetName="sex"
									filterOptions={(x) => x}
									data-testid="demographics.birthSex"
								/>
							)}
						/>
					</Grid>
					<Grid size={{ xs: 12, sm: 4 }}>
						<form.AppField
							name="demographics.gender"
							children={(field) => (
								<field.AppAutocompleteField
									label="Gender Identity"
									required
									referenceDataSetName="gender"
									data-testid="demographics.gender"
								/>
							)}
						/>
					</Grid>
					<Grid size={{ xs: 12, sm: 4 }}>
						<form.AppField
							name="demographics.race"
							children={(field) => (
								<field.AppAutocompleteField
									label="Race"
									required
									referenceDataSetName="race"
									data-testid="demographics.race"
								/>
							)}
						/>
					</Grid>
					<Grid size={{ xs: 12, sm: 4 }}>
						<form.AppField
							name="demographics.maritalStatus"
							children={(field) => (
								<field.AppAutocompleteField
									label="Marital Status"
									required
									referenceDataSetName="maritalStatus"
									data-testid="demographics.maritalStatus"
								/>
							)}
						/>
					</Grid>
					<Grid size={{ xs: 12, sm: 4 }}>
						<form.AppField
							name="demographics.ethnicity"
							children={(field) => (
								<field.AppAutocompleteField
									label="Ethnicity"
									required
									referenceDataSetName="ethnicity"
									data-testid="demographics.ethnicity"
								/>
							)}
						/>
					</Grid>
				</Grid>
			</MainCardContainer>
		);
	},
});

export default DemographicsForm;
