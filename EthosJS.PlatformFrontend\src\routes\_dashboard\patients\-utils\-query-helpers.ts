import { PatientQQueryDto } from '@client/workflows';
import { QueryDto, PatientQ, PatientQuery, Query } from '@utils/query-dsl';
import { PatientSearchQueryParams } from './-definitions';
/**
 * Builds a patient query from search parameters
 * @param params The search parameters to build the query from
 * @returns A QueryDto for patient search or undefined if no valid parameters
 */
export function buildPatientQuery(
  params: PatientSearchQueryParams
): QueryDto<PatientQ> | undefined {
  const queries: QueryDto<PatientQ>[] = [];
  if (!params) {
    return undefined;
  }
  if (params.FirstName) {
    queries.push(Query.literal(PatientQuery.withGivenName(params.FirstName)));
  }

  if (params.LastName) {
    queries.push(Query.literal(PatientQuery.withLastName(params.LastName)));
  }

  if (params.Location) {
    queries.push(Query.literal(PatientQuery.withLastName(params.Location)));
  }

  if (params.DateOfBirth) {
    queries.push(Query.literal(PatientQuery.withDateOfBirth(params.DateOfBirth)));
  }

  if (params.StudyDate) {
    queries.push(Query.literal(PatientQuery.withDateOfBirth(params.StudyDate)));
  }

  if (params.Status) {
    // Note: Status filtering might need a specific query method
    // For now, we'll add it as a generic filter
    // This might need to be adjusted based on the actual API requirements
    queries.push(Query.literal(PatientQuery.withLastName(params.Status)));
  }

  // If no queries, return undefined
  if (queries.length === 0) {
    return undefined;
  }

  // If only one query, return it directly
  if (queries.length === 1) {
    return queries[0];
  }

  // Otherwise, combine with AND
  return Query.and(queries);
}

/**
 * Builds a patient query DTO from search parameters
 * @param params The search parameters to build the query from
 * @returns A PatientQQueryDto for API requests or undefined if no valid parameters
 */
export function buildPatientQueryDto(
  params?: PatientSearchQueryParams
): PatientQQueryDto | undefined {
  if (!params) {
    return undefined;
  }
  const query = buildPatientQuery(params);

  if (!query) {
    return undefined;
  }

  return query as unknown as PatientQQueryDto;
}
