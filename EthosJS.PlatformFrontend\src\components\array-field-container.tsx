import { useState, ReactNode } from 'react';
import ChipSummary, { ChipData } from './chip-summary';
import { CardHeader, IconButton } from '@mui/material';
import { Edit } from '@mui/icons-material';

export interface ArrayFieldContainerProps {
	initialEditState: boolean;
	children: ({ setEdit }: { setEdit: (edit: boolean) => void }) => ReactNode;
	title: string;
	showHeader?: boolean;
	items: Array<ChipData>;
}

export default function ArrayFieldContainer({
	items,
	children,
	title,
	showHeader = true,
	initialEditState,
}: ArrayFieldContainerProps) {
	const [edit, setEdit] = useState(initialEditState);

	if (!edit) {
		if (items.length === 0) {
			if (showHeader) {
				return (
					<CardHeader
						title={title}
						sx={{ borderRadius: 1 }}
						action={
							<IconButton onClick={() => setEdit(true)}>
								<Edit />
							</IconButton>
						}
					/>
				);
			}
			return null;
		}
		return (
			<ChipSummary
				items={items}
				onEdit={() => setEdit(true)}
			/>
		);
	}

	return children({ setEdit });
}
