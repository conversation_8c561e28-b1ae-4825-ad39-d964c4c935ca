import StepCardControl from '@components/step-card-control';
import {
	Box,
	Card,
	CardContent,
	Button,
	FormControlLabel,
	Checkbox,
	Stack,
	Typography,
	Radio,
	RadioGroup,
	FormLabel,
} from '@mui/material';
import { useAppForm } from '@hooks/app-form';
import { useStore } from '@tanstack/react-form';
import {
	MedicalInformation,
	NightsStay,
	History,
	Person,
	Accessibility,
	Language,
	Note,
	Schedule,
} from '@mui/icons-material';
import FormCard from '@components/form-card';
import { useMemo } from 'react';
import { useQuery } from '@tanstack/react-query';
import { getApiReferenceSetsValuesOptions } from '@client/refdata/@tanstack/react-query.gen';
import { PatientCreate, PatientRead } from '@auth/scopes';
import { filter, includes } from 'lodash';
import { ReferenceDataSetKeyValueDto } from '@client/refdata';
import { FormProps } from '@components/forms/predefined-form-props';
import { ClinicalConsiderationsData } from '../types';
import ClinicalConsiderationsList from '../components/clinical-considerations-list';

const defaultValues: ClinicalConsiderationsData = {
	clinicalConsiderations: [] as number[],
	schedulingPreferences: {
		technicianPreference: null!,
		preferredDayOfWeek: [] as number[],
	},
	additionalPatientNotes: '',
	caregiverInformation: '',
};

function clinicalConsiderationsOptions(savedData?: ClinicalConsiderationsData) {
	return {
		defaultValues: {
			...defaultValues,
			...(savedData as typeof defaultValues),
		},
	};
}

export default function ClinicalConsiderationsForm({
	onSubmit,
	onSaveDraft,
	savedData,
	isUpdate,
}: FormProps<ClinicalConsiderationsData>) {
	const options = useMemo(() => clinicalConsiderationsOptions(savedData), [savedData]);
	const form = useAppForm({
		...options,
		onSubmit: async ({ value }) => {
			onSubmit(value);
		},
	});

	const values = useStore(form.store, (state) => state.values);

	const { data: technicianPreferenceOptions, isFetching: isFetchingTechnicianPreferenceOptions } =
		useQuery(
			getApiReferenceSetsValuesOptions({
				responseType: 'json',
				query: {
					setName: 'technicianPreference',
				},
			})
		);

	const technicianPreferenceOptionsItems =
		(technicianPreferenceOptions as { items: ReferenceDataSetKeyValueDto[] })?.items ?? [];

	const { data: weekDayOptions, isFetching: weekDayOptionsIsFetching } = useQuery(
		getApiReferenceSetsValuesOptions({
			responseType: 'json',
			query: {
				setName: 'weekday',
			},
		})
	);

	const weekDayOptionsItems = (weekDayOptions as { items: ReferenceDataSetKeyValueDto[] })
		?.items ?? [
		{ key: { value: 'Monday' }, id: 1 },
		{ key: { value: 'Tuesday' }, id: 2 },
		{ key: { value: 'Wednesday' }, id: 3 },
		{ key: { value: 'Thursday' }, id: 4 },
		{ key: { value: 'Friday' }, id: 5 },
		{ key: { value: 'Saturday' }, id: 6 },
		{ key: { value: 'Sunday' }, id: 7 },
	];

	return (
		<Box
			component="form"
			sx={{ height: '100%' }}
			onSubmit={(e) => {
				e.preventDefault();
				form.handleSubmit();
			}}>
			<Card elevation={0}>
				<CardContent sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
					<form.AppField name="clinicalConsiderations">
						{() => {
							return (
								<Stack spacing={2}>
									<ClinicalConsiderationsList
										title="Medical Status"
										icon={MedicalInformation}
										setName="clinicalConsideration"
										category="Medical Status"
										isItemSelected={(id) => values?.clinicalConsiderations?.includes(id) ?? false}
										onSelect={(id, isChecked) => {
											if (isChecked) {
												form.setFieldValue('clinicalConsiderations', [
													...(values.clinicalConsiderations ?? []),
													id,
												]);
											} else {
												form.setFieldValue(
													'clinicalConsiderations',
													filter(values.clinicalConsiderations, (item) => item !== id)
												);
											}
										}}
									/>
									<ClinicalConsiderationsList
										title="Sleep Disorder Risk Factors"
										icon={NightsStay}
										setName="clinicalConsideration"
										category="Sleep Disorder Risk Factors"
										isItemSelected={(id) => values?.clinicalConsiderations?.includes(id) ?? false}
										onSelect={(id, isChecked) => {
											if (isChecked) {
												form.setFieldValue('clinicalConsiderations', [
													...(values.clinicalConsiderations ?? []),
													id,
												]);
											} else {
												form.setFieldValue(
													'clinicalConsiderations',
													filter(values.clinicalConsiderations, (item) => item !== id)
												);
											}
										}}
									/>
									<ClinicalConsiderationsList
										title="Medical History"
										icon={History}
										setName="clinicalConsideration"
										category="Medical History"
										isItemSelected={(id) => values?.clinicalConsiderations?.includes(id) ?? false}
										onSelect={(id, isChecked) => {
											if (isChecked) {
												form.setFieldValue('clinicalConsiderations', [
													...(values.clinicalConsiderations ?? []),
													id,
												]);
											} else {
												form.setFieldValue(
													'clinicalConsiderations',
													filter(values.clinicalConsiderations, (item) => item !== id)
												);
											}
										}}
									/>
									<ClinicalConsiderationsList
										title="Special Accommodations"
										icon={Language}
										setName="clinicalConsideration"
										category="Special Accommodations"
										isItemSelected={(id) => values?.clinicalConsiderations?.includes(id) ?? false}
										onSelect={(id, isChecked) => {
											if (isChecked) {
												form.setFieldValue('clinicalConsiderations', [
													...(values.clinicalConsiderations ?? []),
													id,
												]);
											} else {
												form.setFieldValue(
													'clinicalConsiderations',
													filter(values.clinicalConsiderations, (item) => item !== id)
												);
											}
										}}
									/>
									<ClinicalConsiderationsList
										title="Mobility and Assistance"
										icon={Accessibility}
										setName="clinicalConsideration"
										category="Mobility & Assistance"
										isItemSelected={(id) => values?.clinicalConsiderations?.includes(id) ?? false}
										onSelect={(id, isChecked) => {
											if (isChecked) {
												form.setFieldValue('clinicalConsiderations', [
													...(values.clinicalConsiderations ?? []),
													id,
												]);
											} else {
												form.setFieldValue(
													'clinicalConsiderations',
													filter(values.clinicalConsiderations, (item) => item !== id)
												);
											}
										}}
									/>
								</Stack>
							);
						}}
					</form.AppField>

					{/* Scheduling Preferences */}
					<form.AppField name="schedulingPreferences">
						{() => (
							<FormCard
								title="Scheduling Preferences"
								icon={Schedule}>
								<FormLabel sx={{ my: 1 }}>Technician Preference</FormLabel>
								<form.AppField name="schedulingPreferences.technicianPreference">
									{(field) => (
										<RadioGroup
											row
											value={field.state.value}
											onChange={(e) => field.handleChange(Number(e.target.value))}>
											{technicianPreferenceOptionsItems.map((option) => (
												<FormControlLabel
													key={option.id}
													value={option.id}
													control={
														<Radio
															data-testid={`schedulingPreferences.technicianPreference.${option.id}`}
														/>
													}
													// @ts-expect-error Type Mismatch
													label={option.values.name}
												/>
											))}
										</RadioGroup>
									)}
								</form.AppField>
								<Typography sx={{ my: 1 }}>Preferred Day(s) of Week</Typography>
								<form.AppField name="schedulingPreferences.preferredDayOfWeek">
									{({ state, handleChange }) => (
										<Stack
											direction={'row'}
											sx={{ flexWrap: 'wrap', gap: 2 }}>
											{weekDayOptionsItems.map((option) => (
												<FormControlLabel
													key={option.id}
													control={
														<Checkbox
															checked={includes(state.value, option.id)}
															onChange={(e) => {
																const prevValue =
																	filter(state.value, (id) => typeof id === 'number') ?? [];
																if (e.target.checked && typeof option.id === 'number') {
																	handleChange([...prevValue, option.id]);
																} else {
																	handleChange(filter(prevValue, (id) => id !== option.id));
																}
															}}
															data-testid={`schedulingPreferences.preferredDaysOfWeek.${option.id}`}
														/>
													}
													// @ts-expect-error Type Mismatch
													label={option.values?.name}
												/>
											))}
										</Stack>
									)}
								</form.AppField>
							</FormCard>
						)}
					</form.AppField>

					{/* Additional Notes */}
					<form.AppField name="additionalPatientNotes">
						{(field) => (
							<FormCard
								title="Additional Patient Notes"
								icon={Note}>
								<field.AppTextField
									fullWidth
									multiline
									rows={4}
									label="Additional Patient Notes"
									data-testid="clinicalConsiderations.additionalPatientNotes"
								/>
							</FormCard>
						)}
					</form.AppField>

					{/* Caregiver Information */}
					<form.AppField name="caregiverInformation">
						{(field) => (
							<FormCard
								title="Caregiver Information"
								icon={Person}>
								<field.AppTextField
									fullWidth
									multiline
									rows={4}
									label="Caregiver Information"
									data-testid="clinicalConsiderations.caregiverInformation"
								/>
							</FormCard>
						)}
					</form.AppField>
				</CardContent>
			</Card>
			<StepCardControl>
				{!isUpdate && (
					<Button
						variant="outlined"
						color="primary"
						onClick={() => onSaveDraft(values)}
						data-testid="clinicalConsiderations.saveDraftButton">
						Save Draft
					</Button>
				)}
				<form.Subscribe
					selector={({ isDirty, canSubmit, isSubmitting }) => ({
						isDirty,
						canSubmit,
						isSubmitting,
					})}>
					{({ isDirty, canSubmit, isSubmitting }) => (
						<Button
							variant="contained"
							color="primary"
							type="submit"
							disabled={!isDirty || !canSubmit || isSubmitting}
							data-testid="clinicalConsiderations.submitButton">
							Submit & Create Order
						</Button>
					)}
				</form.Subscribe>
			</StepCardControl>
		</Box>
	);
}
