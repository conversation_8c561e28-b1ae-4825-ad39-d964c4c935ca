import {
  Add,
  FilterAlt,
  Search,
  SecurityOutlined,
} from '@mui/icons-material'
import {
  Box,
  Button,
  Chip,
  debounce,
  FormControl,
  IconButton,
  InputAdornment,
  InputLabel,
  MenuItem,
  Select,
  Stack,
  TextField,
  Typography,
} from '@mui/material'
import { useQuery } from '@tanstack/react-query'
import { createFileRoute } from '@tanstack/react-router'
import { useEffect, useMemo, useRef, useState } from 'react'
import LoadingComponent from '@components/loading-component'
import PageContainer from '@components/page-container'
import { PatientCreate, PatientRead } from '@auth/scopes'
import {
  DataGridPro,
  GridColDef,
  GridFilterModel,
  useGridApiRef,
} from '@mui/x-data-grid-pro'
import { paginationQueryParams } from '@utils/query'
import { EthosWorkflowsApiOrderDto } from '@client/workflows'
import { postApiOrderSearchOptions } from '@client/workflows/@tanstack/react-query.gen'
import StyledCardFooter from '@components/card-footer'

// Insurance data type for display
interface InsuranceDisplayData {
  id: string
  patientName: string
  patientId: string
  patientDOB: string
  patientEmail: string
  patientContact: string
  orderId: string
  studyType: string
  physicianName: string
  locationName: string
  insuranceName: string
  memberId: string
  policyId: string
  planType: string
  groupNumber: string
  verificationStatus: string
  verifiedDate: string
  copay: string
  deductible: string
  authId: string
  approvedDate: string
  validUntil: string
}

// Sample insurance data for demonstration
const sampleInsuranceData: InsuranceDisplayData[] = [
  {
    id: "INS-2024-001",
    patientName: "Sarah Johnson",
    patientId: "PAT-001",
    patientDOB: "1985-03-15",
    patientEmail: "<EMAIL>",
    patientContact: "(206) 555-1234",
    orderId: "ORD-2024-001",
    studyType: "Sleep Study",
    physicianName: "Dr. Michael Chen",
    locationName: "Tukwila Sleep Center",
    insuranceName: "Blue Cross Blue Shield",
    memberId: "34197564",
    policyId: "7445571",
    planType: "PPO",
    groupNumber: "543210",
    verificationStatus: "Verified",
    verifiedDate: "3/15/2025",
    copay: "$30",
    deductible: "$500",
    authId: "36001-QIDA",
    approvedDate: "3/15/2025",
    validUntil: "5/15/2025"
  },
  {
    id: "INS-2024-002",
    patientName: "Robert Martinez",
    patientId: "PAT-002",
    patientDOB: "1978-11-22",
    patientEmail: "<EMAIL>",
    patientContact: "(206) 555-5678",
    orderId: "ORD-2024-002",
    studyType: "Home Sleep Test",
    physicianName: "Dr. Lisa Wang",
    locationName: "Seattle Sleep Clinic",
    insuranceName: "Aetna",
    memberId: "45208675",
    policyId: "8556682",
    planType: "HMO",
    groupNumber: "654321",
    verificationStatus: "Pending",
    verifiedDate: "3/20/2025",
    copay: "$25",
    deductible: "$750",
    authId: "47002-XYZB",
    approvedDate: "3/20/2025",
    validUntil: "6/20/2025"
  },
  {
    id: "INS-2024-003",
    patientName: "Emily Davis",
    patientId: "PAT-003",
    patientDOB: "1992-07-08",
    patientEmail: "<EMAIL>",
    patientContact: "(425) 555-9012",
    orderId: "ORD-2024-003",
    studyType: "MSLT",
    physicianName: "Dr. Sarah Thompson",
    locationName: "Bellevue Sleep Center",
    insuranceName: "UnitedHealthcare",
    memberId: "56319786",
    policyId: "9667793",
    planType: "EPO",
    groupNumber: "765432",
    verificationStatus: "Denied",
    verifiedDate: "3/18/2025",
    copay: "$40",
    deductible: "$1000",
    authId: "58003-MNOP",
    approvedDate: "3/18/2025",
    validUntil: "7/18/2025"
  },
  {
    id: "INS-2024-004",
    patientName: "David Wilson",
    patientId: "PAT-004",
    patientDOB: "1965-12-03",
    patientEmail: "<EMAIL>",
    patientContact: "(206) 555-3456",
    orderId: "ORD-2024-004",
    studyType: "CPAP Titration",
    physicianName: "Dr. Michael Chen",
    locationName: "Tukwila Sleep Center",
    insuranceName: "Cigna",
    memberId: "67420897",
    policyId: "1778804",
    planType: "POS",
    groupNumber: "876543",
    verificationStatus: "Verified",
    verifiedDate: "3/22/2025",
    copay: "$35",
    deductible: "$600",
    authId: "69004-RSTU",
    approvedDate: "3/22/2025",
    validUntil: "8/22/2025"
  }
]

// Helper function to get verification status chip color and style
const getVerificationStatusChipProps = (status: string) => {
  switch (status?.toLowerCase()) {
    case 'verified':
      return { color: 'success' as const, variant: 'filled' as const }
    case 'pending':
      return { color: 'warning' as const, variant: 'filled' as const }
    case 'denied':
      return { color: 'error' as const, variant: 'filled' as const }
    default:
      return { color: 'default' as const, variant: 'filled' as const }
  }
}

const columns: GridColDef<InsuranceDisplayData>[] = [
  {
    field: 'patientDetails',
    headerName: 'Patient Details',
    flex: 1,
    minWidth: 200,
    sortable: true,
    renderHeader: () => (
      <Box
        sx={{
          display: 'flex',
          fontWeight: 'medium',
          alignItems: 'center',
          gap: 0.5,
          fontSize: '0.875rem',
          padding: '16px',
        }}
      >
        Patient Details
      </Box>
    ),
    renderCell: (params) => {
      const row = params.row

      return (
        <Box
          sx={{ height: '100%', fontSize: '0.875rem' }}
          color="black"
          padding={'16px'}
          display={'flex'}
          flexDirection={'column'}
          justifyContent={'center'}
        >
          <Typography
            variant="body2"
            sx={{ fontWeight: 'bold', lineHeight: 1.2, mb: 0.5 }}
          >
            {row.patientName}
          </Typography>
          <Typography
            variant="body2"
            sx={{ fontWeight: 'regular', lineHeight: 1.2, mb: 0.5 }}
          >
            {row.patientId} • DOB: {row.patientDOB}
          </Typography>
          <Typography
            variant="body2"
            sx={{ fontWeight: 'regular', lineHeight: 1.2, mb: 0.5 }}
          >
            {row.patientEmail}
          </Typography>
          <Typography
            variant="body2"
            sx={{ fontWeight: 'regular', lineHeight: 1.2 }}
          >
            {row.patientContact}
          </Typography>
        </Box>
      )
    },
  },
  {
    field: 'studyDetails',
    headerName: 'Study Details',
    flex: 1,
    minWidth: 200,
    renderHeader: () => (
      <Box
        sx={{
          display: 'flex',
          fontWeight: 'medium',
          alignItems: 'center',
          gap: 0.5,
          fontSize: '0.875rem',
          padding: '16px',
        }}
      >
        Study Details
      </Box>
    ),
    renderCell: (params) => {
      const row = params.row

      return (
        <Box
          sx={{ height: '100%', fontSize: '0.875rem' }}
          color="black"
          padding={'16px'}
          display={'flex'}
          flexDirection={'column'}
          justifyContent={'center'}
        >
          <Typography
            variant="body2"
            sx={{ fontWeight: 'bold', lineHeight: 1.2, mb: 0.5 }}
          >
            Order ID: {row.orderId}
          </Typography>
          <Typography
            variant="body2"
            sx={{ fontWeight: 'regular', lineHeight: 1.2, mb: 0.5 }}
          >
            {row.studyType}
          </Typography>
          <Typography
            variant="body2"
            sx={{ fontWeight: 'regular', lineHeight: 1.2, mb: 0.5 }}
          >
            {row.physicianName}
          </Typography>
          <Typography
            variant="body2"
            sx={{ fontWeight: 'regular', lineHeight: 1.2 }}
          >
            {row.locationName}
          </Typography>
        </Box>
      )
    },
  },
  {
    field: 'insuranceDetails',
    headerName: 'Insurance Details',
    flex: 1,
    minWidth: 200,
    renderHeader: () => (
      <Box
        sx={{
          display: 'flex',
          fontWeight: 'medium',
          alignItems: 'center',
          gap: 0.5,
          fontSize: '0.875rem',
          padding: '16px',
        }}
      >
        Insurance Details
      </Box>
    ),
    renderCell: (params) => {
      const row = params.row

      return (
        <Box
          sx={{ height: '100%', fontSize: '0.875rem' }}
          color="black"
          padding={'16px'}
          display={'flex'}
          flexDirection={'column'}
          justifyContent={'center'}
        >
          <Typography
            variant="body2"
            sx={{ fontWeight: 'bold', lineHeight: 1.2, mb: 0.5 }}
          >
            {row.insuranceName}
          </Typography>
          <Typography
            variant="body2"
            sx={{ fontWeight: 'regular', lineHeight: 1.2, mb: 0.5 }}
          >
            Member ID: {row.memberId}
          </Typography>
          <Typography
            variant="body2"
            sx={{ fontWeight: 'regular', lineHeight: 1.2, mb: 0.5 }}
          >
            Policy ID: {row.policyId}
          </Typography>
          <Typography
            variant="body2"
            sx={{ fontWeight: 'regular', lineHeight: 1.2, mb: 0.5 }}
          >
            Plan Type: {row.planType}
          </Typography>
          <Typography
            variant="body2"
            sx={{ fontWeight: 'regular', lineHeight: 1.2 }}
          >
            Group Number: {row.groupNumber}
          </Typography>
        </Box>
      )
    },
  },
  {
    field: 'verification',
    headerName: 'Verification',
    flex: 1,
    minWidth: 200,
    renderHeader: () => (
      <Box
        sx={{
          display: 'flex',
          fontWeight: 'medium',
          alignItems: 'center',
          gap: 0.5,
          fontSize: '0.875rem',
          padding: '16px',
        }}
      >
        Verification
      </Box>
    ),
    renderCell: (params) => {
      const row = params.row
      const chipProps = getVerificationStatusChipProps(row.verificationStatus)

      return (
        <Box
          sx={{ height: '100%', fontSize: '0.875rem' }}
          color="black"
          padding={'16px'}
          display={'flex'}
          flexDirection={'column'}
          justifyContent={'center'}
        >
          <Box sx={{ mb: 0.5 }}>
            <Chip
              label={row.verificationStatus}
              size="small"
              {...chipProps}
              sx={{
                fontSize: '0.8125rem',
                fontWeight: 'regular',
                borderRadius: 5,
              }}
            />
          </Box>
          <Typography
            variant="body2"
            sx={{ fontWeight: 'regular', lineHeight: 1.2, mb: 0.5 }}
          >
            Verified {row.verifiedDate}
          </Typography>
          <Typography
            variant="body2"
            sx={{ fontWeight: 'regular', lineHeight: 1.2, mb: 0.5 }}
          >
            Copay: {row.copay}
          </Typography>
          <Typography
            variant="body2"
            sx={{ fontWeight: 'regular', lineHeight: 1.2 }}
          >
            Deductible: {row.deductible}
          </Typography>
        </Box>
      )
    },
  },
  {
    field: 'authorization',
    headerName: 'Authorization',
    flex: 1,
    minWidth: 200,
    renderHeader: () => (
      <Box
        sx={{
          display: 'flex',
          fontWeight: 'medium',
          alignItems: 'center',
          gap: 0.5,
          fontSize: '0.875rem',
          padding: '16px',
        }}
      >
        Authorization
      </Box>
    ),
    renderCell: (params) => {
      const row = params.row

      return (
        <Box
          sx={{ height: '100%', fontSize: '0.875rem' }}
          color="black"
          padding={'16px'}
          display={'flex'}
          flexDirection={'column'}
          justifyContent={'center'}
        >
          <Typography
            variant="body2"
            sx={{ fontWeight: 'bold', lineHeight: 1.2, mb: 0.5 }}
          >
            Auth ID: {row.authId}
          </Typography>
          <Typography
            variant="body2"
            sx={{ fontWeight: 'regular', lineHeight: 1.2, mb: 0.5 }}
          >
            Approved: {row.approvedDate}
          </Typography>
          <Typography
            variant="body2"
            sx={{ fontWeight: 'regular', lineHeight: 1.2 }}
          >
            Valid until: {row.validUntil}
          </Typography>
        </Box>
      )
    },
  },

]

interface SearchParams {
  Status: string
}

interface SearchDeps extends Partial<SearchParams> {
  DateOfBirth?: string
  StudyDate?: string
}

export const Route = createFileRoute('/_dashboard/insurance/')({
  component: RouteComponent,
  pendingComponent: () => <LoadingComponent />,
  loader: ({ context: { queryClient } }) =>
    queryClient.ensureQueryData(
      postApiOrderSearchOptions({
        scopes: [PatientCreate.value, PatientRead.value],
        responseType: 'json',
      }),
    ),
})

function RouteComponent() {
  const navigate = Route.useNavigate()

  const [paginationModel, setPaginationModel] = useState({
    pageSize: 100,
    page: 0,
  })

  const [filterModel, setFilterModel] = useState<GridFilterModel>({
    items: [],
  })
  const filterButtonRef = useRef<HTMLButtonElement | null>(null)
  const apiRef = useGridApiRef()
  const [userSearchValue, setUserearchValue] = useState('')
  const [searchValue, setSearchValue] = useState('')
  const debouncedSearch = useMemo(
    () =>
      debounce((value: string) => {
        setSearchValue(value)
      }, 300),
    [],
  )
  const [searchParams, setSearchParams] = useState<SearchParams>({
    Status: 'all',
  })

  const statusOptions = [
    { label: 'All Status', value: 'all' },
    { label: 'Verified', value: 'Verified' },
    { label: 'Pending', value: 'Pending' },
    { label: 'Denied', value: 'Denied' },
  ]

  // Build search parameters from current state
  const searchQueryParams = useMemo(() => {
    const params: any = {}

    // Add search value for insurance search
    if (searchValue) {
      // Search in patient name, insurance name, etc.
      params.searchTerm = searchValue
    }

    // Add verification status filter if not "all"
    if (searchParams.Status && searchParams.Status !== 'all') {
      params.VerificationStatus = searchParams.Status
    }

    return Object.keys(params).length > 0 ? params : undefined
  }, [searchValue, searchParams.Status])

  // For now, we'll use sample data instead of API
  // const { data, isFetching } = useQuery(
  //   postApiInsuranceSearchOptions({
  //     query: {
  //       ...paginationQueryParams(paginationModel),
  //       ...searchQueryParams,
  //     },
  //     scopes: [PatientCreate.value, PatientRead.value],
  //     responseType: 'json',
  //   }),
  // )

  const rowCountRef = useRef(sampleInsuranceData.length || 0)
  const rowCount = useMemo(() => {
    rowCountRef.current = sampleInsuranceData.length
    return rowCountRef.current
  }, [sampleInsuranceData.length])

  const debouncedNavigate = useMemo(
    () =>
      debounce((searchParams: SearchParams) => {
        const deps = Object.keys(searchParams).reduce((acc, key) => {
          const val = searchParams[key as keyof typeof searchParams]
          if (typeof val === 'string') {
            acc[key as keyof typeof searchParams] = val === '' ? undefined : val
          }
          if (val === null) {
            acc[key as keyof typeof searchParams] = undefined
          }
          return acc
        }, {} as SearchDeps)
        navigate({
          search: (old) => ({
            ...old,
            ...deps,
          }),
        })
      }, 300),
    [navigate],
  )

  useEffect(() => {
    if (userSearchValue !== searchValue && searchValue) {
      debouncedSearch(userSearchValue)
    }
  }, [debouncedSearch, searchValue, userSearchValue])

  useEffect(() => {
    if (
      Object.keys(searchParams).some(
        (key) =>
          searchParams[key as keyof typeof searchParams] !== '' ||
          searchParams[key as keyof typeof searchParams] !== null,
      )
    ) {
      debouncedNavigate(searchParams)
    }
  }, [debouncedNavigate, searchParams])

  return (
    <PageContainer
      title="Insurance"
      icon={SecurityOutlined}
      actions={
        <Stack direction="row" alignItems="center" spacing={'16px'}>
          <TextField
            value={userSearchValue}
            onChange={(e) => {
              setUserearchValue(e.target.value)
              debouncedSearch(e.target.value)
            }}
            variant="outlined"
            placeholder="Search"
            size="small"
            sx={{ width: '300px' }}
            slotProps={{
              input: {
                startAdornment: (
                  <InputAdornment position="start">
                    <Search />
                  </InputAdornment>
                ),
              },
            }}
          />
          <FormControl size="small" sx={{ width: '180px' }}>
            <InputLabel id="status-label">Status</InputLabel>
            <Select
              labelId="status-label"
              id="status-select"
              value={searchParams.Status}
              onChange={(e) => {
                setSearchParams((prev) => ({
                  ...prev,
                  Status: e.target.value,
                }))
              }}
              label="Status"
            >
              {statusOptions.map((option) => (
                <MenuItem key={option.value} value={option.value}>
                  {option.label}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
          <IconButton
            ref={filterButtonRef}
            onClick={() => {
              apiRef?.current?.showFilterPanel()
            }}
          >
            <FilterAlt />
          </IconButton>
        </Stack>
      }
    >
      {filterModel.items.some((filter) => filter.value) && (
        <StyledCardFooter
          sx={{
            display: 'flex',
            flexWrap: 'wrap',
            gap: '1px',
            padding: '20px',
          }}
        >
          {filterModel.items.map(
            (filter, index) =>
              filter.value && (
                <Chip
                  key={index}
                  label={`${filter.value}`}
                  onDelete={() => {
                    const newItems = [...filterModel.items]
                    newItems.splice(index, 1)
                    setFilterModel({ items: newItems })
                  }}
                  size="small"
                  variant="filled"
                  color="primary"
                  sx={{
                    borderRadius: 5,
                    minWidth: 70,
                    fontSize: '0.75rem',
                    fontWeight: 500,
                  }}
                />
              ),
          )}
        </StyledCardFooter>
      )}
      <Box
        sx={{
          minHeight: 400,
          width: '100%',
          display: 'flex',
          flexDirection: 'column',
        }}
      >
        <DataGridPro
          className="custom-data-grid"
          loading={false}
          rows={sampleInsuranceData}
          rowCount={sampleInsuranceData.length}
          paginationMode="client"
          paginationModel={paginationModel}
          onPaginationModelChange={setPaginationModel}
          pageSizeOptions={[10, 25, 50, 100]}
          pagination
          columns={columns}
          getRowHeight={() => 'auto'}
          sx={{
            '& .MuiDataGrid-cell': {
              display: 'flex',
              alignItems: 'center',
              lineHeight: 'unset !important',
              maxHeight: 'none !important',
              whiteSpace: 'normal',
            },
            '& .MuiDataGrid-row': {
              maxHeight: 'none !important',
            },
          }}
          filterModel={filterModel}
          onFilterModelChange={setFilterModel}
          filterMode="server"
          filterDebounceMs={500}
          apiRef={apiRef}
        />
      </Box>
    </PageContainer>
  )
}
