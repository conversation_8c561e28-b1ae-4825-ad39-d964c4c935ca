import {
	EthosWorkflowsApiAddressWithUseDto,
	EthosWorkflowsApiCreatePatientInputDto,
	EthosWorkflowsApiGuardianDto,
	EthosWorkflowsApiInsuranceDto,
} from '@client/workflows';

type PatientBasicInformationData = Pick<
	EthosWorkflowsApiCreatePatientInputDto,
	'patientInformation' | 'demographics' | 'physicalMeasurements'
>;

type ContactsData = Pick<EthosWorkflowsApiCreatePatientInputDto, 'contactInformation'>;

type AddressWithUseType = {
	type: number | null;
} & EthosWorkflowsApiAddressWithUseDto;

type AddressesData = {
	addresses: AddressWithUseType[];
};

type InsurancesData = {
	insurances: EthosWorkflowsApiInsuranceDto[];
};

type GuardiansData = {
	guardians: EthosWorkflowsApiGuardianDto[];
};

type ClinicalConsiderationsData = Pick<
	EthosWorkflowsApiCreatePatientInputDto,
	| 'clinicalConsiderations'
	| 'schedulingPreferences'
	| 'additionalPatientNotes'
	| 'caregiverInformation'
>;

export type {
	PatientBasicInformationData,
	ContactsData,
	AddressesData,
	InsurancesData,
	GuardiansData,
	ClinicalConsiderationsData,
	AddressWithUseType,
};
