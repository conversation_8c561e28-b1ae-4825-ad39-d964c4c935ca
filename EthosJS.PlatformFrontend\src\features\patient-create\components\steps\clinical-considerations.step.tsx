import NotificationBanner from '@components/notification-banner';
import NotificationSnackbar, { NotificationState } from '@components/notification-snackbar';
import ClinicalConsiderationsForm from '@features/patient-create/forms/clinical-considerations.form';
import usePatient from '@features/patient-create/hooks/use-patient';
import usePatientValidation from '@features/patient-create/hooks/use-patient-validation';
import {
	ClinicalConsiderationsData,
	PatientState,
	StepPropsNew,
} from '@features/patient-create/types';
import { getNewPatientState } from '@features/patient-create/utils';
import dayjs from 'dayjs';
import { useMemo, useState } from 'react';

interface ClinicalConsiderationsStepProps extends Omit<StepPropsNew, 'successCallback'> {
	successCallback: (patientId: string) => void;
}

export default function ClinicalConsiderationsStep({
	patientId,
	successCallback,
}: ClinicalConsiderationsStepProps) {
	const {
		patientData,
		updatePatient,
		commitDraft,
		updatePatientError,
		resetUpdatePatientMutation,
		commitDraftError,
		saveDraft,
	} = usePatient({
		patientId,
	});

	const { validateFormatted } = usePatientValidation();

	const { data } = patientData ?? {};

	const [notification, setNotification] = useState<NotificationState>({
		message: '',
		severity: 'info',
	});

	const resetToast = () => {
		setNotification({
			message: '',
			severity: 'info',
		});
	};

	const patientState = (data?._state as unknown as PatientState) ?? {};
	const stepStatus = patientState.stepState?.ClinicalConsiderations ?? 'NotStarted';

	const savedData = useMemo(() => {
		return {
			clinicalConsiderations: data?.clinicalConsiderations ?? [],
			schedulingPreferences: data?.schedulingPreferences ?? [],
			additionalPatientNotes: data?.additionalPatientNotes ?? '',
			caregiverInformation: data?.caregiverInformation ?? '',
		} as ClinicalConsiderationsData;
	}, [
		data?.clinicalConsiderations,
		data?.schedulingPreferences,
		data?.additionalPatientNotes,
		data?.caregiverInformation,
	]);

	const onSuccessSave = () => {
		commitDraft(patientId, successCallback);
	};

	return (
		<>
			<NotificationSnackbar
				notification={notification}
				onCloseToast={resetToast}
			/>
			<NotificationBanner
				message={updatePatientError?.message || commitDraftError?.message}
				severity="error"
				scrollIntoView
				onClose={resetUpdatePatientMutation}
			/>
			<ClinicalConsiderationsForm
				savedData={savedData}
				onSubmit={(newData) => {
					updatePatient(
						{
							...data,
							...newData,
						},
						getNewPatientState(patientState, 'ClinicalConsiderations', undefined),
						onSuccessSave
					);
				}}
				onSaveDraft={(newData) => {
					saveDraft(
						{
							...data,
							...newData,
						},
						{
							flowState: {
								...patientState.flowState,
								lastUpdate: dayjs().format('MMM D, YYYY h:mm A'),
							},
							stepState: {
								...patientState.stepState,
							},
						},
						() => {
							setNotification({
								message: 'Draft saved successfully',
								severity: 'success',
							});
						}
					);
				}}
				onValidate={validateFormatted}
				isUpdate={stepStatus === 'Complete'}
			/>
		</>
	);
}
