import { Store } from '@tanstack/react-store';
import { scheduleSteps } from '../routes/_dashboard/patients/$patientId/schedule/route';
import { Status } from '@components/left-menu';

type ScheduleState = {
	steps: typeof scheduleSteps;
	currentStep: (typeof scheduleSteps)[number] | null;
	selectedStep: (typeof scheduleSteps)[number] | null;
	status: 'idle' | 'loading' | 'success' | 'error';
};

const defaultState: ScheduleState = {
	steps: scheduleSteps,
	currentStep: scheduleSteps[0],
	selectedStep: null,
	status: 'idle',
};

const store = new Store<ScheduleState>(defaultState);

const actions = {
	setDefaultState: () => {
		const newSteps = scheduleSteps.map((step, index) =>
			// TODO: Needs to change the login once create appointment done
			index === 0 ? { ...step, status: Status.InProgress } : step
		) as typeof scheduleSteps;
		store.setState((prevState) => ({ ...prevState, steps: newSteps }));
	},
	updateState: (state: Partial<ScheduleState>) => {
		store.setState((prev) => ({
			...prev,
			...state,
		}));
	},
	updateStepStatus: (key: string, status: Status) => {
		store.setState((prev) => ({
			...prev,
			steps: prev.steps.map((step) => {
				if (step.key === key) {
					return {
						...step,
						status,
					};
				}
				return step;
			}),
		}));
	},
	moveToNextStep: () => {
		store.setState((prev) => {
			const currentIndex = prev.steps.findIndex((step) => step.key === prev.currentStep?.key);
			if (currentIndex < prev.steps.length - 1) {
				return {
					...prev,
					currentStep: prev.steps[currentIndex + 1],
					selectedStep: null,
				};
			}
			return prev;
		});
	},
	resetState: () => {
		store.setState(() => defaultState);
	},
};

const useScheduleStore = () => ({ store, actions });

export default useScheduleStore;
