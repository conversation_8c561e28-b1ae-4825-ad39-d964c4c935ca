import { PatientRead, PatientCreate } from '@auth/scopes';
import { ReferenceDataSetKeyValueDto } from '@client/refdata';
import { getApiReferenceSetsValuesOptions } from '@client/refdata/@tanstack/react-query.gen';
import { useFieldContext } from '@hooks/form-context';
import { TextField, MenuItem, OutlinedTextFieldProps, CircularProgress } from '@mui/material';
import { useQuery } from '@tanstack/react-query';
import { useEffect } from 'react';
import { SelectFieldOptionType } from './FieldPropType';
import { filter, find, map } from 'lodash';
import { extractProblemDetails } from '@utils/errors';

type BaseSelectFieldProps = Omit<OutlinedTextFieldProps, 'variant'>;

interface OptionsSelectFieldProps extends BaseSelectFieldProps {
	options: SelectFieldOptionType[];
	referenceDataSetName?: never;
	version?: never;
	referenceDataFilter?: never;
	defaultReferenceValue?: never;
}

interface ReferenceDataSelectFieldProps extends BaseSelectFieldProps {
	options?: never;
	referenceDataSetName: string;
	referenceDataFilter?: string;
	version?: string;
	defaultReferenceValue?: string;
}

type AppSelectFieldProps = OptionsSelectFieldProps | ReferenceDataSelectFieldProps;

export default function AppSelectField({
	options,
	referenceDataSetName,
	version = '',
	defaultReferenceValue,
	referenceDataFilter,
	dataTestId,
	...props
}: AppSelectFieldProps & { dataTestId?: string }) {
	const field = useFieldContext<string>();

	const { data, isFetching, error } = useQuery({
		...getApiReferenceSetsValuesOptions({
			scopes: [PatientRead.value, PatientCreate.value],
			query: {
				setName: referenceDataSetName,
				version: version,
				filter: referenceDataFilter,
			},
			responseType: 'json',
		}),
		enabled: !!referenceDataSetName,
	});

	const items = (data as { items: ReferenceDataSetKeyValueDto[] })?.items ?? [];

	const menuItems =
		options ||
		map(
			filter(items, (item): item is ReferenceDataSetKeyValueDto & { id: string } => !!item.id),
			(item) =>
				({
					label: item.key?.value?.toString() ?? 'Unk',
					value: item.id,
				}) as SelectFieldOptionType
		);

	useEffect(() => {
		if (menuItems.length > 0 && !field.state.value) {
			const selectedOption = defaultReferenceValue
				? find(menuItems, { label: defaultReferenceValue })
				: menuItems[0];
			field.setValue((selectedOption?.value as string) ?? '');
			const meta = field.getMeta();
			field.setMeta({ ...meta, isDirty: true });
		}
	}, [menuItems, field.state.value, defaultReferenceValue, field]);

	const shouldShowError = field.getMeta().isTouched || field.getMeta().isDirty;
	const errors = shouldShowError ? field.getMeta().errors.map((error) => error.message) : [];

	return (
		<TextField
			value={find(menuItems, { value: field.state.value })?.value ?? ''}
			onChange={(e) => {
				const selectedOption = find(menuItems, ({ value }) => value.toString() === e.target.value);
				field.setValue((selectedOption?.value as string) ?? '');
			}}
			error={shouldShowError && errors.length > 0}
			helperText={errors.join(', ')}
			select={true}
			fullWidth
			variant="outlined"
			slotProps={{
				input: {
					inputProps: {
						'data-testid': dataTestId,
					},
				},
			}}
			{...props}>
			{isFetching && (
				<MenuItem
					disabled
					sx={{ justifyContent: 'center' }}>
					<CircularProgress size={20} />
				</MenuItem>
			)}
			{menuItems.length === 0 && !isFetching && !error && (
				<MenuItem
					disabled
					sx={{ justifyContent: 'center' }}>
					No options available
				</MenuItem>
			)}
			{error && (
				<MenuItem
					disabled
					sx={{ justifyContent: 'center' }}>
					{
						//@ts-expect-error Type Mismatch
						extractProblemDetails(error).title ?? 'Error loading options'
					}
				</MenuItem>
			)}
			{menuItems.map((option) => (
				<MenuItem
					key={option.value?.toString()}
					value={option.value?.toString()}>
					{option.label}
				</MenuItem>
			))}
		</TextField>
	);
}
