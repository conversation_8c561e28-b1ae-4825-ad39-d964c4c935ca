import { PatientCreate, PatientRead } from "@auth/scopes";
import { UploadFile } from "@mui/icons-material";
import { <PERSON>ert, Button, CircularProgress, FormHelperText, Box, LinearProgress, Typography, lighten } from "@mui/material";
import { useState, useRef } from "react";
import { useMutation } from "@tanstack/react-query";
import { extractProblemDetails } from "@utils/errors";
import { postApiFileRequestUploadTokenMutation, postApiFileUploadMutation } from "@client/workflows/@tanstack/react-query.gen";

export interface FileUploadProps {
    workflowId: string;
    onFileUpload: (fileId: string, fileName: string) => void;
}

export default function FileUpload({
    workflowId,
    onFileUpload,
}: FileUploadProps) {

    const fileInputRef = useRef<HTMLInputElement>(null);
    const [uploadProgress, setUploadProgress] = useState(0);
    const [selectedFile, setSelectedFile] = useState<File | null>(null);
    const [uploadComplete, setUploadComplete] = useState(false);


    const { mutateAsync: requestToken, isPending: isTokenPending, isError: isTokenError, error: tokenError, } = useMutation(postApiFileRequestUploadTokenMutation({
        scopes: [PatientCreate.value, PatientRead.value]
    },));

    const { mutateAsync: uploadFile, isPending: isUploading, isError: isUploadError, error: uploadError } = useMutation(
        {
            ...postApiFileUploadMutation({
                scopes: [PatientCreate.value, PatientRead.value],
                onUploadProgress: (progressEvent) => {
                    const percentCompleted = Math.round((progressEvent.loaded * 100) / (progressEvent.total || 1));
                    setUploadProgress(percentCompleted);
                },
            }),
            onSettled(data, error) {
                if (error) {
                    console.error(error);
                    removeFile();
                }
                if (data && data.fileId && selectedFile) {
                    setUploadComplete(true);
                    onFileUpload(data.fileId, selectedFile.name);
                }
            }
        }
    );

    const triggerFileInput = () => {
        if (!isTokenPending && !isUploading) {
            fileInputRef.current?.click();
        }
    };

    const handleFileChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
        const file = event.target.files?.[0] || null;
        if (file) {
            setSelectedFile(file);
            const rest = await requestToken({
                body: {
                    contextEntityType: 'Workflow',
                    contextEntityId: workflowId,
                    purpose: 'Workflow'
                }
            });
            const tokenData = JSON.parse(rest as string);
            const fileData = new FormData()
            fileData.append('file', file, file.name);
            if (tokenData.uploadToken && tokenData.uploadUrl) {
                uploadFile({
                    body: fileData as any,
                    headers: {
                        'Upload-Token': tokenData.uploadToken,
                    },
                });
            }
        }
    };

    const removeFile = () => {
        setSelectedFile(null);
        setUploadProgress(0);
        setUploadComplete(false);
        if (fileInputRef.current) {
            fileInputRef.current.value = '';
        }
    };

    return (
        <Box sx={{ mt: 2 }}>
            <input
                ref={fileInputRef}
                type="file"
                accept=".pdf,.jpg,.jpeg,.png"
                style={{ display: 'none' }}
                onChange={handleFileChange}
            />
            {isTokenError && (
                <Alert severity="error" sx={{ mb: 2 }}>
                    {tokenError.message}
                </Alert>
            )}
            {isUploadError && (
                <Alert severity="error" sx={{ mb: 2 }}>
                    {
                        extractProblemDetails(uploadError as any).detail
                    }
                </Alert>
            )}
            <Box
                sx={theme => ({
                    border: '2px dashed',
                    borderColor: 'primary.main',
                    borderRadius: 1,
                    p: 3,
                    backgroundColor: 'primary.lighter',
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'center',
                    justifyContent: 'center',
                    cursor: isTokenPending || isTokenError || isUploading ? 'not-allowed' : 'pointer',
                    opacity: isTokenError ? 0.6 : 1,
                    pointerEvents: isTokenError ? 'none' : 'auto',
                    transition: 'background-color 0.2s ease',
                    '&:hover': {
                        backgroundColor: lighten(theme.palette.primary.light, 0.85),
                        opacity: isTokenPending || isTokenError || isUploading ? 1 : 0.9
                    },
                    position: 'relative'
                })}
                onClick={triggerFileInput}
                onDragOver={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                }}
                onDrop={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    if (isTokenPending || isTokenError || isUploading) return;

                    const files = e.dataTransfer.files;
                    if (files && files.length > 0) {
                        const event = { target: { files } } as React.ChangeEvent<HTMLInputElement>;
                        handleFileChange(event);
                    }
                }}
            >
                {(isTokenPending) && (
                    <Box sx={{
                        position: 'absolute',
                        top: 0,
                        left: 0,
                        right: 0,
                        bottom: 0,
                        display: 'flex',
                        flexDirection: 'column',
                        alignItems: 'center',
                        justifyContent: 'center',
                        backgroundColor: 'rgba(255, 255, 255, 0.7)',
                        zIndex: 1
                    }}>
                        <CircularProgress size={40} />
                    </Box>
                )}

                {isUploading && (
                    <Box sx={{ width: '80%', mt: 2 }}>
                        <Typography variant="body2" align="center">
                            Uploading: {uploadProgress}%
                        </Typography>
                        <LinearProgress
                            variant="determinate"
                            value={uploadProgress}
                            sx={{ mt: 1, height: 8, borderRadius: 4 }}
                        />
                    </Box>
                )}

                <UploadFile fontSize="large" color="primary" />
                <Box sx={{ mt: 1, textAlign: 'center' }}>
                    <Box sx={{ fontWeight: 'bold', mb: 0.5 }}>Drag and drop your file here</Box>
                    <Box sx={{ fontSize: '0.875rem' }}>or <Box component="span" sx={{ color: 'primary.main', textDecoration: 'underline' }}>browse</Box> to choose a file</Box>
                    <Box sx={{ fontSize: '0.75rem', mt: 1, color: 'text.secondary' }}>
                        Supported formats: PDF, JPG, JPEG, PNG
                    </Box>
                </Box>
            </Box>

            {selectedFile && (
                <Box sx={{ mt: 1, display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                    <FormHelperText sx={{ m: 0 }}>
                        File selected: <Box component="span" sx={{ fontWeight: 'bold' }}>{selectedFile.name}</Box>
                        {uploadComplete && <Box component="span" sx={{ color: 'success.main', ml: 1 }}>(Upload complete)</Box>}
                    </FormHelperText>
                    <Button
                        size="small"
                        color="error"
                        onClick={removeFile}
                        disabled={isUploading}
                    >
                        Remove
                    </Button>
                </Box>
            )}
        </Box>
    );
}