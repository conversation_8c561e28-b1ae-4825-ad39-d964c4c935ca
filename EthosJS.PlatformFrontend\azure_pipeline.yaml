name: '$(Date:yyyyMMdd)$(Rev:.r)' # e.g. 20250625.1

trigger:
  branches:
    include:
      - '*'
  paths:
    include:
      - EthosJS.PlatformFrontend/*

pool:
  name: 'Default'

variables:
  acrName: 'ethoscrdev.azurecr.io'
  # Tag will be read from .env-dev file
  isMaster: $[eq(variables['Build.SourceBranch'], 'refs/heads/master')]
  isNewGenerator: $[eq(variables['Build.SourceBranch'], 'refs/heads/new-generator')]
  VITE_APP_VERSION: '$(Build.BuildNumber)'
  VITE_TFVC_CHANGESET: '$(Build.SourceVersion)'
  VERSION: '$(Date:yyyyMMdd)$(Rev:.r)'      # e.g. 20250625.1
parameters:
  - name: envName
    displayName: Environment Name
    type: string
    default: dev
    values:
      - test
      - dev
      - uat

stages:
  - stage: Build
    displayName: "Build and Test Projects"
    jobs:
      - job: Build_Ethos_Workflows
        displayName: "Build and Test Ethos.Workflows"
        # condition: contains(variables['Build.ChangedFiles'], 'EthosJS.PlatformFrontend/')
        steps:
          - script: |
              echo "Reading tag from .env-dev file..."
              
              # Check if .env-dev file exists
              if [ ! -f "EthosJS.PlatformFrontend/external-assets/k8s-manifests/.env-dev" ]; then
                echo ".env-dev file not found. Using default tag for first-time setup."
                echo "##vso[task.setvariable variable=tag]2025.05.2"
              else
                # Read TAG from .env-dev file
                TAG_VALUE=$(grep "^TAG=" EthosJS.PlatformFrontend/external-assets/k8s-manifests/.env-dev | cut -d'=' -f2 | tr -d '"' | tr -d "'")
              
                if [ -z "$TAG_VALUE" ]; then
                  echo "TAG not found in .env-dev file. Using default tag."
                  echo "##vso[task.setvariable variable=tag]2025.05.2"
                else
                  echo "Tag found: $TAG_VALUE"
                  echo "##vso[task.setvariable variable=tag]$TAG_VALUE"
                fi
              fi
              
              echo "Using tag: $(tag)"
            displayName: 'Read Tag from .env-dev File'

          - task: UseDotNet@2
            inputs:
              packageType: 'sdk'
              version: '8.x'
            displayName: 'Install .NET SDK'

          - script: |
              REPORT_TIME=$(date +"%Y_%m_%d_%H_%M_%S")
              echo "##vso[task.setvariable variable=REPORT_TIME]$REPORT_TIME"
            displayName: 'Set SAST Report Timestamp'

          - script: |
              dotnet tool install --global Microsoft.CST.DevSkim.CLI
              export PATH="$PATH:$HOME/.dotnet/tools"
              devskim analyze -I EthosJS.PlatformFrontend/ --output-format sarifv2 --output-file "SAST_Scan_Report_EthosReferenceData_$(REPORT_TIME).sarif"
            displayName: 'Run DevSkim SAST Scan'

          - script: |
              sudo pip install sarif-tools
              python3 EthosJS.PlatformFrontend/external-assets/Scripts/convert_to_html_sast.py "SAST_Scan_Report_EthosReferenceData_$(REPORT_TIME).sarif" "SAST_Scan_Report_EthosReferenceData_$(REPORT_TIME).html"
            displayName: 'Convert SARIF to HTML'

          - publish: SAST_Scan_Report_EthosReferenceData_$(REPORT_TIME).html
            artifact: devskim-html-results
            displayName: 'Publish DevSkim SAST Results in HTML'

          - publish: SAST_Scan_Report_EthosReferenceData_$(REPORT_TIME).sarif
            artifact: devskim-sast-results
            displayName: 'Publish DevSkim SAST Results'

          - script: |
              echo "ENV_NAME passed to the pipeline: ${{ parameters.envName }}"
            displayName: 'Log ENV_NAME'

          - script: |
              echo "Building Docker image with tag: $(tag)"
              echo "VITE_APP_VERSION: $(VITE_APP_VERSION)"
              echo "VITE_TFVC_CHANGESET: $(VITE_TFVC_CHANGESET)"
              echo "VERSION: $(VERSION)"
            displayName: 'Display Tag Information'

          # - task: Docker@2
          #   displayName: Build EthosJS.PlatformFrontend Image
          #   inputs:
          #     command: build
          #     containerRegistry: 'acrdev-service-connection'
          #     repository: 'ethosjs-platformfrontend'
          #     dockerfile: './EthosJS.PlatformFrontend/Dockerfile'
          #     buildContext: './EthosJS.PlatformFrontend'
          #     arguments: '--build-arg ENV_NAME=${{ parameters.envName }}' 
          #     tags: |
          #       $(tag).${{ parameters.envName }}
                
          - task: Docker@2
            displayName: Build EthosJS.PlatformFrontend Image
            inputs:
              command: build
              containerRegistry: 'acrdev-service-connection'
              repository: 'ethosjs-platformfrontend'
              dockerfile: './EthosJS.PlatformFrontend/Dockerfile'
              buildContext: './EthosJS.PlatformFrontend'
              arguments: |
                --build-arg ENV_NAME=${{ parameters.envName }} 
                --build-arg VITE_APP_VERSION=$(VITE_APP_VERSION)
                --build-arg VITE_TFVC_CHANGESET=$(VITE_TFVC_CHANGESET)
              tags: |
                $(tag).${{ parameters.envName }}

          - task: Docker@2
            condition: and(succeeded(), eq(variables['Build.SourceBranch'], 'refs/heads/develop'))
            displayName: Push EthosJS.PlatformFrontend Image
            inputs:
              command: push
              containerRegistry: 'acrdev-service-connection'
              repository: 'ethosjs-platformfrontend'
              tags: |
                $(tag).${{ parameters.envName }}

          - script: |
              # Install Trivy
              sudo apt install wget -y
              wget https://github.com/aquasecurity/trivy/releases/download/v0.63.0/trivy_0.63.0_Linux-64bit.deb
              sudo dpkg -i trivy_0.63.0_Linux-64bit.deb
              trivy --version
            displayName: 'Install Trivy Scanner'

          - script: |
              # Scan dependencies (NuGet packages, etc.)
              echo "Scanning dependencies with Trivy..."
              trivy fs --format json --output "Trivy_Dependencies_SCAN_Report_EthosJSPlatformFrontend_$(REPORT_TIME).json" ./EthosJS.PlatformFrontend/
              echo "Dependencies scan completed"
            displayName: 'Trivy Dependencies Scan'

          - script: |
              # Scan the built Docker image
              echo "Scanning Docker image with Trivy..."
              trivy image --scanners vuln --format json  --output "Trivy_Image_SCAN_Report_EthosJSPlatformFrontend_$(REPORT_TIME).json" ethoscrdev.azurecr.io/ethosjs-platformfrontend:$(tag).${{ parameters.envName }}
              echo "Docker image scan completed"
            displayName: 'Trivy Docker Image Scan'

          - script: |
              # Convert Trivy JSON reports to HTML
              pip install pandas
              python3 EthosJS.PlatformFrontend/external-assets/Scripts/convert_to_html_trivy.py  "Trivy_Dependencies_SCAN_Report_EthosJSPlatformFrontend_$(REPORT_TIME).json" "Trivy_Dependencies_SCAN_Report_EthosJSPlatformFrontend_$(REPORT_TIME).html" "Trivy Dependencies SCAN"
              echo "Trivy dependencies HTML report generated"
              python3 EthosJS.PlatformFrontend/external-assets/Scripts/convert_to_html_trivy.py "Trivy_Image_SCAN_Report_EthosJSPlatformFrontend_$(REPORT_TIME).json" "Trivy_Image_SCAN_Report_EthosJSPlatformFrontend_$(REPORT_TIME).html" "Trivy Docker IMAGE SCAN"
              echo "Trivy Docker HTML report generated"
              ls -l *.sarif *.json *.html
            displayName: 'Convert Trivy Reports to HTML'

          - publish: Trivy_Dependencies_SCAN_Report_EthosJSPlatformFrontend_$(REPORT_TIME).html
            artifact: trivy-dependencies-html-results
            displayName: 'Publish Trivy Dependencies HTML Results'

          - publish: Trivy_Dependencies_SCAN_Report_EthosJSPlatformFrontend_$(REPORT_TIME).json
            artifact: trivy-dependencies-json-results
            displayName: 'Publish Trivy Dependencies JSON Results'

          - publish: Trivy_Image_SCAN_Report_EthosJSPlatformFrontend_$(REPORT_TIME).html
            artifact: trivy-image-html-results
            displayName: 'Publish Trivy Image HTML Results'

          - publish: Trivy_Image_SCAN_Report_EthosJSPlatformFrontend_$(REPORT_TIME).json
            artifact: trivy-image-json-results
            displayName: 'Publish Trivy Image JSON Results'

  - stage: Deploy
    displayName: "Deploy to Kubernetes"
    condition: and(succeeded(), eq(variables['Build.SourceBranch'], 'refs/heads/master'))
    jobs:
      - job: Deploy_to_AKS
        displayName: "Deploy EthosJS.PlatformFrontend"
        steps:
          - script: |
              echo "Reading tag from .env-dev file for deployment..."
              
              # Check if .env-dev file exists  
              if [ ! -f "EthosJS.PlatformFrontend/external-assets/k8s-manifests/.env-dev" ]; then
                echo ".env-dev file not found. Using default tag."
                echo "##vso[task.setvariable variable=deployTag]2025.05.2"
              else
                # Read TAG from .env-dev file
                TAG_VALUE=$(grep "^TAG=" EthosJS.PlatformFrontend/external-assets/k8s-manifests/.env-dev | cut -d'=' -f2 | tr -d '"' | tr -d "'")
              
                if [ -z "$TAG_VALUE" ]; then
                  echo "TAG not found in .env-dev file. Using default tag."
                  echo "##vso[task.setvariable variable=deployTag]2025.05.2"
                else
                  echo "Deploying with tag: $TAG_VALUE"
                  echo "##vso[task.setvariable variable=deployTag]$TAG_VALUE"
                fi
              fi
            displayName: 'Read Tag for Deployment'

          - script: |
              echo "Updating deployment.yaml with dynamic tag..."
              
              # Update the image tag in deployment.yaml
              sed -i "s|ethoscrdev.azurecr.io/ethosjs-platformfrontend:.*|ethoscrdev.azurecr.io/ethosjs-platformfrontend:$(deployTag).${{ parameters.envName }}|g" EthosJS.PlatformFrontend/external-assets/k8s-manifests/deployment.yaml
              
              echo "Updated deployment.yaml:"
              cat EthosJS.PlatformFrontend/external-assets/k8s-manifests/deployment.yaml | grep "image:"

            displayName: 'Update Deployment Manifest with Dynamic Tag'

          - script: |
              echo "Checking if kubectl is installed..."
              if ! command -v kubectl &> /dev/null; then
                echo "kubectl not found! Installing..."
                sudo apt-get update && sudo apt-get install -y kubectl
              else
                echo "kubectl is already installed."
              fi
            displayName: 'Ensure kubectl is Installed'

          - script: |
              echo "Validating kubectl version..."
              kubectl version --client
            displayName: 'Validate kubectl Installation'

          - task: KubernetesManifest@1
            inputs:
              action: 'deploy'
              namespace: 'ethos-ns-dev'
              manifests: |
                ./EthosJS.PlatformFrontend/external-assets/k8s-manifests/deployment.yaml
                ./EthosJS.PlatformFrontend/external-assets/k8s-manifests/service.yaml
              kubernetesServiceEndpoint: 'aks-service-connection'
            displayName: 'Deploy Application to AKS'

          - task: Kubernetes@1
            displayName: 'Restart Deployment'
            inputs:
              connectionType: 'Kubernetes Service Connection'
              kubernetesServiceEndpoint: 'aks-service-connection'
              command: 'rollout'
              arguments: 'restart deployment ethosjs-platformfrontend -n ethos-ns-dev'

          - script: |
              echo "Cleaning up local Docker images..."
              docker images | grep ethosjs-platformfrontend | awk '{print $3}' | xargs -r docker rmi || true
              docker system prune -a -f
            displayName: 'Clean Up Local Docker Images'
            continueOnError: true

          - script: |
              echo "============================================"
              echo "🚀 DEPLOYMENT COMPLETED SUCCESSFULLY! 🚀"
              echo "============================================"
              echo "Deployed Tag: $(deployTag)${{ parameters.envName }}"
              echo "Environment: ethos-ns-dev"
              echo "Application: ethosjs-platformfrontend"
              echo "============================================"
            displayName: 'Deployment Summary'