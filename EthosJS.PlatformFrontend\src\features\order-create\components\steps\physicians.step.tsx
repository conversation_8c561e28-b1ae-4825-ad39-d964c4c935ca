import PhysiciansForm from '@features/order-create/forms/physicians.form';
import useOrder from '@features/order-create/hooks/use-order';
import { AddPhysiciansFormValues, OrderState, StepProps } from '@features/order-create/types';
import dayjs from 'dayjs';
import { useMemo } from 'react';

export default function PhysiciansStep({ orderId, successCallback }: StepProps) {
	const { orderData, updateOrder } = useOrder({ orderId });

	const savedData = useMemo(() => {
		if (!orderData) {
			return {} as AddPhysiciansFormValues;
		}
		const { data } = orderData;
		return {
			primaryCarePhysicianId: data?.primaryCarePhysicianId,
			referringPhysicianId: data?.referringPhysicianId,
			interpretingPhysicianId: data?.interpretingPhysicianId,
		} as AddPhysiciansFormValues;
	}, [orderData]);

	const onPhysiciansSubmit = (newData: AddPhysiciansFormValues) => {
		const { data } = orderData;
		const { _state } = data;
		const { flowState, stepState } = _state as unknown as OrderState;
		updateOrder(
			{
				...data,
				...newData,
			},
			{
				flowState: {
					...flowState,
					status: 'InProgress',
					progress: 75,
					lastUpdate: dayjs().format('MMM D, YYYY h:mm A'),
				},
				stepState: {
					...stepState,
					AddPhysicians: 'Complete',
					ReviewAndSubmitOrder: 'InProgress',
				},
			},
			successCallback
		);
	};

	return (
		<PhysiciansForm
			savedData={savedData}
			onSubmit={onPhysiciansSubmit}
			onSaveDraft={(data) => {
				updateOrder({ ...data });
			}}
			onValidate={async () => {
				return undefined;
			}}
		/>
	);
}
