import StepCardControl from '@components/step-card-control';
import { Box, CardContent, Button, Stack } from '@mui/material';
import { AppFormType, useAppForm } from '@hooks/app-form';
import { formOptions, useStore } from '@tanstack/react-form';
import ArrayFieldContainer from '@components/array-field-container';
import { Siren, Mail, Phone } from 'lucide-react';

import MainCardContainer from '@components/main-container/main-card-container';
import { ValidationErrors } from '@app-types/validation';
import { ContactsData } from '../types';
import { FormProps } from '@components/forms/predefined-form-props';
import { useMemo } from 'react';
import PhoneNumberForm, {
	defaultValues as phoneNumberFormDefaultValues,
} from '@components/forms/app-phone-number-form';
import EmailForm, {
	defaultValues as emailFormDefaultValues,
} from '@components/forms/app-email-form';
import EmergencyContactForm, {
	defaultValues as emergencyContactFormDefaultValues,
} from '@components/forms/app-emergency-contact-form';
import { formHasErrors, getErrorsForIndexField } from '@utils/forms';
import { formatEmailSummary, formatPhoneNumberSummary } from './formatters';

function contactsOptions(savedData?: ContactsData) {
	return formOptions({
		defaultValues: {
			contactInformation: {
				phoneNumbers: [],
				emails: [],
				emergencyContacts: [],
				...savedData?.contactInformation,
			},
		} as ContactsData,
	});
}

export default function Contacts({
	onSubmit,
	onSaveDraft,
	savedData,
	onValidate,
	isUpdate,
}: FormProps<ContactsData>) {
	const options = useMemo(() => contactsOptions(savedData), [savedData]);

	const form = useAppForm({
		...options,
		validators: {
			onChangeAsyncDebounceMs: 500,
			onChangeAsync: async ({ value }) => {
				const res = await onValidate(value);
				return res;
			},
		},
		onSubmit: async ({ value }) => {
			onSubmit(value);
		},
	});

	const values = useStore(form.store, (state) => state.values);

	const handleUpdate = (updateType: string) => {
		form.handleSubmit().then(() => {
			console.log(`${updateType} successfully`);
		});
	};

	return (
		<Box
			component="form"
			sx={{ height: '100%' }}
			onSubmit={(e) => {
				e.preventDefault();
				form.handleSubmit();
			}}>
			<CardContent sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
				<form.AppField name="contactInformation">
					{() => (
						<>
							<form.AppField
								name="contactInformation.phoneNumbers"
								validators={{
									onChangeAsyncDebounceMs: 500,
									onChangeAsync: async ({ value }): Promise<ValidationErrors | undefined> => {
										const vals = {
											contactInformation: {
												phoneNumbers: value,
											},
										} as ContactsData;
										const res = await onValidate(vals);
										return res;
									},
								}}
								mode="array">
								{({ state, pushValue, removeValue, replaceValue }) => (
									<MainCardContainer
										title="Phone Numbers"
										icon={<Phone />}
										color={
											formHasErrors(form as AppFormType, 'contactInformation.phoneNumbers')
												? 'error'
												: 'primary'
										}
										emphasis={state.value && state.value.length > 0 ? 'high' : 'low'}
										primaryActionType="Add"
										onPrimaryAction={() => pushValue(phoneNumberFormDefaultValues)}>
										{state.value && state.value.length > 0 && (
											<Stack spacing={2}>
												{state.value.map((_, i) => (
													<ArrayFieldContainer
														key={i}
														initialEditState={!savedData?.contactInformation?.phoneNumbers?.[i]}
														title="Phone Number"
														items={
															state.value?.[i] ? formatPhoneNumberSummary(state.value?.[i]) : []
														}>
														{({ setEdit }) => (
															<PhoneNumberForm
																formValues={state.value?.[i]}
																onAdd={(data) => {
																	replaceValue(i, data);
																	setEdit(false);
																	if (isUpdate) {
																		handleUpdate('Phone number');
																	}
																}}
																onCancel={(data) => {
																	if (
																		JSON.stringify(data) ===
																		JSON.stringify(phoneNumberFormDefaultValues)
																	) {
																		removeValue(i);
																	}
																	setEdit(false);
																}}
																onValidate={async (data) => {
																	const phoneNumbers =
																		state.value?.map((_, j) => {
																			if (j === i) {
																				return data;
																			}
																			return state.value?.[j];
																		}) ?? [];
																	const vals = {
																		contactInformation: {
																			phoneNumbers,
																			emails: values?.contactInformation?.emails ?? [],
																			emergencyContacts:
																				values?.contactInformation?.emergencyContacts ?? [],
																		},
																	} as ContactsData;
																	const res = await onValidate(vals);
																	return getErrorsForIndexField(
																		`contactInformation.phoneNumbers[${i}]`,
																		res
																	);
																}}
															/>
														)}
													</ArrayFieldContainer>
												))}
											</Stack>
										)}
									</MainCardContainer>
								)}
							</form.AppField>
							<form.AppField
								name="contactInformation.emails"
								validators={{
									onChangeAsyncDebounceMs: 500,
									onChangeAsync: async ({ value }): Promise<ValidationErrors | undefined> => {
										const vals = {
											contactInformation: {
												emails: value,
											},
										} as ContactsData;
										const res = await onValidate(vals);
										return res;
									},
								}}
								mode="array">
								{({ pushValue, removeValue, state, replaceValue }) => (
									<MainCardContainer
										title="Emails"
										icon={<Mail />}
										color={
											formHasErrors(form as AppFormType, 'contactInformation.emails')
												? 'error'
												: 'primary'
										}
										emphasis={state.value && state.value.length > 0 ? 'high' : 'low'}
										primaryActionType="Add"
										onPrimaryAction={() => pushValue(emailFormDefaultValues)}>
										{state.value && state.value.length > 0 && (
											<Stack spacing={2}>
												{state.value.map((_, i) => (
													<ArrayFieldContainer
														key={i}
														initialEditState={!savedData?.contactInformation?.emails?.[i]}
														items={state.value?.[i] ? formatEmailSummary(state.value?.[i]) : []}
														title="Email">
														{({ setEdit }) => (
															<EmailForm
																key={i}
																formValues={state.value?.[i]}
																isUpdate={isUpdate}
																onAdd={(data) => {
																	replaceValue(i, data);
																	setEdit(false);
																	if (isUpdate) {
																		handleUpdate('Email');
																	}
																}}
																onCancel={() => {
																	if (!state.value?.[i].value) {
																		removeValue(i);
																	}
																	setEdit(false);
																}}
																onValidate={async (data) => {
																	const emails =
																		state.value?.map((_, j) => {
																			if (j === i) {
																				return data;
																			}
																			return state.value?.[j];
																		}) ?? [];
																	const vals = {
																		contactInformation: {
																			phoneNumbers: values?.contactInformation?.phoneNumbers ?? [],
																			emails,
																			emergencyContacts:
																				values?.contactInformation?.emergencyContacts ?? [],
																		},
																	} as ContactsData;
																	const res = await onValidate(vals);
																	return getErrorsForIndexField(
																		`contactInformation.emails[${i}]`,
																		res
																	);
																}}
																onDelete={() => {
																	removeValue(i);
																	setEdit(false);
																}}
															/>
														)}
													</ArrayFieldContainer>
												))}
											</Stack>
										)}
									</MainCardContainer>
								)}
							</form.AppField>
							<form.AppField
								name="contactInformation.emergencyContacts"
								mode="array">
								{({ pushValue, removeValue, state, replaceValue }) => (
									<MainCardContainer
										title="Emergency Contacts"
										icon={<Siren />}
										primaryActionType="Add"
										onPrimaryAction={() => pushValue(emergencyContactFormDefaultValues)}
										color={
											formHasErrors(form as AppFormType, 'contactInformation.emergencyContacts')
												? 'error'
												: 'primary'
										}
										emphasis={state.value && state.value.length > 0 ? 'high' : 'low'}>
										{state.value && state.value.length > 0 && (
											<Stack spacing={2}>
												{state.value.map((_, i) => (
													<EmergencyContactForm
														key={i}
														onAdd={(data) => {
															replaceValue(i, data);
															if (isUpdate) {
																handleUpdate('Emergency contact');
															}
														}}
														onCancel={(shouldRemove) => {
															if (shouldRemove) {
																removeValue(i);
															}
														}}
														formValues={state.value?.[i]}
														onValidate={async (data) => {
															const emergencyContacts =
																state.value?.map((_, j) => {
																	if (j === i) {
																		return data;
																	}
																	return state.value?.[j];
																}) ?? [];
															const vals = {
																contactInformation: {
																	emergencyContacts,
																},
															} as ContactsData;
															const res = await onValidate(vals);
															return getErrorsForIndexField(
																`contactInformation.emergencyContacts[${i}]`,
																res
															);
														}}
													/>
												))}
											</Stack>
										)}
									</MainCardContainer>
								)}
							</form.AppField>
						</>
					)}
				</form.AppField>
			</CardContent>
			<StepCardControl>
				{!isUpdate && (
					<Button
						variant="outlined"
						color="primary"
						onClick={() => onSaveDraft(values)}>
						Save Draft
					</Button>
				)}
				<form.Subscribe
					selector={({ isDirty, canSubmit, errorMap, isSubmitting }) => ({
						isDirty,
						canSubmit,
						errorMap,
						isSubmitting,
					})}
					children={({ isDirty, canSubmit, isSubmitting }) => {
						return (
							<Button
								variant="contained"
								color="primary"
								type="submit"
								loading={isSubmitting}
								disabled={!isDirty || !canSubmit}>
								{isUpdate ? 'Update' : 'Next'}
							</Button>
						);
					}}
				/>
			</StepCardControl>
		</Box>
	);
}
