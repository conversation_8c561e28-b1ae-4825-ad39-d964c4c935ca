import { PatientCreate, PatientRead } from '@auth/scopes';
import { EthosModelQueryDto1EthosModelPhysicianQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null } from '@client/workflows';
import { postApiPhysicianSearchOptions } from '@client/workflows/@tanstack/react-query.gen';
import AppPhysicianForm from '@components/forms/app-physician-form';
import { AppPhysicianFormProps } from '@components/forms/app-physician-form/app-physician.form';
import { useQuery } from '@tanstack/react-query';
import { PhysicianQuery, Query } from '@utils/query-dsl';
import { debounce, map } from 'lodash';
import { useMemo, useEffect, useState } from 'react';

function getQuery(searchTerm: string) {
	if (searchTerm === '') return;
	return Query.literal(
		PhysicianQuery.withApproximateName(searchTerm)
	) as unknown as EthosModelQueryDto1EthosModelPhysicianQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null;
}

interface PhysicianSearchFormProps extends AppPhysicianFormProps {
	is?: boolean;
}

export default function PhysicianSearchForm({ ...props }: PhysicianSearchFormProps) {
	const [searchTerm, setSearchTerm] = useState<string>('');
	const [debouncedSearchTerm, setDebouncedSearchTerm] = useState<string>('');

	const { data: physicianData, isFetching: isPhysicianFetching } = useQuery({
		...postApiPhysicianSearchOptions({
			scopes: [PatientCreate.value, PatientRead.value],
			responseType: 'json',
			body: getQuery(debouncedSearchTerm),
		}),
	});

	const debouncedSearch = useMemo(
		() =>
			debounce((searchTerm: string) => {
				setDebouncedSearchTerm(searchTerm);
			}, 300),
		[]
	);

	useEffect(() => {
		if (searchTerm !== debouncedSearchTerm) {
			debouncedSearch(searchTerm);
		}
	}, [debouncedSearch, debouncedSearchTerm, searchTerm]);

	return (
		<AppPhysicianForm
			{...props}
			options={
				map(physicianData?.items, (item) => ({
					title: `${item.names?.[0]?.firstName ?? ''} ${item.names?.[0]?.lastName ?? ''}`,
					id: item.id,
					description: `${item.names?.[0]?.firstName ?? ''} ${item.names?.[0]?.lastName ?? ''}`,
					meta: {
						npi: item.id,
					},
				})) ?? []
			}
			searchLoading={isPhysicianFetching}
			searchTerm={searchTerm}
			onSearchChange={setSearchTerm}
		/>
	);
}
