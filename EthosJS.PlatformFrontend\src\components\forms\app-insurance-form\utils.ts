import { EthosWorkflowsApiInsuranceDto } from '@client/workflows';
import { z } from 'zod';
import { USA_CODE } from '../app-address-form';
import { ChipData } from '@components/chip-summary';

const insruanceTransformer = z.any().transform((raw: EthosWorkflowsApiInsuranceDto) => {
	return {
		...raw,
	};
});

const defaultValues: EthosWorkflowsApiInsuranceDto = {
	insuranceHolder: {
		relationship: 0,
		name: '',
		dateOfBirth: '',
	},
	insuranceCarrier: null!,
	insuranceId: '',
	policyId: '',
	memberId: '',
	groupNumber: '',
	phoneNumber: {
		use: 4783,
		phoneNumber: '',
	},
	email: {
		use: 0,
		email: '',
	},
	address: {
		use: null!,
		address: {
			line1: '',
			line2: null,
			city: '',
			state: null!,
			postalCode: '',
			country: USA_CODE,
		},
	},
};

function insuranceFormOptions(savedData?: EthosWorkflowsApiInsuranceDto) {
	return {
		defaultValues: {
			...defaultValues,
			...(savedData ? savedData : {}),
		},
	};
}

function formatInsuranceSummary(values: EthosWorkflowsApiInsuranceDto): Array<ChipData> {
	const summary: Array<ChipData> = [];

	// Add insurance carrier and plan type
	summary.push({
		label: 'Insurance',
		value: `${values.insuranceCarrier} - ${values.groupNumber}`,
	});

	// Add policy information
	summary.push({
		label: 'Policy',
		value: `#${values.policyId}`,
	});

	// Add member ID if available
	if (values.memberId) {
		summary.push({
			label: 'Member',
			value: `#${values.memberId}`,
		});
	}

	return summary;
}

export { insruanceTransformer, insuranceFormOptions, defaultValues, formatInsuranceSummary };
