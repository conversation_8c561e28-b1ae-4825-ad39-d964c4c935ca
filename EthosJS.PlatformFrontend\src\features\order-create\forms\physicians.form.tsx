import { Box, Button, Stack } from '@mui/material';

import { useStore } from '@tanstack/react-form';

import { useAppForm } from '@hooks/app-form';
import StepCardControl from '@components/step-card-control';
import { FormProps } from '@components/forms/predefined-form-props';
import { AddPhysiciansFormValues } from '../types';
import PhysicianSearchForm from './physician-search.form';
import { useMemo } from 'react';
import { useQuery } from '@tanstack/react-query';
import { getApiCareLocationByIdOptions } from '@client/workflows/@tanstack/react-query.gen';

const defaultValues: AddPhysiciansFormValues = {
	primaryCarePhysicianId: null,
	referringPhysicianId: null,
	interpretingPhysicianId: null,
};

function physiciansFormOptions(savedData?: AddPhysiciansFormValues) {
	return {
		defaultValues: {
			...defaultValues,
			...(savedData ? savedData : {}),
		},
	};
}

export default function PhysiciansForm({
	onSubmit,
	onSaveDraft,
	savedData,
	careLocationId,
}: FormProps<AddPhysiciansFormValues> & { careLocationId?: string }) {
	const options = useMemo(() => physiciansFormOptions(savedData), [savedData]);

	const form = useAppForm({
		...options,
		onSubmit: ({ value }) => {
			onSubmit(value);
		},
	});

	const { data: careLocation } = useQuery({
		...getApiCareLocationByIdOptions({
			path: { id: careLocationId! },
			responseType: 'json',
		}),
		enabled: !!careLocationId,
		select: (data) => data?.name ?? '',
	});

	const values = useStore(form.store, (state) => state.values);

	const onSelectPysicians = (fieldName: keyof typeof defaultValues, fieldValue: string | null) => {
		form.setFieldValue(fieldName, fieldValue);
	};

	return (
		<Stack
			gap={2}
			p={2}
			component="form"
			onSubmit={(e) => {
				e.preventDefault();
				form.handleSubmit();
			}}>
			{/* <PhysicianSearchForm
				title="1. Ordering Physician *"
				isOptional={false}
				attentionTitle={`Recommended physicians associated with ${careLoction}`}
				value={values['orderingPhysician'] ?? null}
				onSelect={(selectedOption) => onSelectPysicians('orderingPhysician', selectedOption)}
				onAdd={(selectedOption) => onSelectPysicians('orderingPhysician', selectedOption)}
				onCancel={() => onSelectPysicians('orderingPhysician', null)}
			/> */}
			<PhysicianSearchForm
				careLocationId={careLocationId}
				title="2. Interpreting Physician *"
				isOptional={false}
				attentionTitle={`Recommended physicians associated with ${careLocation}`}
				value={values['interpretingPhysicianId'] ?? null}
				onSelect={(selectedOption) => onSelectPysicians('interpretingPhysicianId', selectedOption)}
				onAdd={(selectedOption) => onSelectPysicians('interpretingPhysicianId', selectedOption)}
				onCancel={() => onSelectPysicians('interpretingPhysicianId', null)}
			/>
			<PhysicianSearchForm
				careLocationId={careLocationId}
				title="3. Referring Physician (Optional)"
				isOptional={true}
				attentionTitle={`Recommended physicians associated with ${careLocation}`}
				value={values['referringPhysicianId'] ?? null}
				onSelect={(selectedOption) => onSelectPysicians('referringPhysicianId', selectedOption)}
				onAdd={(selectedOption) => onSelectPysicians('referringPhysicianId', selectedOption)}
				onCancel={() => onSelectPysicians('referringPhysicianId', null)}
			/>
			<PhysicianSearchForm
				careLocationId={careLocationId}
				title="4. Primary Care Physician (Optional)"
				isOptional={true}
				attentionTitle={`Recommended physicians associated with ${careLocation}`}
				value={values['primaryCarePhysicianId'] ?? null}
				onSelect={(selectedOption) => onSelectPysicians('primaryCarePhysicianId', selectedOption)}
				onAdd={(selectedOption) => onSelectPysicians('primaryCarePhysicianId', selectedOption)}
				onCancel={() => onSelectPysicians('primaryCarePhysicianId', null)}
			/>
			<Box>
				<StepCardControl>
					<Button
						variant="outlined"
						onClick={() => onSaveDraft(values)}
						color="primary">
						Save Draft
					</Button>
					<form.Subscribe>
						{({ isSubmitting }) => {
							return (
								<Button
									variant="contained"
									color="primary"
									type="submit"
									loading={isSubmitting}>
									Next
								</Button>
							);
						}}
					</form.Subscribe>
				</StepCardControl>
			</Box>
		</Stack>
	);
}
