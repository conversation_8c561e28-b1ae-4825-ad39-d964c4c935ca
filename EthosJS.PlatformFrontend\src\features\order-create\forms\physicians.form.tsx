import { Box, Button, Stack } from '@mui/material';

import { useStore } from '@tanstack/react-form';

import { useAppForm } from '@hooks/app-form';
import StepCardControl from '@components/step-card-control';
import { FormProps } from '@components/forms/predefined-form-props';
import { AddPhysiciansFormValues } from '../types';
import PhysicianSearchForm from './physician-search.form';
import { useMemo } from 'react';

const defaultValues: AddPhysiciansFormValues = {
	primaryCarePhysicianId: null,
	referringPhysicianId: null,
	interpretingPhysicianId: null,
};

function physiciansFormOptions(savedData?: AddPhysiciansFormValues) {
	return {
		defaultValues: {
			...defaultValues,
			...(savedData ? savedData : {}),
		},
	};
}

export default function PhysiciansForm({
	onSubmit,
	onSaveDraft,
	savedData,
}: FormProps<AddPhysiciansFormValues>) {
	const options = useMemo(() => physiciansFormOptions(savedData), [savedData]);

	const form = useAppForm({
		defaultValues: savedData ?? defaultValues,
		defaultState: {},
		onSubmit: ({ value }) => {
			onSubmit(value);
		},
	});

	const values = useStore(form.store, (state) => state.values);

	const onSelectPysicians = (fieldName: keyof typeof defaultValues, fieldValue: string | null) => {
		form.setFieldValue(fieldName, fieldValue);
	};

	const careLoction = 'Tukwila Sleep Center';

	return (
		<Stack
			gap={2}
			p={2}
			component="form"
			onSubmit={(e) => {
				e.preventDefault();
				form.handleSubmit();
			}}>
			{/* <PhysicianSearchForm
				title="1. Ordering Physician *"
				isOptional={false}
				attentionTitle={`Recommended physicians associated with ${careLoction}`}
				value={values['orderingPhysician'] ?? null}
				onSelect={(selectedOption) => onSelectPysicians('orderingPhysician', selectedOption)}
				onAdd={(selectedOption) => onSelectPysicians('orderingPhysician', selectedOption)}
				onCancel={() => onSelectPysicians('orderingPhysician', null)}
			/> */}
			<PhysicianSearchForm
				title="2. Interpreting Physician *"
				isOptional={false}
				attentionTitle={`Recommended physicians associated with ${careLoction}`}
				value={values['interpretingPhysicianId'] ?? null}
				onSelect={(selectedOption) => onSelectPysicians('interpretingPhysicianId', selectedOption)}
				onAdd={(selectedOption) => onSelectPysicians('interpretingPhysicianId', selectedOption)}
				onCancel={() => onSelectPysicians('interpretingPhysicianId', null)}
			/>
			<PhysicianSearchForm
				title="3. Referring Physician (Optional)"
				isOptional={true}
				attentionTitle={`Recommended physicians associated with ${careLoction}`}
				value={values['referringPhysicianId'] ?? null}
				onSelect={(selectedOption) => onSelectPysicians('referringPhysicianId', selectedOption)}
				onAdd={(selectedOption) => onSelectPysicians('referringPhysicianId', selectedOption)}
				onCancel={() => onSelectPysicians('referringPhysicianId', null)}
			/>
			<PhysicianSearchForm
				title="4. Primary Care Physician (Optional)"
				isOptional={true}
				attentionTitle={`Recommended physicians associated with ${careLoction}`}
				value={values['primaryCarePhysicianId'] ?? null}
				onSelect={(selectedOption) => onSelectPysicians('primaryCarePhysicianId', selectedOption)}
				onAdd={(selectedOption) => onSelectPysicians('primaryCarePhysicianId', selectedOption)}
				onCancel={() => onSelectPysicians('primaryCarePhysicianId', null)}
			/>
			<Box>
				<StepCardControl>
					<Button
						variant="outlined"
						color="primary">
						Save Draft
					</Button>
					<form.Subscribe>
						{({ isSubmitting }) => {
							return (
								<Button
									variant="contained"
									color="primary"
									type="submit"
									loading={isSubmitting}>
									Next
								</Button>
							);
						}}
					</form.Subscribe>
				</StepCardControl>
			</Box>
		</Stack>
	);
}
