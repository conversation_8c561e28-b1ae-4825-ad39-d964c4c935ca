import MainCardContainer from '@components/main-container/main-card-container';
import Filters from './filters';
import ChipSummary from '@components/chip-summary';
import { Calendar, LoaderIcon, MapPin } from 'lucide-react';
import { Stack } from '@mui/material';
import { useStore } from '@tanstack/react-store';
import useCreateAppointmentStore from '@hooks/use-create-appointment-store';

import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone'; // if you want to convert to local timezone
import advancedFormat from 'dayjs/plugin/advancedFormat';
import Badge from '@components/main-container/main-card-badge';
import { useEffect, useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import {
	postApiRoomSearchOptions,
	postApiSchedulingFindSlotsOptions,
} from '@client/workflows/@tanstack/react-query.gen';
import { PatientCreate, PatientRead } from '@auth/scopes';
import { EthosWorkflowsApiCareLocationDto } from '@client/workflows';
import { map } from 'lodash';
import useOrderDetails from '@features/scheduling/hooks/use-order-detailts';
import { EthosWorkflowsControllersAvailableSlotDto } from '@client/workflows';

// Mock data for testing - replace with actual API call
const mockAvailableSlots: EthosWorkflowsControllersAvailableSlotDto[] = [
	{
		date: '2024-01-15',
		roomId: 'Room 101',
		careLocationShiftId: 'shift-001',
		softConstraintViolations: [],
	},
	{
		date: '2024-01-15',
		roomId: 'Room 102',
		careLocationShiftId: 'shift-002',
		softConstraintViolations: ['PreferredTechnician'],
	},
	{
		date: '2024-01-16',
		roomId: 'Room 101',
		careLocationShiftId: 'shift-003',
		softConstraintViolations: [],
	},
	{
		date: '2024-01-16',
		roomId: 'Room 103',
		careLocationShiftId: 'shift-004',
		softConstraintViolations: ['PreferredDayOfWeek'],
	},
	{
		date: '2024-01-17',
		roomId: 'Room 102',
		careLocationShiftId: 'shift-005',
		softConstraintViolations: [],
	},
	{
		date: '2024-01-17',
		roomId: 'Room 201',
		careLocationShiftId: 'shift-006',
		softConstraintViolations: ['EquipmentPreference', 'PreferredTechnician'],
	},
	{
		date: '2024-01-18',
		roomId: 'Room 101',
		careLocationShiftId: 'shift-007',
		softConstraintViolations: [],
	},
	{
		date: '2024-01-18',
		roomId: 'Room 202',
		careLocationShiftId: 'shift-008',
		softConstraintViolations: ['PreferredDayOfWeek'],
	},
	{
		date: '2024-01-19',
		roomId: 'Room 103',
		careLocationShiftId: 'shift-009',
		softConstraintViolations: [],
	},
	{
		date: '2024-01-19',
		roomId: 'Room 201',
		careLocationShiftId: 'shift-010',
		softConstraintViolations: ['PreferredTechnician'],
	},
];

dayjs.extend(utc);
dayjs.extend(timezone);
dayjs.extend(advancedFormat);

type FilterValuesTypes = {
	location: string | null;
	startDate: dayjs.Dayjs | null;
	endDate: dayjs.Dayjs | null;
};

const defaultValues = {
	location: null,
	startDate: dayjs(new Date()),
	endDate: null,
};

const SelectAppointmentDate = ({ orderId, studyId }: { orderId: string; studyId: string }) => {
	const [filterValues, setFilterValues] = useState<FilterValuesTypes>(defaultValues);

	const { store, actions } = useCreateAppointmentStore();
	const {
		values: { selectedAppointment },
	} = useStore(store, (state) => state);

	const { careLocationData } = useOrderDetails({ orderId });

	const { id: careLocationId } = careLocationData ?? {};

	const { data: availableSlots, isLoading: loadingAvailableSlots } = useQuery({
		...postApiSchedulingFindSlotsOptions({
			scopes: [PatientCreate.value, PatientRead.value],
			responseType: 'json',
			body: {
				studyId: studyId as string,
				careLocationId: careLocationId as string,
				startDate: dayjs.utc(filterValues.startDate).format('YYYY-MM-DD'),
				endDate: dayjs.utc(filterValues.endDate).format('YYYY-MM-DD'),
			},
		}),
		enabled: !!careLocationId && !!filterValues.startDate && !!filterValues.endDate,
		select: (data) => data?.availableSlots ?? [],
		initialData: {
			availableSlots: mockAvailableSlots,
		},
	});

	const { data: rooms } = useQuery(
		postApiRoomSearchOptions({
			responseType: 'json',
		})
	);

	const [availableSlotsWithRooms, setAvailableSlotsWithRooms] = useState(mockAvailableSlots);

	useEffect(() => {
		if (availableSlots && rooms) {
			const slotsWithRooms = availableSlots.map((slot, index) => {
				const roomId = rooms.items?.[index] ? rooms.items[index].id : slot.roomId;
				return {
					...slot,
					roomId,
				};
			});
			setAvailableSlotsWithRooms(slotsWithRooms);
		}
	}, [availableSlots, rooms]);

	console.log(availableSlotsWithRooms);
	console.log(rooms);
	console.log(availableSlots);

	const onSelectAppointmentDate = (selectedDateTime: typeof selectedAppointment) => {
		actions.setAppointmentDateTime(selectedDateTime);
	};

	useEffect(() => {
		setFilterValues((prevState) => ({ ...prevState, location: careLocationId as string }));
	}, [careLocationId]);

	return (
		<Stack gap={2}>
			<Filters
				careLocationDetails={careLocationData as EthosWorkflowsApiCareLocationDto}
				onFieldChange={(fieldName: string, fieldValue: dayjs.Dayjs) => {
					setFilterValues((prevState) => ({ ...prevState, [fieldName]: fieldValue }));
				}}
				filterValues={filterValues}
			/>
			<MainCardContainer
				icon={<MapPin />}
				color="success"
				emphasis="low"
				title="Primary Location: Available">
				<ChipSummary
					items={[
						{
							label: 'Care Location',
							value: careLocationData?.name as string,
						},
						...map(careLocationData?.contactDetail?.phoneNumbers, (phoneNumber) => ({
							label: 'Phone',
							value: phoneNumber.phoneNumber,
						})),
						...map(careLocationData?.contactDetail?.emails, (email) => ({
							label: 'Email',
							value: email.email,
						})),
						...map(careLocationData?.contactDetail?.addresses, (address) => ({
							label: 'Address',
							value: address.address.line1,
						})),
					]}
				/>
			</MainCardContainer>

			{loadingAvailableSlots ? (
				<Stack sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
					<LoaderIcon />
				</Stack>
			) : (
				<Stack gap={2}>
					{map(availableSlots, ({ date, careLocationShiftId, roomId }, index) => {
						// const start = dayjs(startDateTime);
						// const end = dayjs(endDateTime);

						// const formattedTitle = `${start.format('MMMM D')}, ${start.format('h A')} - ${end.format('h A')}`;
						// const isSelected = id === selectedAppointment.id;
						const isSelected = careLocationShiftId === selectedAppointment.id;

						return (
							<MainCardContainer
								icon={<Calendar />}
								key={index}
								color="primary"
								emphasis="low"
								title={roomId}
								customAction={
									<Badge
										selectable
										onSelect={() => {
											onSelectAppointmentDate({ id: careLocationShiftId, date });
										}}
										label={isSelected ? 'Selected' : 'Select'}
										isSelected={isSelected}
									/>
								}
							/>
						);
					})}
				</Stack>
			)}
		</Stack>
	);
};

export default SelectAppointmentDate;
