import MainCardContainer from '@components/main-container/main-card-container';
import Filters from './filters';
import ChipSummary from '@components/chip-summary';
import { Calendar, LoaderIcon, MapPin } from 'lucide-react';
import { Stack } from '@mui/material';
import { useStore } from '@tanstack/react-store';
import useCreateAppointmentStore from '@hooks/use-create-appointment-store';

import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone'; // if you want to convert to local timezone
import advancedFormat from 'dayjs/plugin/advancedFormat';
import Badge from '@components/main-container/main-card-badge';
import { useEffect, useMemo, useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import {
	getApiCareLocationByIdOptions,
	postApiSchedulingFindSlotsOptions,
} from '@client/workflows/@tanstack/react-query.gen';
import { PatientCreate, PatientRead } from '@auth/scopes';
import { EthosWorkflowsApiCareLocationDto } from '@client/workflows';
import { parseCareLocation } from '../utils/data-parser';

dayjs.extend(utc);
dayjs.extend(timezone);
dayjs.extend(advancedFormat);

type FilterValuesTypes = {
	location: string | null;
	startDate: dayjs.Dayjs | null;
	endDate: dayjs.Dayjs | null;
};

const availableDate = [
	{
		startDateTime: '2025-06-11T00:00:00Z',
		endDateTime: '2025-06-11T00:00:00Z',
		id: '1',
		date: '2025-06-11T00:00:00Z',
	},
	{
		startDateTime: '2025-06-13T00:00:00Z',
		endDateTime: '2025-06-11T00:00:00Z',
		id: '2',
		date: '2025-06-11T00:00:00Z',
	},
	{
		startDateTime: '2025-06-14T00:00:00Z',
		endDateTime: '2025-06-11T00:00:00Z',
		id: '3',
		date: '2025-06-11T00:00:00Z',
	},
	{
		startDateTime: '2025-06-15T00:00:00Z',
		endDateTime: '2025-06-11T00:00:00Z',
		id: '4',
		date: '2025-06-11T00:00:00Z',
	},
	{
		startDateTime: '2025-06-16T00:00:00Z',
		endDateTime: '2025-06-11T00:00:00Z',
		id: '5',
		date: '2025-06-11T00:00:00Z',
	},
];

const defaultValues = {
	location: null,
	startDate: dayjs(new Date()),
	endDate: null,
};

const SelectAppointmentDate = () => {
	const [filterValues, setFilterValues] = useState<FilterValuesTypes>(defaultValues);

	const { store, actions } = useCreateAppointmentStore();
	const {
		values: { selectedAppointment },
	} = useStore(store, (state) => state);

	// const { studyId } = Route.useSearch();
	// const { store: orderState } = useCreateOrderStore();

	// const orderDetails = useStore(orderState, state => state.meta);

	const careLocationId = useMemo(() => parseCareLocation(undefined)?.careLocation, []);

	const { data: careLocationDetails } = useQuery({
		...getApiCareLocationByIdOptions({
			path: { id: careLocationId as string },
			scopes: [PatientCreate.value, PatientRead.value],
			responseType: 'json',
		}),
		enabled: !!careLocationId,
	});

	const studyId = '1';

	const { isLoading: loadingAvailableSlots } = useQuery({
		...postApiSchedulingFindSlotsOptions({
			scopes: [PatientCreate.value, PatientRead.value],
			responseType: 'json',
			body: {
				studyId: studyId as string,
				careLocationId: careLocationId as string,
				startDate: dayjs.utc(filterValues.startDate).format('YYYY-MM-DD'),
				endDate: dayjs.utc(filterValues.endDate).format('YYYY-MM-DD'),
			},
		}),
		enabled: !!careLocationId && !!filterValues.startDate && !!filterValues.endDate,
	});

	const onSelectAppointmentDate = (selectedDateTime: typeof selectedAppointment) => {
		actions.setAppointmentDateTime(selectedDateTime);
	};

	useEffect(() => {
		setFilterValues((prevState) => ({ ...prevState, location: careLocationId as string }));
	}, [careLocationId]);

	return (
		<Stack gap={2}>
			<Filters
				{...{
					careLocationDetails: careLocationDetails as EthosWorkflowsApiCareLocationDto,
					careLocationId: careLocationId as string,
					onFieldChange: (fieldName: string, fieldValue: dayjs.Dayjs) => {
						setFilterValues((prevState) => ({ ...prevState, [fieldName]: fieldValue }));
					},
					filterValues,
				}}
			/>
			<MainCardContainer
				icon={<MapPin />}
				color="success"
				emphasis="low"
				title="Primary Location: Available">
				<ChipSummary
					items={[
						{
							label: 'Care Location',
							value: careLocationDetails?.name as string,
						},
						{
							label: 'Phone',
							value: '(*************',
						},
						{
							label: 'Fax',
							value: '(*************',
						},
						{
							label: 'Address',
							value: '1708 S Yakima Avenue, Ste 105 Tacoma WA 98405-5307',
						},
						{
							hideSeperator: true,
							value: 'Available Equipments',
							color: 'success',
						},
						{
							hideSeperator: true,
							value: 'Female Technicians',
							color: 'success',
						},
					]}
				/>
			</MainCardContainer>

			{loadingAvailableSlots ? (
				<Stack sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
					<LoaderIcon />
				</Stack>
			) : (
				<Stack gap={2}>
					{availableDate.map(({ startDateTime, endDateTime, id, date }, index) => {
						const start = dayjs(startDateTime);
						const end = dayjs(endDateTime);

						const formattedTitle = `${start.format('MMMM D')}, ${start.format('h A')} - ${end.format('h A')}`;
						const isSelected = id === selectedAppointment.id;

						return (
							<MainCardContainer
								icon={<Calendar />}
								key={index}
								color="primary"
								emphasis="low"
								title={formattedTitle}
								customAction={
									<Badge
										selectable
										onSelect={() => {
											onSelectAppointmentDate({ id, date });
										}}
										label={isSelected ? 'Selected' : 'Select'}
										isSelected={isSelected}
									/>
								}
							/>
						);
					})}
				</Stack>
			)}
		</Stack>
	);
};

export default SelectAppointmentDate;
