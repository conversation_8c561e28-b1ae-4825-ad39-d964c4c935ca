import React, { useImperativeHandle } from 'react';
import { Stack, Grid2 as Grid } from '@mui/material';
import { useForm } from '@tanstack/react-form';
import { useAppForm } from '@hooks/app-form';
import { useStore } from '@tanstack/react-store';
import usePreStudyStore from '@hooks/use-pre-study-store';
import MainCardContainer from '@components/main-container/main-card-container';
import { BedDouble } from 'lucide-react';

interface RoomSetupStepProps {
  formRef: React.RefObject<ReturnType<typeof useForm> | null>;
  studyId: string;
  patientId: string;
}

const RoomSetupStep = ({ formRef, studyId, patientId }: RoomSetupStepProps) => {
  const { store, actions } = usePreStudyStore();
  const { values } = useStore(store, (state) => state);

  const form = useAppForm({
    defaultValues: {
      assignedRoom: values.roomSetup?.assignedRoom || '',
      technicianAssignment: values.roomSetup?.technicianAssignment || ''
    },
    onSubmit({ value }) {
      actions.setValues({
        roomSetup: {
          assignedRoom: value.assignedRoom,
          technicianAssignment: value.technicianAssignment
        }
      });
      actions.moveNext();
    }
  });

  const { values: formValues } = useStore(form.store, ({ values }) => ({ values }));

  // Update store whenever form values change
  React.useEffect(() => {
    if (formValues.assignedRoom || formValues.technicianAssignment) {
      actions.setValues({
        roomSetup: {
          assignedRoom: formValues.assignedRoom,
          technicianAssignment: formValues.technicianAssignment
        }
      });
    }
  }, [formValues.assignedRoom, formValues.technicianAssignment, actions]);

  useImperativeHandle(formRef, () => form as unknown as ReturnType<typeof useForm>);

  // Room options - in a real app, these would come from an API
  const roomOptions = [
    { label: 'Room 101', value: 'room-a1' },
    { label: 'Room 102', value: 'room-a2' },
    { label: 'Room 103', value: 'room-b1' },
    { label: 'Room 201', value: 'room-b2' },
    { label: 'Room 202', value: 'room-c1' }
  ];

  // Technician options - in a real app, these would come from an API
  const technicianOptions = [
    { label: 'John Scott', value: 'john' },
    { label: 'Mike Chen', value: 'tech-mike' },
    { label: 'Lisa Rodriguez', value: 'tech-lisa' },
    { label: 'David Kim', value: 'tech-david' },
    { label: 'Emily Davis', value: 'tech-emily' }
  ];

  return (
    <Stack gap={'12px'}>
      <MainCardContainer title="Room Assignment" emphasis="low" color="gray">
        <Grid container spacing="12px" sx={{ p: '12px' }}>
          <Grid size={6}>
            <form.AppField
              name="assignedRoom"
              children={(field) => (
                <field.AppSelectField
                  label="Assigned Room"
                  options={roomOptions}
                  required
                  placeholder="Select a room for the study"
                />
              )}
            />
          </Grid>

          <Grid size={6}>
            <form.AppField
              name="technicianAssignment"
              children={(field) => (
                <field.AppSelectField
                  label="Technician Assignment"
                  options={technicianOptions}
                  required
                  placeholder="Select a technician for the study"
                />
              )}
            />
          </Grid>
        </Grid>
      </MainCardContainer>
    </Stack>
  );
};

export default RoomSetupStep;
