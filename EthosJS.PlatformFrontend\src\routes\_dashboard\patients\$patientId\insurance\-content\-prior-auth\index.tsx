import Card from '@components/card';
import CardHeader from '@components/card-header';
import {
  Button,
  CardContent,
  Typography,
  Stack,
  Box,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Chip
} from '@mui/material';
import { Shield, Lightbulb, Pencil, File } from 'lucide-react';
const PriorAuth = () => {
  const items = [
    'Clinical notes from initial evaluation',
    'Results from previous diagnostic tests',
    'Medical necessity letter',
    'Complete medication list'
  ];
  return (
    <Card>
      <CardHeader
        sx={{ backgroundColor: '#EAECF0' }}
        title="Prior Authorization"
        titleTypographyProps={{ variant: 'h6', color: 'black' }}
        avatar={<Shield />}
        subheader={
          <Typography>
            Prior authorization has not been initiated yet. Authorization will be required before
            the procedure can be scheduled.
          </Typography>
        }
        action={<Chip label="Not Started" sx={{ borderRadius: 2, color: 'black' }} />}
      />
      <CardContent>
        <Stack>
          <Box display="flex" flexDirection="row" gap={2}>
            <Lightbulb />
            <Typography variant="h5" color="black">
              The following documents are required for prior authorization:
            </Typography>
          </Box>

          <List>
            {items.map((text, index) => (
              <ListItem key={index} disableGutters>
                <ListItemIcon sx={{ minWidth: 24 }}>
                  <Box
                    sx={{
                      width: 8,
                      height: 8,
                      borderRadius: '50%',
                      backgroundColor: 'black'
                    }}
                  />
                </ListItemIcon>
                <ListItemText primary={text} sx={{ color: 'black' }} />
              </ListItem>
            ))}
          </List>
          <Box display="flex" gap={2}>
            <Button startIcon={<File />} variant="contained" sx={{ fontSize: 14 }}>
              Start Prior Authorization
            </Button>
            <Button
              startIcon={<Pencil />}
              variant="outlined"
              sx={{ border: '1px solid black', color: 'black', fontSize: 14 }}>
              Edit Insurance
            </Button>
          </Box>
        </Stack>
      </CardContent>
    </Card>
  );
};
export default PriorAuth;
