import MainCardContainer from '@components/main-container/main-card-container';
import { Button } from '@mui/material';
import useFollowUpStore from './-store';
import { useStore } from '@tanstack/react-store';
import PatientOverview from './patient-overview';
import FollowUpEdit from './follow-up-edit';
import AppointmentConfirmation from './confirmation';

interface IStartFollowUpCall {
	onStart: () => void;
}

const StartFollowUpCall = ({ onStart }: IStartFollowUpCall) => {
	return (
		<MainCardContainer
			title={'Next: Follow-up Call'}
			customAction={
				<Button
					onClick={() => onStart?.()}
					variant="contained"
					color="warning">
					Start Here
				</Button>
			}>
			<PatientOverview />
		</MainCardContainer>
	);
};

const AppoinmentFollowUp = () => {
	const { actions, store } = useFollowUpStore();

	const { isStarted, isConfirmed } = useStore(store);

	const onStartRequest = () => {
		actions.onStartFollowUp(true);
	};

	if (isConfirmed) {
		return <AppointmentConfirmation />;
	}

	if (isStarted) {
		return <FollowUpEdit />;
	}

	return <StartFollowUpCall onStart={onStartRequest} />;
};

export default AppoinmentFollowUp;
