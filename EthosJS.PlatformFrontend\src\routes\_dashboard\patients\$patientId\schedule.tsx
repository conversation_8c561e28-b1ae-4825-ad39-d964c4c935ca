import LeftMenu, { Status } from '@components/left-menu'
import StepCard from '@components/step-card'
import { VerifiedUser } from '@mui/icons-material'
import { Box, CardContent, List } from '@mui/material'
import { createFileRoute, Outlet } from '@tanstack/react-router'
import SelectionControls from './schedule/-content/selection-controls'
import useScheduleStore from '@hooks/use-schedule-store'
import { useStore } from '@tanstack/react-store'
import { useEffect } from 'react'
import SmartButtonOutlinedIcon from '@mui/icons-material/SmartButtonOutlined';
import PermPhoneMsgOutlinedIcon from '@mui/icons-material/PermPhoneMsgOutlined';
import { z } from 'zod'

const validateSearch = z.object({
  orderId: z.string(),
  studyId: z.string().optional(),
})

export const Route = createFileRoute(
  '/_dashboard/patients/$patientId/schedule',
)({
  component: RouteComponent,
  validateSearch,
});

export const scheduleSteps = [
  {
    name: 'Appointment Creation',
    key: 'AppointmentCreation',
    icon: SmartButtonOutlinedIcon,
    stepNumber: 1,
    status: Status.None,
    from: undefined,
    to: 'AppointmentCreated',
    data: {},
    navigationURL: `/patients/$patientId/schedule/appointment-creation/dashboard`,
  },
  {
    name: 'Follow-up Call',
    key: 'follow-up-call',
    icon: PermPhoneMsgOutlinedIcon,
    status: Status.None,
    from: undefined,
    stepNumber: 2,
    to: 'AppointmentCreated',
    data: {},
    navigationURL: `/patients/$patientId/schedule/follow-up-call`,
  }
]

function RouteComponent() {
  const { patientId, } = Route.useParams()
  const { orderId, studyId, patientWfId } = Route.useSearch()
  const navigate = Route.useNavigate()

  const { store, actions } = useScheduleStore();

  const { steps } = useStore(store, state => state)

  const onNavigateStep = (selectedStep: typeof scheduleSteps[number]) => {
    const search = { patientWfId: patientWfId, orderId: orderId, studyId: studyId }
    navigate({
      to: selectedStep.navigationURL,
      params: { patientId },
      search,
    })
  }

  const onFieldChange = (fieldName: string, fieldValue: string) => {
    navigate({
      to: '/patients/$patientId/schedule/appointment-creation/dashboard',
      params: { patientId },
      search: (prev) => ({ ...prev, [fieldName]: fieldValue }),
    })
  }

  useEffect(() => {
    actions.setDefaultState();
  }, [])

  useEffect(() => {
    return () => {
      actions.resetState();
    }
  }, [])

  return (
    <Box sx={{ flex: 1, minHeight: 0 }}>
      <StepCard
        title="Verify Insurance"
        icon={VerifiedUser}
        showHeader={false}
        isLoading={false}
      >
        <CardContent
          sx={styles.cardContent}
        >
          <Box sx={styles.cardContentInner}>
            <SelectionControls
              {...{ patientId, onFieldChange: onFieldChange, studyId, orderId }}
            />
            <List sx={{ width: 220 }} disablePadding>
              {steps.map((step, index) => {
                return (
                  <LeftMenu
                    name={step.name}
                    icon={step.icon}
                    status={step.status}
                    stepNumber={step.stepNumber}
                    disabled={step.status === Status.None}
                    onClick={() => onNavigateStep(step)}
                    key={index.toString()}
                  />
                )
              })}
            </List>
          </Box>
          <Box sx={styles.outletContent}>
            <Outlet />
          </Box>
        </CardContent>
      </StepCard>
    </Box>
  )
}

const styles = {
  cardContent: {
    overflow: 'auto',
    minHeight: 0,
    flex: 1,
    display: 'flex',
    gap: 2,
    height: '100%',
  },
  cardContentInner: { overflow: 'auto', height: '100%' },
  outletContent: { flex: 1, overflow: 'auto', height: '100%' }
}