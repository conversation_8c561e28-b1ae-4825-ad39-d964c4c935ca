import { useAppForm } from '@hooks/app-form';
import { Box, Checkbox, FormLabel, FormHelperText, FormControlLabel, Stack } from '@mui/material';
import { useStore } from '@tanstack/react-form';
import ContactMethodContainer from '@components/contact-method-container';
import MainCardContainer from '@components/main-container/main-card-container';
import { Phone } from 'lucide-react';
import { useMemo } from 'react';
import { ValidationErrors } from '@app-types/validation';
import { EthosWorkflowsApiPhoneNumberContactDto } from '@client/workflows';
import { phoneNumberFormOptions } from './utils';

interface PhoneNumberFormProps {
	onAdd?: (values: EthosWorkflowsApiPhoneNumberContactDto) => void;
	onCancel?: (values: EthosWorkflowsApiPhoneNumberContactDto) => void;
	onDelete?: () => void;
	formValues?: EthosWorkflowsApiPhoneNumberContactDto;
	onValidate: (
		data: EthosWorkflowsApiPhoneNumberContactDto
	) => Promise<ValidationErrors | undefined>;
	isUpdate?: boolean;
}

export default function PhoneNumberForm({
	onAdd,
	onCancel,
	onValidate,
	formValues,
	isUpdate,
}: PhoneNumberFormProps) {
	const options = useMemo(() => phoneNumberFormOptions(formValues), [formValues]);
	const hasValues = JSON.stringify(formValues) !== JSON.stringify(options.defaultValues);
	const form = useAppForm({
		...options,
		validators: {
			onChangeAsyncDebounceMs: 500,
			onChangeAsync: async ({ value }) => {
				const res = await onValidate(value);
				return res;
			},
		},
		defaultState: {
			isDirty: hasValues,
			isPristine: !hasValues,
		},
	});

	const { values, canSubmit, isDirty } = useStore(form.store, ({ values, canSubmit, isDirty }) => ({
		values,
		canSubmit,
		isDirty,
	}));

	return (
		<MainCardContainer
			title="Add Phone Number"
			icon={<Phone size={24} />}
			color="primary"
			emphasis="low"
			descriptionSubheader="* Indicates a required field"
			descriptionText="To contact the patient, choose the patient's preferred time."
			containerSlot={
				<ContactMethodContainer>
					<form.AppField name="isPreferred">
						{({ state, handleChange }) => (
							<Box sx={{ display: 'flex', gap: 1, alignItems: 'center' }}>
								<Checkbox
									checked={state.value}
									onChange={(e) => handleChange(e.target.checked)}
									data-testid="phoneNumber.isPreferred"
								/>
								<Box sx={{ display: 'flex', flexDirection: 'column', color: 'primary.dark' }}>
									<FormLabel sx={{ textTransform: 'uppercase', color: 'inherit' }}>
										Contact Method
									</FormLabel>
									<FormHelperText sx={{ color: 'inherit' }}>
										This phone number is the patient's preferred primary contact method.
									</FormHelperText>
								</Box>
							</Box>
						)}
					</form.AppField>
					<form.AppField name="allowsVoice">
						{({ state, handleChange }) => (
							<FormControlLabel
								sx={{ color: 'primary.dark' }}
								control={
									<Checkbox
										checked={(state.value as boolean) ?? false}
										onChange={(e) => handleChange(e.target.checked)}
										data-testid="phoneNumber.allowsVoice"
									/>
								}
								label="CALL"
							/>
						)}
					</form.AppField>
					<form.AppField name="allowsSms">
						{({ state, handleChange }) => (
							<FormControlLabel
								sx={{ color: 'primary.dark' }}
								control={
									<Checkbox
										checked={(state.value as boolean) ?? false}
										onChange={(e) => handleChange(e.target.checked)}
										data-testid="phoneNumber.allowsSMS"
									/>
								}
								label="SMS"
							/>
						)}
					</form.AppField>
				</ContactMethodContainer>
			}
			footerProps={{
				primaryButton1: {
					label: hasValues || isUpdate ? 'Update' : 'Add',
					onClick: () => onAdd?.(values),
					disabled: !canSubmit || !isDirty,
				},
				primaryButton2: {
					label: 'Cancel',
					onClick: () => {
						form.reset();
						onCancel?.(values);
					},
				},
			}}>
			<Stack
				direction="row"
				columnGap={2}
				rowGap={2}
				flexWrap="wrap"
				sx={{ width: '100%' }}>
				<Box
					sx={{
						minWidth: '120px',
						maxWidth: '120px',
						flex: '0 1 15%',
					}}>
					<form.AppField name="type">
						{(field) => (
							<field.AppSelectField
								label="Type"
								referenceDataSetName="phoneUse"
								data-testid="phoneNumber.type"
							/>
						)}
					</form.AppField>
				</Box>
				<Box
					sx={{
						minWidth: '200px',
						flex: '1 1 auto',
					}}>
					<form.AppField
						name="value"
						children={(field) => (
							<field.AppPhoneNumberField
								label="Phone Number"
								required
								data-testid="phoneNumber.value"
							/>
						)}
					/>
				</Box>
				<Box
					sx={{
						minWidth: '180px',
						flex: '0 1 35%',
					}}>
					<form.AppField
						name="preferredTime"
						children={(field) => (
							<field.AppAutocompleteField
								label="Preferred Time"
								referenceDataSetName="preferredContactTime"
								data-testid="phoneNumber.preferredTime"
							/>
						)}
					/>
				</Box>
			</Stack>
		</MainCardContainer>
	);
}
