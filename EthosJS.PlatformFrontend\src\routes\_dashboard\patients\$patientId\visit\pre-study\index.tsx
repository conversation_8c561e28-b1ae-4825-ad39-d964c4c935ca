import { createFileRoute } from '@tanstack/react-router'
import { z } from 'zod'
import Steps from '../-content/-pre-study/-steps'

const validateSearch = z.object({
  orderId: z.string(),
  studyId: z.string().optional(),
  patientWfId: z.string(),
  orderWfId: z.string().optional()
})

export const Route = createFileRoute(
  '/_dashboard/patients/$patientId/visit/pre-study/',
)({
  component: RouteComponent,
  validateSearch,
})

function RouteComponent() {
  const { patientId } = Route.useParams()
  const { patientWfId } = Route.useSearch()
  const search = Route.useSearch()
  const navigate = Route.useNavigate()

  const onCancel = () => {
    navigate({
      to: '/patients/$patientId/visit',
      params: { patientId },
      search,
    })
  }

  return (
    <Steps
      studyId={search.studyId as string}
      patientWfId={patientWfId}
      patientId={patientId}
      onCancel={onCancel}
    />
  )
}
