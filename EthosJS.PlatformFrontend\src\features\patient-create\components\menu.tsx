import MenuItem from '@components/menu/menu-item';
import SecondaryMenu from '@components/menu/secondary-menu';
import { User } from 'lucide-react';
import usePatient from '../hooks/use-patient';
import { PatientState, StepNames } from '../types/state-types';
import { Status } from '@config/status';

const menuItems = [
	{
		stateKey: 'BasicInformation',
		title: 'Basic Information',
		value: 'basic-information',
		icon: User,
		size: 'medium',
		path: '/basic-information',
	},
	{
		stateKey: 'Contacts',
		title: 'Contact Information',
		value: 'contact-information',
		icon: User,
		size: 'medium',
		path: '/contact-information',
	},
	{
		stateKey: 'Addresses',
		title: 'Addresses',
		value: 'addresses',
		icon: User,
		size: 'medium',
		path: '/addresses',
	},
	{
		stateKey: 'Insurances',
		title: 'Insurances',
		value: 'insurances',
		icon: User,
		size: 'medium',
		path: '/insurances',
	},
	{
		stateKey: 'Guardians',
		title: 'Guardians',
		value: 'guardians',
		icon: User,
		size: 'medium',
		path: '/guardians',
	},
	{
		stateKey: 'ClinicalConsiderations',
		title: 'Clinical Considerations',
		value: 'clinical-considerations',
		icon: User,
		size: 'medium',
		path: '/clinical-considerations',
	},
] as const;

interface PatientCreateMenuProps {
	patientId: string;
	activePath: string;
	onClick: (path: string) => void;
}

export default function PatientCreateMenu({
	patientId,
	activePath,
	onClick,
}: PatientCreateMenuProps) {
	const { patientData } = usePatient({ patientId });

	const getProgress = () => {
		if (!patientData) {
			return 0;
		}
		const { data } = patientData;
		const patientState = (data?._state as unknown as PatientState) ?? {};
		const flowState = patientState.flowState ?? {};
		const progress = flowState.progress ?? 0;
		return progress;
	};

	const getStepStatus = (stepName: StepNames) => {
		if (!patientData) {
			return Status.NotStarted;
		}
		const { data } = patientData;
		const patientState = (data?._state as unknown as PatientState) ?? {};
		const stepState = patientState.stepState ?? {};
		const stepStatus = stepState[stepName] ?? 'NotStarted';
		switch (stepStatus) {
			case 'Complete':
				return Status.Success;
			case 'NotStarted':
				return Status.NotStarted;
			case 'InProgress':
				return Status.Process;
			case 'Error':
				return Status.Error;
			case 'Warning':
				return Status.Warning;
			default:
				return Status.NotStarted;
		}
	};

	return (
		<SecondaryMenu
			headerProps={{
				title: 'New Patient Registration',
				subtitle: undefined,
				icon: User,
				type: 'Workflow',
				progress: getProgress(),
				color: 'success',
				description: 'Complete all the patient profile details.',
			}}>
			{menuItems.map((item) => (
				<MenuItem
					key={item.value}
					title={item.title}
					value={item.value}
					icon={item.icon}
					size={item.size}
					selected={activePath.includes(item.path)}
					status={getStepStatus(item.stateKey)}
					onClick={() => {
						onClick(item.path);
					}}
				/>
			))}
		</SecondaryMenu>
	);
}
