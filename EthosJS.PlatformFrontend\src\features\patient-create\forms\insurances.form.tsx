import StepCardControl from '@components/step-card-control';
import { Box, Button, CardContent, Stack } from '@mui/material';
import { useAppForm } from '@hooks/app-form';
import { useStore } from '@tanstack/react-form';
import ArrayFieldContainer from '@components/array-field-container';
import { Receipt } from 'lucide-react';
import MainCardContainer from '@components/main-container/main-card-container';
import { InsurancesData } from '@features/patient-create/types';
import { FormProps } from '@components/forms/predefined-form-props';
import {
	formatInsuranceSummary,
	InsuranceForm,
	insuranceFormOptions,
} from '@components/forms/app-insurance-form';
import { useMemo } from 'react';
import { getErrorsForIndexField } from '@utils/forms';
import { addressesWithUseTransformer } from './transformers';

function insurancesOptions(savedData?: InsurancesData, isUpdate?: boolean) {
	return {
		defaultValues: {
			insurances: !isUpdate && !savedData ? [insuranceFormOptions().defaultValues] : [],
			...(savedData ? savedData : {}),
		},
	};
}

export default function InsurancesForm({
	onSubmit,
	onSaveDraft,
	onValidate,
	savedData,
	patientName,
	dateOfBirth,
	isUpdate,
}: FormProps<InsurancesData> & { patientName: string; dateOfBirth: string }) {
	const options = useMemo(() => insurancesOptions(savedData, isUpdate), [savedData, isUpdate]);

	const form = useAppForm({
		...options,
		validators: {
			onChangeAsyncDebounceMs: 500,
			onChangeAsync: async ({ value }) => {
				const res = await onValidate(value);
				return res;
			},
		},
		onSubmit: async ({ value }) => {
			onSubmit(value);
		},
	});

	const values = useStore(form.store, (state) => state.values);

	return (
		<Box
			component="form"
			sx={{ height: '100%' }}
			onSubmit={(e) => {
				e.preventDefault();
				form.handleSubmit();
			}}>
			<CardContent sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
				<form.AppField
					name="insurances"
					mode="array">
					{({ pushValue, removeValue, state, replaceValue }) => (
						<MainCardContainer
							title="Insurance Information"
							icon={<Receipt />}
							color="primary"
							emphasis={isUpdate ? 'high' : 'low'}
							primaryActionType="Add"
							onPrimaryAction={() => pushValue(insuranceFormOptions().defaultValues)}>
							{state.value && state.value?.length > 0 && (
								<Stack spacing={2}>
									{state.value.map((_, i) => (
										<ArrayFieldContainer
											key={i}
											initialEditState={
												JSON.stringify(state.value[i]) ===
												JSON.stringify(insuranceFormOptions().defaultValues)
											}
											items={formatInsuranceSummary(state.value[i])}
											title="Insurance"
											data-testid={`insurances.container.${i}`}>
											{({ setEdit }) => (
												<InsuranceForm
													patientName={patientName}
													dateOfBirth={dateOfBirth}
													formValues={state.value[i]}
													onValidate={async (data) => {
														const insurances =
															state.value
																?.map((_, j) => {
																	if (j === i) {
																		return data;
																	}
																	return state.value?.[j];
																})
																.map((insurance) => ({
																	...insurance,
																	address: insurance.address
																		? addressesWithUseTransformer(insurance.address)
																		: undefined,
																})) ?? [];
														const vals = {
															insurances,
														} as InsurancesData;
														const res = await onValidate(vals);
														return getErrorsForIndexField(`insurances[${i}]`, res);
													}}
													onAdd={(data) => {
														replaceValue(i, data);
														setEdit(false);
														if (isUpdate) {
															form.handleSubmit();
														}
													}}
													onCancel={(shouldRemove) => {
														if (shouldRemove) {
															removeValue(i);
														}
														setEdit(false);
													}}
													onDelete={() => {
														removeValue(i);
														form.handleSubmit();
														setEdit(false);
													}}
													data-testid={`insurances.form.${i}`}
												/>
											)}
										</ArrayFieldContainer>
									))}
								</Stack>
							)}
						</MainCardContainer>
					)}
				</form.AppField>
			</CardContent>
			<StepCardControl>
				{!isUpdate && (
					<Button
						variant="outlined"
						color="primary"
						onClick={() => onSaveDraft(values)}>
						Save Draft
					</Button>
				)}
				<form.Subscribe
					selector={({ isDirty, canSubmit, isSubmitting }) => ({
						isDirty,
						canSubmit,
						isSubmitting,
					})}>
					{({ isDirty, canSubmit, isSubmitting }) => (
						<Button
							variant="contained"
							color="primary"
							type="submit"
							loading={isSubmitting}
							disabled={!isDirty || !canSubmit}>
							{isUpdate ? 'Update' : 'Next'}
						</Button>
					)}
				</form.Subscribe>
			</StepCardControl>
		</Box>
	);
}
