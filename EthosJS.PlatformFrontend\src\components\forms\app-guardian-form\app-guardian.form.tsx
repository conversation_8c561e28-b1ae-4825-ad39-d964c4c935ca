import { <PERSON><PERSON>ontent, Grid2 as Grid, Typography } from '@mui/material';
import { useAppForm } from '@hooks/app-form';
import { useStore } from '@tanstack/react-form';
import CardInfo from '@components/card-info';
import CardFooter from '@components/card-footer';
import FormFooter from '@components/form-footer';
import { Mail, Phone, UserPlus } from 'lucide-react';
import Card from '@components/card';
import CardHeader from '@components/card-header';
import { MapPin } from 'lucide-react';
import ArrayFieldContainer from '@components/array-field-container';
import MainCardContainer from '@components/main-container/main-card-container';
import {
	EthosWorkflowsApiEmailContactDto,
	EthosWorkflowsApiGuardianDto,
	EthosWorkflowsApiPhoneNumberContactDto,
} from '@client/workflows';
import { useMemo } from 'react';
import { guardianFormOptions } from './utils';
import PhoneNumberForm, { phoneNumberFormOptions } from '../app-phone-number-form';
import { ValidationErrors } from '@app-types/validation';
import EmailForm, { emailFormOptions } from '../app-email-form';
import AddressForm, { addressFormOptions } from '../app-address-form';

interface GuardianFormProps {
	onAdd?: (values: EthosWorkflowsApiGuardianDto) => void;
	onCancel?: (shouldRemove: boolean) => void;
	onDelete?: () => void;
	formValues: EthosWorkflowsApiGuardianDto;
}

export default function GuardianForm({ onAdd, onCancel, onDelete, formValues }: GuardianFormProps) {
	const options = useMemo(() => guardianFormOptions(formValues), [formValues]);
	const hasValues = JSON.stringify(formValues) !== JSON.stringify(options.defaultValues);
	const form = useAppForm({
		...options,
		defaultState: {
			isDirty: hasValues,
			isPristine: !hasValues,
		},
	});

	const values = useStore(form.store, (state) => state.values);

	return (
		<Card emphasis="dark">
			<CardHeader
				emphasis="dark"
				title="Add Guardian"
				avatar={<UserPlus size={32} />}
				slotProps={{
					title: { variant: 'h6' },
				}}
			/>
			<CardInfo
				title="* Indicates a required field"
				subtitle="Enter the guardian's information below."
			/>
			<CardContent>
				<Grid
					container
					spacing={2}>
					{/* Basic Information */}
					<Grid
						container
						spacing={2}
						size={12}>
						<Grid size={12}>
							<Typography
								variant="h6"
								sx={{ mb: 1, color: 'primary.dark' }}>
								Guardian Information
							</Typography>
						</Grid>
						<Grid size={{ xs: 12, sm: 6 }}>
							<form.AppField
								name="guardianBasicInformation.guardianType"
								children={(field) => (
									<field.AppSelectField
										label="Guardian Type"
										required
										referenceDataSetName="relationship"
										referenceDataFilter="relationshipType eq Guardian"
										data-testid="guardianBasicInformation.guardianType"
									/>
								)}
							/>
						</Grid>
					</Grid>
					<Grid
						container
						spacing={2}
						size={12}>
						<Grid size={{ xs: 12, sm: 6, md: 4 }}>
							<form.AppField
								name="guardianBasicInformation.prefix"
								children={(field) => (
									<field.AppAutocompleteField
										label="Prefix"
										referenceDataSetName="namePrefix"
										data-testid="guardianBasicInformation.prefix"
									/>
								)}
							/>
						</Grid>
					</Grid>
					<Grid size={4}>
						<form.AppField
							name="guardianBasicInformation.firstName"
							children={(field) => (
								<field.AppTextField
									label="Legal First Name"
									required
									data-testid="guardianBasicInformation.firstName"
								/>
							)}
						/>
					</Grid>
					<Grid size={4}>
						<form.AppField
							name="guardianBasicInformation.middleName"
							children={(field) => (
								<field.AppTextField
									label="Middle Name"
									data-testid="guardianBasicInformation.middleName"
								/>
							)}
						/>
					</Grid>
					<Grid size={4}>
						<form.AppField
							name="guardianBasicInformation.lastName"
							children={(field) => (
								<field.AppTextField
									label="Legal Last Name"
									required
									data-testid="guardianBasicInformation.lastName"
								/>
							)}
						/>
					</Grid>
					<Grid
						container
						spacing={2}
						size={12}>
						<Grid size={{ xs: 12, sm: 6, md: 4 }}>
							<form.AppField
								name="guardianBasicInformation.suffix"
								children={(field) => (
									<field.AppAutocompleteField
										label="Suffix"
										referenceDataSetName="nameSuffix"
										data-testid="guardianBasicInformation.suffix"
									/>
								)}
							/>
						</Grid>
					</Grid>
					{/* Demographics Information */}
					<Grid size={12}>
						<Typography
							variant="h6"
							sx={{ mb: 1, color: 'primary.dark' }}>
							Guardian Demographics
						</Typography>
					</Grid>
					<Grid size={{ xs: 12, sm: 6 }}>
						<form.AppField
							name="guardianDemographics.gender"
							children={(field) => (
								<field.AppAutocompleteField
									label="Gender"
									referenceDataSetName="gender"
									data-testid="guardianDemographics.gender"
								/>
							)}
						/>
					</Grid>
					<Grid size={{ xs: 12, sm: 6 }}>
						<form.AppField
							name="guardianDemographics.dateOfBirth"
							children={(field) => (
								<field.AppDateField
									label="Date of Birth"
									required
									data-testid="guardianDemographics.dateOfBirth"
								/>
							)}
						/>
					</Grid>
					<Grid size={{ xs: 12, sm: 6 }}>
						<form.AppField
							name="guardianDemographics.ssn"
							children={(field) => (
								<field.AppSsnField
									label="SSN"
									required
									data-testid="guardianDemographics.ssn"
								/>
							)}
						/>
					</Grid>
					{/* <Grid size={{ xs: 12, sm: 6 }}>
                        <form.AppField
                            name="guardianDemographics.relationShipToPatient"
                            children={(field) => (
                                <field.AppSelectField
                                    label="Relationship to Patient"
                                    required
                                    referenceDataSetName="relationship"
                                    referenceDataFilter="relationshipType eq Guardian"
                                />
                            )}
                        />
                    </Grid> */}
					<Grid size={{ xs: 12, sm: 6 }}>
						<form.AppField
							name="guardianDemographics.idType"
							children={(field) => (
								<field.AppSelectField
									label="ID Type"
									referenceDataSetName="idType"
									data-testid="guardianDemographics.idType"
								/>
							)}
						/>
					</Grid>
					<Grid size={{ xs: 12, sm: 6 }}>
						<form.AppField
							name="guardianDemographics.idNumber"
							children={(field) => (
								<field.AppTextField
									label="ID Number"
									data-testid="guardianDemographics.idNumber"
								/>
							)}
						/>
					</Grid>
					<Grid size={12}>
						<form.AppField
							name="phoneNumbers"
							mode="array">
							{({ pushValue, removeValue, state, replaceValue }) => (
								<MainCardContainer
									title="Phone Numbers"
									icon={<Phone />}
									color="primary"
									emphasis="low"
									primaryActionType="Add"
									onPrimaryAction={() => pushValue(phoneNumberFormOptions().defaultValues)}
									data-testid="phoneNumbers.container">
									{state.value.length > 0 &&
										state.value.map((_, i) => {
											return (
												<ArrayFieldContainer
													key={i}
													initialEditState={true}
													items={formatPhoneNumberSummary(state.value[i])}
													title="Phone Number"
													showHeader={false}>
													{({ setEdit }) => (
														<PhoneNumberForm
															formValues={state.value[i]}
															onAdd={(data) => {
																replaceValue(i, data);
																setEdit(false);
															}}
															onCancel={() => {
																if (!hasValues) {
																	removeValue(i);
																}
																setEdit(false);
															}}
															onDelete={() => {
																removeValue(i);
																setEdit(false);
															}}
															onValidate={function (
																data: EthosWorkflowsApiPhoneNumberContactDto
															): Promise<ValidationErrors | undefined> {
																throw new Error('Function not implemented.');
															}}
														/>
													)}
												</ArrayFieldContainer>
											);
										})}
								</MainCardContainer>
							)}
						</form.AppField>
					</Grid>
					<Grid size={12}>
						<form.AppField
							name="emails"
							mode="array">
							{({ pushValue, removeValue, state, replaceValue }) => (
								<MainCardContainer
									title="Emails"
									icon={<Mail />}
									color="primary"
									emphasis="low"
									primaryActionType="Add"
									onPrimaryAction={() => pushValue(emailFormOptions().defaultValues)}
									data-testid="emails.container">
									{state.value.length > 0 &&
										state.value.map((_, i) => {
											return (
												<ArrayFieldContainer
													key={i}
													initialEditState={true}
													items={formatEmailSummary(state.value[i])}
													title="Email">
													{({ setEdit }) => (
														<EmailForm
															formValues={state.value[i]}
															onAdd={(data) => {
																replaceValue(i, data);
																setEdit(false);
															}}
															onCancel={(shouldRemove) => {
																if (shouldRemove) {
																	removeValue(i);
																}
																setEdit(false);
															}}
															onDelete={() => {
																removeValue(i);
																setEdit(false);
															}}
															onValidate={function (
																data: EthosWorkflowsApiEmailContactDto
															): Promise<ValidationErrors | undefined> {
																throw new Error('Function not implemented.');
															}}
														/>
													)}
												</ArrayFieldContainer>
											);
										})}
								</MainCardContainer>
							)}
						</form.AppField>
					</Grid>
					<Grid size={12}>
						<form.AppField
							name="addresses"
							mode="array">
							{({ pushValue, removeValue, state, replaceValue }) => (
								<MainCardContainer
									title="Addresses"
									icon={<MapPin />}
									color="primary"
									emphasis="low"
									primaryActionType="Add"
									onPrimaryAction={() => pushValue(addressFormOptions().defaultValues)}
									data-testid="addresses.container">
									{state.value.length > 0 &&
										state.value.map((_, i) => {
											const hasValues =
												JSON.stringify(state.value[i]) !==
												JSON.stringify(addressFormOptions().defaultValues);
											return (
												<ArrayFieldContainer
													key={i}
													initialEditState={true}
													items={[]}
													title="Address">
													{({ setEdit }) => (
														<AddressForm
															formValues={state.value[i]}
															onAdd={(data) => {
																replaceValue(i, data);
																setEdit(false);
															}}
															onCancel={() => {
																if (!hasValues) {
																	removeValue(i);
																}
																setEdit(false);
															}}
															onDelete={() => {
																removeValue(i);
																setEdit(false);
															}}
															addressUse="Physical"
															addressUseRefValue={0}
														/>
													)}
												</ArrayFieldContainer>
											);
										})}
								</MainCardContainer>
							)}
						</form.AppField>
					</Grid>
					{/* <Grid size={12}>
						<form.AppField
							name="legalDocuments"
							mode="array">
							{({ pushValue, removeValue, state, replaceValue }) => (
								<MainCardContainer
									title="Legal Documents"
									icon={<FileText />}
									color="primary"
									emphasis="low"
									primaryActionType="Add"
									onPrimaryAction={() => pushValue([])}
									data-testid="legalDocuments.container">
									{state.value.length > 0 &&
										state.value.map((_, i) => {
											return (
												<ArrayFieldContainer
													key={i}
													initialEditState={true}
													items={[]}
													title="Legal Document">
													{({ setEdit }) => (
														<LegalDocumentForm
															workflowId={workflowId}
															formValues={state.value[i]}
															onAdd={(data) => {
																replaceValue(i, data);
																setEdit(false);
															}}
															onCancel={() => {
																if (!hasValues) {
																	removeValue(i);
																}
																setEdit(false);
															}}
															onDelete={() => {
																removeValue(i);
																setEdit(false);
															}}
														/>
													)}
												</ArrayFieldContainer>
											);
										})}
								</MainCardContainer>
							)}
						</form.AppField>
					</Grid> */}
				</Grid>
			</CardContent>
			<CardFooter sx={{ justifyContent: 'flex-end' }}>
				<form.Subscribe selector={({ isDirty }) => ({ isDirty })}>
					{({ isDirty }) => (
						<FormFooter
							onCancel={() => {
								form.reset();
								onCancel?.(!hasValues);
							}}
							onSubmit={() => onAdd?.(values)}
							isUpdate={hasValues}
							isDirty={isDirty}
							onDelete={onDelete}
						/>
					)}
				</form.Subscribe>
			</CardFooter>
		</Card>
	);
}
