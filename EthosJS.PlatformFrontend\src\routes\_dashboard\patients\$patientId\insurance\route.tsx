import { createFileRoute, Outlet, useLocation } from '@tanstack/react-router';
import { Stack } from '@mui/material';
import Card from '@components/card';
import InsuranceVerificationMenu from '@features/insruance-verification/components/menu';
import z from 'zod';
import Summary from '@features/insruance-verification/components/summary';

const InsuranceVerificationParams = z.object({
	orderId: z.string(),
	studyId: z.string(),
	insuranceId: z.string().optional(),
});

export const Route = createFileRoute('/_dashboard/patients/$patientId/insurance')({
	component: RouteComponent,
	validateSearch: InsuranceVerificationParams,
	loaderDeps: ({ search }) => ({ ...search }),
});

function RouteComponent() {
	const { patientId } = Route.useParams();
	const { orderId, studyId, insuranceId } = Route.useSearch();
	const { pathname } = useLocation();
	const navigate = Route.useNavigate();

	return (
		<Card
			sx={{
				flex: 1,
				minHeight: 0,
				display: 'flex',
				gap: 2,
				p: 2,
				pb: 0,
				borderRadius: 2,
				borderBottomLeftRadius: 0,
				borderBottomRightRadius: 0,
			}}>
			<InsuranceVerificationMenu
				patientId={patientId}
				orderId={orderId!}
				studyId={studyId!}
				activePath={pathname}
				onClick={(path, orderId, studyId) => {
					navigate({
						to: `/patients/$patientId/insurance${path}`,
						params: { patientId },
						search: { orderId, studyId },
					});
				}}
			/>
			<Card
				color="primary"
				sx={{
					flex: 1,
					minHeight: 0,
					position: 'relative',
					height: '100%',
					p: 2,
					borderBottomLeftRadius: 0,
					borderBottomRightRadius: 0,
				}}>
				<Stack gap={2}>
					<Summary
						patientId={patientId}
						orderId={orderId!}
						studyId={studyId!}
						insuranceId={insuranceId!}
					/>
					<Outlet />
				</Stack>
			</Card>
		</Card>
	);
}
