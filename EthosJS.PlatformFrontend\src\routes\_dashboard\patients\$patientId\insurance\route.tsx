import MainCardContainer from '@components/main-container/main-card-container';
import { createFileRoute, Outlet, useLocation } from '@tanstack/react-router';
import { Stack } from '@mui/material';
import Card from '@components/card';
import InsuranceVerificationMenu from '@features/insruance-verification/components/menu';
import z from 'zod';

const InsuranceVerificationParams = z.object({
	orderId: z.string(),
	studyId: z.string(),
});

export const Route = createFileRoute('/_dashboard/patients/$patientId/insurance')({
	component: RouteComponent,
	validateSearch: InsuranceVerificationParams,
	loaderDeps: ({ search }) => ({ ...search }),
});

function RouteComponent() {
	const { patientId } = Route.useParams();
	const { orderId, studyId } = Route.useSearch();
	const { pathname } = useLocation();
	const navigate = Route.useNavigate();

	return (
		<Card
			sx={{
				flex: 1,
				minHeight: 0,
				display: 'flex',
				gap: 2,
				p: 2,
				pb: 0,
				borderRadius: 2,
				borderBottomLeftRadius: 0,
				borderBottomRightRadius: 0,
			}}>
			<InsuranceVerificationMenu
				patientId={patientId}
				orderId={orderId!}
				studyId={studyId!}
				activePath={pathname}
				onClick={(path) => {
					navigate({
						to: `/patients/$patientId/insurance${path}`,
						params: { patientId },
						search: { orderId, studyId },
					});
				}}
			/>
			<Card
				color="primary"
				sx={{
					flex: 1,
					minHeight: 0,
					position: 'relative',
					height: '100%',
					borderBottomLeftRadius: 0,
					borderBottomRightRadius: 0,
				}}>
				<Stack gap={2}>
					<MainCardContainer
						title="Summary"
						color="primary"
						emphasis="high"
						descriptionSubheader="Verify patient insurance information"
						descriptionText="Select the insurance to verify and submit for electronic verification.">
						{/* <Stack
						gap={2}
						direction="column">
						<Stack
							direction="row"
							gap={2}>
							<Button
								variant={activeTab === 'insurance' ? 'contained' : 'outlined'}
								startIcon={<Shield />}
								onClick={() => setActiveTab('insurance')}>
								Insurance
							</Button>
							<Button
								variant={activeTab === 'order-information' ? 'contained' : 'outlined'}
								startIcon={<File />}
								onClick={() => setActiveTab('order-information')}>
								Order Information
							</Button>
							<Button
								variant={activeTab === 'patient-information' ? 'contained' : 'outlined'}
								startIcon={<User />}
								onClick={() => setActiveTab('patient-information')}>
								Patient Information
							</Button>
						</Stack>
						{activeTab === 'insurance' && (
							<>
								{insuranceItems.map((insurance) => {
									return (
										<MainCardContainer
											title="Insurance Information"
											color="primary"
											emphasis="low"
											key={insurance.id}>
											<ChipSummary
												items={[
													{
														label: 'Insurance Carrier',
														value: insurance.insuranceCarrier
															? insurance.insuranceCarrier?.toString()
															: 'Unknown',
													},
													{
														label: 'Policy ID',
														value: insurance.policyId ? insurance.policyId : 'Unknown',
													},
													{
														label: 'Group Number',
														value: insurance.groupNumber
															? insurance.groupNumber?.toString()
															: 'Unknown',
													},
													{
														label: 'Member ID',
														value: insurance.memberId ? insurance.memberId : 'Unknown',
													},
												]}
											/>
										</MainCardContainer>
									);
								})}
							</>
						)}
						{activeTab === 'order-information' && (
							<MainCardContainer
								title="Order Information"
								color="primary"
								emphasis="low">
								<ChipSummary
									items={[
										{
											label: 'Order ID',
											value: AddStudy?.id ? AddStudy?.order?.id?.toString() : '',
										},
										{
											label: 'Study ID',
											value: AddStudy?.study?.id ? AddStudy?.study?.id?.toString() : '',
										},
									]}
								/>
							</MainCardContainer>
						)}
						{activeTab === 'patient-information' && (
							<MainCardContainer
								title="Patient Information"
								color="primary"
								emphasis="low">
								<ChipSummary
									items={[
										{
											label: 'Patient ID',
											value: patient?.id ? patient?.id?.toString() : '',
										},
										{
											label: 'Name',
											value:
												patient?.names?.[0]?.firstName + ' ' + patient?.names?.[0]?.lastName
													? patient?.names?.[0]?.firstName + ' ' + patient?.names?.[0]?.lastName
													: '',
										},
									]}
								/>
							</MainCardContainer>
						)}
					</Stack> */}
					</MainCardContainer>
					<Outlet />
				</Stack>
			</Card>
		</Card>
	);
}
