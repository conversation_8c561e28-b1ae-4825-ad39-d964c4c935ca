import { createFileRoute, Outlet } from '@tanstack/react-router';
import WorkflowContainer from '@components/workflow/workflow-page-container';
import PrimaryMenu from '@components/menu/primary-menu';
import { FolderCheck, User } from 'lucide-react';
import { Box } from '@mui/material';
import MenuItem from '@components/menu/menu-item';
import z from 'zod';
import PatientInformationMenuItem from '@features/patient-create/components/patient-information-menu-item';
import usePatient from '@features/patient-create/hooks/use-patient';
import {
	EthosWorkflowsApiDemographicsDto,
	EthosWorkflowsApiPatientInformationDto,
} from '@client/workflows';
import dayjs from 'dayjs';

const PatientQueryParams = z.object({
	orderId: z.string().optional(),
	studyId: z.string().optional(),
});

export const Route = createFileRoute('/_dashboard/patients/$patientId')({
	component: RouteComponent,
	validateSearch: PatientQueryParams,
	loaderDeps: ({ search }) => ({ ...search }),
});

function RouteComponent() {
	const { patientId } = Route.useParams();
	const { orderId, studyId } = Route.useSearch();
	const navigate = Route.useNavigate();

	const { patientData } = usePatient({ patientId });

	const getPatientInformation = () => {
		if (!patientData) {
			return {
				title: 'Patient Information',
				subtitle: undefined,
			};
		}
		const { data } = patientData;
		const patientInformation =
			(data?.patientInformation as unknown as EthosWorkflowsApiPatientInformationDto) ?? {};
		const firstName = patientInformation.firstName || '';
		const lastName = patientInformation.lastName || '';
		const mrn = patientInformation.mrn || '';
		const patientDemographics =
			(data?.demographics as unknown as EthosWorkflowsApiDemographicsDto) ?? {};
		const dateOfBirth = patientDemographics.dateOfBirth || '';
		let title = '';
		if (firstName && lastName) {
			title = `${firstName} ${lastName}`.trim();
		}
		if (!title) {
			title = 'Patient Information';
		}
		let subtitle: string | undefined = '';
		if (mrn) {
			subtitle += `ID: #${mrn}`;
		}
		if (mrn && dateOfBirth) {
			subtitle += ` · DOB: ${dayjs(dateOfBirth).format('MM/DD/YYYY')}`;
		}
		if (dateOfBirth && !mrn) {
			subtitle = `DOB: ${dayjs(dateOfBirth).format('MM/DD/YYYY')}`;
		}
		if (!mrn && !dateOfBirth) {
			subtitle = undefined;
		}
		return {
			title: `${firstName} ${lastName}`.trim(),
			subtitle,
		};
	};

	return (
		<WorkflowContainer>
			<Box height={'100%'}>
				<PrimaryMenu
					color="primary"
					headerProps={{
						title: getPatientInformation().title,
						subtitle: getPatientInformation().subtitle,
						icon: User,
					}}
					footerProps={{ text: 'View All', icon: FolderCheck }}>
					<PatientInformationMenuItem
						patientId={patientId}
						onClick={() => {
							navigate({
								to: '/patients/$patientId/patient-information',
								params: { patientId },
							});
						}}
					/>
					<MenuItem
						title="Order"
						value="order"
						icon={User}
						size="large"
						disabled={!orderId}
						onClick={() => {
							if (orderId) {
								navigate({
									to: '/patients/$patientId/order',
									params: { patientId },
									search: { orderId, studyId },
								});
							}
						}}
					/>
					<MenuItem
						title="Insurance"
						value="insurance"
						icon={User}
						size="large"
						onClick={() => {
							// navigate({
							//     to: '/patients/$patientId/insurance',
							//     params: { patientId },
							// })
						}}
					/>
					<MenuItem
						title="Schedule"
						value="schedule"
						icon={User}
						size="large"
						onClick={() => {
							navigate({
								to: '/patients/$patientId/schedule',
								params: { patientId },
							});
						}}
					/>
				</PrimaryMenu>
			</Box>
			<Outlet />
		</WorkflowContainer>
	);
}
