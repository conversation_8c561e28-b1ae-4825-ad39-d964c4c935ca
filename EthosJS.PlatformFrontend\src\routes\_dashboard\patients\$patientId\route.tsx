import { createFileRoute, Outlet, useLocation } from '@tanstack/react-router';
import PrimaryMenu from '@components/menu/primary-menu';
import { FolderCheck, User } from 'lucide-react';
import { Box } from '@mui/material';
import MenuItem from '@components/menu/menu-item';
import WorkflowContainer from '@components/workflow-page-container';
import z from 'zod';
import PatientInformationMenuItem from '@features/patient-create/components/patient-information-menu-item';
import usePatient from '@features/patient-create/hooks/use-patient';
import {
	EthosWorkflowsApiDemographicsDto,
	EthosWorkflowsApiPatientInformationDto,
} from '@client/workflows';
import dayjs from 'dayjs';
import OrderMenuItem from '@features/order-create/components/order-menu-item';
import InsuranceMenuItem from '@features/insruance-verification/components/insruance-menu-item';

const PatientQueryParams = z.object({
	orderId: z.string().optional(),
	studyId: z.string().optional(),
});

export const Route = createFileRoute('/_dashboard/patients/$patientId')({
	component: RouteComponent,
	validateSearch: PatientQueryParams,
	loaderDeps: ({ search }) => ({ ...search }),
});

function RouteComponent() {
	const { patientId } = Route.useParams();
	const { orderId, studyId } = Route.useSearch();
	const navigate = Route.useNavigate();
	const { pathname } = useLocation();

	const { patientData } = usePatient({ patientId });

	const getPatientInformation = () => {
		if (!patientData) {
			return {
				title: 'Patient Information',
				subtitle: undefined,
			};
		}
		const { data } = patientData;
		const patientInformation =
			(data?.patientInformation as unknown as EthosWorkflowsApiPatientInformationDto) ?? {};
		const firstName = patientInformation.firstName || '';
		const lastName = patientInformation.lastName || '';
		const mrn = patientInformation.mrn || '';
		const patientDemographics =
			(data?.demographics as unknown as EthosWorkflowsApiDemographicsDto) ?? {};
		const dateOfBirth = patientDemographics.dateOfBirth || '';
		let title = '';
		if (firstName && lastName) {
			title = `${firstName} ${lastName}`.trim();
		}
		if (!title) {
			title = 'Patient Information';
		}
		let subtitle: string | undefined = '';
		if (mrn) {
			subtitle += `ID: #${mrn}`;
		}
		if (mrn && dateOfBirth) {
			subtitle += ` · DOB: ${dayjs(dateOfBirth).format('MM/DD/YYYY')}`;
		}
		if (dateOfBirth && !mrn) {
			subtitle = `DOB: ${dayjs(dateOfBirth).format('MM/DD/YYYY')}`;
		}
		if (!mrn && !dateOfBirth) {
			subtitle = undefined;
		}
		return {
			title,
			subtitle,
		};
	};

	return (
		<WorkflowContainer>
			<Box height={'100%'}>
				<PrimaryMenu
					color="primary"
					headerProps={{
						title: getPatientInformation().title,
						subtitle: getPatientInformation().subtitle,
						icon: User,
					}}
					footerProps={{ text: 'View All', icon: FolderCheck }}>
					<PatientInformationMenuItem
						patientId={patientId}
						selected={pathname.includes('/patient-information')}
						onClick={() => {
							navigate({
								to: '/patients/$patientId/patient-information',
								params: { patientId },
								search: { orderId, studyId },
							});
						}}
					/>
					<OrderMenuItem
						patientId={patientId}
						orderId={orderId!}
						selected={pathname.includes('/order')}
						onClick={(clickedOrderId) => {
							navigate({
								to: '/patients/$patientId/order/study',
								params: { patientId },
								search: { orderId: clickedOrderId, studyId },
							});
						}}
					/>
					<InsuranceMenuItem
						studyId={studyId!}
						selected={pathname.includes('/insurance')}
						onClick={() => {
							if (orderId && studyId) {
								navigate({
									to: '/patients/$patientId/insurance',
									params: { patientId },
									search: { orderId, studyId },
								});
							}
						}}
					/>
					<MenuItem
						title="Schedule"
						value="schedule"
						icon={User}
						size="large"
						onClick={() => {
							navigate({
								to: '/patients/$patientId/schedule/appointment-creation/dashboard',
								params: { patientId },
								search: { orderId: orderId!, studyId },
							});
						}}
					/>
				</PrimaryMenu>
			</Box>
			<Outlet />
		</WorkflowContainer>
	);
}
