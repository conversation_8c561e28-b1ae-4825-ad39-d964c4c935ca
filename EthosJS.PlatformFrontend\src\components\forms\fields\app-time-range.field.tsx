import { useFieldContext } from "@hooks/form-context";
import { SingleInputTimeRangeField } from "@mui/x-date-pickers-pro";
import dayjs from "dayjs";
import { FieldPropType } from "./FieldPropType";

export default function AppTimeRangeField({ label }: FieldPropType) {
    const { state, handleChange } = useFieldContext<{ start: { hour: number, minute: number }, end: { hour: number, minute: number } }>();
    let startDate = null;
    let endDate = null;
    if (state.value) {
        startDate = dayjs().set('hour', state.value.start.hour).set('minute', state.value.start.minute);
        endDate = dayjs().set('hour', state.value.end.hour).set('minute', state.value.end.minute);
    }
    return (
        <SingleInputTimeRangeField
            label={label}
            value={[startDate, endDate]}
            onChange={(newValue) => handleChange({
                start: {
                    hour: newValue[0]?.hour() ?? 9,
                    minute: newValue[0]?.minute() ?? 0
                },
                end: {
                    hour: newValue[1]?.hour() ?? 17,
                    minute: newValue[1]?.minute() ?? 0
                }
            })}
            fullWidth
        />
    )
}