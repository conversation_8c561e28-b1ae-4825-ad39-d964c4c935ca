import MainCardContainer from '@components/main-container/main-card-container';
import useInsuranceVerification from '@features/insruance-verification/hooks/use-insurance-verification';
import useInsuranceVerificationStart from '@features/insruance-verification/hooks/use-insurance-verification-start';
import { NotStartedStatus } from '../insurance-auth-states';
import { CircularProgress, Box } from '@mui/material';
import { ShieldCheck } from 'lucide-react';
import ErrorState from '../insurance-auth-states/error-state';
import CompletedState from '../insurance-auth-states/completed-state';
import PendingState from '../insurance-auth-states/pending-state';

export default function InsuranceVerificationStep({ studyId }: { studyId?: string }) {
	const { verificationStatus, isFetchingVerificationStatus } = useInsuranceVerification({
		studyId,
	});
	const { startInsuranceVerification } = useInsuranceVerificationStart();
	const handleStartVerification = () => {
		if (studyId) {
			startInsuranceVerification({
				studyId,
				serviceId: '00000000-0000-0000-0000-000000000001',
			});
		}
	};

	const renderVerificationStatus = () => {
		if (isFetchingVerificationStatus) {
			return (
				<MainCardContainer
					title="Insurance Verification"
					headerSize="medium"
					icon={<ShieldCheck />}
					color="gray"
					emphasis="low"
					descriptionSubheader="System is checking coverage and eligibility for the ordered services."
					customAction={<CircularProgress size={20} />}
				/>
			);
		}
		if (!studyId) {
			return (
				<MainCardContainer
					title="Insurance Verification"
					headerSize="medium"
					icon={<ShieldCheck />}
					color="gray"
					emphasis="low"
					descriptionSubheader="Select a study to verify insurance."
				/>
			);
		}
		if (studyId && !verificationStatus) {
			return (
				<NotStartedStatus
					onStartVerification={handleStartVerification}
					disabled={!studyId || !!verificationStatus}
				/>
			);
		}
		if (!verificationStatus) return null;
		const { currentCoarseState } = verificationStatus;
		console.log('currentCoarseState', currentCoarseState);
		switch (currentCoarseState) {
			case 'Processing':
			case 'Pending':
			case 'WaitingForExternalAction':
				return <PendingState verificationStatus={verificationStatus} />;
			case 'Completed':
				return (
					<CompletedState
						verificationStatus={verificationStatus}
						onEditInsurance={handleStartVerification}
						onRetryVerification={handleStartVerification}
					/>
				);
			case 'Error':
				return <ErrorState />;
			default:
				return null;
		}
	};
	return <Box>{renderVerificationStatus()}</Box>;
}
