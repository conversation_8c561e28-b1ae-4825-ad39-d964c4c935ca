import ChipSummary from "@components/chip-summary";
import MainCardContainer from "@components/main-container/main-card-container";
import { Stack } from "@mui/material";

const PatientOverview = () => {
   return (
      <Stack sx={{ gap: 2 }}>
         <MainCardContainer title='Patient Information' color='gray'>
            <ChipSummary
               items={([
                  { label: 'Full Name', value: '<PERSON>' },
                  { label: 'Alternative', value: '(541) 600-9856' },
                  { label: 'Best time', value: 'Afternoons (1-5 PM)' },
               ])}
            />
         </MainCardContainer>
         <MainCardContainer title='Contact Information' color='gray'>
            <ChipSummary
               items={([
                  { label: 'Full Name', value: '<PERSON>' },
                  { label: 'Alternative', value: '(541) 600-9856' },
                  { label: 'Best time', value: 'Afternoons (1-5 PM)' },
               ])}
            />
         </MainCardContainer>
         <MainCardContainer title='Appointment Details' color='gray'>
            <ChipSummary
               items={([
                  { label: 'Full Name', value: '<PERSON>' },
                  { label: 'Alternative', value: '(541) 600-9856' },
                  { label: 'Best time', value: 'Afternoons (1-5 PM)' },
               ])}
            />
         </MainCardContainer>
      </Stack>
   )
}

export default PatientOverview;