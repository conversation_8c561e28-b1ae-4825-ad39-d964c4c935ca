import StepCardControl from '@components/step-card-control';
import { Box, CardContent, Button, Stack } from '@mui/material';
import { useAppForm } from '@hooks/app-form';
import { formOptions, useStore } from '@tanstack/react-form';
import ArrayFieldContainer from '@components/array-field-container';
import { ChipData } from '@components/chip-summary';
import { MapPin, Truck, Receipt } from 'lucide-react';
import MainCardContainer from '@components/main-container/main-card-container';
import { useQuery } from '@tanstack/react-query';
import { getApiReferenceSetsValuesOptions } from '@client/refdata/@tanstack/react-query.gen';
import { GetApiReferenceSetsResponse, ReferenceDataSetKeyValueDto } from '@client/refdata';
import { filter, orderBy } from 'lodash';
import { FormProps } from '@components/forms/predefined-form-props';
import { AddressesData } from '../types';
import { useMemo } from 'react';
import AddressForm, { addressFormOptions } from '@components/forms/app-address-form';
import { AddressUse } from '@components/forms/app-address-form/app-address.form';
import { addressesWithUseTypeTransformer } from './transformers';
import LoadingComponent from '@components/loading-component';
import { getErrorsForIndexField } from '@utils/forms';
import { AddressWithUseType } from '../types/form-types';

type RefDataResponse = Omit<GetApiReferenceSetsResponse, 'items'> & {
	items: ReferenceDataSetKeyValueDto[];
};

function formatAddressSummary(values: AddressWithUseType | undefined): Array<ChipData> {
	if (!values) {
		return [];
	}
	const summary: Array<ChipData> = [];

	// Add address type
	summary.push({
		label: 'Type',
		value: values.type ? values.type.toString() : '',
	});

	// Format the full address in standard format
	const addressLines = [];
	addressLines.push(values?.address?.line1 || '');
	if (values?.address?.line2) {
		addressLines.push(values?.address?.line2);
	}
	addressLines.push(
		`${values?.address?.city || ''}, ${values?.address?.state || ''} ${values?.address?.postalCode || ''}`
	);

	summary.push({
		label: 'Address',
		value: addressLines.join('\n'),
	});

	return summary;
}

const defaultValues: AddressesData = {
	addresses: [],
};

function addressesOptions(savedData?: AddressesData) {
	return formOptions({
		defaultValues: {
			...defaultValues,
			...savedData,
		},
	});
}

export default function AddressesForm({
	onSubmit,
	onSaveDraft,
	savedData,
	onValidate,
	isUpdate,
}: FormProps<AddressesData>) {
	const options = useMemo(() => addressesOptions(savedData), [savedData]);
	const form = useAppForm({
		...options,
		onSubmit: async ({ value }) => {
			const vals = addressesWithUseTypeTransformer(value.addresses);
			onSubmit({
				addresses: vals,
			} as AddressesData);
		},
	});

	const values = useStore(form.store, (state) => state.values);

	const { data: addressUses, isFetching } = useQuery(
		getApiReferenceSetsValuesOptions({
			responseType: 'json',
			query: {
				setName: 'addressUse',
			},
		})
	);

	if (isFetching) {
		return (
			<Box sx={{ height: '100%', display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
				<LoadingComponent />
			</Box>
		);
	}

	return (
		<Box
			component="form"
			sx={{ height: '100%' }}
			onSubmit={(e) => {
				e.preventDefault();
				form.handleSubmit();
			}}>
			<CardContent sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
				<form.AppField
					name="addresses"
					mode="array">
					{({ pushValue, removeValue, state, replaceValue }) => {
						const addressUsesOrdered = orderBy(
							(addressUses as RefDataResponse)?.items,
							[
								(item) => {
									if (item.key?.value === 'Physical') return 1;
									if (item.key?.value === 'Billing') return 2;
									if (item.key?.value === 'Shipping') return 3;
									return 4; // Any other types come last
								},
							],
							['asc']
						);

						return (
							<Stack gap={2}>
								{addressUsesOrdered.map((item) => {
									return (
										<MainCardContainer
											key={item.id}
											title={`${item.key?.value} Addresses`}
											color="primary"
											emphasis={isUpdate ? 'high' : 'low'}
											icon={
												item.key?.value === 'Physical' ? (
													<MapPin />
												) : item.key?.value === 'Delivery' ? (
													<Truck />
												) : (
													<Receipt />
												)
											}
											primaryActionType="Add"
											onPrimaryAction={() =>
												pushValue(addressFormOptions(undefined, item.id).defaultValues)
											}>
											{state.value &&
												state.value.length > 0 &&
												state.value.map((_, i) => {
													if (state.value?.[i].use !== Number(item.id)) {
														return null;
													}
													return (
														<Box
															key={i}
															sx={{
																':not(:last-child)': {
																	mb: 2,
																},
															}}>
															<ArrayFieldContainer
																initialEditState={!isUpdate}
																items={formatAddressSummary(state.value?.[i])}
																title={`${item.key?.value} Addresses`}
																showHeader={false}>
																{({ setEdit }) => (
																	<AddressForm
																		formValues={state.value?.[i]}
																		onValidate={async (data) => {
																			const addresses =
																				state.value?.map((_, j) => {
																					if (j === i) {
																						return data;
																					}
																					return state.value?.[j];
																				}) ?? [];
																			const vals = {
																				addresses,
																			} as AddressesData;
																			const res = await onValidate(vals);
																			return getErrorsForIndexField(
																				`contactInformation.addresses[${i}]`,
																				res
																			);
																		}}
																		onAdd={(data) => {
																			replaceValue(i, data);
																			setEdit(false);
																		}}
																		onCancel={(shouldRemove) => {
																			if (shouldRemove) {
																				removeValue(i);
																			}
																			setEdit(false);
																		}}
																		addressUse={item.key?.value as AddressUse}
																		physicalAddresses={filter(state.value, {
																			use: addressUsesOrdered[0].id,
																		})}
																		addressUseRefValue={Number(item.id)}
																	/>
																)}
															</ArrayFieldContainer>
														</Box>
													);
												})}
										</MainCardContainer>
									);
								})}
							</Stack>
						);
					}}
				</form.AppField>
			</CardContent>
			<StepCardControl>
				{!isUpdate && (
					<Button
						variant="outlined"
						color="primary"
						onClick={() => onSaveDraft(values)}>
						Save Draft
					</Button>
				)}
				<form.Subscribe
					selector={({ isDirty, canSubmit, isSubmitting }) => ({
						isDirty,
						canSubmit,
						isSubmitting,
					})}>
					{({ isDirty, canSubmit, isSubmitting }) => (
						<Button
							variant="contained"
							color="primary"
							type="submit"
							loading={isSubmitting}
							disabled={!isDirty || !canSubmit}>
							{isUpdate ? 'Update' : 'Next'}
						</Button>
					)}
				</form.Subscribe>
			</StepCardControl>
		</Box>
	);
}
