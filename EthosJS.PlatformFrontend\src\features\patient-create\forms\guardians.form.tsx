import StepCardControl from '@components/step-card-control';
import { Box, Button, CardContent } from '@mui/material';
import { useAppForm } from '@hooks/app-form';
import { formOptions, useStore } from '@tanstack/react-form';
import ArrayFieldContainer, { type ChipData } from '@components/array-field-container';
import { User2, UserPlus } from 'lucide-react';
import MainCardContainer from '@components/main-container/main-card-container';
import { FormProps } from '@components/forms/predefined-form-props';
import { GuardiansData } from '../types';
import { useMemo } from 'react';
import GuardianForm from '@components/forms/app-guardian-form';
import { EthosWorkflowsApiGuardianBasicInformationDto, EthosWorkflowsApiGuardianDto } from '@client/workflows';
import DetailsReviewContainer from '@components/details-review-container';
import dayjs from 'dayjs';

/**
 * Note: Defined to development purpose, when GuardiansData has proper type defined, feel free to remove
 */
interface I<PERSON><PERSON><PERSON> extends GuardiansData {
	guardianBasicInformation: EthosWorkflowsApiGuardianBasicInformationDto
	phoneNumbers: Array<{ value: string }>
	emails: Array<{ value: string }>
	addresses: Array<{
		address: {
			line1: string
			line2: string
			city: string
			state: string
			postalCode: string
		}
		type: number
	}>
}

function formatGuardianSummary(values: IGuardian, hasValues: boolean): Array<ChipData> {
	console.log(values)
	if (!hasValues) {
		return [];
	}
	const summary: Array<ChipData> = [];


	// Add name and relationship
	summary.push({
		label: 'Guardian',
		value: `${values.guardianBasicInformation.firstName} ${values.guardianBasicInformation.lastName} (${values.guardianBasicInformation.guardianType})`,
	});

	// Add phone number if available
	if (values.phoneNumbers?.length > 0) {
		summary.push({
			label: 'Phone',
			value: values.phoneNumbers[0].value,
		});
	}

	// Add email if available
	if (values.emails?.length > 0 && values.emails[0].value) {
		summary.push({
			label: 'Email',
			value: values.emails[0].value,
		});
	}

	// Add address information if available
	if (values.addresses?.length > 0) {
		const address = values.addresses[0];
		if (address.address) {
			const addressLines = [address.address.line1 || ''];
			if (address.address.line2) {
				addressLines.push(address.address.line2);
			}
			addressLines.push(
				`${address.address.city || ''}, ${address.address.state || ''} ${address.address.postalCode || ''}`
			);

			summary.push({
				label: typeof address.type === 'number' ? address.type.toString() : 'Address',
				value: addressLines.join('\n'),
			});
		}
	}

	return summary;
}

const defaultValues: GuardiansData = {
	guardians: [],
};

function guardiansFormOptions(savedData?: GuardiansData) {
	return formOptions({
		defaultValues: {
			...defaultValues,
			...(savedData ? savedData : {}),
		},
	});
}

export default function GuardiansForm({
	onSubmit,
	onSaveDraft,
	onValidate,
	savedData,
	patientDob,
	isUpdate,
}: FormProps<GuardiansData> & { patientDob: string }) {
	const options = useMemo(() => guardiansFormOptions(savedData), [savedData]);
	const form = useAppForm({
		...options,
		validators: {
			onChangeAsyncDebounceMs: 500,
			onChangeAsync: async ({ value }) => {
				const res = await onValidate(value);
				return res;
			},
		},
		onSubmit: async ({ value }) => {
			onSubmit(value);
		},
	});

	const values = useStore(form.store, (state) => state.values);

	const isUnder18 = dayjs().diff(dayjs(patientDob), 'year') < 18;

	return (
		<Box
			component="form"
			sx={{ height: '100%' }}
			onSubmit={(e) => {
				e.preventDefault();
				form.handleSubmit();
			}}>
			<CardContent sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
				<form.AppField
					name="guardians"
					mode="array">
					{({ pushValue, removeValue, state, replaceValue }) => (
						<MainCardContainer
							title={'Guardian Information'}
							icon={<UserPlus />}
							color="primary"
							emphasis="medium"
							primaryActionType="Add"
							onPrimaryAction={() => pushValue(guardiansFormOptions().defaultValues as EthosWorkflowsApiGuardianDto)}>
							{state.value.length > 0 &&
								state.value.map((_, i) => {
									const value = state?.value?.[i];

									return (
										<ArrayFieldContainer
											key={i}
											initialEditState={true}
											items={[]}
											title="Guardian"
											showHeader={false}
											displayMode='tab'
											mainCardContainerProps={{
												title: value?.guardianBasicInformation?.firstName,
												icon: <User2 />,
												emphasis: "medium",
												primaryActionType: 'Edit'
											}}
											defaultTabValue='basic-details'
											tabItems={[
												{
													name: 'Basic Details',
													value: 'basic-details',
													chipSummaryData: [
														{
															label: 'Full Name',
															value: value?.guardianBasicInformation?.firstName as string
														},
														{
															label: 'Gender',
															value: value?.guardianDemographics?.gender as string
														},
														{
															label: 'Date of Birth',
															value: value?.guardianDemographics?.dateOfBirth as string
														},
														{
															label: 'Social Security Number',
															value: value?.guardianDemographics?.ssn as string
														},
														{
															label: 'Relationship to Patient',
															value: value?.guardianDemographics?.relationShipToPatient as string
														},
														{
															label: 'State ID Number',
															value: 'XY87415564'
														}
													]
												},
												{
													name: 'Contact Info',
													value: 'contact-info',
													chipSummaryData: [

														// email details
														...value?.emails?.map((email) => {
															const tempObj = {
																use: email.use as unknown as string,
																value: email?.value,
															};
															return Object.entries(tempObj).map(([key, value]) => {
																return { value, hideSeperator: true, variant: key === 'isPreferred' && value ? 'filled' : null }
															}) ?? []
														}) ?? [],

														// phone details
														...value?.phoneNumbers?.map((phone) => {
															const tempObj = {
																use: phone.use as unknown as string,
																value: phone?.value,
															};

															return Object.entries(tempObj).map(([, value]) => {
																return { value, hideSeperator: true, }
															}) ?? []
														}) ?? []
													],
													mode: 'array',
												},
												{
													name: 'Address',
													value: 'address',
													chipSummaryData: value?.addresses?.map((address) => {
														const tempObj = {
															line1: address.address.line1,
															line2: address.address.line2,
															city: address.address.city,
															state: address.address.state,
															postalCode: address.address.postalCode,
														};
														return Object.entries(tempObj).map(([, value]) => ({ value, hideSeperator: true })) ?? []
													}) ?? [],
													mode: 'array',
												},
												{
													name: 'Documents',
													value: 'documents',
													chipSummaryData: []
												}
											]}
										>
											{({ setEdit }) => (
												<GuardianForm
													formValues={value}
													onAdd={(data) => {
														replaceValue(i, data);
														setEdit(false);
													}}
													onCancel={(shouldRemove) => {
														if (shouldRemove) {
															removeValue(i);
														}
														setEdit(false);
													}}
													onDelete={() => {
														removeValue(i);
														setEdit(false);
													}}
												/>
											)}
										</ArrayFieldContainer>
									);
								})}
						</MainCardContainer>
					)}
				</form.AppField>
			</CardContent>
			<StepCardControl>
				{!isUpdate && (
					<Button
						variant="outlined"
						color="primary"
						onClick={() => onSaveDraft(values)}>
						Save Draft
					</Button>
				)}
				<form.Subscribe
					selector={({ isDirty, canSubmit, isSubmitting }) => ({
						isDirty,
						canSubmit,
						isSubmitting,
					})}>
					{({ isDirty, canSubmit, isSubmitting }) => {
						return (
							<Button
								variant="contained"
								color="primary"
								type="submit"
								loading={isSubmitting}
								disabled={!isDirty || !canSubmit || isUnder18}>
								{isUpdate ? 'Update' : 'Next'}
							</Button>
						);
					}}
				</form.Subscribe>
			</StepCardControl>
		</Box>
	);
}
