import StepCardControl from '@components/step-card-control';
import { Box, Button, CardContent } from '@mui/material';
import { useAppForm } from '@hooks/app-form';
import { formOptions, useStore } from '@tanstack/react-form';
import ArrayFieldContainer from '@components/array-field-container';
import { ChipData } from '@components/chip-summary';
import { UserPlus } from 'lucide-react';
import MainCardContainer from '@components/main-container/main-card-container';
import { FormProps } from '@components/forms/predefined-form-props';
import { GuardiansData } from '../types';
import { useMemo } from 'react';
import GuardianForm from '@components/forms/app-guardian-form';

function formatGuardianSummary(values: GuardiansData, hasValues: boolean): Array<ChipData> {
	if (!hasValues) {
		return [];
	}
	const summary: Array<ChipData> = [];

	// Add name and relationship
	summary.push({
		label: 'Guardian',
		value: `${values.guardianBasicInformation.firstName} ${values.guardianBasicInformation.lastName} (${values.guardianBasicInformation.guardianType})`,
	});

	// Add phone number if available
	if (values.phoneNumbers?.length > 0) {
		summary.push({
			label: 'Phone',
			value: values.phoneNumbers[0].value,
		});
	}

	// Add email if available
	if (values.emails?.length > 0 && values.emails[0].value) {
		summary.push({
			label: 'Email',
			value: values.emails[0].value,
		});
	}

	// Add address information if available
	if (values.addresses?.length > 0) {
		const address = values.addresses[0];
		if (address.address) {
			const addressLines = [address.address.line1 || ''];
			if (address.address.line2) {
				addressLines.push(address.address.line2);
			}
			addressLines.push(
				`${address.address.city || ''}, ${address.address.state || ''} ${address.address.postalCode || ''}`
			);

			summary.push({
				label: typeof address.type === 'number' ? address.type.toString() : 'Address',
				value: addressLines.join('\n'),
			});
		}
	}

	return summary;
}

const defaultValues: GuardiansData = {
	guardians: [],
};

function guardiansFormOptions(savedData?: GuardiansData) {
	return formOptions({
		defaultValues: {
			...defaultValues,
			...(savedData ? savedData : {}),
		},
	});
}

export default function GuardiansForm({
	onSubmit,
	onSaveDraft,
	onValidate,
	savedData,
	patientDob,
	isUpdate,
}: FormProps<GuardiansData> & { patientDob: string }) {
	const options = useMemo(() => guardiansFormOptions(savedData), [savedData]);
	const form = useAppForm({
		...options,
		validators: {
			onChangeAsyncDebounceMs: 500,
			onChangeAsync: async ({ value }) => {
				const res = await onValidate(value);
				return res;
			},
		},
		onSubmit: async ({ value }) => {
			onSubmit(value);
		},
	});

	const values = useStore(form.store, (state) => state.values);

	// const isUnder18 = dayjs().diff(dayjs(patientDob), 'year') < 18;

	return (
		<Box
			component="form"
			sx={{ height: '100%' }}
			onSubmit={(e) => {
				e.preventDefault();
				form.handleSubmit();
			}}>
			<CardContent sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
				<form.AppField
					name="guardians"
					mode="array">
					{({ pushValue, removeValue, state, replaceValue }) => (
						<MainCardContainer
							title={'Guardian Information'}
							icon={<UserPlus />}
							color="primary"
							emphasis="low"
							primaryActionType="Add"
							onPrimaryAction={() => pushValue(guardiansFormOptions().defaultValues)}>
							{state.value.length > 0 &&
								state.value.map((_, i) => {
									return (
										<ArrayFieldContainer
											key={i}
											initialEditState={true}
											items={formatGuardianSummary(state.value[i], false)}
											title="Guardian"
											showHeader={false}>
											{({ setEdit }) => (
												<GuardianForm
													formValues={state.value[i]}
													onAdd={(data) => {
														replaceValue(i, data);
														setEdit(false);
													}}
													onCancel={(shouldRemove) => {
														if (shouldRemove) {
															removeValue(i);
														}
														setEdit(false);
													}}
													onDelete={() => {
														removeValue(i);
														setEdit(false);
													}}
												/>
											)}
										</ArrayFieldContainer>
									);
								})}
						</MainCardContainer>
					)}
				</form.AppField>
			</CardContent>
			<StepCardControl>
				{!isUpdate && (
					<Button
						variant="outlined"
						color="primary"
						onClick={() => onSaveDraft(values)}>
						Save Draft
					</Button>
				)}
				<form.Subscribe
					selector={({ isDirty, canSubmit, isSubmitting }) => ({
						isDirty,
						canSubmit,
						isSubmitting,
					})}>
					{({ isDirty, canSubmit, isSubmitting }) => {
						return (
							<Button
								variant="contained"
								color="primary"
								type="submit"
								loading={isSubmitting}
								disabled={!isDirty || !canSubmit}>
								{isUpdate ? 'Update' : 'Next'}
							</Button>
						);
					}}
				</form.Subscribe>
			</StepCardControl>
		</Box>
	);
}
