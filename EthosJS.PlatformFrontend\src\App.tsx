import './App.css';
import { createRouter, ErrorComponent, RouterProvider } from '@tanstack/react-router';
import { LicenseInfo } from '@mui/x-license';
// Import the generated route tree
import { routeTree } from './routeTree.gen';
import { MsalProvider } from '@azure/msal-react';
import { msalInstance } from './auth/msal';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import LoadingComponent from './components/loading-component';
import { CssBaseline, ThemeProvider } from '@mui/material';
import { AdapterDayjs } from '@mui/x-date-pickers-pro/AdapterDayjs';
import { LocalizationProvider } from '@mui/x-date-pickers-pro/LocalizationProvider';
import theme from './theme/mui-theme';
import applyWorkflowsCLientConfig from '@config/client.workflows';
import applyRefdataCLientConfig from '@config/client.refdata';
import { AuthProvider } from '@contexts/auth-context';

export const queryClient = new QueryClient({
	defaultOptions: {
		queries: {
			refetchInterval: false,
			refetchIntervalInBackground: true,
			refetchOnWindowFocus: false,
		},
	},
});

applyWorkflowsCLientConfig();
applyRefdataCLientConfig();

LicenseInfo.setLicenseKey(import.meta.env.VITE_MUI_LICENSE_KEY);
// Create a new router instance
const router = createRouter({
	routeTree,
	defaultPendingComponent: () => <LoadingComponent />,
	defaultErrorComponent: ({ error }) => <ErrorComponent error={error} />,
	context: {
		queryClient,
		msalInstance,
	},
	defaultPreload: 'intent',
	scrollRestoration: true,
});

// Register the router instance for type safety
declare module '@tanstack/react-router' {
	interface Register {
		router: typeof router;
	}
}

function App() {
	return (
		<MsalProvider instance={msalInstance}>
			<AuthProvider>
				<LocalizationProvider dateAdapter={AdapterDayjs}>
					<QueryClientProvider client={queryClient}>
						<ThemeProvider
							theme={theme}
							disableTransitionOnChange>
							<CssBaseline enableColorScheme />
							<RouterProvider router={router} />
						</ThemeProvider>
					</QueryClientProvider>
				</LocalizationProvider>
			</AuthProvider>
		</MsalProvider>
	);
}

export default App;
