import { EthosWorkflowsApiPhoneNumberContactDto } from '@client/workflows';
import { formOptions } from '@tanstack/react-form';
import { z } from 'zod';

const phoneNumberSchema = z.any().transform((raw: EthosWorkflowsApiPhoneNumberContactDto) => {
	return {
		...raw,
	};
});

const defaultValues: EthosWorkflowsApiPhoneNumberContactDto = {
	type: null!,
	value: '',
	preferredTime: null!,
	isPreferred: false,
	allowsVoice: false,
	allowsSms: false,
};

function phoneNumberFormOptions(savedData?: EthosWorkflowsApiPhoneNumberContactDto) {
	return formOptions({
		defaultValues: {
			...defaultValues,
			...(savedData ? savedData : {}),
		},
	});
}

export { phoneNumberFormOptions, defaultValues, phoneNumberSchema };
