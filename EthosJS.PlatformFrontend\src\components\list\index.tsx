import { Fragment, ReactNode, useCallback, useState } from "react"
import { Stack, Checkbox, Radio, CheckboxProps, RadioProps, Divider, FormControlLabel } from "@mui/material"
import ListItem from './list-item'

type Item<MetaType = object> = {
  icon?: ReactNode
  title: string
  subTitle?: string
  value?: string
  extra?: ReactNode
  meta?: MetaType
}

type Value = Item['value']

interface IList<MetaType> {
  renderTitle?: (item: Item<MetaType>) => void;
  // Select input props
  selectable: boolean
  selectMode?: 'checkbox' | 'radio'
  selectPlacement?: 'start' | 'end'

  items: Array<Item>
  selectedItems?: Array<Value>
  selectedItem?: Value

  onSelectItems?: (selectedItems: Array<Value>) => void
  onSelectItem?: (selectedItem: Value) => void

  size?: 'small' | 'large'

  /**
   * Load the selection label beside the textbox ot radio button
   */
  renderSelectionLabel?: (items: Item) => ReactNode

  renderActions?: (item: Item) => ReactNode
}

function List<MetaType>({
  items = [],
  selectable = false,
  selectMode = 'checkbox',
  selectPlacement = 'end',

  renderSelectionLabel,
  renderActions,

  selectedItems: selectedItemsProp,
  onSelectItems: onSelectItemsProp,
  onSelectItem: onSelectItemProp,
  selectedItem: selectedItemProp,

  size = 'small',
  renderTitle
}: IList<MetaType>) {

  const [selectedItemsLocal, setSelectedItemsLocal] = useState<Array<Value>>([]);
  const [selectedItemLocal, setSelectedItemLocal] = useState<Value>();

  const selectedItems = selectedItemsProp ?? selectedItemsLocal;
  const selectedItem = selectedItemProp ?? selectedItemLocal;

  const isMulti = selectMode === 'checkbox';

  const getSelectionContainer = useCallback((item: Item) => {
    const { value } = item;
    const inputProps: Pick<CheckboxProps | RadioProps, 'onChange'> = {
      onChange: () => onSelectItems(item)
    }

    const inputElement: ReactNode = isMulti ? <Checkbox checked={selectedItems.includes(value)} {...inputProps} /> :
      <Radio checked={selectedItem === value} {...inputProps} />;

    const label = renderSelectionLabel?.(item)
    return (
      selectable && (
        <FormControlLabel label={label} control={inputElement} />
      )
    )
  }, [selectMode, selectedItems, selectedItem, isMulti])

  const getExtraElements = useCallback(({ extra, ...rest }: Item) => {
    return (
      <>
        {selectPlacement === 'end' ? getSelectionContainer(rest) : null}
        {extra}
      </>
    )
  }, [selectMode, selectedItems, selectedItem, isMulti, selectPlacement, renderSelectionLabel]);

  const onSelectItems = (selectedItem: Item) => {
    const { value } = selectedItem;

    if (isMulti) {
      /**
      * For Multi Select
      */
      let updated = selectedItems;
      if (selectedItems?.includes(value)) {
        updated = selectedItems.filter((i) => i !== value);
      } else {
        updated = [...selectedItems, value]
      };

      setSelectedItemsLocal(updated);
      onSelectItemsProp?.(updated);
    } else {
      /**
       * For Single Select
      */
      setSelectedItemLocal(value);
      onSelectItemProp?.(value);
    }
  }

  return (
    <Stack gap={.1} >
      {items.map((item, index) => {
        let props = {}
        if (typeof renderTitle === 'function') {
          props = {
            ...props,
            renderTitle: () => renderTitle(item as Item<MetaType>),
          }
        } else if (typeof renderActions === 'function') {
          props = {
            ...props,
            renderActions: () => renderActions(item),
          }
        }

        return (
          <Fragment key={index}>
            <ListItem
              icon={item.icon}
              title={item.title}
              subTitle={item.subTitle}
              extra={getExtraElements(item)}
              size={size}
              {...props}
            />
            {index < items?.length - 1 && (
              <Divider
                sx={({ palette }) => {
                  return {
                    borderColor: palette.grey[200]
                  }
                }}
              />
            )}
          </Fragment>
        )
      })}
    </Stack>
  );

}

export default List
