import { useFieldContext } from "@hooks/form-context";
import { InputAdornment, TextField } from "@mui/material";
import { FieldPropType } from "./FieldPropType";
import { Mail } from "lucide-react";

export default function AppEmailField({ label, required }: FieldPropType) {

    const field = useFieldContext<string>();

    const shouldShowError = field.getMeta().isTouched || field.getMeta().isDirty;
    const errors = shouldShowError ? field.getMeta().errors.map(error => error.message) : [];

    return (
        <TextField
            label={label}
            value={field.state.value}
            required={required}
            type="email"
            onChange={(e) => field.handleChange(e.target.value)}
            onBlur={field.handleBlur}
            error={shouldShowError && errors.length > 0}
            helperText={shouldShowError ? errors.join(', ') : undefined}
            fullWidth
            placeholder="<EMAIL>"
            slotProps={{
                input: {
                    inputMode: 'email',
                    startAdornment: <InputAdornment position="start"><Mail /></InputAdornment>
                }
            }}
        />
    );
}