import { webhookData } from '@app-types/insurance-webhook';
import { createFileRoute } from '@tanstack/react-router';
import { useState } from 'react';
import WebhookStatus from '@features/insruance-verification/components/webhook-status';
import { Button, Stack } from '@mui/material';

export const Route = createFileRoute(
	'/_dashboard/patients/$patientId/insurance/prior-authorization'
)({
	component: RouteComponent,
});

function RouteComponent() {
	const [webhookDataItem, setWebhookDataItem] = useState(webhookData[0]);
	const getRandomWebhookData = () => {
		// Select a random index from the webhookData array
		const randomIndex = Math.floor(Math.random() * webhookData.length);
		setWebhookDataItem(webhookData[randomIndex]);
	};
	return (
		<Stack gap={2}>
			<Button onClick={getRandomWebhookData}>Get Random Webhook Data</Button>
			<WebhookStatus webhookData={webhookDataItem} />
		</Stack>
	);
}
