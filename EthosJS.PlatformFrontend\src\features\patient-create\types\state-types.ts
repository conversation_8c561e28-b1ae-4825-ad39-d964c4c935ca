interface StepPropsNew {
	patientId: string;
	successCallback: () => void;
}

import { SystemTextJsonNodesJsonNode } from '@client/workflows';

type StateTypes = 'Complete' | 'NotStarted' | 'InProgress' | 'Error' | 'Warning';
type StepNames =
	| 'BasicInformation'
	| 'Contacts'
	| 'Addresses'
	| 'Insurances'
	| 'Guardians'
	| 'ClinicalConsiderations';

type PatientState = {
	flowState: {
		status: StateTypes;
		progress: number;
		lastUpdate: string;
	};
	stepState: Record<StepNames, StateTypes>;
} & SystemTextJsonNodesJsonNode;

export type { PatientState, StateTypes, StepNames, StepPropsNew };
