import { useState } from 'react';
import { Typography, Stack, Checkbox, Button } from '@mui/material';
import StepCardControl from '@components/step-card-control';
import MainCardContainer from '@components/main-container/main-card-container';
import { ClipboardList, Signature } from 'lucide-react';
import { ConfirmationStepProps } from '@features/order-create/types';
import useOrder from '@features/order-create/hooks/use-order';
import { EthosWorkflowsApiCreateOrderDto } from '@client/workflows';
import AssociatedInsuranceReview from '../associated-insurance-review';
import PhysicianReview from '../physician-review';
import CareLocationsReview from '../care-locations-review';
import useStudy from '@features/order-create/hooks/use-study';
import StudyDetails from '../study-details';
import { entries, map } from 'lodash';

export default function ConfirmatioStep({
	orderId,
	studyId,
	successCallback,
}: ConfirmationStepProps) {
	const [checked, setChecked] = useState(false);
	const { orderData, commitOrderDraft } = useOrder({ orderId });
	const { updateStudy, commitStudyDraft, studyData } = useStudy({ studyId });

	const { data } = orderData ?? {};
	const { careLocationId, referringPhysicianId, interpretingPhysicianId, primaryCarePhysicianId } =
		(data as EthosWorkflowsApiCreateOrderDto) ?? {};

	const { data: studyDataContent } = studyData ?? {};
	const { encounterType, studyType, studyAttributes, insurances } = studyDataContent ?? {};

	const onCommitSuccessCallback = () => {
		commitStudyDraft(studyId, successCallback);
	};

	const updateOrderAndCommit = () => {
		updateStudy(
			{
				...studyDataContent,
				insurances: map(insurances, (id) => id?.toString() ?? ''),
			},
			() => {
				commitStudyDraft(studyId, onCommitSuccessCallback);
			}
		);
	};

	return (
		<Stack
			gap={2}
			sx={{ mb: 2 }}>
			<MainCardContainer
				icon={<ClipboardList />}
				title="Review Study"
				color="primary"
				emphasis="high">
				<Stack gap={2}>
					<StudyDetails
						encounterTypeId={encounterType}
						studyTypeId={studyType}
						studyAttributes={
							map(entries(studyAttributes ?? []), ([, value]) => value as number) ?? []
						}
					/>
					<AssociatedInsuranceReview
						insurances={map(insurances, (id) => id?.toString() ?? '') ?? []}
					/>
					<PhysicianReview
						orderingPhysicianId={''}
						interpretingPhysicianId={interpretingPhysicianId}
						referringPhysicianId={referringPhysicianId}
						primaryCarePhysicianId={primaryCarePhysicianId}
					/>
					<CareLocationsReview careLocationId={careLocationId} />
				</Stack>
			</MainCardContainer>
			<MainCardContainer
				title="Confirmation Checklist"
				color="primary"
				emphasis="high">
				<Stack
					flexDirection="row"
					justifyContent="space-between">
					<Stack
						flexDirection="row"
						gap={2}>
						<Signature color="#422F7D" />
						<Typography>
							I have reviewed the patient information and confirmed it is correct.
						</Typography>
					</Stack>
					<Checkbox
						checked={checked}
						onChange={(e) => setChecked(e.target.checked)}
					/>
				</Stack>
			</MainCardContainer>
			<StepCardControl>
				<Button
					variant="contained"
					onClick={() => commitOrderDraft(orderId, updateOrderAndCommit)}>
					Create Order & Exit
				</Button>
			</StepCardControl>
		</Stack>
	);
}
