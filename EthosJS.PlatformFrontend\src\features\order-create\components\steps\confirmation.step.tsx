import { useState } from 'react';
import { Typography, Stack, Checkbox, Button } from '@mui/material';
import StepCardControl from '@components/step-card-control';
import MainCardContainer from '@components/main-container/main-card-container';
import { ClipboardList, Signature } from 'lucide-react';
import { ConfirmationStepProps, OrderState } from '@features/order-create/types';
import useOrder from '@features/order-create/hooks/use-order';
import { EthosWorkflowsApiCreateOrderDto } from '@client/workflows';
import AssociatedInsuranceReview from '../associated-insurance-review';
import PhysicianReview from '../physician-review';
import CareLocationsReview from '../care-locations-review';
import useStudy from '@features/order-create/hooks/use-study';
import StudyDetails from '../study-details';
import { entries, map } from 'lodash';
import dayjs from 'dayjs';

export default function ConfirmatioStep({
	orderId,
	studyId,
	successCallback,
}: ConfirmationStepProps) {
	const [checked, setChecked] = useState(false);
	const { orderData, updateOrder, commitOrderDraft } = useOrder({ orderId });
	const { updateStudy, commitStudyDraft, studyData } = useStudy({ studyId });

	const { data } = orderData ?? {};
	const { careLocationId, referringPhysicianId, interpretingPhysicianId, primaryCarePhysicianId } =
		(data as EthosWorkflowsApiCreateOrderDto) ?? {};

	const orderState = (data?._state as unknown as OrderState) ?? {};
	const { flowState, stepState } = orderState ?? {};

	const { data: studyDataContent } = studyData ?? {};
	const { encounterType, studyType, studyAttributes, insurances } = studyDataContent ?? {};

	const onCommitSuccessCallback = () => {
		commitStudyDraft(studyId, successCallback);
	};

	const updateStudyAndCommit = () => {
		updateStudy(
			{
				...studyDataContent,
				insurances: map(insurances, (id) => id?.toString() ?? ''),
			},
			() => {
				commitStudyDraft(studyId, onCommitSuccessCallback);
			}
		);
	};

	const updateOrderAndCommit = () => {
		updateOrder(
			{
				...data,
			},
			{
				flowState: {
					...flowState,
					status: 'Complete',
					progress: 100,
					lastUpdate: dayjs().format('MMM D, YYYY h:mm A'),
				},
				stepState: {
					...stepState,
					ReviewAndSubmitOrder: 'Complete',
				},
			},
			() => commitOrderDraft(orderId, updateStudyAndCommit)
		);
	};

	return (
		<Stack
			gap={2}
			sx={{ mb: 2 }}>
			<MainCardContainer
				icon={<ClipboardList />}
				title="Review Study"
				color="primary"
				emphasis="high">
				<Stack gap={2}>
					<StudyDetails
						encounterTypeId={encounterType}
						studyTypeId={studyType}
						studyAttributes={
							map(entries(studyAttributes ?? []), ([, value]) => value as number) ?? []
						}
					/>
					<AssociatedInsuranceReview
						insurances={map(insurances, (id) => id?.toString() ?? '') ?? []}
					/>
					<PhysicianReview
						orderingPhysicianId={''}
						interpretingPhysicianId={interpretingPhysicianId}
						referringPhysicianId={referringPhysicianId}
						primaryCarePhysicianId={primaryCarePhysicianId}
					/>
					<CareLocationsReview careLocationId={careLocationId} />
				</Stack>
			</MainCardContainer>
			<MainCardContainer
				title="Confirmation Checklist"
				color="primary"
				emphasis="high">
				<Stack
					flexDirection="row"
					justifyContent="space-between">
					<Stack
						flexDirection="row"
						gap={2}>
						<Signature color="#422F7D" />
						<Typography>
							I have reviewed the patient information and confirmed it is correct.
						</Typography>
					</Stack>
					<Checkbox
						checked={checked}
						onChange={(e) => setChecked(e.target.checked)}
					/>
				</Stack>
			</MainCardContainer>
			<StepCardControl>
				<Button
					variant="contained"
					onClick={() => updateOrderAndCommit()}>
					Create Order & Exit
				</Button>
			</StepCardControl>
		</Stack>
	);
}
