import { <PERSON><PERSON>, Button, Box } from '@mui/material';

import StepCardControl from '@components/step-card-control';

import { useAppForm } from '@hooks/app-form';

import { PredefinedFormProps } from '../predefined-form-props';
import LoadingComponent from '@components/loading-component';
import MainCardContainer from '@components/main-container/main-card-container';
import { EthosWorkflowsApiInsuranceOutputDto } from '@client/workflows';
import { useQuery } from '@tanstack/react-query';
import { PatientRead, PatientCreate } from '@auth/scopes';
import { getApiReferenceSetsValuesOptions } from '@client/refdata/@tanstack/react-query.gen';
import { map, find } from 'lodash';
import { useStore } from '@tanstack/react-form';
import { useEffect, useMemo, useState } from 'react';
import AssociatedInsurances from '../../../features/order-create/forms/associated-insurance-card';

const defaultValues = {
  studyPreferences: {
    encounterType: null,
    studyType: null,
    studyAttributes: []
  },
  associatedInsurance: []
};

export default function CreateStudyForm({
  onSubmit,
  isLoading,
  savedData,
  onSaveDraft,
  insurances
}: PredefinedFormProps<any> & { insurances: Array<EthosWorkflowsApiInsuranceOutputDto> }) {
  const initialValues = useMemo(() => {
    if (!savedData) return defaultValues;
    const sp = savedData.studyPreferences || {};
    return {
      studyPreferences: {
        encounterType: sp.encounterType != null ? sp.encounterType.toString() : null,
        studyType: sp.studyType != null ? sp.studyType.toString() : null,
        studyAttributes: Array.isArray(sp.studyAttributes)
          ? sp.studyAttributes.map((id: any) => id.toString())
          : []
      },
      associatedInsurance: Array.isArray(savedData.associatedInsurance)
        ? savedData.associatedInsurance.map((id: any) => id.toString())
        : []
    };
  }, [savedData]);

  const form = useAppForm({
    defaultValues: initialValues,
    onSubmit: async ({ value }) => {
      const out = {
        studyPreferences: {
          encounterType: value.studyPreferences.encounterType
            ? Number(value.studyPreferences.encounterType)
            : null,
          studyType: value.studyPreferences.studyType
            ? Number(value.studyPreferences.studyType)
            : null,
          studyAttributes: Array.isArray(value.studyPreferences.studyAttributes)
            ? value.studyPreferences.studyAttributes.map((v: string) => Number(v))
            : []
        },
        associatedInsurance: Array.isArray(value.associatedInsurance)
          ? value.associatedInsurance
          : []
      };
      onSubmit(out);
    }
  });

  const [encounterTypeName, setEncounterTypeName] = useState<string | null>(null);
  const [studyTypeName, setStudyTypeName] = useState<string | null>(null);

  const { values } = useStore(form.store, ({ values }) => ({ values }));

  const { data: encounterTypeOptions, isFetching: isFetchingEncounterTypeOptions } = useQuery({
    ...getApiReferenceSetsValuesOptions({
      ,
      responseType: 'json',
      query: { setName: 'encounterType' }
    }) as any,
  });
  const { data: studyTypeOptions, isFetching: isFetchingStudyTypeOptions } = useQuery({
    ...getApiReferenceSetsValuesOptions({
      ,
      responseType: 'json',
      query: {
        setName: 'studyType',
        filter: `encounterType eq ${encounterTypeName}`
      }
    }) as any,
    enabled: !!encounterTypeName
  });
  const { data: studyAttributesOptions, isFetching: isFetchingStudyAttributesOptions } = useQuery({
    ...getApiReferenceSetsValuesOptions({
      ,
      responseType: 'json',
      query: {
        setName: 'studyAttributes',
        filter: `encounterType eq ${encounterTypeName}`
      }
    }) as any,
    enabled: !!studyTypeName
  });

  useEffect(() => {
    const savedEncId = form.getFieldValue('studyPreferences.encounterType');
    if (encounterTypeOptions?.items && savedEncId) {
      const encItem = find(encounterTypeOptions.items, { id: Number(savedEncId) });
      if (encItem) {
        setEncounterTypeName(encItem.values?.name?.toString() ?? null);
      }
    }
  }, [encounterTypeOptions, form]);

  useEffect(() => {
    const savedStudId = form.getFieldValue('studyPreferences.studyType');
    if (studyTypeOptions?.items && savedStudId) {
      const stItem = find(studyTypeOptions.items, { id: Number(savedStudId) });
      if (stItem) {
        setStudyTypeName(stItem.values?.name?.toString() ?? null);
      }
    }
  }, [studyTypeOptions, form]);

  if (isLoading) {
    return <LoadingComponent />;
  }

  return (
    <Box
      sx={{ height: '100%' }}
      component="form"
      onSubmit={(e) => {
        e.preventDefault();
        form.handleSubmit();
      }}>
      <Stack gap={3}>
        <MainCardContainer
          title="Study Preferences"
          color="primary"
          emphasis="low"
          descriptionSubheader="Select the appropriate study and attributes based on the physician's order."
          descriptionText="Each type has different protocols and equipment requirements.">
          <Stack sx={{ display: 'flex', flexDirection: 'column', gap: 3 }}>
            {isFetchingEncounterTypeOptions ? (
              <LoadingComponent />
            ) : (
              <form.AppField
                name="studyPreferences.encounterType"
                children={(field) => (
                  <field.AppRadioField
                    label="1. Encounter Type"
                    options={map(encounterTypeOptions?.items ?? [], (item) => ({
                      title: item.key?.value?.toString() ?? 'Unknown',
                      description: item.values?.description?.toString() ?? '',
                      value: item.id.toString()
                    }))}
                    onValueChange={(value) => {
                      if (typeof value === 'string') {
                        form.setFieldValue('studyPreferences.studyType', null);
                        form.setFieldValue('studyPreferences.studyAttributes', []);
                        setStudyTypeName(null);

                        const encItem = find(encounterTypeOptions?.items, {
                          id: Number(value)
                        });
                        if (encItem) {
                          setEncounterTypeName(encItem.values?.name?.toString() ?? null);
                        } else {
                          setEncounterTypeName(null);
                        }
                      }
                    }}
                  />
                )}
              />
            )}

            {!!encounterTypeName &&
              (isFetchingStudyTypeOptions ? (
                <LoadingComponent />
              ) : (
                <form.AppField
                  name="studyPreferences.studyType"
                  children={(field) => (
                    <field.AppRadioField
                      label="2. Study Type"
                      options={map(studyTypeOptions?.items ?? [], (item) => ({
                        title: item.key?.value?.toString() ?? 'Unknown',
                        description: item.values?.description?.toString() ?? '',
                        value: item.id.toString()
                      }))}
                      onValueChange={(value) => {
                        if (typeof value === 'string') {
                          form.setFieldValue('studyPreferences.studyAttributes', []);
                          const stItem = find(studyTypeOptions?.items, {
                            id: Number(value)
                          });
                          if (stItem) {
                            setStudyTypeName(stItem.values?.name?.toString() ?? null);
                          } else {
                            setStudyTypeName(null);
                          }
                        }
                      }}
                    />
                  )}
                />
              ))}

            {!!studyTypeName &&
              (isFetchingStudyAttributesOptions ? (
                <LoadingComponent />
              ) : (
                <form.AppField
                  name="studyPreferences.studyAttributes"
                  children={(field) => (
                    <field.AppRadioField
                      label="3. Study Attributes"
                      isMulti
                      options={map(studyAttributesOptions?.items ?? [], (item) => ({
                        title: item.key?.value?.toString() ?? 'Unknown',
                        description: item.values?.description?.toString() ?? '',
                        value: item.id.toString()
                      }))}
                    />
                  )}
                />
              ))}
          </Stack>
        </MainCardContainer>
        <AssociatedInsurances form={form} insurances={insurances} />
        <StepCardControl>
          <Button variant="outlined" color="primary" onClick={() => onSaveDraft(values)}>
            Save Draft
          </Button>
          <form.Subscribe
            selector={({ isDirty, canSubmit, isSubmitting }) => ({
              isDirty,
              canSubmit,
              isSubmitting
            })}>
            {({ isDirty, canSubmit, isSubmitting }) => (
              <Button
                variant="contained"
                color="primary"
                type="submit"
                loading={isSubmitting}
                disabled={!isDirty || !canSubmit}>
                Next
              </Button>
            )}
          </form.Subscribe>
        </StepCardControl>
      </Stack>
    </Box>
  );
}
