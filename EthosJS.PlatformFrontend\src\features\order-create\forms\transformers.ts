import { EthosWorkflowsApiCreateStudyDto } from '@client/workflows';
import { entries, reduce, map } from 'lodash';
import z from 'zod';
import { StudyFormData } from '../types';

const studyFormDataTransformer = z.any().transform((raw: StudyFormData) => {
	return {
		...raw,
		encounterType: raw.encounterType ? Number(raw.encounterType) : null,
		studyType: raw.studyType ? Number(raw.studyType) : null,
		studyAttributes: Array.isArray(raw.studyAttributes)
			? reduce(
					raw.studyAttributes,
					(acc, curr) => {
						acc[curr.key] = curr.value;
						return acc;
					},
					{} as { [key: string]: number }
				)
			: {},
	};
});

const studyPreferencesTransformer = z.any().transform((raw: EthosWorkflowsApiCreateStudyDto) => {
	return {
		...raw,
		studyAttributes: raw.studyAttributes
			? map(entries(raw.studyAttributes), ([key, value]) => ({ key, value }))
			: [],
	};
});

function studyFormDataToDto(data: StudyFormData) {
	return studyFormDataTransformer.parse(data);
}

function studyDtoToFormData(data?: Omit<EthosWorkflowsApiCreateStudyDto, 'orderId'>) {
	if (!data) return {};
	return studyPreferencesTransformer.parse(data);
}

export { studyFormDataToDto, studyDtoToFormData };
