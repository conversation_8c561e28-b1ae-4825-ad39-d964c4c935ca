import { PatientCreate, PatientRead } from '@auth/scopes';
// import { StudyQQueryDto } from "@client/workflows";
import {
  postApiAddNewOrderListOptions,
  postApiStudySearchOptions
} from '@client/workflows/@tanstack/react-query.gen';
import { Autocomplete, Stack, TextField } from '@mui/material';
import { useQuery } from '@tanstack/react-query';

interface SelectionControlsProps {
  patientId: string;
  orderId: string;
  studyId?: string;
  onChange?: (orderId: string, studyId?: string) => void;
  onFieldChange?: (fieldName: string, studyId: string) => void;
}

export default function SelectionControls({
  patientId,
  orderId,
  studyId,
  onFieldChange
}: SelectionControlsProps) {
  const { data: order } = useQuery({
    ...postApiAddNewOrderListOptions({
      scopes: [PatientCreate.value, PatientRead.value],
      responseType: 'json',
      body: {
        Patient: patientId
      }
    }),
    enabled: !!patientId
  });

  const { data: studies } = useQuery({
    ...postApiStudySearchOptions({
      scopes: [PatientCreate.value, PatientRead.value],
      responseType: 'json'
      // TODO(Gk): Since no data, just removed it
      // body: Query.literal(StudyQuery.withOrderId(orderId)) as unknown as StudyQQueryDto,
    }),
    enabled: !!patientId && !!orderId
  });

  const onChangeOrderIdField = (requestedValues: string) => {
    onFieldChange?.('orderId', requestedValues as string);
  };

  const onChangeStudyIdField = (requestedValues: string) => {
    onFieldChange?.('studyId', requestedValues as string);
  };

  const onChangeDateField = (requestedValues: string) => {
    onFieldChange?.('date', requestedValues as string);
  };
  return (
    <Stack direction="column" gap={2} sx={{ flex: 1, overflow: 'auto', py: 1 }}>
      <Autocomplete
        disablePortal
        sx={{ minWidth: 200 }}
        value={orderId}
        onChange={(_, newValue) => {
          onChangeOrderIdField(newValue);
        }}
        defaultValue={orderId}
        disableClearable
        renderInput={(params) => <TextField {...params} label="Order Id" />}
        options={order as string[]}
        filterOptions={(x) => x}
      />
      <Autocomplete
        disablePortal
        sx={{ minWidth: 200 }}
        value={studyId ?? null}
        onChange={(_, newValue) => {
          onChangeStudyIdField(newValue ?? '');
        }}
        renderInput={(params) => <TextField {...params} label="Study" />}
        options={studies?.items?.map((item) => item.id) ?? []}
        filterOptions={(x) => x}
      />
      <Autocomplete
        disablePortal
        sx={{ minWidth: 200 }}
        value={'01/01/2025 — 11AM'}
        onChange={(_, newValue) => {
          onChangeDateField(newValue ?? '');
        }}
        renderInput={(params) => <TextField {...params} label="Date" />}
        options={['01/01/2025 — 11AM']}
        filterOptions={(x) => x}
      />
    </Stack>
  );
}
