import CollapsibleMainCardContainer from "@components/collapsible-main-card-container"
import { MenuItem, Stack, TextField, Typography } from "@mui/material"
import PatientOverview from "./-patient-overview";
import useFollowUpStore from "./-store";
import { useStore } from "@tanstack/react-store";
import { useMemo } from "react";
import StatusBanner from "@components/status.banner";
import { Status } from "@config/status";
import PatientAppointmentAvailableDateTimeBanner from "./-patient-appointment-available-banner";

type Option = { label: string, value: string }

const resonOptions: Array<Option> = [
   { label: 'Patient has a work conflict on original date', value: '60036a49-fb2b-4bac-8994-ef19fe122a78' },
   { label: 'Patient ordered cancellation', value: '60036a49-fb2b-4bac-8994-ef19fe122a79' },
];



const FollowUpEdit = () => {

   const { actions, store } = useFollowUpStore();

   const { selectedConfirmationStatus, selectedReason, statusOptions, appointmentStatusDetails } = useStore(store);

   const isConfirming = appointmentStatusDetails?.status === 'confirmed';
   const isReschedule = appointmentStatusDetails?.status === 're-scheduled';
   const isWillNotAttend = appointmentStatusDetails?.status === 'cancelled';
   const isUnableToReachPatient = appointmentStatusDetails?.status === 'unable-to-reach';

   const onConfirmation = () => {
      actions.onConfirm(true);
   }

   const renderStatusBanner = useMemo(() => {
      if (isConfirming) {
         return <PatientAppointmentAvailableDateTimeBanner />
      } else if (isReschedule) {
         return (
            <StatusBanner
               title='From October 21, 11 AM - 5 PM to October 22, 11 AM - 5 PM'
               subTitle='Patient requested to reschedule the appointment to a new date and time. verbally confirmed they will attend the appointment as scheduled. System will send the confirmation details to Patient’s preferred contact method.'
               status={Status.Warning}
            />
         )
      }
      return null;
   }, [isConfirming, isReschedule])

   const renderCurrentPrimaryActionButtonProps = useMemo(() => {
      if (isConfirming) {
         return {
            label: 'Save Confirmation',
            disabled: !selectedConfirmationStatus,
            onClick: () => onConfirmation?.()
         }
      } else if (isReschedule) {
         return {
            label: 'Save & Reschedule',
            disabled: !selectedReason,
            onClick: () => onConfirmation?.()
         }
      } else if (isWillNotAttend) {
         return {
            label: 'Save & Cancel Appointment',
            disabled: !selectedReason,
            onClick: () => onConfirmation?.()
         }
      } else if (isUnableToReachPatient) {
         return {
            label: 'Schedule Next Attempt',
            disabled: !selectedReason,
            onClick: () => onConfirmation?.()
         }
      }
      return { label: '' }
   }, [selectedConfirmationStatus, isConfirming, isReschedule, selectedReason, isUnableToReachPatient])

   const containerStatus = useMemo(() => {
      if (isReschedule) return 'warning';
      if (isWillNotAttend || isUnableToReachPatient) return 'error'
      return 'primary'
   }, [isReschedule])

   return (
      <CollapsibleMainCardContainer
         mainContainerProps={{
            title: 'Follow-up Call',
            emphasis: 'high',
            footerProps: {
               primaryButton1: renderCurrentPrimaryActionButtonProps,
               primaryButton2: { label: 'Cancel' },
            },
            color: containerStatus
         }}
         collapse
      >
         <Stack gap={2} >
            <PatientOverview />
            {renderStatusBanner}

            <Typography variant='h6' sx={(t) => ({ color: t.palette.common.black })}>Call Outcome</Typography>
            <TextField
               value={selectedConfirmationStatus?.value}
               onChange={(e) => {
                  actions.onSelectConfirmationStatus(e.target as unknown as { label: string, value: string })
               }}
               select={true}
               fullWidth
               variant="outlined"
               label='Select Confirmation Status'
            >
               {statusOptions.map((option) => (
                  <MenuItem key={option.value?.toString()} value={option.value?.toString()}>
                     {option.label}
                  </MenuItem>
               ))}
            </TextField>
            {isReschedule || isWillNotAttend || isUnableToReachPatient ? (
               <TextField
                  value={selectedReason?.value}
                  onChange={(e) => {
                     actions.onSelectReason(e.target as unknown as { label: string, value: string })
                  }}
                  select={true}
                  fullWidth
                  variant="outlined"
                  label='Select Confirmation Status'
               >
                  {
                     resonOptions.map((option) => (
                        <MenuItem key={option.value?.toString()} value={option.value?.toString()}>
                           {option.label}
                        </MenuItem>
                     ))
                  }
               </TextField>
            ) : null}
         </Stack>
      </CollapsibleMainCardContainer>
   )
}

export default FollowUpEdit;