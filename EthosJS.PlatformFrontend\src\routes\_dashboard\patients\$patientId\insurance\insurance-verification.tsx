import { GetApiInsuranceVerificationStatusByJobIdResponse } from '@client/workflows';
import MainCardContainer from '@components/main-container/main-card-container';
import useInsuranceVerification from '@features/insruance-verification/hooks/use-insurance-verification';
import useInsuranceVerificationStart from '@features/insruance-verification/hooks/use-insurance-verification-start';
import { Chip, CircularProgress } from '@mui/material';
import { createFileRoute } from '@tanstack/react-router';
import { ShieldCheck } from 'lucide-react';

const ProcessingStatus = ({
	verificationStatus,
}: {
	verificationStatus: GetApiInsuranceVerificationStatusByJobIdResponse;
}) => {
	return (
		<MainCardContainer
			title="Insurance Verification"
			headerSize="medium"
			icon={<ShieldCheck />}
			color="primary"
			emphasis="high"
			descriptionSubheader={
				verificationStatus.currentFineGrainedStateName ||
				'System is checking coverage and eligibility for the ordered services.'
			}
			customAction={
				<Chip
					color="primary"
					label={verificationStatus.currentCoarseState}
					sx={{ borderRadius: 2 }}
				/>
			}
		/>
	);
};

const ErrorStatus = ({
	verificationStatus,
}: {
	verificationStatus: GetApiInsuranceVerificationStatusByJobIdResponse;
}) => {
	return (
		<MainCardContainer
			title="Insurance Verification"
			headerSize="medium"
			icon={<ShieldCheck />}
			color="error"
			emphasis="low"
			descriptionSubheader={
				verificationStatus.coarseStateMessage ||
				'System is checking coverage and eligibility for the ordered services.'
			}
			customAction={
				<Chip
					label={verificationStatus.currentCoarseState}
					sx={{ borderRadius: 2, color: 'black' }}
				/>
			}
		/>
	);
};

const PendingStatus = ({
	verificationStatus,
}: {
	verificationStatus: GetApiInsuranceVerificationStatusByJobIdResponse;
}) => {
	return (
		<MainCardContainer
			title="Insurance Verification"
			headerSize="medium"
			icon={<ShieldCheck />}
			color="primary"
			emphasis="low"
			descriptionSubheader={
				verificationStatus.coarseStateMessage ||
				'System is checking coverage and eligibility for the ordered services.'
			}
			customAction={
				<Chip
					color="primary"
					label={verificationStatus.currentCoarseState}
					sx={{ borderRadius: 2 }}
				/>
			}
		/>
	);
};

const VerifiedStatus = ({
	verificationStatus,
}: {
	verificationStatus: GetApiInsuranceVerificationStatusByJobIdResponse;
}) => {
	return (
		<MainCardContainer
			title="Insurance Verification"
			headerSize="medium"
			icon={<ShieldCheck />}
			color="success"
			emphasis="low"
			descriptionSubheader={
				verificationStatus.coarseStateMessage ||
				'System is checking coverage and eligibility for the ordered services.'
			}
			customAction={
				<Chip
					label={verificationStatus.currentCoarseState}
					sx={{ borderRadius: 2, color: 'black' }}
				/>
			}
		/>
	);
};

export const Route = createFileRoute(
	'/_dashboard/patients/$patientId/insurance/insurance-verification'
)({
	component: RouteComponent,
});

function RouteComponent() {
	const { studyId } = Route.useSearch();
	const { verificationStatus, isFetchingVerificationStatus, verificationStatusError } =
		useInsuranceVerification({
			studyId,
		});
	const { startInsuranceVerification, isStartingVerification, startVerificationError } =
		useInsuranceVerificationStart();

	const renderVerificationStatus = () => {
		if (isFetchingVerificationStatus) {
			return (
				<MainCardContainer
					title="Insurance Verification"
					headerSize="medium"
					icon={<ShieldCheck />}
					color="gray"
					emphasis="low"
					descriptionSubheader="System is checking coverage and eligibility for the ordered services."
					customAction={<CircularProgress size={20} />}
				/>
			);
		}
		if (!studyId) {
			return (
				<MainCardContainer
					title="Insurance Verification"
					headerSize="medium"
					icon={<ShieldCheck />}
					color="gray"
					emphasis="low"
					descriptionSubheader="Select a study to verify insurance."
				/>
			);
		}
		if (studyId && !verificationStatus) {
			return (
				<MainCardContainer
					title="Insurance Verification"
					headerSize="medium"
					icon={<ShieldCheck />}
					color="gray"
					emphasis="low"
					descriptionSubheader="System is checking coverage and eligibility for the ordered services."
					customAction={
						<Chip
							label="Not Started"
							sx={{ borderRadius: 2, color: 'black' }}
							onClick={() => {
								startInsuranceVerification({
									studyId,
									serviceId: '00000000-0000-0000-0000-000000000001',
								});
							}}
							disabled={!studyId || !!verificationStatus}
						/>
					}
				/>
			);
		}
		if (!verificationStatus) {
			return null;
		}

		// Match the coarse state to the appropriate component
		switch (verificationStatus.currentCoarseState) {
			case 'Processing':
				return <ProcessingStatus verificationStatus={verificationStatus} />;
			case 'Error':
				return <ErrorStatus verificationStatus={verificationStatus} />;
			case 'Pending':
				return <PendingStatus verificationStatus={verificationStatus} />;
			case 'Completed':
				if (verificationStatus.coarseStateSucceeded) {
					return <VerifiedStatus verificationStatus={verificationStatus} />;
				} else {
					return <ErrorStatus verificationStatus={verificationStatus} />;
				}
			case 'WaitingForExternalAction':
				return <PendingStatus verificationStatus={verificationStatus} />;
			case 'WaitingForManualAction':
				return <PendingStatus verificationStatus={verificationStatus} />;
			default:
				return null;
		}
	};
	return <>{renderVerificationStatus()}</>;
}
