import { createFileRoute } from '@tanstack/react-router';
import PageContainer from '../../components/page-container';
import { Dashboard } from '@mui/icons-material';
import { PatientCreate, PatientRead } from '@auth/scopes';
import FlowStatus from '@components/workflow/flow-status';
import { Grid2 } from '@mui/material';
import { postApiAddNewPatientListOptions } from '@client/workflows/@tanstack/react-query.gen';
export const Route = createFileRoute('/_dashboard/dashboard')({
	component: RouteComponent,
	loader: async ({ context: { queryClient } }) => {
		return queryClient.fetchQuery({
			...postApiAddNewPatientListOptions({
				scopes: [PatientCreate.value, PatientRead.value],
				responseType: 'json',
			}),
			staleTime: -1,
		});
	},
});

function RouteComponent() {
	const data = Route.useLoaderData();
	const navigate = Route.useNavigate();

	return (
		<PageContainer
			title="Dashboard"
			icon={Dashboard}>
			<Grid2
				container
				spacing={2}>
				{data?.map((item) => {
					return (
						<FlowStatus
							key={item}
							workflowId={item}
							navigateToFlow={({ patientId, addNewPatient }) => {
								navigate({
									to: '/patients/$patientId/patient-information',
									params: { patientId },
									search: {
										patientWfId: addNewPatient,
									},
								});
							}}
						/>
					);
				})}
			</Grid2>
		</PageContainer>
	);
}
