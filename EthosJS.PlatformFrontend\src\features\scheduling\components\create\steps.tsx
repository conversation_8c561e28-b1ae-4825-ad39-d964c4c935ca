import CollapsibleMainCardContainer from '@components/collapsible-main-card-container';
import SmartButtonOutlinedIcon from '@mui/icons-material/SmartButtonOutlined';

import { JSX, useMemo, useRef, useState, useTransition } from 'react';

import useCreateAppointmentStore from '@hooks/use-create-appointment-store';
import { useStore } from '@tanstack/react-store';
import SelectAppointmentDate from './select-appointment-date';
import Instructions from './instruction';
import Review from './review';
import { LoaderIcon } from 'lucide-react';
import { Box } from '@mui/material';
import { useForm } from '@tanstack/react-form';
import { useMutation, useQuery } from '@tanstack/react-query';
import {
	postApiPatientAppointmentMutation,
	postApiRoomSearchOptions,
} from '@client/workflows/@tanstack/react-query.gen';
import { PatientCreate, PatientRead } from '@auth/scopes';
import { useNavigate } from '@tanstack/react-router';
import NotificationSnackbar, { NotificationState } from '@components/notification-snackbar';

const STEP_COMPONENT_MAP: Record<
	number,
	{
		title: string;
		component: (props: {
			formRef: React.RefObject<ReturnType<typeof useForm> | null>;
		}) => JSX.Element;
	}
> = {
	0: {
		title: 'Select Date and Time',
		component: SelectAppointmentDate,
	},
	1: {
		title: 'Instructions & Documents',
		component: Instructions,
	},
	2: {
		title: 'Review Appointment Details',
		component: Review,
	},
};

interface StepProps {
	patientId: string;
	studyId: string;
	onCancelCreate: () => void;
}

const Steps = ({ patientId, studyId, onCancelCreate }: StepProps) => {
	const [notificationState, setNotification] = useState({
		showToast: false,
		notification: { message: '', severity: 'info' } as NotificationState,
	});

	const navigate = useNavigate();

	const [isPending, startTransition] = useTransition();

	const { store, actions } = useCreateAppointmentStore();
	const {
		totalStep,
		activeStep,
		values: { selectedAppointment, isConfirmed },
		isLastStep,
	} = useStore(store, (state) => state);

	const { mutateAsync: createPatientAppointment, isPending: isCreating } = useMutation({
		...postApiPatientAppointmentMutation({
			scopes: [PatientCreate.value, PatientRead.value],
			responseType: 'json',
		}),
		onSuccess: () => {
			navigate({
				to: '/patients/$patientId/schedule/appointment-creation/dashboard/preview',
				params: { patientId },
				search,
			});
		},
		onSettled(data, error) {
			if (error) {
				setNotification({
					showToast: true,
					notification: {
						message: (error.response?.data.message as string) ?? 'Something went wrong',
						severity: 'error',
					},
				});
			}
			if (!error && data) {
				setNotification({
					showToast: true,
					notification: {
						message: 'Appointment created successfully',
						severity: 'success',
					},
				});
			}
		},
	});

	const { data: room } = useQuery(
		postApiRoomSearchOptions({
			scopes: [PatientCreate.value, PatientRead.value],
			responseType: 'json',
		})
	);

	const formRef = useRef<ReturnType<typeof useForm> | null>(null);

	const isValidNext = useMemo(() => {
		if (activeStep === 0) {
			return Object.values(selectedAppointment).filter(Boolean).length;
		} else if (activeStep === 2) {
			return isConfirmed;
		}
		return true;
	}, [activeStep, selectedAppointment, isConfirmed]);

	const onNext = () => {
		if (!isLastStep) {
			// activeStep === 0;
			// ? createPatientAppointment({
			// 		body: {
			// 			studyId,
			// 			roomId: room?.items?.[0]?.id as string,
			// 			careLocationShiftId: selectedAppointment.id as string,
			// 			date: selectedAppointment.date as string,
			// 		},
			// 	})
			// : formRef.current?.handleSubmit();
		} else {
			// TODO(GK): Appointment confirmation flow goes here
		}
	};

	const onBack = () => {
		startTransition(actions.moveBack);
	};

	const onCloseToast = () => {
		setNotification({
			showToast: false,
			notification: { message: '', severity: 'info' } as NotificationState,
		});
	};

	const currentStepContext = STEP_COMPONENT_MAP?.[activeStep];

	const StepComponent = currentStepContext?.component ?? null;

	return (
		<CollapsibleMainCardContainer
			mainContainerProps={{
				title: `Create an appointment (${activeStep + 1}/${totalStep})`,
				emphasis: 'high',
				icon: <SmartButtonOutlinedIcon />,
				descriptionSubheader: currentStepContext?.title,
				footerProps: {
					primaryButton1: {
						label: !isLastStep ? (isCreating ? 'Creating...' : 'Next') : 'Create & Exit',
						'data-testid': 'create-appointment-step-next',
						disabled: !isValidNext,
						onClick: onNext,
					},
					...(activeStep
						? {
								primaryButton2: {
									label: 'Back',
									'data-testid': 'create-appointment-step-back',
									onClick: onBack,
								},
							}
						: {}),
					secondaryButton1: {
						label: 'Cancel',
						'data-testid': 'create-appointment-step-cancel',
						onClick: onCancelCreate,
					},
				},
			}}
			defaultCollapse>
			{isPending ? (
				<Box sx={styles.centered}>
					<LoaderIcon />
				</Box>
			) : (
				<StepComponent {...{ formRef }} />
			)}
			<NotificationSnackbar
				notification={notificationState.notification}
				open={notificationState.showToast}
				onCloseToast={onCloseToast}
			/>
		</CollapsibleMainCardContainer>
	);
};

const styles = {
	centered: {
		display: 'flex',
		justifyContent: 'center',
		alignItems: 'center',
	},
};

export default Steps;
