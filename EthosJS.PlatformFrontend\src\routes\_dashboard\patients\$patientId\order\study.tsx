import { createFileRoute } from '@tanstack/react-router';
import StudyStep from '@features/order-create/components/steps/study.step';

export const Route = createFileRoute('/_dashboard/patients/$patientId/order/study')({
	component: RouteComponent,
	loaderDeps: ({ search }) => ({ ...search }),
});

function RouteComponent() {
	const { patientId } = Route.useParams();
	const { orderId, studyId } = Route.useSearch();
	const navigate = Route.useNavigate();

	const onSuccessCallback = (studyId: string) => {
		navigate({
			to: '/patients/$patientId/order/care-location',
			params: { patientId },
			search: { orderId, studyId },
		});
	};

	return (
		<StudyStep
			patientId={patientId}
			orderId={orderId!}
			studyId={studyId}
			successCallback={onSuccessCallback}
		/>
	);
}
