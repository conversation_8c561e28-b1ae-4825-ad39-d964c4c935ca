// This file is auto-generated by @hey-api/openapi-ts

import {
	type Options,
	getApiAddNewOrderValidationRules,
	getApiAddNewOrderStateById,
	postApiAddNewOrderStart,
	postApiAddNewOrderRewind,
	postApiAddNewOrderList,
	postApiAddNewOrderAddStudy,
	postApiAddNewOrderAddStudyDraft,
	postApiAddNewOrderAddStudyValidate,
	postApiAddNewOrderAddCareLocation,
	postApiAddNewOrderAddCareLocationDraft,
	postApiAddNewOrderAddCareLocationValidate,
	postApiAddNewOrderAddPhysicians,
	postApiAddNewOrderAddPhysiciansDraft,
	postApiAddNewOrderAddPhysiciansValidate,
	postApiAddNewOrderReviewAndSubmitOrder,
	postApiAddNewOrderReviewAndSubmitOrderDraft,
	postApiAddNewOrderReviewAndSubmitOrderValidate,
	getApiAddNewPatientValidationRules,
	getApiAddNewPatientStateById,
	postApiAddNewPatientStart,
	postApiAddNewPatientRewind,
	postApiAddNewPatientList,
	postApiAddNewPatientAddBasicInformation,
	postApiAddNewPatientAddBasicInformationDraft,
	postApiAddNewPatientAddBasicInformationValidate,
	postApiAddNewPatientAddContacts,
	postApiAddNewPatientAddContactsDraft,
	postApiAddNewPatientAddContactsValidate,
	postApiAddNewPatientAddAddresses,
	postApiAddNewPatientAddAddressesDraft,
	postApiAddNewPatientAddAddressesValidate,
	postApiAddNewPatientAddInsurances,
	postApiAddNewPatientAddInsurancesDraft,
	postApiAddNewPatientAddInsurancesValidate,
	postApiAddNewPatientAddGuardians,
	postApiAddNewPatientAddGuardiansDraft,
	postApiAddNewPatientAddGuardiansValidate,
	postApiAddNewPatientAddClinicalInformation,
	postApiAddNewPatientAddClinicalInformationDraft,
	postApiAddNewPatientAddClinicalInformationValidate,
	getApiCareLocationById,
	patchApiCareLocationById,
	putApiCareLocationById,
	postApiCareLocationSearch,
	getApiCareLocation,
	postApiCareLocation,
	postApiCareLocationDraft,
	getApiCareLocationDraftByEntityId,
	putApiCareLocationDraftByEntityId,
	postApiCareLocationDraftByEntityIdCommit,
	postApiCareLocationDraftValidate,
	postApiCareLocationDraftByEntityIdValidate,
	getApiDraftById,
	patchApiDraftById,
	putApiDraftById,
	postApiDraftSearch,
	getApiDraft,
	postApiDraft,
	postApiDraftDraft,
	getApiDraftDraftByEntityId,
	putApiDraftDraftByEntityId,
	postApiDraftDraftByEntityIdCommit,
	postApiDraftDraftValidate,
	postApiDraftDraftByEntityIdValidate,
	getApiEquipmentById,
	patchApiEquipmentById,
	putApiEquipmentById,
	postApiEquipmentSearch,
	getApiEquipment,
	postApiEquipment,
	postApiEquipmentDraft,
	getApiEquipmentDraftByEntityId,
	putApiEquipmentDraftByEntityId,
	postApiEquipmentDraftByEntityIdCommit,
	postApiEquipmentDraftValidate,
	postApiEquipmentDraftByEntityIdValidate,
	postApiFileRequestUploadToken,
	postApiFileUpload,
	getApiFileStatusByFileId,
	getApiHealth,
	getApiInsuranceById,
	patchApiInsuranceById,
	putApiInsuranceById,
	postApiInsuranceSearch,
	getApiInsurance,
	postApiInsurance,
	postApiInsuranceDraft,
	getApiInsuranceDraftByEntityId,
	putApiInsuranceDraftByEntityId,
	postApiInsuranceDraftByEntityIdCommit,
	postApiInsuranceDraftValidate,
	postApiInsuranceDraftByEntityIdValidate,
	postApiInsuranceVerificationStart,
	getApiInsuranceVerificationStatusByJobId,
	postApiInsuranceVerificationSearch,
	postApiInsuranceVerificationFastauthWebhook,
	postApiLogin,
	getApiLoginCheck,
	postApiMockDbDelete,
	getApiMockDbReset,
	getApiNoteById,
	patchApiNoteById,
	putApiNoteById,
	postApiNoteSearch,
	getApiNote,
	postApiNote,
	postApiNoteDraft,
	getApiNoteDraftByEntityId,
	putApiNoteDraftByEntityId,
	postApiNoteDraftByEntityIdCommit,
	postApiNoteDraftValidate,
	postApiNoteDraftByEntityIdValidate,
	getApiOrderById,
	patchApiOrderById,
	putApiOrderById,
	postApiOrderSearch,
	getApiOrder,
	postApiOrder,
	postApiOrderDraft,
	getApiOrderDraftByEntityId,
	putApiOrderDraftByEntityId,
	postApiOrderDraftByEntityIdCommit,
	postApiOrderDraftValidate,
	postApiOrderDraftByEntityIdValidate,
	getApiPatientById,
	patchApiPatientById,
	putApiPatientById,
	postApiPatientSearch,
	getApiPatient,
	postApiPatient,
	postApiPatientDraft,
	getApiPatientDraftByEntityId,
	putApiPatientDraftByEntityId,
	postApiPatientDraftByEntityIdCommit,
	postApiPatientDraftValidate,
	postApiPatientDraftByEntityIdValidate,
	getApiPatientAppointmentById,
	patchApiPatientAppointmentById,
	putApiPatientAppointmentById,
	postApiPatientAppointmentSearch,
	getApiPatientAppointment,
	postApiPatientAppointment,
	postApiPatientAppointmentDraft,
	getApiPatientAppointmentDraftByEntityId,
	putApiPatientAppointmentDraftByEntityId,
	postApiPatientAppointmentDraftByEntityIdCommit,
	postApiPatientAppointmentDraftValidate,
	postApiPatientAppointmentDraftByEntityIdValidate,
	getApiPatientAppointmentConfirmationById,
	patchApiPatientAppointmentConfirmationById,
	putApiPatientAppointmentConfirmationById,
	postApiPatientAppointmentConfirmationSearch,
	getApiPatientAppointmentConfirmation,
	postApiPatientAppointmentConfirmation,
	postApiPatientAppointmentConfirmationDraft,
	getApiPatientAppointmentConfirmationDraftByEntityId,
	putApiPatientAppointmentConfirmationDraftByEntityId,
	postApiPatientAppointmentConfirmationDraftByEntityIdCommit,
	postApiPatientAppointmentConfirmationDraftValidate,
	postApiPatientAppointmentConfirmationDraftByEntityIdValidate,
	getApiPhysicianById,
	patchApiPhysicianById,
	putApiPhysicianById,
	postApiPhysicianSearch,
	getApiPhysician,
	postApiPhysician,
	postApiPhysicianDraft,
	getApiPhysicianDraftByEntityId,
	putApiPhysicianDraftByEntityId,
	postApiPhysicianDraftByEntityIdCommit,
	postApiPhysicianDraftValidate,
	postApiPhysicianDraftByEntityIdValidate,
	getApiProviderById,
	patchApiProviderById,
	putApiProviderById,
	postApiProviderSearch,
	getApiProvider,
	postApiProvider,
	postApiProviderDraft,
	getApiProviderDraftByEntityId,
	putApiProviderDraftByEntityId,
	postApiProviderDraftByEntityIdCommit,
	postApiProviderDraftValidate,
	postApiProviderDraftByEntityIdValidate,
	getPyapiZip,
	getTsapiZip,
	getApiRoomById,
	patchApiRoomById,
	putApiRoomById,
	postApiRoomSearch,
	getApiRoom,
	postApiRoom,
	postApiRoomDraft,
	getApiRoomDraftByEntityId,
	putApiRoomDraftByEntityId,
	postApiRoomDraftByEntityIdCommit,
	postApiRoomDraftValidate,
	postApiRoomDraftByEntityIdValidate,
	postApiSchedulingFindSlots,
	getApiSchedulingConstraintById,
	patchApiSchedulingConstraintById,
	putApiSchedulingConstraintById,
	postApiSchedulingConstraintSearch,
	getApiSchedulingConstraint,
	postApiSchedulingConstraint,
	postApiSchedulingConstraintDraft,
	getApiSchedulingConstraintDraftByEntityId,
	putApiSchedulingConstraintDraftByEntityId,
	postApiSchedulingConstraintDraftByEntityIdCommit,
	postApiSchedulingConstraintDraftValidate,
	postApiSchedulingConstraintDraftByEntityIdValidate,
	getApiStudyById,
	patchApiStudyById,
	putApiStudyById,
	postApiStudySearch,
	getApiStudy,
	postApiStudy,
	postApiStudyDraft,
	getApiStudyDraftByEntityId,
	putApiStudyDraftByEntityId,
	postApiStudyDraftByEntityIdCommit,
	postApiStudyDraftValidate,
	postApiStudyDraftByEntityIdValidate,
	getApiTechnicianById,
	patchApiTechnicianById,
	putApiTechnicianById,
	postApiTechnicianSearch,
	getApiTechnician,
	postApiTechnician,
	postApiTechnicianDraft,
	getApiTechnicianDraftByEntityId,
	putApiTechnicianDraftByEntityId,
	postApiTechnicianDraftByEntityIdCommit,
	postApiTechnicianDraftValidate,
	postApiTechnicianDraftByEntityIdValidate,
	getApiTechnicianAppointmentById,
	patchApiTechnicianAppointmentById,
	putApiTechnicianAppointmentById,
	postApiTechnicianAppointmentSearch,
	getApiTechnicianAppointment,
	postApiTechnicianAppointment,
	postApiTechnicianAppointmentDraft,
	getApiTechnicianAppointmentDraftByEntityId,
	putApiTechnicianAppointmentDraftByEntityId,
	postApiTechnicianAppointmentDraftByEntityIdCommit,
	postApiTechnicianAppointmentDraftValidate,
	postApiTechnicianAppointmentDraftByEntityIdValidate,
	getApiWorkflowFlows,
	getApiWorkflowMyTasks,
	getApiWorkflowProfile,
} from '../sdk.gen';
import {
	queryOptions,
	type UseMutationOptions,
	type DefaultError,
	infiniteQueryOptions,
	type InfiniteData,
} from '@tanstack/react-query';
import type {
	GetApiAddNewOrderValidationRulesData,
	GetApiAddNewOrderStateByIdData,
	PostApiAddNewOrderStartData,
	PostApiAddNewOrderStartResponse,
	PostApiAddNewOrderRewindData,
	PostApiAddNewOrderListData,
	PostApiAddNewOrderListResponse,
	PostApiAddNewOrderAddStudyData,
	PostApiAddNewOrderAddStudyResponse,
	PostApiAddNewOrderAddStudyDraftData,
	PostApiAddNewOrderAddStudyDraftResponse,
	PostApiAddNewOrderAddStudyValidateData,
	PostApiAddNewOrderAddStudyValidateResponse,
	PostApiAddNewOrderAddCareLocationData,
	PostApiAddNewOrderAddCareLocationResponse,
	PostApiAddNewOrderAddCareLocationDraftData,
	PostApiAddNewOrderAddCareLocationDraftResponse,
	PostApiAddNewOrderAddCareLocationValidateData,
	PostApiAddNewOrderAddCareLocationValidateResponse,
	PostApiAddNewOrderAddPhysiciansData,
	PostApiAddNewOrderAddPhysiciansResponse,
	PostApiAddNewOrderAddPhysiciansDraftData,
	PostApiAddNewOrderAddPhysiciansDraftResponse,
	PostApiAddNewOrderAddPhysiciansValidateData,
	PostApiAddNewOrderAddPhysiciansValidateResponse,
	PostApiAddNewOrderReviewAndSubmitOrderData,
	PostApiAddNewOrderReviewAndSubmitOrderResponse,
	PostApiAddNewOrderReviewAndSubmitOrderDraftData,
	PostApiAddNewOrderReviewAndSubmitOrderDraftResponse,
	PostApiAddNewOrderReviewAndSubmitOrderValidateData,
	PostApiAddNewOrderReviewAndSubmitOrderValidateResponse,
	GetApiAddNewPatientValidationRulesData,
	GetApiAddNewPatientStateByIdData,
	PostApiAddNewPatientStartData,
	PostApiAddNewPatientStartResponse,
	PostApiAddNewPatientRewindData,
	PostApiAddNewPatientListData,
	PostApiAddNewPatientListResponse,
	PostApiAddNewPatientAddBasicInformationData,
	PostApiAddNewPatientAddBasicInformationResponse,
	PostApiAddNewPatientAddBasicInformationDraftData,
	PostApiAddNewPatientAddBasicInformationDraftResponse,
	PostApiAddNewPatientAddBasicInformationValidateData,
	PostApiAddNewPatientAddBasicInformationValidateResponse,
	PostApiAddNewPatientAddContactsData,
	PostApiAddNewPatientAddContactsResponse,
	PostApiAddNewPatientAddContactsDraftData,
	PostApiAddNewPatientAddContactsDraftResponse,
	PostApiAddNewPatientAddContactsValidateData,
	PostApiAddNewPatientAddContactsValidateResponse,
	PostApiAddNewPatientAddAddressesData,
	PostApiAddNewPatientAddAddressesResponse,
	PostApiAddNewPatientAddAddressesDraftData,
	PostApiAddNewPatientAddAddressesDraftResponse,
	PostApiAddNewPatientAddAddressesValidateData,
	PostApiAddNewPatientAddAddressesValidateResponse,
	PostApiAddNewPatientAddInsurancesData,
	PostApiAddNewPatientAddInsurancesResponse,
	PostApiAddNewPatientAddInsurancesDraftData,
	PostApiAddNewPatientAddInsurancesDraftResponse,
	PostApiAddNewPatientAddInsurancesValidateData,
	PostApiAddNewPatientAddInsurancesValidateResponse,
	PostApiAddNewPatientAddGuardiansData,
	PostApiAddNewPatientAddGuardiansResponse,
	PostApiAddNewPatientAddGuardiansDraftData,
	PostApiAddNewPatientAddGuardiansDraftResponse,
	PostApiAddNewPatientAddGuardiansValidateData,
	PostApiAddNewPatientAddGuardiansValidateResponse,
	PostApiAddNewPatientAddClinicalInformationData,
	PostApiAddNewPatientAddClinicalInformationResponse,
	PostApiAddNewPatientAddClinicalInformationDraftData,
	PostApiAddNewPatientAddClinicalInformationDraftResponse,
	PostApiAddNewPatientAddClinicalInformationValidateData,
	PostApiAddNewPatientAddClinicalInformationValidateResponse,
	GetApiCareLocationByIdData,
	PatchApiCareLocationByIdData,
	PatchApiCareLocationByIdError,
	PatchApiCareLocationByIdResponse,
	PutApiCareLocationByIdData,
	PutApiCareLocationByIdError,
	PutApiCareLocationByIdResponse,
	PostApiCareLocationSearchData,
	PostApiCareLocationSearchResponse,
	GetApiCareLocationData,
	GetApiCareLocationResponse,
	PostApiCareLocationData,
	PostApiCareLocationError,
	PostApiCareLocationResponse,
	PostApiCareLocationDraftData,
	PostApiCareLocationDraftError,
	PostApiCareLocationDraftResponse,
	GetApiCareLocationDraftByEntityIdData,
	PutApiCareLocationDraftByEntityIdData,
	PutApiCareLocationDraftByEntityIdError,
	PutApiCareLocationDraftByEntityIdResponse,
	PostApiCareLocationDraftByEntityIdCommitData,
	PostApiCareLocationDraftByEntityIdCommitError,
	PostApiCareLocationDraftByEntityIdCommitResponse,
	PostApiCareLocationDraftValidateData,
	PostApiCareLocationDraftValidateError,
	PostApiCareLocationDraftValidateResponse,
	PostApiCareLocationDraftByEntityIdValidateData,
	PostApiCareLocationDraftByEntityIdValidateError,
	PostApiCareLocationDraftByEntityIdValidateResponse,
	GetApiDraftByIdData,
	PatchApiDraftByIdData,
	PatchApiDraftByIdError,
	PatchApiDraftByIdResponse,
	PutApiDraftByIdData,
	PutApiDraftByIdError,
	PutApiDraftByIdResponse,
	PostApiDraftSearchData,
	PostApiDraftSearchResponse,
	GetApiDraftData,
	GetApiDraftResponse,
	PostApiDraftData,
	PostApiDraftError,
	PostApiDraftResponse,
	PostApiDraftDraftData,
	PostApiDraftDraftError,
	PostApiDraftDraftResponse,
	GetApiDraftDraftByEntityIdData,
	PutApiDraftDraftByEntityIdData,
	PutApiDraftDraftByEntityIdError,
	PutApiDraftDraftByEntityIdResponse,
	PostApiDraftDraftByEntityIdCommitData,
	PostApiDraftDraftByEntityIdCommitError,
	PostApiDraftDraftByEntityIdCommitResponse,
	PostApiDraftDraftValidateData,
	PostApiDraftDraftValidateError,
	PostApiDraftDraftValidateResponse,
	PostApiDraftDraftByEntityIdValidateData,
	PostApiDraftDraftByEntityIdValidateError,
	PostApiDraftDraftByEntityIdValidateResponse,
	GetApiEquipmentByIdData,
	PatchApiEquipmentByIdData,
	PatchApiEquipmentByIdError,
	PatchApiEquipmentByIdResponse,
	PutApiEquipmentByIdData,
	PutApiEquipmentByIdError,
	PutApiEquipmentByIdResponse,
	PostApiEquipmentSearchData,
	PostApiEquipmentSearchResponse,
	GetApiEquipmentData,
	GetApiEquipmentResponse,
	PostApiEquipmentData,
	PostApiEquipmentError,
	PostApiEquipmentResponse,
	PostApiEquipmentDraftData,
	PostApiEquipmentDraftError,
	PostApiEquipmentDraftResponse,
	GetApiEquipmentDraftByEntityIdData,
	PutApiEquipmentDraftByEntityIdData,
	PutApiEquipmentDraftByEntityIdError,
	PutApiEquipmentDraftByEntityIdResponse,
	PostApiEquipmentDraftByEntityIdCommitData,
	PostApiEquipmentDraftByEntityIdCommitError,
	PostApiEquipmentDraftByEntityIdCommitResponse,
	PostApiEquipmentDraftValidateData,
	PostApiEquipmentDraftValidateError,
	PostApiEquipmentDraftValidateResponse,
	PostApiEquipmentDraftByEntityIdValidateData,
	PostApiEquipmentDraftByEntityIdValidateError,
	PostApiEquipmentDraftByEntityIdValidateResponse,
	PostApiFileRequestUploadTokenData,
	PostApiFileRequestUploadTokenError,
	PostApiFileRequestUploadTokenResponse,
	PostApiFileUploadData,
	PostApiFileUploadError,
	PostApiFileUploadResponse,
	GetApiFileStatusByFileIdData,
	GetApiHealthData,
	GetApiInsuranceByIdData,
	PatchApiInsuranceByIdData,
	PatchApiInsuranceByIdError,
	PatchApiInsuranceByIdResponse,
	PutApiInsuranceByIdData,
	PutApiInsuranceByIdError,
	PutApiInsuranceByIdResponse,
	PostApiInsuranceSearchData,
	PostApiInsuranceSearchResponse,
	GetApiInsuranceData,
	GetApiInsuranceResponse,
	PostApiInsuranceData,
	PostApiInsuranceError,
	PostApiInsuranceResponse,
	PostApiInsuranceDraftData,
	PostApiInsuranceDraftError,
	PostApiInsuranceDraftResponse,
	GetApiInsuranceDraftByEntityIdData,
	PutApiInsuranceDraftByEntityIdData,
	PutApiInsuranceDraftByEntityIdError,
	PutApiInsuranceDraftByEntityIdResponse,
	PostApiInsuranceDraftByEntityIdCommitData,
	PostApiInsuranceDraftByEntityIdCommitError,
	PostApiInsuranceDraftByEntityIdCommitResponse,
	PostApiInsuranceDraftValidateData,
	PostApiInsuranceDraftValidateError,
	PostApiInsuranceDraftValidateResponse,
	PostApiInsuranceDraftByEntityIdValidateData,
	PostApiInsuranceDraftByEntityIdValidateError,
	PostApiInsuranceDraftByEntityIdValidateResponse,
	PostApiInsuranceVerificationStartData,
	PostApiInsuranceVerificationStartError,
	GetApiInsuranceVerificationStatusByJobIdData,
	PostApiInsuranceVerificationSearchData,
	PostApiInsuranceVerificationSearchError,
	PostApiInsuranceVerificationSearchResponse,
	PostApiInsuranceVerificationFastauthWebhookData,
	PostApiInsuranceVerificationFastauthWebhookError,
	PostApiLoginData,
	GetApiLoginCheckData,
	PostApiMockDbDeleteData,
	GetApiMockDbResetData,
	GetApiNoteByIdData,
	PatchApiNoteByIdData,
	PatchApiNoteByIdError,
	PatchApiNoteByIdResponse,
	PutApiNoteByIdData,
	PutApiNoteByIdError,
	PutApiNoteByIdResponse,
	PostApiNoteSearchData,
	PostApiNoteSearchResponse,
	GetApiNoteData,
	GetApiNoteResponse,
	PostApiNoteData,
	PostApiNoteError,
	PostApiNoteResponse,
	PostApiNoteDraftData,
	PostApiNoteDraftError,
	PostApiNoteDraftResponse,
	GetApiNoteDraftByEntityIdData,
	PutApiNoteDraftByEntityIdData,
	PutApiNoteDraftByEntityIdError,
	PutApiNoteDraftByEntityIdResponse,
	PostApiNoteDraftByEntityIdCommitData,
	PostApiNoteDraftByEntityIdCommitError,
	PostApiNoteDraftByEntityIdCommitResponse,
	PostApiNoteDraftValidateData,
	PostApiNoteDraftValidateError,
	PostApiNoteDraftValidateResponse,
	PostApiNoteDraftByEntityIdValidateData,
	PostApiNoteDraftByEntityIdValidateError,
	PostApiNoteDraftByEntityIdValidateResponse,
	GetApiOrderByIdData,
	PatchApiOrderByIdData,
	PatchApiOrderByIdError,
	PatchApiOrderByIdResponse,
	PutApiOrderByIdData,
	PutApiOrderByIdError,
	PutApiOrderByIdResponse,
	PostApiOrderSearchData,
	PostApiOrderSearchResponse,
	GetApiOrderData,
	GetApiOrderResponse,
	PostApiOrderData,
	PostApiOrderError,
	PostApiOrderResponse,
	PostApiOrderDraftData,
	PostApiOrderDraftError,
	PostApiOrderDraftResponse,
	GetApiOrderDraftByEntityIdData,
	PutApiOrderDraftByEntityIdData,
	PutApiOrderDraftByEntityIdError,
	PutApiOrderDraftByEntityIdResponse,
	PostApiOrderDraftByEntityIdCommitData,
	PostApiOrderDraftByEntityIdCommitError,
	PostApiOrderDraftByEntityIdCommitResponse,
	PostApiOrderDraftValidateData,
	PostApiOrderDraftValidateError,
	PostApiOrderDraftValidateResponse,
	PostApiOrderDraftByEntityIdValidateData,
	PostApiOrderDraftByEntityIdValidateError,
	PostApiOrderDraftByEntityIdValidateResponse,
	GetApiPatientByIdData,
	PatchApiPatientByIdData,
	PatchApiPatientByIdError,
	PatchApiPatientByIdResponse,
	PutApiPatientByIdData,
	PutApiPatientByIdError,
	PutApiPatientByIdResponse,
	PostApiPatientSearchData,
	PostApiPatientSearchResponse,
	GetApiPatientData,
	GetApiPatientResponse,
	PostApiPatientData,
	PostApiPatientError,
	PostApiPatientResponse,
	PostApiPatientDraftData,
	PostApiPatientDraftError,
	PostApiPatientDraftResponse,
	GetApiPatientDraftByEntityIdData,
	PutApiPatientDraftByEntityIdData,
	PutApiPatientDraftByEntityIdError,
	PutApiPatientDraftByEntityIdResponse,
	PostApiPatientDraftByEntityIdCommitData,
	PostApiPatientDraftByEntityIdCommitError,
	PostApiPatientDraftByEntityIdCommitResponse,
	PostApiPatientDraftValidateData,
	PostApiPatientDraftValidateError,
	PostApiPatientDraftValidateResponse,
	PostApiPatientDraftByEntityIdValidateData,
	PostApiPatientDraftByEntityIdValidateError,
	PostApiPatientDraftByEntityIdValidateResponse,
	GetApiPatientAppointmentByIdData,
	PatchApiPatientAppointmentByIdData,
	PatchApiPatientAppointmentByIdError,
	PatchApiPatientAppointmentByIdResponse,
	PutApiPatientAppointmentByIdData,
	PutApiPatientAppointmentByIdError,
	PutApiPatientAppointmentByIdResponse,
	PostApiPatientAppointmentSearchData,
	PostApiPatientAppointmentSearchResponse,
	GetApiPatientAppointmentData,
	GetApiPatientAppointmentResponse,
	PostApiPatientAppointmentData,
	PostApiPatientAppointmentError,
	PostApiPatientAppointmentResponse,
	PostApiPatientAppointmentDraftData,
	PostApiPatientAppointmentDraftError,
	PostApiPatientAppointmentDraftResponse,
	GetApiPatientAppointmentDraftByEntityIdData,
	PutApiPatientAppointmentDraftByEntityIdData,
	PutApiPatientAppointmentDraftByEntityIdError,
	PutApiPatientAppointmentDraftByEntityIdResponse,
	PostApiPatientAppointmentDraftByEntityIdCommitData,
	PostApiPatientAppointmentDraftByEntityIdCommitError,
	PostApiPatientAppointmentDraftByEntityIdCommitResponse,
	PostApiPatientAppointmentDraftValidateData,
	PostApiPatientAppointmentDraftValidateError,
	PostApiPatientAppointmentDraftValidateResponse,
	PostApiPatientAppointmentDraftByEntityIdValidateData,
	PostApiPatientAppointmentDraftByEntityIdValidateError,
	PostApiPatientAppointmentDraftByEntityIdValidateResponse,
	GetApiPatientAppointmentConfirmationByIdData,
	PatchApiPatientAppointmentConfirmationByIdData,
	PatchApiPatientAppointmentConfirmationByIdError,
	PatchApiPatientAppointmentConfirmationByIdResponse,
	PutApiPatientAppointmentConfirmationByIdData,
	PutApiPatientAppointmentConfirmationByIdError,
	PutApiPatientAppointmentConfirmationByIdResponse,
	PostApiPatientAppointmentConfirmationSearchData,
	PostApiPatientAppointmentConfirmationSearchResponse,
	GetApiPatientAppointmentConfirmationData,
	GetApiPatientAppointmentConfirmationResponse,
	PostApiPatientAppointmentConfirmationData,
	PostApiPatientAppointmentConfirmationError,
	PostApiPatientAppointmentConfirmationResponse,
	PostApiPatientAppointmentConfirmationDraftData,
	PostApiPatientAppointmentConfirmationDraftError,
	PostApiPatientAppointmentConfirmationDraftResponse,
	GetApiPatientAppointmentConfirmationDraftByEntityIdData,
	PutApiPatientAppointmentConfirmationDraftByEntityIdData,
	PutApiPatientAppointmentConfirmationDraftByEntityIdError,
	PutApiPatientAppointmentConfirmationDraftByEntityIdResponse,
	PostApiPatientAppointmentConfirmationDraftByEntityIdCommitData,
	PostApiPatientAppointmentConfirmationDraftByEntityIdCommitError,
	PostApiPatientAppointmentConfirmationDraftByEntityIdCommitResponse,
	PostApiPatientAppointmentConfirmationDraftValidateData,
	PostApiPatientAppointmentConfirmationDraftValidateError,
	PostApiPatientAppointmentConfirmationDraftValidateResponse,
	PostApiPatientAppointmentConfirmationDraftByEntityIdValidateData,
	PostApiPatientAppointmentConfirmationDraftByEntityIdValidateError,
	PostApiPatientAppointmentConfirmationDraftByEntityIdValidateResponse,
	GetApiPhysicianByIdData,
	PatchApiPhysicianByIdData,
	PatchApiPhysicianByIdError,
	PatchApiPhysicianByIdResponse,
	PutApiPhysicianByIdData,
	PutApiPhysicianByIdError,
	PutApiPhysicianByIdResponse,
	PostApiPhysicianSearchData,
	PostApiPhysicianSearchResponse,
	GetApiPhysicianData,
	GetApiPhysicianResponse,
	PostApiPhysicianData,
	PostApiPhysicianError,
	PostApiPhysicianResponse,
	PostApiPhysicianDraftData,
	PostApiPhysicianDraftError,
	PostApiPhysicianDraftResponse,
	GetApiPhysicianDraftByEntityIdData,
	PutApiPhysicianDraftByEntityIdData,
	PutApiPhysicianDraftByEntityIdError,
	PutApiPhysicianDraftByEntityIdResponse,
	PostApiPhysicianDraftByEntityIdCommitData,
	PostApiPhysicianDraftByEntityIdCommitError,
	PostApiPhysicianDraftByEntityIdCommitResponse,
	PostApiPhysicianDraftValidateData,
	PostApiPhysicianDraftValidateError,
	PostApiPhysicianDraftValidateResponse,
	PostApiPhysicianDraftByEntityIdValidateData,
	PostApiPhysicianDraftByEntityIdValidateError,
	PostApiPhysicianDraftByEntityIdValidateResponse,
	GetApiProviderByIdData,
	PatchApiProviderByIdData,
	PatchApiProviderByIdError,
	PatchApiProviderByIdResponse,
	PutApiProviderByIdData,
	PutApiProviderByIdError,
	PutApiProviderByIdResponse,
	PostApiProviderSearchData,
	PostApiProviderSearchResponse,
	GetApiProviderData,
	GetApiProviderResponse,
	PostApiProviderData,
	PostApiProviderError,
	PostApiProviderResponse,
	PostApiProviderDraftData,
	PostApiProviderDraftError,
	PostApiProviderDraftResponse,
	GetApiProviderDraftByEntityIdData,
	PutApiProviderDraftByEntityIdData,
	PutApiProviderDraftByEntityIdError,
	PutApiProviderDraftByEntityIdResponse,
	PostApiProviderDraftByEntityIdCommitData,
	PostApiProviderDraftByEntityIdCommitError,
	PostApiProviderDraftByEntityIdCommitResponse,
	PostApiProviderDraftValidateData,
	PostApiProviderDraftValidateError,
	PostApiProviderDraftValidateResponse,
	PostApiProviderDraftByEntityIdValidateData,
	PostApiProviderDraftByEntityIdValidateError,
	PostApiProviderDraftByEntityIdValidateResponse,
	GetPyapiZipData,
	GetTsapiZipData,
	GetApiRoomByIdData,
	PatchApiRoomByIdData,
	PatchApiRoomByIdError,
	PatchApiRoomByIdResponse,
	PutApiRoomByIdData,
	PutApiRoomByIdError,
	PutApiRoomByIdResponse,
	PostApiRoomSearchData,
	PostApiRoomSearchResponse,
	GetApiRoomData,
	GetApiRoomResponse,
	PostApiRoomData,
	PostApiRoomError,
	PostApiRoomResponse,
	PostApiRoomDraftData,
	PostApiRoomDraftError,
	PostApiRoomDraftResponse,
	GetApiRoomDraftByEntityIdData,
	PutApiRoomDraftByEntityIdData,
	PutApiRoomDraftByEntityIdError,
	PutApiRoomDraftByEntityIdResponse,
	PostApiRoomDraftByEntityIdCommitData,
	PostApiRoomDraftByEntityIdCommitError,
	PostApiRoomDraftByEntityIdCommitResponse,
	PostApiRoomDraftValidateData,
	PostApiRoomDraftValidateError,
	PostApiRoomDraftValidateResponse,
	PostApiRoomDraftByEntityIdValidateData,
	PostApiRoomDraftByEntityIdValidateError,
	PostApiRoomDraftByEntityIdValidateResponse,
	PostApiSchedulingFindSlotsData,
	PostApiSchedulingFindSlotsResponse,
	GetApiSchedulingConstraintByIdData,
	PatchApiSchedulingConstraintByIdData,
	PatchApiSchedulingConstraintByIdError,
	PatchApiSchedulingConstraintByIdResponse,
	PutApiSchedulingConstraintByIdData,
	PutApiSchedulingConstraintByIdError,
	PutApiSchedulingConstraintByIdResponse,
	PostApiSchedulingConstraintSearchData,
	PostApiSchedulingConstraintSearchResponse,
	GetApiSchedulingConstraintData,
	GetApiSchedulingConstraintResponse,
	PostApiSchedulingConstraintData,
	PostApiSchedulingConstraintError,
	PostApiSchedulingConstraintResponse,
	PostApiSchedulingConstraintDraftData,
	PostApiSchedulingConstraintDraftError,
	PostApiSchedulingConstraintDraftResponse,
	GetApiSchedulingConstraintDraftByEntityIdData,
	PutApiSchedulingConstraintDraftByEntityIdData,
	PutApiSchedulingConstraintDraftByEntityIdError,
	PutApiSchedulingConstraintDraftByEntityIdResponse,
	PostApiSchedulingConstraintDraftByEntityIdCommitData,
	PostApiSchedulingConstraintDraftByEntityIdCommitError,
	PostApiSchedulingConstraintDraftByEntityIdCommitResponse,
	PostApiSchedulingConstraintDraftValidateData,
	PostApiSchedulingConstraintDraftValidateError,
	PostApiSchedulingConstraintDraftValidateResponse,
	PostApiSchedulingConstraintDraftByEntityIdValidateData,
	PostApiSchedulingConstraintDraftByEntityIdValidateError,
	PostApiSchedulingConstraintDraftByEntityIdValidateResponse,
	GetApiStudyByIdData,
	PatchApiStudyByIdData,
	PatchApiStudyByIdError,
	PatchApiStudyByIdResponse,
	PutApiStudyByIdData,
	PutApiStudyByIdError,
	PutApiStudyByIdResponse,
	PostApiStudySearchData,
	PostApiStudySearchResponse,
	GetApiStudyData,
	GetApiStudyResponse,
	PostApiStudyData,
	PostApiStudyError,
	PostApiStudyResponse,
	PostApiStudyDraftData,
	PostApiStudyDraftError,
	PostApiStudyDraftResponse,
	GetApiStudyDraftByEntityIdData,
	PutApiStudyDraftByEntityIdData,
	PutApiStudyDraftByEntityIdError,
	PutApiStudyDraftByEntityIdResponse,
	PostApiStudyDraftByEntityIdCommitData,
	PostApiStudyDraftByEntityIdCommitError,
	PostApiStudyDraftByEntityIdCommitResponse,
	PostApiStudyDraftValidateData,
	PostApiStudyDraftValidateError,
	PostApiStudyDraftValidateResponse,
	PostApiStudyDraftByEntityIdValidateData,
	PostApiStudyDraftByEntityIdValidateError,
	PostApiStudyDraftByEntityIdValidateResponse,
	GetApiTechnicianByIdData,
	PatchApiTechnicianByIdData,
	PatchApiTechnicianByIdError,
	PatchApiTechnicianByIdResponse,
	PutApiTechnicianByIdData,
	PutApiTechnicianByIdError,
	PutApiTechnicianByIdResponse,
	PostApiTechnicianSearchData,
	PostApiTechnicianSearchResponse,
	GetApiTechnicianData,
	GetApiTechnicianResponse,
	PostApiTechnicianData,
	PostApiTechnicianError,
	PostApiTechnicianResponse,
	PostApiTechnicianDraftData,
	PostApiTechnicianDraftError,
	PostApiTechnicianDraftResponse,
	GetApiTechnicianDraftByEntityIdData,
	PutApiTechnicianDraftByEntityIdData,
	PutApiTechnicianDraftByEntityIdError,
	PutApiTechnicianDraftByEntityIdResponse,
	PostApiTechnicianDraftByEntityIdCommitData,
	PostApiTechnicianDraftByEntityIdCommitError,
	PostApiTechnicianDraftByEntityIdCommitResponse,
	PostApiTechnicianDraftValidateData,
	PostApiTechnicianDraftValidateError,
	PostApiTechnicianDraftValidateResponse,
	PostApiTechnicianDraftByEntityIdValidateData,
	PostApiTechnicianDraftByEntityIdValidateError,
	PostApiTechnicianDraftByEntityIdValidateResponse,
	GetApiTechnicianAppointmentByIdData,
	PatchApiTechnicianAppointmentByIdData,
	PatchApiTechnicianAppointmentByIdError,
	PatchApiTechnicianAppointmentByIdResponse,
	PutApiTechnicianAppointmentByIdData,
	PutApiTechnicianAppointmentByIdError,
	PutApiTechnicianAppointmentByIdResponse,
	PostApiTechnicianAppointmentSearchData,
	PostApiTechnicianAppointmentSearchResponse,
	GetApiTechnicianAppointmentData,
	GetApiTechnicianAppointmentResponse,
	PostApiTechnicianAppointmentData,
	PostApiTechnicianAppointmentError,
	PostApiTechnicianAppointmentResponse,
	PostApiTechnicianAppointmentDraftData,
	PostApiTechnicianAppointmentDraftError,
	PostApiTechnicianAppointmentDraftResponse,
	GetApiTechnicianAppointmentDraftByEntityIdData,
	PutApiTechnicianAppointmentDraftByEntityIdData,
	PutApiTechnicianAppointmentDraftByEntityIdError,
	PutApiTechnicianAppointmentDraftByEntityIdResponse,
	PostApiTechnicianAppointmentDraftByEntityIdCommitData,
	PostApiTechnicianAppointmentDraftByEntityIdCommitError,
	PostApiTechnicianAppointmentDraftByEntityIdCommitResponse,
	PostApiTechnicianAppointmentDraftValidateData,
	PostApiTechnicianAppointmentDraftValidateError,
	PostApiTechnicianAppointmentDraftValidateResponse,
	PostApiTechnicianAppointmentDraftByEntityIdValidateData,
	PostApiTechnicianAppointmentDraftByEntityIdValidateError,
	PostApiTechnicianAppointmentDraftByEntityIdValidateResponse,
	GetApiWorkflowFlowsData,
	GetApiWorkflowMyTasksData,
	GetApiWorkflowProfileData,
} from '../types.gen';
import type { AxiosError } from 'axios';
import { client as _heyApiClient } from '../client.gen';

export type QueryKey<TOptions extends Options> = [
	Pick<TOptions, 'baseURL' | 'body' | 'headers' | 'path' | 'query'> & {
		_id: string;
		_infinite?: boolean;
	},
];

const createQueryKey = <TOptions extends Options>(
	id: string,
	options?: TOptions,
	infinite?: boolean
): [QueryKey<TOptions>[0]] => {
	const params: QueryKey<TOptions>[0] = {
		_id: id,
		baseURL: (options?.client ?? _heyApiClient).getConfig().baseURL,
	} as QueryKey<TOptions>[0];
	if (infinite) {
		params._infinite = infinite;
	}
	if (options?.body) {
		params.body = options.body;
	}
	if (options?.headers) {
		params.headers = options.headers;
	}
	if (options?.path) {
		params.path = options.path;
	}
	if (options?.query) {
		params.query = options.query;
	}
	return [params];
};

export const getApiAddNewOrderValidationRulesQueryKey = (
	options?: Options<GetApiAddNewOrderValidationRulesData>
) => createQueryKey('getApiAddNewOrderValidationRules', options);

export const getApiAddNewOrderValidationRulesOptions = (
	options?: Options<GetApiAddNewOrderValidationRulesData>
) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await getApiAddNewOrderValidationRules({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: getApiAddNewOrderValidationRulesQueryKey(options),
	});
};

export const getApiAddNewOrderStateByIdQueryKey = (
	options: Options<GetApiAddNewOrderStateByIdData>
) => createQueryKey('getApiAddNewOrderStateById', options);

export const getApiAddNewOrderStateByIdOptions = (
	options: Options<GetApiAddNewOrderStateByIdData>
) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await getApiAddNewOrderStateById({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: getApiAddNewOrderStateByIdQueryKey(options),
	});
};

export const postApiAddNewOrderStartQueryKey = (options?: Options<PostApiAddNewOrderStartData>) =>
	createQueryKey('postApiAddNewOrderStart', options);

export const postApiAddNewOrderStartOptions = (options?: Options<PostApiAddNewOrderStartData>) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await postApiAddNewOrderStart({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: postApiAddNewOrderStartQueryKey(options),
	});
};

export const postApiAddNewOrderStartMutation = (
	options?: Partial<Options<PostApiAddNewOrderStartData>>
) => {
	const mutationOptions: UseMutationOptions<
		PostApiAddNewOrderStartResponse,
		AxiosError<DefaultError>,
		Options<PostApiAddNewOrderStartData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await postApiAddNewOrderStart({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const postApiAddNewOrderRewindQueryKey = (options?: Options<PostApiAddNewOrderRewindData>) =>
	createQueryKey('postApiAddNewOrderRewind', options);

export const postApiAddNewOrderRewindOptions = (
	options?: Options<PostApiAddNewOrderRewindData>
) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await postApiAddNewOrderRewind({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: postApiAddNewOrderRewindQueryKey(options),
	});
};

export const postApiAddNewOrderRewindMutation = (
	options?: Partial<Options<PostApiAddNewOrderRewindData>>
) => {
	const mutationOptions: UseMutationOptions<
		unknown,
		AxiosError<DefaultError>,
		Options<PostApiAddNewOrderRewindData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await postApiAddNewOrderRewind({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const postApiAddNewOrderListQueryKey = (options?: Options<PostApiAddNewOrderListData>) =>
	createQueryKey('postApiAddNewOrderList', options);

export const postApiAddNewOrderListOptions = (options?: Options<PostApiAddNewOrderListData>) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await postApiAddNewOrderList({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: postApiAddNewOrderListQueryKey(options),
	});
};

export const postApiAddNewOrderListMutation = (
	options?: Partial<Options<PostApiAddNewOrderListData>>
) => {
	const mutationOptions: UseMutationOptions<
		PostApiAddNewOrderListResponse,
		AxiosError<DefaultError>,
		Options<PostApiAddNewOrderListData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await postApiAddNewOrderList({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const postApiAddNewOrderAddStudyQueryKey = (
	options?: Options<PostApiAddNewOrderAddStudyData>
) => createQueryKey('postApiAddNewOrderAddStudy', options);

export const postApiAddNewOrderAddStudyOptions = (
	options?: Options<PostApiAddNewOrderAddStudyData>
) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await postApiAddNewOrderAddStudy({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: postApiAddNewOrderAddStudyQueryKey(options),
	});
};

export const postApiAddNewOrderAddStudyMutation = (
	options?: Partial<Options<PostApiAddNewOrderAddStudyData>>
) => {
	const mutationOptions: UseMutationOptions<
		PostApiAddNewOrderAddStudyResponse,
		AxiosError<DefaultError>,
		Options<PostApiAddNewOrderAddStudyData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await postApiAddNewOrderAddStudy({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const postApiAddNewOrderAddStudyDraftQueryKey = (
	options?: Options<PostApiAddNewOrderAddStudyDraftData>
) => createQueryKey('postApiAddNewOrderAddStudyDraft', options);

export const postApiAddNewOrderAddStudyDraftOptions = (
	options?: Options<PostApiAddNewOrderAddStudyDraftData>
) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await postApiAddNewOrderAddStudyDraft({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: postApiAddNewOrderAddStudyDraftQueryKey(options),
	});
};

export const postApiAddNewOrderAddStudyDraftMutation = (
	options?: Partial<Options<PostApiAddNewOrderAddStudyDraftData>>
) => {
	const mutationOptions: UseMutationOptions<
		PostApiAddNewOrderAddStudyDraftResponse,
		AxiosError<DefaultError>,
		Options<PostApiAddNewOrderAddStudyDraftData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await postApiAddNewOrderAddStudyDraft({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const postApiAddNewOrderAddStudyValidateQueryKey = (
	options?: Options<PostApiAddNewOrderAddStudyValidateData>
) => createQueryKey('postApiAddNewOrderAddStudyValidate', options);

export const postApiAddNewOrderAddStudyValidateOptions = (
	options?: Options<PostApiAddNewOrderAddStudyValidateData>
) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await postApiAddNewOrderAddStudyValidate({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: postApiAddNewOrderAddStudyValidateQueryKey(options),
	});
};

export const postApiAddNewOrderAddStudyValidateMutation = (
	options?: Partial<Options<PostApiAddNewOrderAddStudyValidateData>>
) => {
	const mutationOptions: UseMutationOptions<
		PostApiAddNewOrderAddStudyValidateResponse,
		AxiosError<DefaultError>,
		Options<PostApiAddNewOrderAddStudyValidateData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await postApiAddNewOrderAddStudyValidate({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const postApiAddNewOrderAddCareLocationQueryKey = (
	options?: Options<PostApiAddNewOrderAddCareLocationData>
) => createQueryKey('postApiAddNewOrderAddCareLocation', options);

export const postApiAddNewOrderAddCareLocationOptions = (
	options?: Options<PostApiAddNewOrderAddCareLocationData>
) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await postApiAddNewOrderAddCareLocation({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: postApiAddNewOrderAddCareLocationQueryKey(options),
	});
};

export const postApiAddNewOrderAddCareLocationMutation = (
	options?: Partial<Options<PostApiAddNewOrderAddCareLocationData>>
) => {
	const mutationOptions: UseMutationOptions<
		PostApiAddNewOrderAddCareLocationResponse,
		AxiosError<DefaultError>,
		Options<PostApiAddNewOrderAddCareLocationData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await postApiAddNewOrderAddCareLocation({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const postApiAddNewOrderAddCareLocationDraftQueryKey = (
	options?: Options<PostApiAddNewOrderAddCareLocationDraftData>
) => createQueryKey('postApiAddNewOrderAddCareLocationDraft', options);

export const postApiAddNewOrderAddCareLocationDraftOptions = (
	options?: Options<PostApiAddNewOrderAddCareLocationDraftData>
) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await postApiAddNewOrderAddCareLocationDraft({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: postApiAddNewOrderAddCareLocationDraftQueryKey(options),
	});
};

export const postApiAddNewOrderAddCareLocationDraftMutation = (
	options?: Partial<Options<PostApiAddNewOrderAddCareLocationDraftData>>
) => {
	const mutationOptions: UseMutationOptions<
		PostApiAddNewOrderAddCareLocationDraftResponse,
		AxiosError<DefaultError>,
		Options<PostApiAddNewOrderAddCareLocationDraftData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await postApiAddNewOrderAddCareLocationDraft({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const postApiAddNewOrderAddCareLocationValidateQueryKey = (
	options?: Options<PostApiAddNewOrderAddCareLocationValidateData>
) => createQueryKey('postApiAddNewOrderAddCareLocationValidate', options);

export const postApiAddNewOrderAddCareLocationValidateOptions = (
	options?: Options<PostApiAddNewOrderAddCareLocationValidateData>
) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await postApiAddNewOrderAddCareLocationValidate({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: postApiAddNewOrderAddCareLocationValidateQueryKey(options),
	});
};

export const postApiAddNewOrderAddCareLocationValidateMutation = (
	options?: Partial<Options<PostApiAddNewOrderAddCareLocationValidateData>>
) => {
	const mutationOptions: UseMutationOptions<
		PostApiAddNewOrderAddCareLocationValidateResponse,
		AxiosError<DefaultError>,
		Options<PostApiAddNewOrderAddCareLocationValidateData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await postApiAddNewOrderAddCareLocationValidate({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const postApiAddNewOrderAddPhysiciansQueryKey = (
	options?: Options<PostApiAddNewOrderAddPhysiciansData>
) => createQueryKey('postApiAddNewOrderAddPhysicians', options);

export const postApiAddNewOrderAddPhysiciansOptions = (
	options?: Options<PostApiAddNewOrderAddPhysiciansData>
) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await postApiAddNewOrderAddPhysicians({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: postApiAddNewOrderAddPhysiciansQueryKey(options),
	});
};

export const postApiAddNewOrderAddPhysiciansMutation = (
	options?: Partial<Options<PostApiAddNewOrderAddPhysiciansData>>
) => {
	const mutationOptions: UseMutationOptions<
		PostApiAddNewOrderAddPhysiciansResponse,
		AxiosError<DefaultError>,
		Options<PostApiAddNewOrderAddPhysiciansData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await postApiAddNewOrderAddPhysicians({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const postApiAddNewOrderAddPhysiciansDraftQueryKey = (
	options?: Options<PostApiAddNewOrderAddPhysiciansDraftData>
) => createQueryKey('postApiAddNewOrderAddPhysiciansDraft', options);

export const postApiAddNewOrderAddPhysiciansDraftOptions = (
	options?: Options<PostApiAddNewOrderAddPhysiciansDraftData>
) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await postApiAddNewOrderAddPhysiciansDraft({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: postApiAddNewOrderAddPhysiciansDraftQueryKey(options),
	});
};

export const postApiAddNewOrderAddPhysiciansDraftMutation = (
	options?: Partial<Options<PostApiAddNewOrderAddPhysiciansDraftData>>
) => {
	const mutationOptions: UseMutationOptions<
		PostApiAddNewOrderAddPhysiciansDraftResponse,
		AxiosError<DefaultError>,
		Options<PostApiAddNewOrderAddPhysiciansDraftData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await postApiAddNewOrderAddPhysiciansDraft({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const postApiAddNewOrderAddPhysiciansValidateQueryKey = (
	options?: Options<PostApiAddNewOrderAddPhysiciansValidateData>
) => createQueryKey('postApiAddNewOrderAddPhysiciansValidate', options);

export const postApiAddNewOrderAddPhysiciansValidateOptions = (
	options?: Options<PostApiAddNewOrderAddPhysiciansValidateData>
) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await postApiAddNewOrderAddPhysiciansValidate({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: postApiAddNewOrderAddPhysiciansValidateQueryKey(options),
	});
};

export const postApiAddNewOrderAddPhysiciansValidateMutation = (
	options?: Partial<Options<PostApiAddNewOrderAddPhysiciansValidateData>>
) => {
	const mutationOptions: UseMutationOptions<
		PostApiAddNewOrderAddPhysiciansValidateResponse,
		AxiosError<DefaultError>,
		Options<PostApiAddNewOrderAddPhysiciansValidateData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await postApiAddNewOrderAddPhysiciansValidate({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const postApiAddNewOrderReviewAndSubmitOrderQueryKey = (
	options?: Options<PostApiAddNewOrderReviewAndSubmitOrderData>
) => createQueryKey('postApiAddNewOrderReviewAndSubmitOrder', options);

export const postApiAddNewOrderReviewAndSubmitOrderOptions = (
	options?: Options<PostApiAddNewOrderReviewAndSubmitOrderData>
) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await postApiAddNewOrderReviewAndSubmitOrder({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: postApiAddNewOrderReviewAndSubmitOrderQueryKey(options),
	});
};

export const postApiAddNewOrderReviewAndSubmitOrderMutation = (
	options?: Partial<Options<PostApiAddNewOrderReviewAndSubmitOrderData>>
) => {
	const mutationOptions: UseMutationOptions<
		PostApiAddNewOrderReviewAndSubmitOrderResponse,
		AxiosError<DefaultError>,
		Options<PostApiAddNewOrderReviewAndSubmitOrderData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await postApiAddNewOrderReviewAndSubmitOrder({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const postApiAddNewOrderReviewAndSubmitOrderDraftQueryKey = (
	options?: Options<PostApiAddNewOrderReviewAndSubmitOrderDraftData>
) => createQueryKey('postApiAddNewOrderReviewAndSubmitOrderDraft', options);

export const postApiAddNewOrderReviewAndSubmitOrderDraftOptions = (
	options?: Options<PostApiAddNewOrderReviewAndSubmitOrderDraftData>
) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await postApiAddNewOrderReviewAndSubmitOrderDraft({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: postApiAddNewOrderReviewAndSubmitOrderDraftQueryKey(options),
	});
};

export const postApiAddNewOrderReviewAndSubmitOrderDraftMutation = (
	options?: Partial<Options<PostApiAddNewOrderReviewAndSubmitOrderDraftData>>
) => {
	const mutationOptions: UseMutationOptions<
		PostApiAddNewOrderReviewAndSubmitOrderDraftResponse,
		AxiosError<DefaultError>,
		Options<PostApiAddNewOrderReviewAndSubmitOrderDraftData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await postApiAddNewOrderReviewAndSubmitOrderDraft({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const postApiAddNewOrderReviewAndSubmitOrderValidateQueryKey = (
	options?: Options<PostApiAddNewOrderReviewAndSubmitOrderValidateData>
) => createQueryKey('postApiAddNewOrderReviewAndSubmitOrderValidate', options);

export const postApiAddNewOrderReviewAndSubmitOrderValidateOptions = (
	options?: Options<PostApiAddNewOrderReviewAndSubmitOrderValidateData>
) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await postApiAddNewOrderReviewAndSubmitOrderValidate({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: postApiAddNewOrderReviewAndSubmitOrderValidateQueryKey(options),
	});
};

export const postApiAddNewOrderReviewAndSubmitOrderValidateMutation = (
	options?: Partial<Options<PostApiAddNewOrderReviewAndSubmitOrderValidateData>>
) => {
	const mutationOptions: UseMutationOptions<
		PostApiAddNewOrderReviewAndSubmitOrderValidateResponse,
		AxiosError<DefaultError>,
		Options<PostApiAddNewOrderReviewAndSubmitOrderValidateData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await postApiAddNewOrderReviewAndSubmitOrderValidate({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const getApiAddNewPatientValidationRulesQueryKey = (
	options?: Options<GetApiAddNewPatientValidationRulesData>
) => createQueryKey('getApiAddNewPatientValidationRules', options);

export const getApiAddNewPatientValidationRulesOptions = (
	options?: Options<GetApiAddNewPatientValidationRulesData>
) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await getApiAddNewPatientValidationRules({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: getApiAddNewPatientValidationRulesQueryKey(options),
	});
};

export const getApiAddNewPatientStateByIdQueryKey = (
	options: Options<GetApiAddNewPatientStateByIdData>
) => createQueryKey('getApiAddNewPatientStateById', options);

export const getApiAddNewPatientStateByIdOptions = (
	options: Options<GetApiAddNewPatientStateByIdData>
) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await getApiAddNewPatientStateById({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: getApiAddNewPatientStateByIdQueryKey(options),
	});
};

export const postApiAddNewPatientStartQueryKey = (
	options?: Options<PostApiAddNewPatientStartData>
) => createQueryKey('postApiAddNewPatientStart', options);

export const postApiAddNewPatientStartOptions = (
	options?: Options<PostApiAddNewPatientStartData>
) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await postApiAddNewPatientStart({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: postApiAddNewPatientStartQueryKey(options),
	});
};

export const postApiAddNewPatientStartMutation = (
	options?: Partial<Options<PostApiAddNewPatientStartData>>
) => {
	const mutationOptions: UseMutationOptions<
		PostApiAddNewPatientStartResponse,
		AxiosError<DefaultError>,
		Options<PostApiAddNewPatientStartData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await postApiAddNewPatientStart({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const postApiAddNewPatientRewindQueryKey = (
	options?: Options<PostApiAddNewPatientRewindData>
) => createQueryKey('postApiAddNewPatientRewind', options);

export const postApiAddNewPatientRewindOptions = (
	options?: Options<PostApiAddNewPatientRewindData>
) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await postApiAddNewPatientRewind({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: postApiAddNewPatientRewindQueryKey(options),
	});
};

export const postApiAddNewPatientRewindMutation = (
	options?: Partial<Options<PostApiAddNewPatientRewindData>>
) => {
	const mutationOptions: UseMutationOptions<
		unknown,
		AxiosError<DefaultError>,
		Options<PostApiAddNewPatientRewindData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await postApiAddNewPatientRewind({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const postApiAddNewPatientListQueryKey = (options?: Options<PostApiAddNewPatientListData>) =>
	createQueryKey('postApiAddNewPatientList', options);

export const postApiAddNewPatientListOptions = (
	options?: Options<PostApiAddNewPatientListData>
) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await postApiAddNewPatientList({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: postApiAddNewPatientListQueryKey(options),
	});
};

export const postApiAddNewPatientListMutation = (
	options?: Partial<Options<PostApiAddNewPatientListData>>
) => {
	const mutationOptions: UseMutationOptions<
		PostApiAddNewPatientListResponse,
		AxiosError<DefaultError>,
		Options<PostApiAddNewPatientListData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await postApiAddNewPatientList({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const postApiAddNewPatientAddBasicInformationQueryKey = (
	options?: Options<PostApiAddNewPatientAddBasicInformationData>
) => createQueryKey('postApiAddNewPatientAddBasicInformation', options);

export const postApiAddNewPatientAddBasicInformationOptions = (
	options?: Options<PostApiAddNewPatientAddBasicInformationData>
) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await postApiAddNewPatientAddBasicInformation({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: postApiAddNewPatientAddBasicInformationQueryKey(options),
	});
};

export const postApiAddNewPatientAddBasicInformationMutation = (
	options?: Partial<Options<PostApiAddNewPatientAddBasicInformationData>>
) => {
	const mutationOptions: UseMutationOptions<
		PostApiAddNewPatientAddBasicInformationResponse,
		AxiosError<DefaultError>,
		Options<PostApiAddNewPatientAddBasicInformationData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await postApiAddNewPatientAddBasicInformation({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const postApiAddNewPatientAddBasicInformationDraftQueryKey = (
	options?: Options<PostApiAddNewPatientAddBasicInformationDraftData>
) => createQueryKey('postApiAddNewPatientAddBasicInformationDraft', options);

export const postApiAddNewPatientAddBasicInformationDraftOptions = (
	options?: Options<PostApiAddNewPatientAddBasicInformationDraftData>
) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await postApiAddNewPatientAddBasicInformationDraft({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: postApiAddNewPatientAddBasicInformationDraftQueryKey(options),
	});
};

export const postApiAddNewPatientAddBasicInformationDraftMutation = (
	options?: Partial<Options<PostApiAddNewPatientAddBasicInformationDraftData>>
) => {
	const mutationOptions: UseMutationOptions<
		PostApiAddNewPatientAddBasicInformationDraftResponse,
		AxiosError<DefaultError>,
		Options<PostApiAddNewPatientAddBasicInformationDraftData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await postApiAddNewPatientAddBasicInformationDraft({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const postApiAddNewPatientAddBasicInformationValidateQueryKey = (
	options?: Options<PostApiAddNewPatientAddBasicInformationValidateData>
) => createQueryKey('postApiAddNewPatientAddBasicInformationValidate', options);

export const postApiAddNewPatientAddBasicInformationValidateOptions = (
	options?: Options<PostApiAddNewPatientAddBasicInformationValidateData>
) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await postApiAddNewPatientAddBasicInformationValidate({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: postApiAddNewPatientAddBasicInformationValidateQueryKey(options),
	});
};

export const postApiAddNewPatientAddBasicInformationValidateMutation = (
	options?: Partial<Options<PostApiAddNewPatientAddBasicInformationValidateData>>
) => {
	const mutationOptions: UseMutationOptions<
		PostApiAddNewPatientAddBasicInformationValidateResponse,
		AxiosError<DefaultError>,
		Options<PostApiAddNewPatientAddBasicInformationValidateData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await postApiAddNewPatientAddBasicInformationValidate({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const postApiAddNewPatientAddContactsQueryKey = (
	options?: Options<PostApiAddNewPatientAddContactsData>
) => createQueryKey('postApiAddNewPatientAddContacts', options);

export const postApiAddNewPatientAddContactsOptions = (
	options?: Options<PostApiAddNewPatientAddContactsData>
) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await postApiAddNewPatientAddContacts({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: postApiAddNewPatientAddContactsQueryKey(options),
	});
};

export const postApiAddNewPatientAddContactsMutation = (
	options?: Partial<Options<PostApiAddNewPatientAddContactsData>>
) => {
	const mutationOptions: UseMutationOptions<
		PostApiAddNewPatientAddContactsResponse,
		AxiosError<DefaultError>,
		Options<PostApiAddNewPatientAddContactsData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await postApiAddNewPatientAddContacts({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const postApiAddNewPatientAddContactsDraftQueryKey = (
	options?: Options<PostApiAddNewPatientAddContactsDraftData>
) => createQueryKey('postApiAddNewPatientAddContactsDraft', options);

export const postApiAddNewPatientAddContactsDraftOptions = (
	options?: Options<PostApiAddNewPatientAddContactsDraftData>
) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await postApiAddNewPatientAddContactsDraft({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: postApiAddNewPatientAddContactsDraftQueryKey(options),
	});
};

export const postApiAddNewPatientAddContactsDraftMutation = (
	options?: Partial<Options<PostApiAddNewPatientAddContactsDraftData>>
) => {
	const mutationOptions: UseMutationOptions<
		PostApiAddNewPatientAddContactsDraftResponse,
		AxiosError<DefaultError>,
		Options<PostApiAddNewPatientAddContactsDraftData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await postApiAddNewPatientAddContactsDraft({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const postApiAddNewPatientAddContactsValidateQueryKey = (
	options?: Options<PostApiAddNewPatientAddContactsValidateData>
) => createQueryKey('postApiAddNewPatientAddContactsValidate', options);

export const postApiAddNewPatientAddContactsValidateOptions = (
	options?: Options<PostApiAddNewPatientAddContactsValidateData>
) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await postApiAddNewPatientAddContactsValidate({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: postApiAddNewPatientAddContactsValidateQueryKey(options),
	});
};

export const postApiAddNewPatientAddContactsValidateMutation = (
	options?: Partial<Options<PostApiAddNewPatientAddContactsValidateData>>
) => {
	const mutationOptions: UseMutationOptions<
		PostApiAddNewPatientAddContactsValidateResponse,
		AxiosError<DefaultError>,
		Options<PostApiAddNewPatientAddContactsValidateData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await postApiAddNewPatientAddContactsValidate({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const postApiAddNewPatientAddAddressesQueryKey = (
	options?: Options<PostApiAddNewPatientAddAddressesData>
) => createQueryKey('postApiAddNewPatientAddAddresses', options);

export const postApiAddNewPatientAddAddressesOptions = (
	options?: Options<PostApiAddNewPatientAddAddressesData>
) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await postApiAddNewPatientAddAddresses({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: postApiAddNewPatientAddAddressesQueryKey(options),
	});
};

export const postApiAddNewPatientAddAddressesMutation = (
	options?: Partial<Options<PostApiAddNewPatientAddAddressesData>>
) => {
	const mutationOptions: UseMutationOptions<
		PostApiAddNewPatientAddAddressesResponse,
		AxiosError<DefaultError>,
		Options<PostApiAddNewPatientAddAddressesData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await postApiAddNewPatientAddAddresses({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const postApiAddNewPatientAddAddressesDraftQueryKey = (
	options?: Options<PostApiAddNewPatientAddAddressesDraftData>
) => createQueryKey('postApiAddNewPatientAddAddressesDraft', options);

export const postApiAddNewPatientAddAddressesDraftOptions = (
	options?: Options<PostApiAddNewPatientAddAddressesDraftData>
) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await postApiAddNewPatientAddAddressesDraft({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: postApiAddNewPatientAddAddressesDraftQueryKey(options),
	});
};

export const postApiAddNewPatientAddAddressesDraftMutation = (
	options?: Partial<Options<PostApiAddNewPatientAddAddressesDraftData>>
) => {
	const mutationOptions: UseMutationOptions<
		PostApiAddNewPatientAddAddressesDraftResponse,
		AxiosError<DefaultError>,
		Options<PostApiAddNewPatientAddAddressesDraftData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await postApiAddNewPatientAddAddressesDraft({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const postApiAddNewPatientAddAddressesValidateQueryKey = (
	options?: Options<PostApiAddNewPatientAddAddressesValidateData>
) => createQueryKey('postApiAddNewPatientAddAddressesValidate', options);

export const postApiAddNewPatientAddAddressesValidateOptions = (
	options?: Options<PostApiAddNewPatientAddAddressesValidateData>
) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await postApiAddNewPatientAddAddressesValidate({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: postApiAddNewPatientAddAddressesValidateQueryKey(options),
	});
};

export const postApiAddNewPatientAddAddressesValidateMutation = (
	options?: Partial<Options<PostApiAddNewPatientAddAddressesValidateData>>
) => {
	const mutationOptions: UseMutationOptions<
		PostApiAddNewPatientAddAddressesValidateResponse,
		AxiosError<DefaultError>,
		Options<PostApiAddNewPatientAddAddressesValidateData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await postApiAddNewPatientAddAddressesValidate({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const postApiAddNewPatientAddInsurancesQueryKey = (
	options?: Options<PostApiAddNewPatientAddInsurancesData>
) => createQueryKey('postApiAddNewPatientAddInsurances', options);

export const postApiAddNewPatientAddInsurancesOptions = (
	options?: Options<PostApiAddNewPatientAddInsurancesData>
) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await postApiAddNewPatientAddInsurances({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: postApiAddNewPatientAddInsurancesQueryKey(options),
	});
};

export const postApiAddNewPatientAddInsurancesMutation = (
	options?: Partial<Options<PostApiAddNewPatientAddInsurancesData>>
) => {
	const mutationOptions: UseMutationOptions<
		PostApiAddNewPatientAddInsurancesResponse,
		AxiosError<DefaultError>,
		Options<PostApiAddNewPatientAddInsurancesData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await postApiAddNewPatientAddInsurances({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const postApiAddNewPatientAddInsurancesDraftQueryKey = (
	options?: Options<PostApiAddNewPatientAddInsurancesDraftData>
) => createQueryKey('postApiAddNewPatientAddInsurancesDraft', options);

export const postApiAddNewPatientAddInsurancesDraftOptions = (
	options?: Options<PostApiAddNewPatientAddInsurancesDraftData>
) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await postApiAddNewPatientAddInsurancesDraft({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: postApiAddNewPatientAddInsurancesDraftQueryKey(options),
	});
};

export const postApiAddNewPatientAddInsurancesDraftMutation = (
	options?: Partial<Options<PostApiAddNewPatientAddInsurancesDraftData>>
) => {
	const mutationOptions: UseMutationOptions<
		PostApiAddNewPatientAddInsurancesDraftResponse,
		AxiosError<DefaultError>,
		Options<PostApiAddNewPatientAddInsurancesDraftData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await postApiAddNewPatientAddInsurancesDraft({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const postApiAddNewPatientAddInsurancesValidateQueryKey = (
	options?: Options<PostApiAddNewPatientAddInsurancesValidateData>
) => createQueryKey('postApiAddNewPatientAddInsurancesValidate', options);

export const postApiAddNewPatientAddInsurancesValidateOptions = (
	options?: Options<PostApiAddNewPatientAddInsurancesValidateData>
) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await postApiAddNewPatientAddInsurancesValidate({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: postApiAddNewPatientAddInsurancesValidateQueryKey(options),
	});
};

export const postApiAddNewPatientAddInsurancesValidateMutation = (
	options?: Partial<Options<PostApiAddNewPatientAddInsurancesValidateData>>
) => {
	const mutationOptions: UseMutationOptions<
		PostApiAddNewPatientAddInsurancesValidateResponse,
		AxiosError<DefaultError>,
		Options<PostApiAddNewPatientAddInsurancesValidateData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await postApiAddNewPatientAddInsurancesValidate({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const postApiAddNewPatientAddGuardiansQueryKey = (
	options?: Options<PostApiAddNewPatientAddGuardiansData>
) => createQueryKey('postApiAddNewPatientAddGuardians', options);

export const postApiAddNewPatientAddGuardiansOptions = (
	options?: Options<PostApiAddNewPatientAddGuardiansData>
) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await postApiAddNewPatientAddGuardians({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: postApiAddNewPatientAddGuardiansQueryKey(options),
	});
};

export const postApiAddNewPatientAddGuardiansMutation = (
	options?: Partial<Options<PostApiAddNewPatientAddGuardiansData>>
) => {
	const mutationOptions: UseMutationOptions<
		PostApiAddNewPatientAddGuardiansResponse,
		AxiosError<DefaultError>,
		Options<PostApiAddNewPatientAddGuardiansData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await postApiAddNewPatientAddGuardians({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const postApiAddNewPatientAddGuardiansDraftQueryKey = (
	options?: Options<PostApiAddNewPatientAddGuardiansDraftData>
) => createQueryKey('postApiAddNewPatientAddGuardiansDraft', options);

export const postApiAddNewPatientAddGuardiansDraftOptions = (
	options?: Options<PostApiAddNewPatientAddGuardiansDraftData>
) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await postApiAddNewPatientAddGuardiansDraft({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: postApiAddNewPatientAddGuardiansDraftQueryKey(options),
	});
};

export const postApiAddNewPatientAddGuardiansDraftMutation = (
	options?: Partial<Options<PostApiAddNewPatientAddGuardiansDraftData>>
) => {
	const mutationOptions: UseMutationOptions<
		PostApiAddNewPatientAddGuardiansDraftResponse,
		AxiosError<DefaultError>,
		Options<PostApiAddNewPatientAddGuardiansDraftData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await postApiAddNewPatientAddGuardiansDraft({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const postApiAddNewPatientAddGuardiansValidateQueryKey = (
	options?: Options<PostApiAddNewPatientAddGuardiansValidateData>
) => createQueryKey('postApiAddNewPatientAddGuardiansValidate', options);

export const postApiAddNewPatientAddGuardiansValidateOptions = (
	options?: Options<PostApiAddNewPatientAddGuardiansValidateData>
) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await postApiAddNewPatientAddGuardiansValidate({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: postApiAddNewPatientAddGuardiansValidateQueryKey(options),
	});
};

export const postApiAddNewPatientAddGuardiansValidateMutation = (
	options?: Partial<Options<PostApiAddNewPatientAddGuardiansValidateData>>
) => {
	const mutationOptions: UseMutationOptions<
		PostApiAddNewPatientAddGuardiansValidateResponse,
		AxiosError<DefaultError>,
		Options<PostApiAddNewPatientAddGuardiansValidateData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await postApiAddNewPatientAddGuardiansValidate({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const postApiAddNewPatientAddClinicalInformationQueryKey = (
	options?: Options<PostApiAddNewPatientAddClinicalInformationData>
) => createQueryKey('postApiAddNewPatientAddClinicalInformation', options);

export const postApiAddNewPatientAddClinicalInformationOptions = (
	options?: Options<PostApiAddNewPatientAddClinicalInformationData>
) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await postApiAddNewPatientAddClinicalInformation({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: postApiAddNewPatientAddClinicalInformationQueryKey(options),
	});
};

export const postApiAddNewPatientAddClinicalInformationMutation = (
	options?: Partial<Options<PostApiAddNewPatientAddClinicalInformationData>>
) => {
	const mutationOptions: UseMutationOptions<
		PostApiAddNewPatientAddClinicalInformationResponse,
		AxiosError<DefaultError>,
		Options<PostApiAddNewPatientAddClinicalInformationData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await postApiAddNewPatientAddClinicalInformation({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const postApiAddNewPatientAddClinicalInformationDraftQueryKey = (
	options?: Options<PostApiAddNewPatientAddClinicalInformationDraftData>
) => createQueryKey('postApiAddNewPatientAddClinicalInformationDraft', options);

export const postApiAddNewPatientAddClinicalInformationDraftOptions = (
	options?: Options<PostApiAddNewPatientAddClinicalInformationDraftData>
) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await postApiAddNewPatientAddClinicalInformationDraft({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: postApiAddNewPatientAddClinicalInformationDraftQueryKey(options),
	});
};

export const postApiAddNewPatientAddClinicalInformationDraftMutation = (
	options?: Partial<Options<PostApiAddNewPatientAddClinicalInformationDraftData>>
) => {
	const mutationOptions: UseMutationOptions<
		PostApiAddNewPatientAddClinicalInformationDraftResponse,
		AxiosError<DefaultError>,
		Options<PostApiAddNewPatientAddClinicalInformationDraftData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await postApiAddNewPatientAddClinicalInformationDraft({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const postApiAddNewPatientAddClinicalInformationValidateQueryKey = (
	options?: Options<PostApiAddNewPatientAddClinicalInformationValidateData>
) => createQueryKey('postApiAddNewPatientAddClinicalInformationValidate', options);

export const postApiAddNewPatientAddClinicalInformationValidateOptions = (
	options?: Options<PostApiAddNewPatientAddClinicalInformationValidateData>
) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await postApiAddNewPatientAddClinicalInformationValidate({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: postApiAddNewPatientAddClinicalInformationValidateQueryKey(options),
	});
};

export const postApiAddNewPatientAddClinicalInformationValidateMutation = (
	options?: Partial<Options<PostApiAddNewPatientAddClinicalInformationValidateData>>
) => {
	const mutationOptions: UseMutationOptions<
		PostApiAddNewPatientAddClinicalInformationValidateResponse,
		AxiosError<DefaultError>,
		Options<PostApiAddNewPatientAddClinicalInformationValidateData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await postApiAddNewPatientAddClinicalInformationValidate({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const getApiCareLocationByIdQueryKey = (options: Options<GetApiCareLocationByIdData>) =>
	createQueryKey('getApiCareLocationById', options);

export const getApiCareLocationByIdOptions = (options: Options<GetApiCareLocationByIdData>) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await getApiCareLocationById({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: getApiCareLocationByIdQueryKey(options),
	});
};

export const patchApiCareLocationByIdMutation = (
	options?: Partial<Options<PatchApiCareLocationByIdData>>
) => {
	const mutationOptions: UseMutationOptions<
		PatchApiCareLocationByIdResponse,
		AxiosError<PatchApiCareLocationByIdError>,
		Options<PatchApiCareLocationByIdData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await patchApiCareLocationById({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const putApiCareLocationByIdMutation = (
	options?: Partial<Options<PutApiCareLocationByIdData>>
) => {
	const mutationOptions: UseMutationOptions<
		PutApiCareLocationByIdResponse,
		AxiosError<PutApiCareLocationByIdError>,
		Options<PutApiCareLocationByIdData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await putApiCareLocationById({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const postApiCareLocationSearchQueryKey = (
	options?: Options<PostApiCareLocationSearchData>
) => createQueryKey('postApiCareLocationSearch', options);

export const postApiCareLocationSearchOptions = (
	options?: Options<PostApiCareLocationSearchData>
) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await postApiCareLocationSearch({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: postApiCareLocationSearchQueryKey(options),
	});
};

const createInfiniteParams = <
	K extends Pick<QueryKey<Options>[0], 'body' | 'headers' | 'path' | 'query'>,
>(
	queryKey: QueryKey<Options>,
	page: K
) => {
	const params = queryKey[0];
	if (page.body) {
		params.body = {
			...(queryKey[0].body as any),
			...(page.body as any),
		};
	}
	if (page.headers) {
		params.headers = {
			...queryKey[0].headers,
			...page.headers,
		};
	}
	if (page.path) {
		params.path = {
			...(queryKey[0].path as any),
			...(page.path as any),
		};
	}
	if (page.query) {
		params.query = {
			...(queryKey[0].query as any),
			...(page.query as any),
		};
	}
	return params as unknown as typeof page;
};

export const postApiCareLocationSearchInfiniteQueryKey = (
	options?: Options<PostApiCareLocationSearchData>
): QueryKey<Options<PostApiCareLocationSearchData>> =>
	createQueryKey('postApiCareLocationSearch', options, true);

export const postApiCareLocationSearchInfiniteOptions = (
	options?: Options<PostApiCareLocationSearchData>
) => {
	return infiniteQueryOptions<
		PostApiCareLocationSearchResponse,
		AxiosError<DefaultError>,
		InfiniteData<PostApiCareLocationSearchResponse>,
		QueryKey<Options<PostApiCareLocationSearchData>>,
		| number
		| Pick<
				QueryKey<Options<PostApiCareLocationSearchData>>[0],
				'body' | 'headers' | 'path' | 'query'
		  >
	>(
		// @ts-ignore
		{
			queryFn: async ({ pageParam, queryKey, signal }) => {
				// @ts-ignore
				const page: Pick<
					QueryKey<Options<PostApiCareLocationSearchData>>[0],
					'body' | 'headers' | 'path' | 'query'
				> =
					typeof pageParam === 'object'
						? pageParam
						: {
								query: {
									offset: pageParam,
								},
							};
				const params = createInfiniteParams(queryKey, page);
				const { data } = await postApiCareLocationSearch({
					...options,
					...params,
					signal,
					throwOnError: true,
				});
				return data;
			},
			queryKey: postApiCareLocationSearchInfiniteQueryKey(options),
		}
	);
};

export const postApiCareLocationSearchMutation = (
	options?: Partial<Options<PostApiCareLocationSearchData>>
) => {
	const mutationOptions: UseMutationOptions<
		PostApiCareLocationSearchResponse,
		AxiosError<DefaultError>,
		Options<PostApiCareLocationSearchData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await postApiCareLocationSearch({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const getApiCareLocationQueryKey = (options?: Options<GetApiCareLocationData>) =>
	createQueryKey('getApiCareLocation', options);

export const getApiCareLocationOptions = (options?: Options<GetApiCareLocationData>) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await getApiCareLocation({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: getApiCareLocationQueryKey(options),
	});
};

export const getApiCareLocationInfiniteQueryKey = (
	options?: Options<GetApiCareLocationData>
): QueryKey<Options<GetApiCareLocationData>> => createQueryKey('getApiCareLocation', options, true);

export const getApiCareLocationInfiniteOptions = (options?: Options<GetApiCareLocationData>) => {
	return infiniteQueryOptions<
		GetApiCareLocationResponse,
		AxiosError<DefaultError>,
		InfiniteData<GetApiCareLocationResponse>,
		QueryKey<Options<GetApiCareLocationData>>,
		| number
		| Pick<QueryKey<Options<GetApiCareLocationData>>[0], 'body' | 'headers' | 'path' | 'query'>
	>(
		// @ts-ignore
		{
			queryFn: async ({ pageParam, queryKey, signal }) => {
				// @ts-ignore
				const page: Pick<
					QueryKey<Options<GetApiCareLocationData>>[0],
					'body' | 'headers' | 'path' | 'query'
				> =
					typeof pageParam === 'object'
						? pageParam
						: {
								query: {
									offset: pageParam,
								},
							};
				const params = createInfiniteParams(queryKey, page);
				const { data } = await getApiCareLocation({
					...options,
					...params,
					signal,
					throwOnError: true,
				});
				return data;
			},
			queryKey: getApiCareLocationInfiniteQueryKey(options),
		}
	);
};

export const postApiCareLocationQueryKey = (options?: Options<PostApiCareLocationData>) =>
	createQueryKey('postApiCareLocation', options);

export const postApiCareLocationOptions = (options?: Options<PostApiCareLocationData>) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await postApiCareLocation({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: postApiCareLocationQueryKey(options),
	});
};

export const postApiCareLocationMutation = (
	options?: Partial<Options<PostApiCareLocationData>>
) => {
	const mutationOptions: UseMutationOptions<
		PostApiCareLocationResponse,
		AxiosError<PostApiCareLocationError>,
		Options<PostApiCareLocationData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await postApiCareLocation({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const postApiCareLocationDraftQueryKey = (options?: Options<PostApiCareLocationDraftData>) =>
	createQueryKey('postApiCareLocationDraft', options);

export const postApiCareLocationDraftOptions = (
	options?: Options<PostApiCareLocationDraftData>
) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await postApiCareLocationDraft({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: postApiCareLocationDraftQueryKey(options),
	});
};

export const postApiCareLocationDraftMutation = (
	options?: Partial<Options<PostApiCareLocationDraftData>>
) => {
	const mutationOptions: UseMutationOptions<
		PostApiCareLocationDraftResponse,
		AxiosError<PostApiCareLocationDraftError>,
		Options<PostApiCareLocationDraftData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await postApiCareLocationDraft({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const getApiCareLocationDraftByEntityIdQueryKey = (
	options: Options<GetApiCareLocationDraftByEntityIdData>
) => createQueryKey('getApiCareLocationDraftByEntityId', options);

export const getApiCareLocationDraftByEntityIdOptions = (
	options: Options<GetApiCareLocationDraftByEntityIdData>
) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await getApiCareLocationDraftByEntityId({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: getApiCareLocationDraftByEntityIdQueryKey(options),
	});
};

export const putApiCareLocationDraftByEntityIdMutation = (
	options?: Partial<Options<PutApiCareLocationDraftByEntityIdData>>
) => {
	const mutationOptions: UseMutationOptions<
		PutApiCareLocationDraftByEntityIdResponse,
		AxiosError<PutApiCareLocationDraftByEntityIdError>,
		Options<PutApiCareLocationDraftByEntityIdData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await putApiCareLocationDraftByEntityId({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const postApiCareLocationDraftByEntityIdCommitQueryKey = (
	options: Options<PostApiCareLocationDraftByEntityIdCommitData>
) => createQueryKey('postApiCareLocationDraftByEntityIdCommit', options);

export const postApiCareLocationDraftByEntityIdCommitOptions = (
	options: Options<PostApiCareLocationDraftByEntityIdCommitData>
) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await postApiCareLocationDraftByEntityIdCommit({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: postApiCareLocationDraftByEntityIdCommitQueryKey(options),
	});
};

export const postApiCareLocationDraftByEntityIdCommitMutation = (
	options?: Partial<Options<PostApiCareLocationDraftByEntityIdCommitData>>
) => {
	const mutationOptions: UseMutationOptions<
		PostApiCareLocationDraftByEntityIdCommitResponse,
		AxiosError<PostApiCareLocationDraftByEntityIdCommitError>,
		Options<PostApiCareLocationDraftByEntityIdCommitData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await postApiCareLocationDraftByEntityIdCommit({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const postApiCareLocationDraftValidateQueryKey = (
	options?: Options<PostApiCareLocationDraftValidateData>
) => createQueryKey('postApiCareLocationDraftValidate', options);

export const postApiCareLocationDraftValidateOptions = (
	options?: Options<PostApiCareLocationDraftValidateData>
) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await postApiCareLocationDraftValidate({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: postApiCareLocationDraftValidateQueryKey(options),
	});
};

export const postApiCareLocationDraftValidateMutation = (
	options?: Partial<Options<PostApiCareLocationDraftValidateData>>
) => {
	const mutationOptions: UseMutationOptions<
		PostApiCareLocationDraftValidateResponse,
		AxiosError<PostApiCareLocationDraftValidateError>,
		Options<PostApiCareLocationDraftValidateData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await postApiCareLocationDraftValidate({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const postApiCareLocationDraftByEntityIdValidateQueryKey = (
	options: Options<PostApiCareLocationDraftByEntityIdValidateData>
) => createQueryKey('postApiCareLocationDraftByEntityIdValidate', options);

export const postApiCareLocationDraftByEntityIdValidateOptions = (
	options: Options<PostApiCareLocationDraftByEntityIdValidateData>
) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await postApiCareLocationDraftByEntityIdValidate({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: postApiCareLocationDraftByEntityIdValidateQueryKey(options),
	});
};

export const postApiCareLocationDraftByEntityIdValidateMutation = (
	options?: Partial<Options<PostApiCareLocationDraftByEntityIdValidateData>>
) => {
	const mutationOptions: UseMutationOptions<
		PostApiCareLocationDraftByEntityIdValidateResponse,
		AxiosError<PostApiCareLocationDraftByEntityIdValidateError>,
		Options<PostApiCareLocationDraftByEntityIdValidateData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await postApiCareLocationDraftByEntityIdValidate({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

// export const postApiCareLocationDraftQueryKey = (options?: Options<PostApiCareLocationDraftData>) =>
// 	createQueryKey('postApiCareLocationDraft', options);

// export const postApiCareLocationDraftOptions = (
// 	options?: Options<PostApiCareLocationDraftData>
// ) => {
// 	return queryOptions({
// 		queryFn: async ({ queryKey, signal }) => {
// 			const { data } = await postApiCareLocationDraft({
// 				...options,
// 				...queryKey[0],
// 				signal,
// 				throwOnError: true,
// 			});
// 			return data;
// 		},
// 		queryKey: postApiCareLocationDraftQueryKey(options),
// 	});
// };

// export const postApiCareLocationDraftMutation = (
// 	options?: Partial<Options<PostApiCareLocationDraftData>>
// ) => {
// 	const mutationOptions: UseMutationOptions<
// 		PostApiCareLocationDraftResponse,
// 		AxiosError<PostApiCareLocationDraftError>,
// 		Options<PostApiCareLocationDraftData>
// 	> = {
// 		mutationFn: async (localOptions) => {
// 			const { data } = await postApiCareLocationDraft({
// 				...options,
// 				...localOptions,
// 				throwOnError: true,
// 			});
// 			return data;
// 		},
// 	};
// 	return mutationOptions;
// };

// export const getApiCareLocationDraftByEntityIdQueryKey = (
// 	options: Options<GetApiCareLocationDraftByEntityIdData>
// ) => createQueryKey('getApiCareLocationDraftByEntityId', options);

// export const getApiCareLocationDraftByEntityIdOptions = (
// 	options: Options<GetApiCareLocationDraftByEntityIdData>
// ) => {
// 	return queryOptions({
// 		queryFn: async ({ queryKey, signal }) => {
// 			const { data } = await getApiCareLocationDraftByEntityId({
// 				...options,
// 				...queryKey[0],
// 				signal,
// 				throwOnError: true,
// 			});
// 			return data;
// 		},
// 		queryKey: getApiCareLocationDraftByEntityIdQueryKey(options),
// 	});
// };

// export const putApiCareLocationDraftByEntityIdMutation = (
// 	options?: Partial<Options<PutApiCareLocationDraftByEntityIdData>>
// ) => {
// 	const mutationOptions: UseMutationOptions<
// 		PutApiCareLocationDraftByEntityIdResponse,
// 		AxiosError<PutApiCareLocationDraftByEntityIdError>,
// 		Options<PutApiCareLocationDraftByEntityIdData>
// 	> = {
// 		mutationFn: async (localOptions) => {
// 			const { data } = await putApiCareLocationDraftByEntityId({
// 				...options,
// 				...localOptions,
// 				throwOnError: true,
// 			});
// 			return data;
// 		},
// 	};
// 	return mutationOptions;
// };

// export const postApiCareLocationDraftByEntityIdCommitQueryKey = (
// 	options: Options<PostApiCareLocationDraftByEntityIdCommitData>
// ) => createQueryKey('postApiCareLocationDraftByEntityIdCommit', options);

// export const postApiCareLocationDraftByEntityIdCommitOptions = (
// 	options: Options<PostApiCareLocationDraftByEntityIdCommitData>
// ) => {
// 	return queryOptions({
// 		queryFn: async ({ queryKey, signal }) => {
// 			const { data } = await postApiCareLocationDraftByEntityIdCommit({
// 				...options,
// 				...queryKey[0],
// 				signal,
// 				throwOnError: true,
// 			});
// 			return data;
// 		},
// 		queryKey: postApiCareLocationDraftByEntityIdCommitQueryKey(options),
// 	});
// };

// export const postApiCareLocationDraftByEntityIdCommitMutation = (
// 	options?: Partial<Options<PostApiCareLocationDraftByEntityIdCommitData>>
// ) => {
// 	const mutationOptions: UseMutationOptions<
// 		PostApiCareLocationDraftByEntityIdCommitResponse,
// 		AxiosError<PostApiCareLocationDraftByEntityIdCommitError>,
// 		Options<PostApiCareLocationDraftByEntityIdCommitData>
// 	> = {
// 		mutationFn: async (localOptions) => {
// 			const { data } = await postApiCareLocationDraftByEntityIdCommit({
// 				...options,
// 				...localOptions,
// 				throwOnError: true,
// 			});
// 			return data;
// 		},
// 	};
// 	return mutationOptions;
// };

// export const postApiCareLocationDraftValidateQueryKey = (
// 	options?: Options<PostApiCareLocationDraftValidateData>
// ) => createQueryKey('postApiCareLocationDraftValidate', options);

// export const postApiCareLocationDraftValidateOptions = (
// 	options?: Options<PostApiCareLocationDraftValidateData>
// ) => {
// 	return queryOptions({
// 		queryFn: async ({ queryKey, signal }) => {
// 			const { data } = await postApiCareLocationDraftValidate({
// 				...options,
// 				...queryKey[0],
// 				signal,
// 				throwOnError: true,
// 			});
// 			return data;
// 		},
// 		queryKey: postApiCareLocationDraftValidateQueryKey(options),
// 	});
// };

// export const postApiCareLocationDraftValidateMutation = (
// 	options?: Partial<Options<PostApiCareLocationDraftValidateData>>
// ) => {
// 	const mutationOptions: UseMutationOptions<
// 		PostApiCareLocationDraftValidateResponse,
// 		AxiosError<PostApiCareLocationDraftValidateError>,
// 		Options<PostApiCareLocationDraftValidateData>
// 	> = {
// 		mutationFn: async (localOptions) => {
// 			const { data } = await postApiCareLocationDraftValidate({
// 				...options,
// 				...localOptions,
// 				throwOnError: true,
// 			});
// 			return data;
// 		},
// 	};
// 	return mutationOptions;
// };

// export const postApiCareLocationDraftByEntityIdValidateQueryKey = (
// 	options: Options<PostApiCareLocationDraftByEntityIdValidateData>
// ) => createQueryKey('postApiCareLocationDraftByEntityIdValidate', options);

// export const postApiCareLocationDraftByEntityIdValidateOptions = (
// 	options: Options<PostApiCareLocationDraftByEntityIdValidateData>
// ) => {
// 	return queryOptions({
// 		queryFn: async ({ queryKey, signal }) => {
// 			const { data } = await postApiCareLocationDraftByEntityIdValidate({
// 				...options,
// 				...queryKey[0],
// 				signal,
// 				throwOnError: true,
// 			});
// 			return data;
// 		},
// 		queryKey: postApiCareLocationDraftByEntityIdValidateQueryKey(options),
// 	});
// };

// export const postApiCareLocationDraftByEntityIdValidateMutation = (
// 	options?: Partial<Options<PostApiCareLocationDraftByEntityIdValidateData>>
// ) => {
// 	const mutationOptions: UseMutationOptions<
// 		PostApiCareLocationDraftByEntityIdValidateResponse,
// 		AxiosError<PostApiCareLocationDraftByEntityIdValidateError>,
// 		Options<PostApiCareLocationDraftByEntityIdValidateData>
// 	> = {
// 		mutationFn: async (localOptions) => {
// 			const { data } = await postApiCareLocationDraftByEntityIdValidate({
// 				...options,
// 				...localOptions,
// 				throwOnError: true,
// 			});
// 			return data;
// 		},
// 	};
// 	return mutationOptions;
// };

export const getApiDraftByIdQueryKey = (options: Options<GetApiDraftByIdData>) =>
	createQueryKey('getApiDraftById', options);

export const getApiDraftByIdOptions = (options: Options<GetApiDraftByIdData>) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await getApiDraftById({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: getApiDraftByIdQueryKey(options),
	});
};

export const patchApiDraftByIdMutation = (options?: Partial<Options<PatchApiDraftByIdData>>) => {
	const mutationOptions: UseMutationOptions<
		PatchApiDraftByIdResponse,
		AxiosError<PatchApiDraftByIdError>,
		Options<PatchApiDraftByIdData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await patchApiDraftById({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const putApiDraftByIdMutation = (options?: Partial<Options<PutApiDraftByIdData>>) => {
	const mutationOptions: UseMutationOptions<
		PutApiDraftByIdResponse,
		AxiosError<PutApiDraftByIdError>,
		Options<PutApiDraftByIdData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await putApiDraftById({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const postApiDraftSearchQueryKey = (options?: Options<PostApiDraftSearchData>) =>
	createQueryKey('postApiDraftSearch', options);

export const postApiDraftSearchOptions = (options?: Options<PostApiDraftSearchData>) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await postApiDraftSearch({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: postApiDraftSearchQueryKey(options),
	});
};

export const postApiDraftSearchInfiniteQueryKey = (
	options?: Options<PostApiDraftSearchData>
): QueryKey<Options<PostApiDraftSearchData>> => createQueryKey('postApiDraftSearch', options, true);

export const postApiDraftSearchInfiniteOptions = (options?: Options<PostApiDraftSearchData>) => {
	return infiniteQueryOptions<
		PostApiDraftSearchResponse,
		AxiosError<DefaultError>,
		InfiniteData<PostApiDraftSearchResponse>,
		QueryKey<Options<PostApiDraftSearchData>>,
		| number
		| Pick<QueryKey<Options<PostApiDraftSearchData>>[0], 'body' | 'headers' | 'path' | 'query'>
	>(
		// @ts-ignore
		{
			queryFn: async ({ pageParam, queryKey, signal }) => {
				// @ts-ignore
				const page: Pick<
					QueryKey<Options<PostApiDraftSearchData>>[0],
					'body' | 'headers' | 'path' | 'query'
				> =
					typeof pageParam === 'object'
						? pageParam
						: {
								query: {
									offset: pageParam,
								},
							};
				const params = createInfiniteParams(queryKey, page);
				const { data } = await postApiDraftSearch({
					...options,
					...params,
					signal,
					throwOnError: true,
				});
				return data;
			},
			queryKey: postApiDraftSearchInfiniteQueryKey(options),
		}
	);
};

export const postApiDraftSearchMutation = (options?: Partial<Options<PostApiDraftSearchData>>) => {
	const mutationOptions: UseMutationOptions<
		PostApiDraftSearchResponse,
		AxiosError<DefaultError>,
		Options<PostApiDraftSearchData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await postApiDraftSearch({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const getApiDraftQueryKey = (options?: Options<GetApiDraftData>) =>
	createQueryKey('getApiDraft', options);

export const getApiDraftOptions = (options?: Options<GetApiDraftData>) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await getApiDraft({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: getApiDraftQueryKey(options),
	});
};

export const getApiDraftInfiniteQueryKey = (
	options?: Options<GetApiDraftData>
): QueryKey<Options<GetApiDraftData>> => createQueryKey('getApiDraft', options, true);

export const getApiDraftInfiniteOptions = (options?: Options<GetApiDraftData>) => {
	return infiniteQueryOptions<
		GetApiDraftResponse,
		AxiosError<DefaultError>,
		InfiniteData<GetApiDraftResponse>,
		QueryKey<Options<GetApiDraftData>>,
		number | Pick<QueryKey<Options<GetApiDraftData>>[0], 'body' | 'headers' | 'path' | 'query'>
	>(
		// @ts-ignore
		{
			queryFn: async ({ pageParam, queryKey, signal }) => {
				// @ts-ignore
				const page: Pick<
					QueryKey<Options<GetApiDraftData>>[0],
					'body' | 'headers' | 'path' | 'query'
				> =
					typeof pageParam === 'object'
						? pageParam
						: {
								query: {
									offset: pageParam,
								},
							};
				const params = createInfiniteParams(queryKey, page);
				const { data } = await getApiDraft({
					...options,
					...params,
					signal,
					throwOnError: true,
				});
				return data;
			},
			queryKey: getApiDraftInfiniteQueryKey(options),
		}
	);
};

export const postApiDraftQueryKey = (options?: Options<PostApiDraftData>) =>
	createQueryKey('postApiDraft', options);

export const postApiDraftOptions = (options?: Options<PostApiDraftData>) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await postApiDraft({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: postApiDraftQueryKey(options),
	});
};

export const postApiDraftMutation = (options?: Partial<Options<PostApiDraftData>>) => {
	const mutationOptions: UseMutationOptions<
		PostApiDraftResponse,
		AxiosError<PostApiDraftError>,
		Options<PostApiDraftData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await postApiDraft({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

// export const postApiDraftDraftQueryKey = (options?: Options<PostApiDraftDraftData>) =>
// 	createQueryKey('postApiDraftDraft', options);

// export const postApiDraftDraftOptions = (options?: Options<PostApiDraftDraftData>) => {
// 	return queryOptions({
// 		queryFn: async ({ queryKey, signal }) => {
// 			const { data } = await postApiDraftDraft({
// 				...options,
// 				...queryKey[0],
// 				signal,
// 				throwOnError: true,
// 			});
// 			return data;
// 		},
// 		queryKey: postApiDraftDraftQueryKey(options),
// 	});
// };

// export const postApiDraftDraftMutation = (options?: Partial<Options<PostApiDraftDraftData>>) => {
// 	const mutationOptions: UseMutationOptions<
// 		PostApiDraftDraftResponse,
// 		AxiosError<PostApiDraftDraftError>,
// 		Options<PostApiDraftDraftData>
// 	> = {
// 		mutationFn: async (localOptions) => {
// 			const { data } = await postApiDraftDraft({
// 				...options,
// 				...localOptions,
// 				throwOnError: true,
// 			});
// 			return data;
// 		},
// 	};
// 	return mutationOptions;
// };

// export const getApiDraftDraftByEntityIdQueryKey = (
// 	options: Options<GetApiDraftDraftByEntityIdData>
// ) => createQueryKey('getApiDraftDraftByEntityId', options);

// export const getApiDraftDraftByEntityIdOptions = (
// 	options: Options<GetApiDraftDraftByEntityIdData>
// ) => {
// 	return queryOptions({
// 		queryFn: async ({ queryKey, signal }) => {
// 			const { data } = await getApiDraftDraftByEntityId({
// 				...options,
// 				...queryKey[0],
// 				signal,
// 				throwOnError: true,
// 			});
// 			return data;
// 		},
// 		queryKey: getApiDraftDraftByEntityIdQueryKey(options),
// 	});
// };

// export const putApiDraftDraftByEntityIdMutation = (
// 	options?: Partial<Options<PutApiDraftDraftByEntityIdData>>
// ) => {
// 	const mutationOptions: UseMutationOptions<
// 		PutApiDraftDraftByEntityIdResponse,
// 		AxiosError<PutApiDraftDraftByEntityIdError>,
// 		Options<PutApiDraftDraftByEntityIdData>
// 	> = {
// 		mutationFn: async (localOptions) => {
// 			const { data } = await putApiDraftDraftByEntityId({
// 				...options,
// 				...localOptions,
// 				throwOnError: true,
// 			});
// 			return data;
// 		},
// 	};
// 	return mutationOptions;
// };

// export const postApiDraftDraftByEntityIdCommitQueryKey = (
// 	options: Options<PostApiDraftDraftByEntityIdCommitData>
// ) => createQueryKey('postApiDraftDraftByEntityIdCommit', options);

// export const postApiDraftDraftByEntityIdCommitOptions = (
// 	options: Options<PostApiDraftDraftByEntityIdCommitData>
// ) => {
// 	return queryOptions({
// 		queryFn: async ({ queryKey, signal }) => {
// 			const { data } = await postApiDraftDraftByEntityIdCommit({
// 				...options,
// 				...queryKey[0],
// 				signal,
// 				throwOnError: true,
// 			});
// 			return data;
// 		},
// 		queryKey: postApiDraftDraftByEntityIdCommitQueryKey(options),
// 	});
// };

// export const postApiDraftDraftByEntityIdCommitMutation = (
// 	options?: Partial<Options<PostApiDraftDraftByEntityIdCommitData>>
// ) => {
// 	const mutationOptions: UseMutationOptions<
// 		PostApiDraftDraftByEntityIdCommitResponse,
// 		AxiosError<PostApiDraftDraftByEntityIdCommitError>,
// 		Options<PostApiDraftDraftByEntityIdCommitData>
// 	> = {
// 		mutationFn: async (localOptions) => {
// 			const { data } = await postApiDraftDraftByEntityIdCommit({
// 				...options,
// 				...localOptions,
// 				throwOnError: true,
// 			});
// 			return data;
// 		},
// 	};
// 	return mutationOptions;
// };

// export const postApiDraftDraftValidateQueryKey = (
// 	options?: Options<PostApiDraftDraftValidateData>
// ) => createQueryKey('postApiDraftDraftValidate', options);

// export const postApiDraftDraftValidateOptions = (
// 	options?: Options<PostApiDraftDraftValidateData>
// ) => {
// 	return queryOptions({
// 		queryFn: async ({ queryKey, signal }) => {
// 			const { data } = await postApiDraftDraftValidate({
// 				...options,
// 				...queryKey[0],
// 				signal,
// 				throwOnError: true,
// 			});
// 			return data;
// 		},
// 		queryKey: postApiDraftDraftValidateQueryKey(options),
// 	});
// };

// export const postApiDraftDraftValidateMutation = (
// 	options?: Partial<Options<PostApiDraftDraftValidateData>>
// ) => {
// 	const mutationOptions: UseMutationOptions<
// 		PostApiDraftDraftValidateResponse,
// 		AxiosError<PostApiDraftDraftValidateError>,
// 		Options<PostApiDraftDraftValidateData>
// 	> = {
// 		mutationFn: async (localOptions) => {
// 			const { data } = await postApiDraftDraftValidate({
// 				...options,
// 				...localOptions,
// 				throwOnError: true,
// 			});
// 			return data;
// 		},
// 	};
// 	return mutationOptions;
// };

// export const postApiDraftDraftByEntityIdValidateQueryKey = (
// 	options: Options<PostApiDraftDraftByEntityIdValidateData>
// ) => createQueryKey('postApiDraftDraftByEntityIdValidate', options);

// export const postApiDraftDraftByEntityIdValidateOptions = (
// 	options: Options<PostApiDraftDraftByEntityIdValidateData>
// ) => {
// 	return queryOptions({
// 		queryFn: async ({ queryKey, signal }) => {
// 			const { data } = await postApiDraftDraftByEntityIdValidate({
// 				...options,
// 				...queryKey[0],
// 				signal,
// 				throwOnError: true,
// 			});
// 			return data;
// 		},
// 		queryKey: postApiDraftDraftByEntityIdValidateQueryKey(options),
// 	});
// };

// export const postApiDraftDraftByEntityIdValidateMutation = (
// 	options?: Partial<Options<PostApiDraftDraftByEntityIdValidateData>>
// ) => {
// 	const mutationOptions: UseMutationOptions<
// 		PostApiDraftDraftByEntityIdValidateResponse,
// 		AxiosError<PostApiDraftDraftByEntityIdValidateError>,
// 		Options<PostApiDraftDraftByEntityIdValidateData>
// 	> = {
// 		mutationFn: async (localOptions) => {
// 			const { data } = await postApiDraftDraftByEntityIdValidate({
// 				...options,
// 				...localOptions,
// 				throwOnError: true,
// 			});
// 			return data;
// 		},
// 	};
// 	return mutationOptions;
// };

export const postApiDraftDraftQueryKey = (options?: Options<PostApiDraftDraftData>) =>
	createQueryKey('postApiDraftDraft', options);

export const postApiDraftDraftOptions = (options?: Options<PostApiDraftDraftData>) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await postApiDraftDraft({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: postApiDraftDraftQueryKey(options),
	});
};

export const postApiDraftDraftMutation = (options?: Partial<Options<PostApiDraftDraftData>>) => {
	const mutationOptions: UseMutationOptions<
		PostApiDraftDraftResponse,
		AxiosError<PostApiDraftDraftError>,
		Options<PostApiDraftDraftData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await postApiDraftDraft({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const getApiDraftDraftByEntityIdQueryKey = (
	options: Options<GetApiDraftDraftByEntityIdData>
) => createQueryKey('getApiDraftDraftByEntityId', options);

export const getApiDraftDraftByEntityIdOptions = (
	options: Options<GetApiDraftDraftByEntityIdData>
) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await getApiDraftDraftByEntityId({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: getApiDraftDraftByEntityIdQueryKey(options),
	});
};

export const putApiDraftDraftByEntityIdMutation = (
	options?: Partial<Options<PutApiDraftDraftByEntityIdData>>
) => {
	const mutationOptions: UseMutationOptions<
		PutApiDraftDraftByEntityIdResponse,
		AxiosError<PutApiDraftDraftByEntityIdError>,
		Options<PutApiDraftDraftByEntityIdData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await putApiDraftDraftByEntityId({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const postApiDraftDraftByEntityIdCommitQueryKey = (
	options: Options<PostApiDraftDraftByEntityIdCommitData>
) => createQueryKey('postApiDraftDraftByEntityIdCommit', options);

export const postApiDraftDraftByEntityIdCommitOptions = (
	options: Options<PostApiDraftDraftByEntityIdCommitData>
) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await postApiDraftDraftByEntityIdCommit({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: postApiDraftDraftByEntityIdCommitQueryKey(options),
	});
};

export const postApiDraftDraftByEntityIdCommitMutation = (
	options?: Partial<Options<PostApiDraftDraftByEntityIdCommitData>>
) => {
	const mutationOptions: UseMutationOptions<
		PostApiDraftDraftByEntityIdCommitResponse,
		AxiosError<PostApiDraftDraftByEntityIdCommitError>,
		Options<PostApiDraftDraftByEntityIdCommitData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await postApiDraftDraftByEntityIdCommit({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const postApiDraftDraftValidateQueryKey = (
	options?: Options<PostApiDraftDraftValidateData>
) => createQueryKey('postApiDraftDraftValidate', options);

export const postApiDraftDraftValidateOptions = (
	options?: Options<PostApiDraftDraftValidateData>
) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await postApiDraftDraftValidate({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: postApiDraftDraftValidateQueryKey(options),
	});
};

export const postApiDraftDraftValidateMutation = (
	options?: Partial<Options<PostApiDraftDraftValidateData>>
) => {
	const mutationOptions: UseMutationOptions<
		PostApiDraftDraftValidateResponse,
		AxiosError<PostApiDraftDraftValidateError>,
		Options<PostApiDraftDraftValidateData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await postApiDraftDraftValidate({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const postApiDraftDraftByEntityIdValidateQueryKey = (
	options: Options<PostApiDraftDraftByEntityIdValidateData>
) => createQueryKey('postApiDraftDraftByEntityIdValidate', options);

export const postApiDraftDraftByEntityIdValidateOptions = (
	options: Options<PostApiDraftDraftByEntityIdValidateData>
) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await postApiDraftDraftByEntityIdValidate({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: postApiDraftDraftByEntityIdValidateQueryKey(options),
	});
};

export const postApiDraftDraftByEntityIdValidateMutation = (
	options?: Partial<Options<PostApiDraftDraftByEntityIdValidateData>>
) => {
	const mutationOptions: UseMutationOptions<
		PostApiDraftDraftByEntityIdValidateResponse,
		AxiosError<PostApiDraftDraftByEntityIdValidateError>,
		Options<PostApiDraftDraftByEntityIdValidateData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await postApiDraftDraftByEntityIdValidate({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const getApiEquipmentByIdQueryKey = (options: Options<GetApiEquipmentByIdData>) =>
	createQueryKey('getApiEquipmentById', options);

export const getApiEquipmentByIdOptions = (options: Options<GetApiEquipmentByIdData>) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await getApiEquipmentById({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: getApiEquipmentByIdQueryKey(options),
	});
};

export const patchApiEquipmentByIdMutation = (
	options?: Partial<Options<PatchApiEquipmentByIdData>>
) => {
	const mutationOptions: UseMutationOptions<
		PatchApiEquipmentByIdResponse,
		AxiosError<PatchApiEquipmentByIdError>,
		Options<PatchApiEquipmentByIdData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await patchApiEquipmentById({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const putApiEquipmentByIdMutation = (
	options?: Partial<Options<PutApiEquipmentByIdData>>
) => {
	const mutationOptions: UseMutationOptions<
		PutApiEquipmentByIdResponse,
		AxiosError<PutApiEquipmentByIdError>,
		Options<PutApiEquipmentByIdData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await putApiEquipmentById({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const postApiEquipmentSearchQueryKey = (options?: Options<PostApiEquipmentSearchData>) =>
	createQueryKey('postApiEquipmentSearch', options);

export const postApiEquipmentSearchOptions = (options?: Options<PostApiEquipmentSearchData>) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await postApiEquipmentSearch({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: postApiEquipmentSearchQueryKey(options),
	});
};

export const postApiEquipmentSearchInfiniteQueryKey = (
	options?: Options<PostApiEquipmentSearchData>
): QueryKey<Options<PostApiEquipmentSearchData>> =>
	createQueryKey('postApiEquipmentSearch', options, true);

export const postApiEquipmentSearchInfiniteOptions = (
	options?: Options<PostApiEquipmentSearchData>
) => {
	return infiniteQueryOptions<
		PostApiEquipmentSearchResponse,
		AxiosError<DefaultError>,
		InfiniteData<PostApiEquipmentSearchResponse>,
		QueryKey<Options<PostApiEquipmentSearchData>>,
		| number
		| Pick<QueryKey<Options<PostApiEquipmentSearchData>>[0], 'body' | 'headers' | 'path' | 'query'>
	>(
		// @ts-ignore
		{
			queryFn: async ({ pageParam, queryKey, signal }) => {
				// @ts-ignore
				const page: Pick<
					QueryKey<Options<PostApiEquipmentSearchData>>[0],
					'body' | 'headers' | 'path' | 'query'
				> =
					typeof pageParam === 'object'
						? pageParam
						: {
								query: {
									offset: pageParam,
								},
							};
				const params = createInfiniteParams(queryKey, page);
				const { data } = await postApiEquipmentSearch({
					...options,
					...params,
					signal,
					throwOnError: true,
				});
				return data;
			},
			queryKey: postApiEquipmentSearchInfiniteQueryKey(options),
		}
	);
};

export const postApiEquipmentSearchMutation = (
	options?: Partial<Options<PostApiEquipmentSearchData>>
) => {
	const mutationOptions: UseMutationOptions<
		PostApiEquipmentSearchResponse,
		AxiosError<DefaultError>,
		Options<PostApiEquipmentSearchData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await postApiEquipmentSearch({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const getApiEquipmentQueryKey = (options?: Options<GetApiEquipmentData>) =>
	createQueryKey('getApiEquipment', options);

export const getApiEquipmentOptions = (options?: Options<GetApiEquipmentData>) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await getApiEquipment({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: getApiEquipmentQueryKey(options),
	});
};

export const getApiEquipmentInfiniteQueryKey = (
	options?: Options<GetApiEquipmentData>
): QueryKey<Options<GetApiEquipmentData>> => createQueryKey('getApiEquipment', options, true);

export const getApiEquipmentInfiniteOptions = (options?: Options<GetApiEquipmentData>) => {
	return infiniteQueryOptions<
		GetApiEquipmentResponse,
		AxiosError<DefaultError>,
		InfiniteData<GetApiEquipmentResponse>,
		QueryKey<Options<GetApiEquipmentData>>,
		number | Pick<QueryKey<Options<GetApiEquipmentData>>[0], 'body' | 'headers' | 'path' | 'query'>
	>(
		// @ts-ignore
		{
			queryFn: async ({ pageParam, queryKey, signal }) => {
				// @ts-ignore
				const page: Pick<
					QueryKey<Options<GetApiEquipmentData>>[0],
					'body' | 'headers' | 'path' | 'query'
				> =
					typeof pageParam === 'object'
						? pageParam
						: {
								query: {
									offset: pageParam,
								},
							};
				const params = createInfiniteParams(queryKey, page);
				const { data } = await getApiEquipment({
					...options,
					...params,
					signal,
					throwOnError: true,
				});
				return data;
			},
			queryKey: getApiEquipmentInfiniteQueryKey(options),
		}
	);
};

export const postApiEquipmentQueryKey = (options?: Options<PostApiEquipmentData>) =>
	createQueryKey('postApiEquipment', options);

export const postApiEquipmentOptions = (options?: Options<PostApiEquipmentData>) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await postApiEquipment({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: postApiEquipmentQueryKey(options),
	});
};

export const postApiEquipmentMutation = (options?: Partial<Options<PostApiEquipmentData>>) => {
	const mutationOptions: UseMutationOptions<
		PostApiEquipmentResponse,
		AxiosError<PostApiEquipmentError>,
		Options<PostApiEquipmentData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await postApiEquipment({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const postApiEquipmentDraftQueryKey = (options?: Options<PostApiEquipmentDraftData>) =>
	createQueryKey('postApiEquipmentDraft', options);

export const postApiEquipmentDraftOptions = (options?: Options<PostApiEquipmentDraftData>) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await postApiEquipmentDraft({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: postApiEquipmentDraftQueryKey(options),
	});
};

export const postApiEquipmentDraftMutation = (
	options?: Partial<Options<PostApiEquipmentDraftData>>
) => {
	const mutationOptions: UseMutationOptions<
		PostApiEquipmentDraftResponse,
		AxiosError<PostApiEquipmentDraftError>,
		Options<PostApiEquipmentDraftData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await postApiEquipmentDraft({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const getApiEquipmentDraftByEntityIdQueryKey = (
	options: Options<GetApiEquipmentDraftByEntityIdData>
) => createQueryKey('getApiEquipmentDraftByEntityId', options);

export const getApiEquipmentDraftByEntityIdOptions = (
	options: Options<GetApiEquipmentDraftByEntityIdData>
) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await getApiEquipmentDraftByEntityId({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: getApiEquipmentDraftByEntityIdQueryKey(options),
	});
};

export const putApiEquipmentDraftByEntityIdMutation = (
	options?: Partial<Options<PutApiEquipmentDraftByEntityIdData>>
) => {
	const mutationOptions: UseMutationOptions<
		PutApiEquipmentDraftByEntityIdResponse,
		AxiosError<PutApiEquipmentDraftByEntityIdError>,
		Options<PutApiEquipmentDraftByEntityIdData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await putApiEquipmentDraftByEntityId({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const postApiEquipmentDraftByEntityIdCommitQueryKey = (
	options: Options<PostApiEquipmentDraftByEntityIdCommitData>
) => createQueryKey('postApiEquipmentDraftByEntityIdCommit', options);

export const postApiEquipmentDraftByEntityIdCommitOptions = (
	options: Options<PostApiEquipmentDraftByEntityIdCommitData>
) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await postApiEquipmentDraftByEntityIdCommit({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: postApiEquipmentDraftByEntityIdCommitQueryKey(options),
	});
};

export const postApiEquipmentDraftByEntityIdCommitMutation = (
	options?: Partial<Options<PostApiEquipmentDraftByEntityIdCommitData>>
) => {
	const mutationOptions: UseMutationOptions<
		PostApiEquipmentDraftByEntityIdCommitResponse,
		AxiosError<PostApiEquipmentDraftByEntityIdCommitError>,
		Options<PostApiEquipmentDraftByEntityIdCommitData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await postApiEquipmentDraftByEntityIdCommit({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const postApiEquipmentDraftValidateQueryKey = (
	options?: Options<PostApiEquipmentDraftValidateData>
) => createQueryKey('postApiEquipmentDraftValidate', options);

export const postApiEquipmentDraftValidateOptions = (
	options?: Options<PostApiEquipmentDraftValidateData>
) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await postApiEquipmentDraftValidate({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: postApiEquipmentDraftValidateQueryKey(options),
	});
};

export const postApiEquipmentDraftValidateMutation = (
	options?: Partial<Options<PostApiEquipmentDraftValidateData>>
) => {
	const mutationOptions: UseMutationOptions<
		PostApiEquipmentDraftValidateResponse,
		AxiosError<PostApiEquipmentDraftValidateError>,
		Options<PostApiEquipmentDraftValidateData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await postApiEquipmentDraftValidate({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const postApiEquipmentDraftByEntityIdValidateQueryKey = (
	options: Options<PostApiEquipmentDraftByEntityIdValidateData>
) => createQueryKey('postApiEquipmentDraftByEntityIdValidate', options);

export const postApiEquipmentDraftByEntityIdValidateOptions = (
	options: Options<PostApiEquipmentDraftByEntityIdValidateData>
) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await postApiEquipmentDraftByEntityIdValidate({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: postApiEquipmentDraftByEntityIdValidateQueryKey(options),
	});
};

export const postApiEquipmentDraftByEntityIdValidateMutation = (
	options?: Partial<Options<PostApiEquipmentDraftByEntityIdValidateData>>
) => {
	const mutationOptions: UseMutationOptions<
		PostApiEquipmentDraftByEntityIdValidateResponse,
		AxiosError<PostApiEquipmentDraftByEntityIdValidateError>,
		Options<PostApiEquipmentDraftByEntityIdValidateData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await postApiEquipmentDraftByEntityIdValidate({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const postApiFileRequestUploadTokenQueryKey = (
	options?: Options<PostApiFileRequestUploadTokenData>
) => createQueryKey('postApiFileRequestUploadToken', options);

export const postApiFileRequestUploadTokenOptions = (
	options?: Options<PostApiFileRequestUploadTokenData>
) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await postApiFileRequestUploadToken({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: postApiFileRequestUploadTokenQueryKey(options),
	});
};

export const postApiFileRequestUploadTokenMutation = (
	options?: Partial<Options<PostApiFileRequestUploadTokenData>>
) => {
	const mutationOptions: UseMutationOptions<
		PostApiFileRequestUploadTokenResponse,
		AxiosError<PostApiFileRequestUploadTokenError>,
		Options<PostApiFileRequestUploadTokenData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await postApiFileRequestUploadToken({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const postApiFileUploadQueryKey = (options: Options<PostApiFileUploadData>) =>
	createQueryKey('postApiFileUpload', options);

export const postApiFileUploadOptions = (options: Options<PostApiFileUploadData>) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await postApiFileUpload({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: postApiFileUploadQueryKey(options),
	});
};

export const postApiFileUploadMutation = (options?: Partial<Options<PostApiFileUploadData>>) => {
	const mutationOptions: UseMutationOptions<
		PostApiFileUploadResponse,
		AxiosError<PostApiFileUploadError>,
		Options<PostApiFileUploadData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await postApiFileUpload({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const getApiFileStatusByFileIdQueryKey = (options: Options<GetApiFileStatusByFileIdData>) =>
	createQueryKey('getApiFileStatusByFileId', options);

export const getApiFileStatusByFileIdOptions = (options: Options<GetApiFileStatusByFileIdData>) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await getApiFileStatusByFileId({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: getApiFileStatusByFileIdQueryKey(options),
	});
};

export const getApiHealthQueryKey = (options?: Options<GetApiHealthData>) =>
	createQueryKey('getApiHealth', options);

export const getApiHealthOptions = (options?: Options<GetApiHealthData>) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await getApiHealth({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: getApiHealthQueryKey(options),
	});
};

export const getApiInsuranceByIdQueryKey = (options: Options<GetApiInsuranceByIdData>) =>
	createQueryKey('getApiInsuranceById', options);

export const getApiInsuranceByIdOptions = (options: Options<GetApiInsuranceByIdData>) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await getApiInsuranceById({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: getApiInsuranceByIdQueryKey(options),
	});
};

export const patchApiInsuranceByIdMutation = (
	options?: Partial<Options<PatchApiInsuranceByIdData>>
) => {
	const mutationOptions: UseMutationOptions<
		PatchApiInsuranceByIdResponse,
		AxiosError<PatchApiInsuranceByIdError>,
		Options<PatchApiInsuranceByIdData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await patchApiInsuranceById({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const putApiInsuranceByIdMutation = (
	options?: Partial<Options<PutApiInsuranceByIdData>>
) => {
	const mutationOptions: UseMutationOptions<
		PutApiInsuranceByIdResponse,
		AxiosError<PutApiInsuranceByIdError>,
		Options<PutApiInsuranceByIdData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await putApiInsuranceById({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const postApiInsuranceSearchQueryKey = (options?: Options<PostApiInsuranceSearchData>) =>
	createQueryKey('postApiInsuranceSearch', options);

export const postApiInsuranceSearchOptions = (options?: Options<PostApiInsuranceSearchData>) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await postApiInsuranceSearch({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: postApiInsuranceSearchQueryKey(options),
	});
};

export const postApiInsuranceSearchInfiniteQueryKey = (
	options?: Options<PostApiInsuranceSearchData>
): QueryKey<Options<PostApiInsuranceSearchData>> =>
	createQueryKey('postApiInsuranceSearch', options, true);

export const postApiInsuranceSearchInfiniteOptions = (
	options?: Options<PostApiInsuranceSearchData>
) => {
	return infiniteQueryOptions<
		PostApiInsuranceSearchResponse,
		AxiosError<DefaultError>,
		InfiniteData<PostApiInsuranceSearchResponse>,
		QueryKey<Options<PostApiInsuranceSearchData>>,
		| number
		| Pick<QueryKey<Options<PostApiInsuranceSearchData>>[0], 'body' | 'headers' | 'path' | 'query'>
	>(
		// @ts-ignore
		{
			queryFn: async ({ pageParam, queryKey, signal }) => {
				// @ts-ignore
				const page: Pick<
					QueryKey<Options<PostApiInsuranceSearchData>>[0],
					'body' | 'headers' | 'path' | 'query'
				> =
					typeof pageParam === 'object'
						? pageParam
						: {
								query: {
									offset: pageParam,
								},
							};
				const params = createInfiniteParams(queryKey, page);
				const { data } = await postApiInsuranceSearch({
					...options,
					...params,
					signal,
					throwOnError: true,
				});
				return data;
			},
			queryKey: postApiInsuranceSearchInfiniteQueryKey(options),
		}
	);
};

export const postApiInsuranceSearchMutation = (
	options?: Partial<Options<PostApiInsuranceSearchData>>
) => {
	const mutationOptions: UseMutationOptions<
		PostApiInsuranceSearchResponse,
		AxiosError<DefaultError>,
		Options<PostApiInsuranceSearchData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await postApiInsuranceSearch({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const getApiInsuranceQueryKey = (options?: Options<GetApiInsuranceData>) =>
	createQueryKey('getApiInsurance', options);

export const getApiInsuranceOptions = (options?: Options<GetApiInsuranceData>) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await getApiInsurance({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: getApiInsuranceQueryKey(options),
	});
};

export const getApiInsuranceInfiniteQueryKey = (
	options?: Options<GetApiInsuranceData>
): QueryKey<Options<GetApiInsuranceData>> => createQueryKey('getApiInsurance', options, true);

export const getApiInsuranceInfiniteOptions = (options?: Options<GetApiInsuranceData>) => {
	return infiniteQueryOptions<
		GetApiInsuranceResponse,
		AxiosError<DefaultError>,
		InfiniteData<GetApiInsuranceResponse>,
		QueryKey<Options<GetApiInsuranceData>>,
		number | Pick<QueryKey<Options<GetApiInsuranceData>>[0], 'body' | 'headers' | 'path' | 'query'>
	>(
		// @ts-ignore
		{
			queryFn: async ({ pageParam, queryKey, signal }) => {
				// @ts-ignore
				const page: Pick<
					QueryKey<Options<GetApiInsuranceData>>[0],
					'body' | 'headers' | 'path' | 'query'
				> =
					typeof pageParam === 'object'
						? pageParam
						: {
								query: {
									offset: pageParam,
								},
							};
				const params = createInfiniteParams(queryKey, page);
				const { data } = await getApiInsurance({
					...options,
					...params,
					signal,
					throwOnError: true,
				});
				return data;
			},
			queryKey: getApiInsuranceInfiniteQueryKey(options),
		}
	);
};

export const postApiInsuranceQueryKey = (options?: Options<PostApiInsuranceData>) =>
	createQueryKey('postApiInsurance', options);

export const postApiInsuranceOptions = (options?: Options<PostApiInsuranceData>) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await postApiInsurance({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: postApiInsuranceQueryKey(options),
	});
};

export const postApiInsuranceMutation = (options?: Partial<Options<PostApiInsuranceData>>) => {
	const mutationOptions: UseMutationOptions<
		PostApiInsuranceResponse,
		AxiosError<PostApiInsuranceError>,
		Options<PostApiInsuranceData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await postApiInsurance({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

// export const postApiInsuranceDraftQueryKey = (options?: Options<PostApiInsuranceDraftData>) =>
// 	createQueryKey('postApiInsuranceDraft', options);

// export const postApiInsuranceDraftOptions = (options?: Options<PostApiInsuranceDraftData>) => {
// 	return queryOptions({
// 		queryFn: async ({ queryKey, signal }) => {
// 			const { data } = await postApiInsuranceDraft({
// 				...options,
// 				...queryKey[0],
// 				signal,
// 				throwOnError: true,
// 			});
// 			return data;
// 		},
// 		queryKey: postApiInsuranceDraftQueryKey(options),
// 	});
// };

// export const postApiInsuranceDraftMutation = (
// 	options?: Partial<Options<PostApiInsuranceDraftData>>
// ) => {
// 	const mutationOptions: UseMutationOptions<
// 		PostApiInsuranceDraftResponse,
// 		AxiosError<PostApiInsuranceDraftError>,
// 		Options<PostApiInsuranceDraftData>
// 	> = {
// 		mutationFn: async (localOptions) => {
// 			const { data } = await postApiInsuranceDraft({
// 				...options,
// 				...localOptions,
// 				throwOnError: true,
// 			});
// 			return data;
// 		},
// 	};
// 	return mutationOptions;
// };

// export const getApiInsuranceDraftByEntityIdQueryKey = (
// 	options: Options<GetApiInsuranceDraftByEntityIdData>
// ) => createQueryKey('getApiInsuranceDraftByEntityId', options);

// export const getApiInsuranceDraftByEntityIdOptions = (
// 	options: Options<GetApiInsuranceDraftByEntityIdData>
// ) => {
// 	return queryOptions({
// 		queryFn: async ({ queryKey, signal }) => {
// 			const { data } = await getApiInsuranceDraftByEntityId({
// 				...options,
// 				...queryKey[0],
// 				signal,
// 				throwOnError: true,
// 			});
// 			return data;
// 		},
// 		queryKey: getApiInsuranceDraftByEntityIdQueryKey(options),
// 	});
// };

// export const putApiInsuranceDraftByEntityIdMutation = (
// 	options?: Partial<Options<PutApiInsuranceDraftByEntityIdData>>
// ) => {
// 	const mutationOptions: UseMutationOptions<
// 		PutApiInsuranceDraftByEntityIdResponse,
// 		AxiosError<PutApiInsuranceDraftByEntityIdError>,
// 		Options<PutApiInsuranceDraftByEntityIdData>
// 	> = {
// 		mutationFn: async (localOptions) => {
// 			const { data } = await putApiInsuranceDraftByEntityId({
// 				...options,
// 				...localOptions,
// 				throwOnError: true,
// 			});
// 			return data;
// 		},
// 	};
// 	return mutationOptions;
// };

// export const postApiInsuranceDraftByEntityIdCommitQueryKey = (
// 	options: Options<PostApiInsuranceDraftByEntityIdCommitData>
// ) => createQueryKey('postApiInsuranceDraftByEntityIdCommit', options);

// export const postApiInsuranceDraftByEntityIdCommitOptions = (
// 	options: Options<PostApiInsuranceDraftByEntityIdCommitData>
// ) => {
// 	return queryOptions({
// 		queryFn: async ({ queryKey, signal }) => {
// 			const { data } = await postApiInsuranceDraftByEntityIdCommit({
// 				...options,
// 				...queryKey[0],
// 				signal,
// 				throwOnError: true,
// 			});
// 			return data;
// 		},
// 		queryKey: postApiInsuranceDraftByEntityIdCommitQueryKey(options),
// 	});
// };

// export const postApiInsuranceDraftByEntityIdCommitMutation = (
// 	options?: Partial<Options<PostApiInsuranceDraftByEntityIdCommitData>>
// ) => {
// 	const mutationOptions: UseMutationOptions<
// 		PostApiInsuranceDraftByEntityIdCommitResponse,
// 		AxiosError<PostApiInsuranceDraftByEntityIdCommitError>,
// 		Options<PostApiInsuranceDraftByEntityIdCommitData>
// 	> = {
// 		mutationFn: async (localOptions) => {
// 			const { data } = await postApiInsuranceDraftByEntityIdCommit({
// 				...options,
// 				...localOptions,
// 				throwOnError: true,
// 			});
// 			return data;
// 		},
// 	};
// 	return mutationOptions;
// };

// export const postApiInsuranceDraftValidateQueryKey = (
// 	options?: Options<PostApiInsuranceDraftValidateData>
// ) => createQueryKey('postApiInsuranceDraftValidate', options);

// export const postApiInsuranceDraftValidateOptions = (
// 	options?: Options<PostApiInsuranceDraftValidateData>
// ) => {
// 	return queryOptions({
// 		queryFn: async ({ queryKey, signal }) => {
// 			const { data } = await postApiInsuranceDraftValidate({
// 				...options,
// 				...queryKey[0],
// 				signal,
// 				throwOnError: true,
// 			});
// 			return data;
// 		},
// 		queryKey: postApiInsuranceDraftValidateQueryKey(options),
// 	});
// };

// export const postApiInsuranceDraftValidateMutation = (
// 	options?: Partial<Options<PostApiInsuranceDraftValidateData>>
// ) => {
// 	const mutationOptions: UseMutationOptions<
// 		PostApiInsuranceDraftValidateResponse,
// 		AxiosError<PostApiInsuranceDraftValidateError>,
// 		Options<PostApiInsuranceDraftValidateData>
// 	> = {
// 		mutationFn: async (localOptions) => {
// 			const { data } = await postApiInsuranceDraftValidate({
// 				...options,
// 				...localOptions,
// 				throwOnError: true,
// 			});
// 			return data;
// 		},
// 	};
// 	return mutationOptions;
// };

// export const postApiInsuranceDraftByEntityIdValidateQueryKey = (
// 	options: Options<PostApiInsuranceDraftByEntityIdValidateData>
// ) => createQueryKey('postApiInsuranceDraftByEntityIdValidate', options);

// export const postApiInsuranceDraftByEntityIdValidateOptions = (
// 	options: Options<PostApiInsuranceDraftByEntityIdValidateData>
// ) => {
// 	return queryOptions({
// 		queryFn: async ({ queryKey, signal }) => {
// 			const { data } = await postApiInsuranceDraftByEntityIdValidate({
// 				...options,
// 				...queryKey[0],
// 				signal,
// 				throwOnError: true,
// 			});
// 			return data;
// 		},
// 		queryKey: postApiInsuranceDraftByEntityIdValidateQueryKey(options),
// 	});
// };

// export const postApiInsuranceDraftByEntityIdValidateMutation = (
// 	options?: Partial<Options<PostApiInsuranceDraftByEntityIdValidateData>>
// ) => {
// 	const mutationOptions: UseMutationOptions<
// 		PostApiInsuranceDraftByEntityIdValidateResponse,
// 		AxiosError<PostApiInsuranceDraftByEntityIdValidateError>,
// 		Options<PostApiInsuranceDraftByEntityIdValidateData>
// 	> = {
// 		mutationFn: async (localOptions) => {
// 			const { data } = await postApiInsuranceDraftByEntityIdValidate({
// 				...options,
// 				...localOptions,
// 				throwOnError: true,
// 			});
// 			return data;
// 		},
// 	};
// 	return mutationOptions;
// };

export const postApiInsuranceDraftQueryKey = (options?: Options<PostApiInsuranceDraftData>) =>
	createQueryKey('postApiInsuranceDraft', options);

export const postApiInsuranceDraftOptions = (options?: Options<PostApiInsuranceDraftData>) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await postApiInsuranceDraft({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: postApiInsuranceDraftQueryKey(options),
	});
};

export const postApiInsuranceDraftMutation = (
	options?: Partial<Options<PostApiInsuranceDraftData>>
) => {
	const mutationOptions: UseMutationOptions<
		PostApiInsuranceDraftResponse,
		AxiosError<PostApiInsuranceDraftError>,
		Options<PostApiInsuranceDraftData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await postApiInsuranceDraft({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const getApiInsuranceDraftByEntityIdQueryKey = (
	options: Options<GetApiInsuranceDraftByEntityIdData>
) => createQueryKey('getApiInsuranceDraftByEntityId', options);

export const getApiInsuranceDraftByEntityIdOptions = (
	options: Options<GetApiInsuranceDraftByEntityIdData>
) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await getApiInsuranceDraftByEntityId({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: getApiInsuranceDraftByEntityIdQueryKey(options),
	});
};

export const putApiInsuranceDraftByEntityIdMutation = (
	options?: Partial<Options<PutApiInsuranceDraftByEntityIdData>>
) => {
	const mutationOptions: UseMutationOptions<
		PutApiInsuranceDraftByEntityIdResponse,
		AxiosError<PutApiInsuranceDraftByEntityIdError>,
		Options<PutApiInsuranceDraftByEntityIdData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await putApiInsuranceDraftByEntityId({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const postApiInsuranceDraftByEntityIdCommitQueryKey = (
	options: Options<PostApiInsuranceDraftByEntityIdCommitData>
) => createQueryKey('postApiInsuranceDraftByEntityIdCommit', options);

export const postApiInsuranceDraftByEntityIdCommitOptions = (
	options: Options<PostApiInsuranceDraftByEntityIdCommitData>
) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await postApiInsuranceDraftByEntityIdCommit({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: postApiInsuranceDraftByEntityIdCommitQueryKey(options),
	});
};

export const postApiInsuranceDraftByEntityIdCommitMutation = (
	options?: Partial<Options<PostApiInsuranceDraftByEntityIdCommitData>>
) => {
	const mutationOptions: UseMutationOptions<
		PostApiInsuranceDraftByEntityIdCommitResponse,
		AxiosError<PostApiInsuranceDraftByEntityIdCommitError>,
		Options<PostApiInsuranceDraftByEntityIdCommitData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await postApiInsuranceDraftByEntityIdCommit({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const postApiInsuranceDraftValidateQueryKey = (
	options?: Options<PostApiInsuranceDraftValidateData>
) => createQueryKey('postApiInsuranceDraftValidate', options);

export const postApiInsuranceDraftValidateOptions = (
	options?: Options<PostApiInsuranceDraftValidateData>
) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await postApiInsuranceDraftValidate({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: postApiInsuranceDraftValidateQueryKey(options),
	});
};

export const postApiInsuranceDraftValidateMutation = (
	options?: Partial<Options<PostApiInsuranceDraftValidateData>>
) => {
	const mutationOptions: UseMutationOptions<
		PostApiInsuranceDraftValidateResponse,
		AxiosError<PostApiInsuranceDraftValidateError>,
		Options<PostApiInsuranceDraftValidateData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await postApiInsuranceDraftValidate({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const postApiInsuranceDraftByEntityIdValidateQueryKey = (
	options: Options<PostApiInsuranceDraftByEntityIdValidateData>
) => createQueryKey('postApiInsuranceDraftByEntityIdValidate', options);

export const postApiInsuranceDraftByEntityIdValidateOptions = (
	options: Options<PostApiInsuranceDraftByEntityIdValidateData>
) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await postApiInsuranceDraftByEntityIdValidate({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: postApiInsuranceDraftByEntityIdValidateQueryKey(options),
	});
};

export const postApiInsuranceDraftByEntityIdValidateMutation = (
	options?: Partial<Options<PostApiInsuranceDraftByEntityIdValidateData>>
) => {
	const mutationOptions: UseMutationOptions<
		PostApiInsuranceDraftByEntityIdValidateResponse,
		AxiosError<PostApiInsuranceDraftByEntityIdValidateError>,
		Options<PostApiInsuranceDraftByEntityIdValidateData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await postApiInsuranceDraftByEntityIdValidate({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const postApiInsuranceVerificationStartQueryKey = (
	options?: Options<PostApiInsuranceVerificationStartData>
) => createQueryKey('postApiInsuranceVerificationStart', options);

export const postApiInsuranceVerificationStartOptions = (
	options?: Options<PostApiInsuranceVerificationStartData>
) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await postApiInsuranceVerificationStart({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: postApiInsuranceVerificationStartQueryKey(options),
	});
};

export const postApiInsuranceVerificationStartMutation = (
	options?: Partial<Options<PostApiInsuranceVerificationStartData>>
) => {
	const mutationOptions: UseMutationOptions<
		unknown,
		AxiosError<PostApiInsuranceVerificationStartError>,
		Options<PostApiInsuranceVerificationStartData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await postApiInsuranceVerificationStart({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const getApiInsuranceVerificationStatusByJobIdQueryKey = (
	options: Options<GetApiInsuranceVerificationStatusByJobIdData>
) => createQueryKey('getApiInsuranceVerificationStatusByJobId', options);

export const getApiInsuranceVerificationStatusByJobIdOptions = (
	options: Options<GetApiInsuranceVerificationStatusByJobIdData>
) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await getApiInsuranceVerificationStatusByJobId({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: getApiInsuranceVerificationStatusByJobIdQueryKey(options),
	});
};

export const postApiInsuranceVerificationSearchQueryKey = (
	options?: Options<PostApiInsuranceVerificationSearchData>
) => createQueryKey('postApiInsuranceVerificationSearch', options);

export const postApiInsuranceVerificationSearchOptions = (
	options?: Options<PostApiInsuranceVerificationSearchData>
) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await postApiInsuranceVerificationSearch({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: postApiInsuranceVerificationSearchQueryKey(options),
	});
};

export const postApiInsuranceVerificationSearchInfiniteQueryKey = (
	options?: Options<PostApiInsuranceVerificationSearchData>
): QueryKey<Options<PostApiInsuranceVerificationSearchData>> =>
	createQueryKey('postApiInsuranceVerificationSearch', options, true);

export const postApiInsuranceVerificationSearchInfiniteOptions = (
	options?: Options<PostApiInsuranceVerificationSearchData>
) => {
	return infiniteQueryOptions<
		PostApiInsuranceVerificationSearchResponse,
		AxiosError<PostApiInsuranceVerificationSearchError>,
		InfiniteData<PostApiInsuranceVerificationSearchResponse>,
		QueryKey<Options<PostApiInsuranceVerificationSearchData>>,
		| number
		| Pick<
				QueryKey<Options<PostApiInsuranceVerificationSearchData>>[0],
				'body' | 'headers' | 'path' | 'query'
		  >
	>(
		// @ts-ignore
		{
			queryFn: async ({ pageParam, queryKey, signal }) => {
				// @ts-ignore
				const page: Pick<
					QueryKey<Options<PostApiInsuranceVerificationSearchData>>[0],
					'body' | 'headers' | 'path' | 'query'
				> =
					typeof pageParam === 'object'
						? pageParam
						: {
								query: {
									offset: pageParam,
								},
							};
				const params = createInfiniteParams(queryKey, page);
				const { data } = await postApiInsuranceVerificationSearch({
					...options,
					...params,
					signal,
					throwOnError: true,
				});
				return data;
			},
			queryKey: postApiInsuranceVerificationSearchInfiniteQueryKey(options),
		}
	);
};

export const postApiInsuranceVerificationSearchMutation = (
	options?: Partial<Options<PostApiInsuranceVerificationSearchData>>
) => {
	const mutationOptions: UseMutationOptions<
		PostApiInsuranceVerificationSearchResponse,
		AxiosError<PostApiInsuranceVerificationSearchError>,
		Options<PostApiInsuranceVerificationSearchData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await postApiInsuranceVerificationSearch({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const postApiInsuranceVerificationFastauthWebhookQueryKey = (
	options?: Options<PostApiInsuranceVerificationFastauthWebhookData>
) => createQueryKey('postApiInsuranceVerificationFastauthWebhook', options);

export const postApiInsuranceVerificationFastauthWebhookOptions = (
	options?: Options<PostApiInsuranceVerificationFastauthWebhookData>
) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await postApiInsuranceVerificationFastauthWebhook({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: postApiInsuranceVerificationFastauthWebhookQueryKey(options),
	});
};

export const postApiInsuranceVerificationFastauthWebhookMutation = (
	options?: Partial<Options<PostApiInsuranceVerificationFastauthWebhookData>>
) => {
	const mutationOptions: UseMutationOptions<
		unknown,
		AxiosError<PostApiInsuranceVerificationFastauthWebhookError>,
		Options<PostApiInsuranceVerificationFastauthWebhookData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await postApiInsuranceVerificationFastauthWebhook({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const postApiLoginQueryKey = (options?: Options<PostApiLoginData>) =>
	createQueryKey('postApiLogin', options);

export const postApiLoginOptions = (options?: Options<PostApiLoginData>) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await postApiLogin({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: postApiLoginQueryKey(options),
	});
};

export const postApiLoginMutation = (options?: Partial<Options<PostApiLoginData>>) => {
	const mutationOptions: UseMutationOptions<
		unknown,
		AxiosError<DefaultError>,
		Options<PostApiLoginData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await postApiLogin({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const getApiLoginCheckQueryKey = (options?: Options<GetApiLoginCheckData>) =>
	createQueryKey('getApiLoginCheck', options);

export const getApiLoginCheckOptions = (options?: Options<GetApiLoginCheckData>) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await getApiLoginCheck({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: getApiLoginCheckQueryKey(options),
	});
};

export const postApiMockDbDeleteQueryKey = (options?: Options<PostApiMockDbDeleteData>) =>
	createQueryKey('postApiMockDbDelete', options);

export const postApiMockDbDeleteOptions = (options?: Options<PostApiMockDbDeleteData>) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await postApiMockDbDelete({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: postApiMockDbDeleteQueryKey(options),
	});
};

export const postApiMockDbDeleteMutation = (
	options?: Partial<Options<PostApiMockDbDeleteData>>
) => {
	const mutationOptions: UseMutationOptions<
		unknown,
		AxiosError<DefaultError>,
		Options<PostApiMockDbDeleteData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await postApiMockDbDelete({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const getApiMockDbResetQueryKey = (options?: Options<GetApiMockDbResetData>) =>
	createQueryKey('getApiMockDbReset', options);

export const getApiMockDbResetOptions = (options?: Options<GetApiMockDbResetData>) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await getApiMockDbReset({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: getApiMockDbResetQueryKey(options),
	});
};

export const getApiNoteByIdQueryKey = (options: Options<GetApiNoteByIdData>) =>
	createQueryKey('getApiNoteById', options);

export const getApiNoteByIdOptions = (options: Options<GetApiNoteByIdData>) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await getApiNoteById({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: getApiNoteByIdQueryKey(options),
	});
};

export const patchApiNoteByIdMutation = (options?: Partial<Options<PatchApiNoteByIdData>>) => {
	const mutationOptions: UseMutationOptions<
		PatchApiNoteByIdResponse,
		AxiosError<PatchApiNoteByIdError>,
		Options<PatchApiNoteByIdData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await patchApiNoteById({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const putApiNoteByIdMutation = (options?: Partial<Options<PutApiNoteByIdData>>) => {
	const mutationOptions: UseMutationOptions<
		PutApiNoteByIdResponse,
		AxiosError<PutApiNoteByIdError>,
		Options<PutApiNoteByIdData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await putApiNoteById({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const postApiNoteSearchQueryKey = (options?: Options<PostApiNoteSearchData>) =>
	createQueryKey('postApiNoteSearch', options);

export const postApiNoteSearchOptions = (options?: Options<PostApiNoteSearchData>) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await postApiNoteSearch({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: postApiNoteSearchQueryKey(options),
	});
};

export const postApiNoteSearchInfiniteQueryKey = (
	options?: Options<PostApiNoteSearchData>
): QueryKey<Options<PostApiNoteSearchData>> => createQueryKey('postApiNoteSearch', options, true);

export const postApiNoteSearchInfiniteOptions = (options?: Options<PostApiNoteSearchData>) => {
	return infiniteQueryOptions<
		PostApiNoteSearchResponse,
		AxiosError<DefaultError>,
		InfiniteData<PostApiNoteSearchResponse>,
		QueryKey<Options<PostApiNoteSearchData>>,
		| number
		| Pick<QueryKey<Options<PostApiNoteSearchData>>[0], 'body' | 'headers' | 'path' | 'query'>
	>(
		// @ts-ignore
		{
			queryFn: async ({ pageParam, queryKey, signal }) => {
				// @ts-ignore
				const page: Pick<
					QueryKey<Options<PostApiNoteSearchData>>[0],
					'body' | 'headers' | 'path' | 'query'
				> =
					typeof pageParam === 'object'
						? pageParam
						: {
								query: {
									offset: pageParam,
								},
							};
				const params = createInfiniteParams(queryKey, page);
				const { data } = await postApiNoteSearch({
					...options,
					...params,
					signal,
					throwOnError: true,
				});
				return data;
			},
			queryKey: postApiNoteSearchInfiniteQueryKey(options),
		}
	);
};

export const postApiNoteSearchMutation = (options?: Partial<Options<PostApiNoteSearchData>>) => {
	const mutationOptions: UseMutationOptions<
		PostApiNoteSearchResponse,
		AxiosError<DefaultError>,
		Options<PostApiNoteSearchData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await postApiNoteSearch({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const getApiNoteQueryKey = (options?: Options<GetApiNoteData>) =>
	createQueryKey('getApiNote', options);

export const getApiNoteOptions = (options?: Options<GetApiNoteData>) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await getApiNote({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: getApiNoteQueryKey(options),
	});
};

export const getApiNoteInfiniteQueryKey = (
	options?: Options<GetApiNoteData>
): QueryKey<Options<GetApiNoteData>> => createQueryKey('getApiNote', options, true);

export const getApiNoteInfiniteOptions = (options?: Options<GetApiNoteData>) => {
	return infiniteQueryOptions<
		GetApiNoteResponse,
		AxiosError<DefaultError>,
		InfiniteData<GetApiNoteResponse>,
		QueryKey<Options<GetApiNoteData>>,
		number | Pick<QueryKey<Options<GetApiNoteData>>[0], 'body' | 'headers' | 'path' | 'query'>
	>(
		// @ts-ignore
		{
			queryFn: async ({ pageParam, queryKey, signal }) => {
				// @ts-ignore
				const page: Pick<
					QueryKey<Options<GetApiNoteData>>[0],
					'body' | 'headers' | 'path' | 'query'
				> =
					typeof pageParam === 'object'
						? pageParam
						: {
								query: {
									offset: pageParam,
								},
							};
				const params = createInfiniteParams(queryKey, page);
				const { data } = await getApiNote({
					...options,
					...params,
					signal,
					throwOnError: true,
				});
				return data;
			},
			queryKey: getApiNoteInfiniteQueryKey(options),
		}
	);
};

export const postApiNoteQueryKey = (options?: Options<PostApiNoteData>) =>
	createQueryKey('postApiNote', options);

export const postApiNoteOptions = (options?: Options<PostApiNoteData>) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await postApiNote({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: postApiNoteQueryKey(options),
	});
};

export const postApiNoteMutation = (options?: Partial<Options<PostApiNoteData>>) => {
	const mutationOptions: UseMutationOptions<
		PostApiNoteResponse,
		AxiosError<PostApiNoteError>,
		Options<PostApiNoteData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await postApiNote({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

// export const postApiNoteDraftQueryKey = (options?: Options<PostApiNoteDraftData>) =>
// 	createQueryKey('postApiNoteDraft', options);

// export const postApiNoteDraftOptions = (options?: Options<PostApiNoteDraftData>) => {
// 	return queryOptions({
// 		queryFn: async ({ queryKey, signal }) => {
// 			const { data } = await postApiNoteDraft({
// 				...options,
// 				...queryKey[0],
// 				signal,
// 				throwOnError: true,
// 			});
// 			return data;
// 		},
// 		queryKey: postApiNoteDraftQueryKey(options),
// 	});
// };

// export const postApiNoteDraftMutation = (options?: Partial<Options<PostApiNoteDraftData>>) => {
// 	const mutationOptions: UseMutationOptions<
// 		PostApiNoteDraftResponse,
// 		AxiosError<PostApiNoteDraftError>,
// 		Options<PostApiNoteDraftData>
// 	> = {
// 		mutationFn: async (localOptions) => {
// 			const { data } = await postApiNoteDraft({
// 				...options,
// 				...localOptions,
// 				throwOnError: true,
// 			});
// 			return data;
// 		},
// 	};
// 	return mutationOptions;
// };

// export const getApiNoteDraftByEntityIdQueryKey = (
// 	options: Options<GetApiNoteDraftByEntityIdData>
// ) => createQueryKey('getApiNoteDraftByEntityId', options);

// export const getApiNoteDraftByEntityIdOptions = (
// 	options: Options<GetApiNoteDraftByEntityIdData>
// ) => {
// 	return queryOptions({
// 		queryFn: async ({ queryKey, signal }) => {
// 			const { data } = await getApiNoteDraftByEntityId({
// 				...options,
// 				...queryKey[0],
// 				signal,
// 				throwOnError: true,
// 			});
// 			return data;
// 		},
// 		queryKey: getApiNoteDraftByEntityIdQueryKey(options),
// 	});
// };

// export const putApiNoteDraftByEntityIdMutation = (
// 	options?: Partial<Options<PutApiNoteDraftByEntityIdData>>
// ) => {
// 	const mutationOptions: UseMutationOptions<
// 		PutApiNoteDraftByEntityIdResponse,
// 		AxiosError<PutApiNoteDraftByEntityIdError>,
// 		Options<PutApiNoteDraftByEntityIdData>
// 	> = {
// 		mutationFn: async (localOptions) => {
// 			const { data } = await putApiNoteDraftByEntityId({
// 				...options,
// 				...localOptions,
// 				throwOnError: true,
// 			});
// 			return data;
// 		},
// 	};
// 	return mutationOptions;
// };

// export const postApiNoteDraftByEntityIdCommitQueryKey = (
// 	options: Options<PostApiNoteDraftByEntityIdCommitData>
// ) => createQueryKey('postApiNoteDraftByEntityIdCommit', options);

// export const postApiNoteDraftByEntityIdCommitOptions = (
// 	options: Options<PostApiNoteDraftByEntityIdCommitData>
// ) => {
// 	return queryOptions({
// 		queryFn: async ({ queryKey, signal }) => {
// 			const { data } = await postApiNoteDraftByEntityIdCommit({
// 				...options,
// 				...queryKey[0],
// 				signal,
// 				throwOnError: true,
// 			});
// 			return data;
// 		},
// 		queryKey: postApiNoteDraftByEntityIdCommitQueryKey(options),
// 	});
// };

// export const postApiNoteDraftByEntityIdCommitMutation = (
// 	options?: Partial<Options<PostApiNoteDraftByEntityIdCommitData>>
// ) => {
// 	const mutationOptions: UseMutationOptions<
// 		PostApiNoteDraftByEntityIdCommitResponse,
// 		AxiosError<PostApiNoteDraftByEntityIdCommitError>,
// 		Options<PostApiNoteDraftByEntityIdCommitData>
// 	> = {
// 		mutationFn: async (localOptions) => {
// 			const { data } = await postApiNoteDraftByEntityIdCommit({
// 				...options,
// 				...localOptions,
// 				throwOnError: true,
// 			});
// 			return data;
// 		},
// 	};
// 	return mutationOptions;
// };

// export const postApiNoteDraftValidateQueryKey = (options?: Options<PostApiNoteDraftValidateData>) =>
// 	createQueryKey('postApiNoteDraftValidate', options);

// export const postApiNoteDraftValidateOptions = (
// 	options?: Options<PostApiNoteDraftValidateData>
// ) => {
// 	return queryOptions({
// 		queryFn: async ({ queryKey, signal }) => {
// 			const { data } = await postApiNoteDraftValidate({
// 				...options,
// 				...queryKey[0],
// 				signal,
// 				throwOnError: true,
// 			});
// 			return data;
// 		},
// 		queryKey: postApiNoteDraftValidateQueryKey(options),
// 	});
// };

// export const postApiNoteDraftValidateMutation = (
// 	options?: Partial<Options<PostApiNoteDraftValidateData>>
// ) => {
// 	const mutationOptions: UseMutationOptions<
// 		PostApiNoteDraftValidateResponse,
// 		AxiosError<PostApiNoteDraftValidateError>,
// 		Options<PostApiNoteDraftValidateData>
// 	> = {
// 		mutationFn: async (localOptions) => {
// 			const { data } = await postApiNoteDraftValidate({
// 				...options,
// 				...localOptions,
// 				throwOnError: true,
// 			});
// 			return data;
// 		},
// 	};
// 	return mutationOptions;
// };

// export const postApiNoteDraftByEntityIdValidateQueryKey = (
// 	options: Options<PostApiNoteDraftByEntityIdValidateData>
// ) => createQueryKey('postApiNoteDraftByEntityIdValidate', options);

// export const postApiNoteDraftByEntityIdValidateOptions = (
// 	options: Options<PostApiNoteDraftByEntityIdValidateData>
// ) => {
// 	return queryOptions({
// 		queryFn: async ({ queryKey, signal }) => {
// 			const { data } = await postApiNoteDraftByEntityIdValidate({
// 				...options,
// 				...queryKey[0],
// 				signal,
// 				throwOnError: true,
// 			});
// 			return data;
// 		},
// 		queryKey: postApiNoteDraftByEntityIdValidateQueryKey(options),
// 	});
// };

// export const postApiNoteDraftByEntityIdValidateMutation = (
// 	options?: Partial<Options<PostApiNoteDraftByEntityIdValidateData>>
// ) => {
// 	const mutationOptions: UseMutationOptions<
// 		PostApiNoteDraftByEntityIdValidateResponse,
// 		AxiosError<PostApiNoteDraftByEntityIdValidateError>,
// 		Options<PostApiNoteDraftByEntityIdValidateData>
// 	> = {
// 		mutationFn: async (localOptions) => {
// 			const { data } = await postApiNoteDraftByEntityIdValidate({
// 				...options,
// 				...localOptions,
// 				throwOnError: true,
// 			});
// 			return data;
// 		},
// 	};
// 	return mutationOptions;
// };

export const postApiNoteDraftQueryKey = (options?: Options<PostApiNoteDraftData>) =>
	createQueryKey('postApiNoteDraft', options);

export const postApiNoteDraftOptions = (options?: Options<PostApiNoteDraftData>) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await postApiNoteDraft({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: postApiNoteDraftQueryKey(options),
	});
};

export const postApiNoteDraftMutation = (options?: Partial<Options<PostApiNoteDraftData>>) => {
	const mutationOptions: UseMutationOptions<
		PostApiNoteDraftResponse,
		AxiosError<PostApiNoteDraftError>,
		Options<PostApiNoteDraftData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await postApiNoteDraft({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const getApiNoteDraftByEntityIdQueryKey = (
	options: Options<GetApiNoteDraftByEntityIdData>
) => createQueryKey('getApiNoteDraftByEntityId', options);

export const getApiNoteDraftByEntityIdOptions = (
	options: Options<GetApiNoteDraftByEntityIdData>
) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await getApiNoteDraftByEntityId({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: getApiNoteDraftByEntityIdQueryKey(options),
	});
};

export const putApiNoteDraftByEntityIdMutation = (
	options?: Partial<Options<PutApiNoteDraftByEntityIdData>>
) => {
	const mutationOptions: UseMutationOptions<
		PutApiNoteDraftByEntityIdResponse,
		AxiosError<PutApiNoteDraftByEntityIdError>,
		Options<PutApiNoteDraftByEntityIdData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await putApiNoteDraftByEntityId({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const postApiNoteDraftByEntityIdCommitQueryKey = (
	options: Options<PostApiNoteDraftByEntityIdCommitData>
) => createQueryKey('postApiNoteDraftByEntityIdCommit', options);

export const postApiNoteDraftByEntityIdCommitOptions = (
	options: Options<PostApiNoteDraftByEntityIdCommitData>
) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await postApiNoteDraftByEntityIdCommit({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: postApiNoteDraftByEntityIdCommitQueryKey(options),
	});
};

export const postApiNoteDraftByEntityIdCommitMutation = (
	options?: Partial<Options<PostApiNoteDraftByEntityIdCommitData>>
) => {
	const mutationOptions: UseMutationOptions<
		PostApiNoteDraftByEntityIdCommitResponse,
		AxiosError<PostApiNoteDraftByEntityIdCommitError>,
		Options<PostApiNoteDraftByEntityIdCommitData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await postApiNoteDraftByEntityIdCommit({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const postApiNoteDraftValidateQueryKey = (options?: Options<PostApiNoteDraftValidateData>) =>
	createQueryKey('postApiNoteDraftValidate', options);

export const postApiNoteDraftValidateOptions = (
	options?: Options<PostApiNoteDraftValidateData>
) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await postApiNoteDraftValidate({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: postApiNoteDraftValidateQueryKey(options),
	});
};

export const postApiNoteDraftValidateMutation = (
	options?: Partial<Options<PostApiNoteDraftValidateData>>
) => {
	const mutationOptions: UseMutationOptions<
		PostApiNoteDraftValidateResponse,
		AxiosError<PostApiNoteDraftValidateError>,
		Options<PostApiNoteDraftValidateData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await postApiNoteDraftValidate({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const postApiNoteDraftByEntityIdValidateQueryKey = (
	options: Options<PostApiNoteDraftByEntityIdValidateData>
) => createQueryKey('postApiNoteDraftByEntityIdValidate', options);

export const postApiNoteDraftByEntityIdValidateOptions = (
	options: Options<PostApiNoteDraftByEntityIdValidateData>
) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await postApiNoteDraftByEntityIdValidate({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: postApiNoteDraftByEntityIdValidateQueryKey(options),
	});
};

export const postApiNoteDraftByEntityIdValidateMutation = (
	options?: Partial<Options<PostApiNoteDraftByEntityIdValidateData>>
) => {
	const mutationOptions: UseMutationOptions<
		PostApiNoteDraftByEntityIdValidateResponse,
		AxiosError<PostApiNoteDraftByEntityIdValidateError>,
		Options<PostApiNoteDraftByEntityIdValidateData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await postApiNoteDraftByEntityIdValidate({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const getApiOrderByIdQueryKey = (options: Options<GetApiOrderByIdData>) =>
	createQueryKey('getApiOrderById', options);

export const getApiOrderByIdOptions = (options: Options<GetApiOrderByIdData>) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await getApiOrderById({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: getApiOrderByIdQueryKey(options),
	});
};

export const patchApiOrderByIdMutation = (options?: Partial<Options<PatchApiOrderByIdData>>) => {
	const mutationOptions: UseMutationOptions<
		PatchApiOrderByIdResponse,
		AxiosError<PatchApiOrderByIdError>,
		Options<PatchApiOrderByIdData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await patchApiOrderById({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const putApiOrderByIdMutation = (options?: Partial<Options<PutApiOrderByIdData>>) => {
	const mutationOptions: UseMutationOptions<
		PutApiOrderByIdResponse,
		AxiosError<PutApiOrderByIdError>,
		Options<PutApiOrderByIdData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await putApiOrderById({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const postApiOrderSearchQueryKey = (options?: Options<PostApiOrderSearchData>) =>
	createQueryKey('postApiOrderSearch', options);

export const postApiOrderSearchOptions = (options?: Options<PostApiOrderSearchData>) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await postApiOrderSearch({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: postApiOrderSearchQueryKey(options),
	});
};

export const postApiOrderSearchInfiniteQueryKey = (
	options?: Options<PostApiOrderSearchData>
): QueryKey<Options<PostApiOrderSearchData>> => createQueryKey('postApiOrderSearch', options, true);

export const postApiOrderSearchInfiniteOptions = (options?: Options<PostApiOrderSearchData>) => {
	return infiniteQueryOptions<
		PostApiOrderSearchResponse,
		AxiosError<DefaultError>,
		InfiniteData<PostApiOrderSearchResponse>,
		QueryKey<Options<PostApiOrderSearchData>>,
		| number
		| Pick<QueryKey<Options<PostApiOrderSearchData>>[0], 'body' | 'headers' | 'path' | 'query'>
	>(
		// @ts-ignore
		{
			queryFn: async ({ pageParam, queryKey, signal }) => {
				// @ts-ignore
				const page: Pick<
					QueryKey<Options<PostApiOrderSearchData>>[0],
					'body' | 'headers' | 'path' | 'query'
				> =
					typeof pageParam === 'object'
						? pageParam
						: {
								query: {
									offset: pageParam,
								},
							};
				const params = createInfiniteParams(queryKey, page);
				const { data } = await postApiOrderSearch({
					...options,
					...params,
					signal,
					throwOnError: true,
				});
				return data;
			},
			queryKey: postApiOrderSearchInfiniteQueryKey(options),
		}
	);
};

export const postApiOrderSearchMutation = (options?: Partial<Options<PostApiOrderSearchData>>) => {
	const mutationOptions: UseMutationOptions<
		PostApiOrderSearchResponse,
		AxiosError<DefaultError>,
		Options<PostApiOrderSearchData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await postApiOrderSearch({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const getApiOrderQueryKey = (options?: Options<GetApiOrderData>) =>
	createQueryKey('getApiOrder', options);

export const getApiOrderOptions = (options?: Options<GetApiOrderData>) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await getApiOrder({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: getApiOrderQueryKey(options),
	});
};

export const getApiOrderInfiniteQueryKey = (
	options?: Options<GetApiOrderData>
): QueryKey<Options<GetApiOrderData>> => createQueryKey('getApiOrder', options, true);

export const getApiOrderInfiniteOptions = (options?: Options<GetApiOrderData>) => {
	return infiniteQueryOptions<
		GetApiOrderResponse,
		AxiosError<DefaultError>,
		InfiniteData<GetApiOrderResponse>,
		QueryKey<Options<GetApiOrderData>>,
		number | Pick<QueryKey<Options<GetApiOrderData>>[0], 'body' | 'headers' | 'path' | 'query'>
	>(
		// @ts-ignore
		{
			queryFn: async ({ pageParam, queryKey, signal }) => {
				// @ts-ignore
				const page: Pick<
					QueryKey<Options<GetApiOrderData>>[0],
					'body' | 'headers' | 'path' | 'query'
				> =
					typeof pageParam === 'object'
						? pageParam
						: {
								query: {
									offset: pageParam,
								},
							};
				const params = createInfiniteParams(queryKey, page);
				const { data } = await getApiOrder({
					...options,
					...params,
					signal,
					throwOnError: true,
				});
				return data;
			},
			queryKey: getApiOrderInfiniteQueryKey(options),
		}
	);
};

export const postApiOrderQueryKey = (options?: Options<PostApiOrderData>) =>
	createQueryKey('postApiOrder', options);

export const postApiOrderOptions = (options?: Options<PostApiOrderData>) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await postApiOrder({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: postApiOrderQueryKey(options),
	});
};

export const postApiOrderMutation = (options?: Partial<Options<PostApiOrderData>>) => {
	const mutationOptions: UseMutationOptions<
		PostApiOrderResponse,
		AxiosError<PostApiOrderError>,
		Options<PostApiOrderData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await postApiOrder({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

// export const postApiOrderDraftQueryKey = (options?: Options<PostApiOrderDraftData>) =>
// 	createQueryKey('postApiOrderDraft', options);

// export const postApiOrderDraftOptions = (options?: Options<PostApiOrderDraftData>) => {
// 	return queryOptions({
// 		queryFn: async ({ queryKey, signal }) => {
// 			const { data } = await postApiOrderDraft({
// 				...options,
// 				...queryKey[0],
// 				signal,
// 				throwOnError: true,
// 			});
// 			return data;
// 		},
// 		queryKey: postApiOrderDraftQueryKey(options),
// 	});
// };

// export const postApiOrderDraftMutation = (options?: Partial<Options<PostApiOrderDraftData>>) => {
// 	const mutationOptions: UseMutationOptions<
// 		PostApiOrderDraftResponse,
// 		AxiosError<PostApiOrderDraftError>,
// 		Options<PostApiOrderDraftData>
// 	> = {
// 		mutationFn: async (localOptions) => {
// 			const { data } = await postApiOrderDraft({
// 				...options,
// 				...localOptions,
// 				throwOnError: true,
// 			});
// 			return data;
// 		},
// 	};
// 	return mutationOptions;
// };

// export const getApiOrderDraftByEntityIdQueryKey = (
// 	options: Options<GetApiOrderDraftByEntityIdData>
// ) => createQueryKey('getApiOrderDraftByEntityId', options);

// export const getApiOrderDraftByEntityIdOptions = (
// 	options: Options<GetApiOrderDraftByEntityIdData>
// ) => {
// 	return queryOptions({
// 		queryFn: async ({ queryKey, signal }) => {
// 			const { data } = await getApiOrderDraftByEntityId({
// 				...options,
// 				...queryKey[0],
// 				signal,
// 				throwOnError: true,
// 			});
// 			return data;
// 		},
// 		queryKey: getApiOrderDraftByEntityIdQueryKey(options),
// 	});
// };

// export const putApiOrderDraftByEntityIdMutation = (
// 	options?: Partial<Options<PutApiOrderDraftByEntityIdData>>
// ) => {
// 	const mutationOptions: UseMutationOptions<
// 		PutApiOrderDraftByEntityIdResponse,
// 		AxiosError<PutApiOrderDraftByEntityIdError>,
// 		Options<PutApiOrderDraftByEntityIdData>
// 	> = {
// 		mutationFn: async (localOptions) => {
// 			const { data } = await putApiOrderDraftByEntityId({
// 				...options,
// 				...localOptions,
// 				throwOnError: true,
// 			});
// 			return data;
// 		},
// 	};
// 	return mutationOptions;
// };

// export const postApiOrderDraftByEntityIdCommitQueryKey = (
// 	options: Options<PostApiOrderDraftByEntityIdCommitData>
// ) => createQueryKey('postApiOrderDraftByEntityIdCommit', options);

// export const postApiOrderDraftByEntityIdCommitOptions = (
// 	options: Options<PostApiOrderDraftByEntityIdCommitData>
// ) => {
// 	return queryOptions({
// 		queryFn: async ({ queryKey, signal }) => {
// 			const { data } = await postApiOrderDraftByEntityIdCommit({
// 				...options,
// 				...queryKey[0],
// 				signal,
// 				throwOnError: true,
// 			});
// 			return data;
// 		},
// 		queryKey: postApiOrderDraftByEntityIdCommitQueryKey(options),
// 	});
// };

// export const postApiOrderDraftByEntityIdCommitMutation = (
// 	options?: Partial<Options<PostApiOrderDraftByEntityIdCommitData>>
// ) => {
// 	const mutationOptions: UseMutationOptions<
// 		PostApiOrderDraftByEntityIdCommitResponse,
// 		AxiosError<PostApiOrderDraftByEntityIdCommitError>,
// 		Options<PostApiOrderDraftByEntityIdCommitData>
// 	> = {
// 		mutationFn: async (localOptions) => {
// 			const { data } = await postApiOrderDraftByEntityIdCommit({
// 				...options,
// 				...localOptions,
// 				throwOnError: true,
// 			});
// 			return data;
// 		},
// 	};
// 	return mutationOptions;
// };

// export const postApiOrderDraftValidateQueryKey = (
// 	options?: Options<PostApiOrderDraftValidateData>
// ) => createQueryKey('postApiOrderDraftValidate', options);

// export const postApiOrderDraftValidateOptions = (
// 	options?: Options<PostApiOrderDraftValidateData>
// ) => {
// 	return queryOptions({
// 		queryFn: async ({ queryKey, signal }) => {
// 			const { data } = await postApiOrderDraftValidate({
// 				...options,
// 				...queryKey[0],
// 				signal,
// 				throwOnError: true,
// 			});
// 			return data;
// 		},
// 		queryKey: postApiOrderDraftValidateQueryKey(options),
// 	});
// };

// export const postApiOrderDraftValidateMutation = (
// 	options?: Partial<Options<PostApiOrderDraftValidateData>>
// ) => {
// 	const mutationOptions: UseMutationOptions<
// 		PostApiOrderDraftValidateResponse,
// 		AxiosError<PostApiOrderDraftValidateError>,
// 		Options<PostApiOrderDraftValidateData>
// 	> = {
// 		mutationFn: async (localOptions) => {
// 			const { data } = await postApiOrderDraftValidate({
// 				...options,
// 				...localOptions,
// 				throwOnError: true,
// 			});
// 			return data;
// 		},
// 	};
// 	return mutationOptions;
// };

// export const postApiOrderDraftByEntityIdValidateQueryKey = (
// 	options: Options<PostApiOrderDraftByEntityIdValidateData>
// ) => createQueryKey('postApiOrderDraftByEntityIdValidate', options);

// export const postApiOrderDraftByEntityIdValidateOptions = (
// 	options: Options<PostApiOrderDraftByEntityIdValidateData>
// ) => {
// 	return queryOptions({
// 		queryFn: async ({ queryKey, signal }) => {
// 			const { data } = await postApiOrderDraftByEntityIdValidate({
// 				...options,
// 				...queryKey[0],
// 				signal,
// 				throwOnError: true,
// 			});
// 			return data;
// 		},
// 		queryKey: postApiOrderDraftByEntityIdValidateQueryKey(options),
// 	});
// };

// export const postApiOrderDraftByEntityIdValidateMutation = (
// 	options?: Partial<Options<PostApiOrderDraftByEntityIdValidateData>>
// ) => {
// 	const mutationOptions: UseMutationOptions<
// 		PostApiOrderDraftByEntityIdValidateResponse,
// 		AxiosError<PostApiOrderDraftByEntityIdValidateError>,
// 		Options<PostApiOrderDraftByEntityIdValidateData>
// 	> = {
// 		mutationFn: async (localOptions) => {
// 			const { data } = await postApiOrderDraftByEntityIdValidate({
// 				...options,
// 				...localOptions,
// 				throwOnError: true,
// 			});
// 			return data;
// 		},
// 	};
// 	return mutationOptions;
// };

export const postApiOrderDraftQueryKey = (options?: Options<PostApiOrderDraftData>) =>
	createQueryKey('postApiOrderDraft', options);

export const postApiOrderDraftOptions = (options?: Options<PostApiOrderDraftData>) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await postApiOrderDraft({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: postApiOrderDraftQueryKey(options),
	});
};

export const postApiOrderDraftMutation = (options?: Partial<Options<PostApiOrderDraftData>>) => {
	const mutationOptions: UseMutationOptions<
		PostApiOrderDraftResponse,
		AxiosError<PostApiOrderDraftError>,
		Options<PostApiOrderDraftData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await postApiOrderDraft({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const getApiOrderDraftByEntityIdQueryKey = (
	options: Options<GetApiOrderDraftByEntityIdData>
) => createQueryKey('getApiOrderDraftByEntityId', options);

export const getApiOrderDraftByEntityIdOptions = (
	options: Options<GetApiOrderDraftByEntityIdData>
) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await getApiOrderDraftByEntityId({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: getApiOrderDraftByEntityIdQueryKey(options),
	});
};

export const putApiOrderDraftByEntityIdMutation = (
	options?: Partial<Options<PutApiOrderDraftByEntityIdData>>
) => {
	const mutationOptions: UseMutationOptions<
		PutApiOrderDraftByEntityIdResponse,
		AxiosError<PutApiOrderDraftByEntityIdError>,
		Options<PutApiOrderDraftByEntityIdData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await putApiOrderDraftByEntityId({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const postApiOrderDraftByEntityIdCommitQueryKey = (
	options: Options<PostApiOrderDraftByEntityIdCommitData>
) => createQueryKey('postApiOrderDraftByEntityIdCommit', options);

export const postApiOrderDraftByEntityIdCommitOptions = (
	options: Options<PostApiOrderDraftByEntityIdCommitData>
) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await postApiOrderDraftByEntityIdCommit({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: postApiOrderDraftByEntityIdCommitQueryKey(options),
	});
};

export const postApiOrderDraftByEntityIdCommitMutation = (
	options?: Partial<Options<PostApiOrderDraftByEntityIdCommitData>>
) => {
	const mutationOptions: UseMutationOptions<
		PostApiOrderDraftByEntityIdCommitResponse,
		AxiosError<PostApiOrderDraftByEntityIdCommitError>,
		Options<PostApiOrderDraftByEntityIdCommitData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await postApiOrderDraftByEntityIdCommit({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const postApiOrderDraftValidateQueryKey = (
	options?: Options<PostApiOrderDraftValidateData>
) => createQueryKey('postApiOrderDraftValidate', options);

export const postApiOrderDraftValidateOptions = (
	options?: Options<PostApiOrderDraftValidateData>
) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await postApiOrderDraftValidate({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: postApiOrderDraftValidateQueryKey(options),
	});
};

export const postApiOrderDraftValidateMutation = (
	options?: Partial<Options<PostApiOrderDraftValidateData>>
) => {
	const mutationOptions: UseMutationOptions<
		PostApiOrderDraftValidateResponse,
		AxiosError<PostApiOrderDraftValidateError>,
		Options<PostApiOrderDraftValidateData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await postApiOrderDraftValidate({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const postApiOrderDraftByEntityIdValidateQueryKey = (
	options: Options<PostApiOrderDraftByEntityIdValidateData>
) => createQueryKey('postApiOrderDraftByEntityIdValidate', options);

export const postApiOrderDraftByEntityIdValidateOptions = (
	options: Options<PostApiOrderDraftByEntityIdValidateData>
) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await postApiOrderDraftByEntityIdValidate({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: postApiOrderDraftByEntityIdValidateQueryKey(options),
	});
};

export const postApiOrderDraftByEntityIdValidateMutation = (
	options?: Partial<Options<PostApiOrderDraftByEntityIdValidateData>>
) => {
	const mutationOptions: UseMutationOptions<
		PostApiOrderDraftByEntityIdValidateResponse,
		AxiosError<PostApiOrderDraftByEntityIdValidateError>,
		Options<PostApiOrderDraftByEntityIdValidateData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await postApiOrderDraftByEntityIdValidate({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const getApiPatientByIdQueryKey = (options: Options<GetApiPatientByIdData>) =>
	createQueryKey('getApiPatientById', options);

export const getApiPatientByIdOptions = (options: Options<GetApiPatientByIdData>) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await getApiPatientById({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: getApiPatientByIdQueryKey(options),
	});
};

export const patchApiPatientByIdMutation = (
	options?: Partial<Options<PatchApiPatientByIdData>>
) => {
	const mutationOptions: UseMutationOptions<
		PatchApiPatientByIdResponse,
		AxiosError<PatchApiPatientByIdError>,
		Options<PatchApiPatientByIdData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await patchApiPatientById({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const putApiPatientByIdMutation = (options?: Partial<Options<PutApiPatientByIdData>>) => {
	const mutationOptions: UseMutationOptions<
		PutApiPatientByIdResponse,
		AxiosError<PutApiPatientByIdError>,
		Options<PutApiPatientByIdData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await putApiPatientById({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const postApiPatientSearchQueryKey = (options?: Options<PostApiPatientSearchData>) =>
	createQueryKey('postApiPatientSearch', options);

export const postApiPatientSearchOptions = (options?: Options<PostApiPatientSearchData>) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await postApiPatientSearch({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: postApiPatientSearchQueryKey(options),
	});
};

export const postApiPatientSearchInfiniteQueryKey = (
	options?: Options<PostApiPatientSearchData>
): QueryKey<Options<PostApiPatientSearchData>> =>
	createQueryKey('postApiPatientSearch', options, true);

export const postApiPatientSearchInfiniteOptions = (
	options?: Options<PostApiPatientSearchData>
) => {
	return infiniteQueryOptions<
		PostApiPatientSearchResponse,
		AxiosError<DefaultError>,
		InfiniteData<PostApiPatientSearchResponse>,
		QueryKey<Options<PostApiPatientSearchData>>,
		| number
		| Pick<QueryKey<Options<PostApiPatientSearchData>>[0], 'body' | 'headers' | 'path' | 'query'>
	>(
		// @ts-ignore
		{
			queryFn: async ({ pageParam, queryKey, signal }) => {
				// @ts-ignore
				const page: Pick<
					QueryKey<Options<PostApiPatientSearchData>>[0],
					'body' | 'headers' | 'path' | 'query'
				> =
					typeof pageParam === 'object'
						? pageParam
						: {
								query: {
									offset: pageParam,
								},
							};
				const params = createInfiniteParams(queryKey, page);
				const { data } = await postApiPatientSearch({
					...options,
					...params,
					signal,
					throwOnError: true,
				});
				return data;
			},
			queryKey: postApiPatientSearchInfiniteQueryKey(options),
		}
	);
};

export const postApiPatientSearchMutation = (
	options?: Partial<Options<PostApiPatientSearchData>>
) => {
	const mutationOptions: UseMutationOptions<
		PostApiPatientSearchResponse,
		AxiosError<DefaultError>,
		Options<PostApiPatientSearchData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await postApiPatientSearch({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const getApiPatientQueryKey = (options?: Options<GetApiPatientData>) =>
	createQueryKey('getApiPatient', options);

export const getApiPatientOptions = (options?: Options<GetApiPatientData>) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await getApiPatient({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: getApiPatientQueryKey(options),
	});
};

export const getApiPatientInfiniteQueryKey = (
	options?: Options<GetApiPatientData>
): QueryKey<Options<GetApiPatientData>> => createQueryKey('getApiPatient', options, true);

export const getApiPatientInfiniteOptions = (options?: Options<GetApiPatientData>) => {
	return infiniteQueryOptions<
		GetApiPatientResponse,
		AxiosError<DefaultError>,
		InfiniteData<GetApiPatientResponse>,
		QueryKey<Options<GetApiPatientData>>,
		number | Pick<QueryKey<Options<GetApiPatientData>>[0], 'body' | 'headers' | 'path' | 'query'>
	>(
		// @ts-ignore
		{
			queryFn: async ({ pageParam, queryKey, signal }) => {
				// @ts-ignore
				const page: Pick<
					QueryKey<Options<GetApiPatientData>>[0],
					'body' | 'headers' | 'path' | 'query'
				> =
					typeof pageParam === 'object'
						? pageParam
						: {
								query: {
									offset: pageParam,
								},
							};
				const params = createInfiniteParams(queryKey, page);
				const { data } = await getApiPatient({
					...options,
					...params,
					signal,
					throwOnError: true,
				});
				return data;
			},
			queryKey: getApiPatientInfiniteQueryKey(options),
		}
	);
};

export const postApiPatientQueryKey = (options?: Options<PostApiPatientData>) =>
	createQueryKey('postApiPatient', options);

export const postApiPatientOptions = (options?: Options<PostApiPatientData>) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await postApiPatient({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: postApiPatientQueryKey(options),
	});
};

export const postApiPatientMutation = (options?: Partial<Options<PostApiPatientData>>) => {
	const mutationOptions: UseMutationOptions<
		PostApiPatientResponse,
		AxiosError<PostApiPatientError>,
		Options<PostApiPatientData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await postApiPatient({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

// export const postApiPatientDraftQueryKey = (options?: Options<PostApiPatientDraftData>) =>
// 	createQueryKey('postApiPatientDraft', options);

// export const postApiPatientDraftOptions = (options?: Options<PostApiPatientDraftData>) => {
// 	return queryOptions({
// 		queryFn: async ({ queryKey, signal }) => {
// 			const { data } = await postApiPatientDraft({
// 				...options,
// 				...queryKey[0],
// 				signal,
// 				throwOnError: true,
// 			});
// 			return data;
// 		},
// 		queryKey: postApiPatientDraftQueryKey(options),
// 	});
// };

// export const postApiPatientDraftMutation = (
// 	options?: Partial<Options<PostApiPatientDraftData>>
// ) => {
// 	const mutationOptions: UseMutationOptions<
// 		PostApiPatientDraftResponse,
// 		AxiosError<PostApiPatientDraftError>,
// 		Options<PostApiPatientDraftData>
// 	> = {
// 		mutationFn: async (localOptions) => {
// 			const { data } = await postApiPatientDraft({
// 				...options,
// 				...localOptions,
// 				throwOnError: true,
// 			});
// 			return data;
// 		},
// 	};
// 	return mutationOptions;
// };

// export const getApiPatientDraftByEntityIdQueryKey = (
// 	options: Options<GetApiPatientDraftByEntityIdData>
// ) => createQueryKey('getApiPatientDraftByEntityId', options);

// export const getApiPatientDraftByEntityIdOptions = (
// 	options: Options<GetApiPatientDraftByEntityIdData>
// ) => {
// 	return queryOptions({
// 		queryFn: async ({ queryKey, signal }) => {
// 			const { data } = await getApiPatientDraftByEntityId({
// 				...options,
// 				...queryKey[0],
// 				signal,
// 				throwOnError: true,
// 			});
// 			return data;
// 		},
// 		queryKey: getApiPatientDraftByEntityIdQueryKey(options),
// 	});
// };

// export const putApiPatientDraftByEntityIdMutation = (
// 	options?: Partial<Options<PutApiPatientDraftByEntityIdData>>
// ) => {
// 	const mutationOptions: UseMutationOptions<
// 		PutApiPatientDraftByEntityIdResponse,
// 		AxiosError<PutApiPatientDraftByEntityIdError>,
// 		Options<PutApiPatientDraftByEntityIdData>
// 	> = {
// 		mutationFn: async (localOptions) => {
// 			const { data } = await putApiPatientDraftByEntityId({
// 				...options,
// 				...localOptions,
// 				throwOnError: true,
// 			});
// 			return data;
// 		},
// 	};
// 	return mutationOptions;
// };

// export const postApiPatientDraftByEntityIdCommitQueryKey = (
// 	options: Options<PostApiPatientDraftByEntityIdCommitData>
// ) => createQueryKey('postApiPatientDraftByEntityIdCommit', options);

// export const postApiPatientDraftByEntityIdCommitOptions = (
// 	options: Options<PostApiPatientDraftByEntityIdCommitData>
// ) => {
// 	return queryOptions({
// 		queryFn: async ({ queryKey, signal }) => {
// 			const { data } = await postApiPatientDraftByEntityIdCommit({
// 				...options,
// 				...queryKey[0],
// 				signal,
// 				throwOnError: true,
// 			});
// 			return data;
// 		},
// 		queryKey: postApiPatientDraftByEntityIdCommitQueryKey(options),
// 	});
// };

// export const postApiPatientDraftByEntityIdCommitMutation = (
// 	options?: Partial<Options<PostApiPatientDraftByEntityIdCommitData>>
// ) => {
// 	const mutationOptions: UseMutationOptions<
// 		PostApiPatientDraftByEntityIdCommitResponse,
// 		AxiosError<PostApiPatientDraftByEntityIdCommitError>,
// 		Options<PostApiPatientDraftByEntityIdCommitData>
// 	> = {
// 		mutationFn: async (localOptions) => {
// 			const { data } = await postApiPatientDraftByEntityIdCommit({
// 				...options,
// 				...localOptions,
// 				throwOnError: true,
// 			});
// 			return data;
// 		},
// 	};
// 	return mutationOptions;
// };

// export const postApiPatientDraftValidateQueryKey = (
// 	options?: Options<PostApiPatientDraftValidateData>
// ) => createQueryKey('postApiPatientDraftValidate', options);

// export const postApiPatientDraftValidateOptions = (
// 	options?: Options<PostApiPatientDraftValidateData>
// ) => {
// 	return queryOptions({
// 		queryFn: async ({ queryKey, signal }) => {
// 			const { data } = await postApiPatientDraftValidate({
// 				...options,
// 				...queryKey[0],
// 				signal,
// 				throwOnError: true,
// 			});
// 			return data;
// 		},
// 		queryKey: postApiPatientDraftValidateQueryKey(options),
// 	});
// };

// export const postApiPatientDraftValidateMutation = (
// 	options?: Partial<Options<PostApiPatientDraftValidateData>>
// ) => {
// 	const mutationOptions: UseMutationOptions<
// 		PostApiPatientDraftValidateResponse,
// 		AxiosError<PostApiPatientDraftValidateError>,
// 		Options<PostApiPatientDraftValidateData>
// 	> = {
// 		mutationFn: async (localOptions) => {
// 			const { data } = await postApiPatientDraftValidate({
// 				...options,
// 				...localOptions,
// 				throwOnError: true,
// 			});
// 			return data;
// 		},
// 	};
// 	return mutationOptions;
// };

// export const postApiPatientDraftByEntityIdValidateQueryKey = (
// 	options: Options<PostApiPatientDraftByEntityIdValidateData>
// ) => createQueryKey('postApiPatientDraftByEntityIdValidate', options);

// export const postApiPatientDraftByEntityIdValidateOptions = (
// 	options: Options<PostApiPatientDraftByEntityIdValidateData>
// ) => {
// 	return queryOptions({
// 		queryFn: async ({ queryKey, signal }) => {
// 			const { data } = await postApiPatientDraftByEntityIdValidate({
// 				...options,
// 				...queryKey[0],
// 				signal,
// 				throwOnError: true,
// 			});
// 			return data;
// 		},
// 		queryKey: postApiPatientDraftByEntityIdValidateQueryKey(options),
// 	});
// };

// export const postApiPatientDraftByEntityIdValidateMutation = (
// 	options?: Partial<Options<PostApiPatientDraftByEntityIdValidateData>>
// ) => {
// 	const mutationOptions: UseMutationOptions<
// 		PostApiPatientDraftByEntityIdValidateResponse,
// 		AxiosError<PostApiPatientDraftByEntityIdValidateError>,
// 		Options<PostApiPatientDraftByEntityIdValidateData>
// 	> = {
// 		mutationFn: async (localOptions) => {
// 			const { data } = await postApiPatientDraftByEntityIdValidate({
// 				...options,
// 				...localOptions,
// 				throwOnError: true,
// 			});
// 			return data;
// 		},
// 	};
// 	return mutationOptions;
// };

export const postApiPatientDraftQueryKey = (options?: Options<PostApiPatientDraftData>) =>
	createQueryKey('postApiPatientDraft', options);

export const postApiPatientDraftOptions = (options?: Options<PostApiPatientDraftData>) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await postApiPatientDraft({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: postApiPatientDraftQueryKey(options),
	});
};

export const postApiPatientDraftMutation = (
	options?: Partial<Options<PostApiPatientDraftData>>
) => {
	const mutationOptions: UseMutationOptions<
		PostApiPatientDraftResponse,
		AxiosError<PostApiPatientDraftError>,
		Options<PostApiPatientDraftData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await postApiPatientDraft({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const getApiPatientDraftByEntityIdQueryKey = (
	options: Options<GetApiPatientDraftByEntityIdData>
) => createQueryKey('getApiPatientDraftByEntityId', options);

export const getApiPatientDraftByEntityIdOptions = (
	options: Options<GetApiPatientDraftByEntityIdData>
) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await getApiPatientDraftByEntityId({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: getApiPatientDraftByEntityIdQueryKey(options),
	});
};

export const putApiPatientDraftByEntityIdMutation = (
	options?: Partial<Options<PutApiPatientDraftByEntityIdData>>
) => {
	const mutationOptions: UseMutationOptions<
		PutApiPatientDraftByEntityIdResponse,
		AxiosError<PutApiPatientDraftByEntityIdError>,
		Options<PutApiPatientDraftByEntityIdData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await putApiPatientDraftByEntityId({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const postApiPatientDraftByEntityIdCommitQueryKey = (
	options: Options<PostApiPatientDraftByEntityIdCommitData>
) => createQueryKey('postApiPatientDraftByEntityIdCommit', options);

export const postApiPatientDraftByEntityIdCommitOptions = (
	options: Options<PostApiPatientDraftByEntityIdCommitData>
) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await postApiPatientDraftByEntityIdCommit({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: postApiPatientDraftByEntityIdCommitQueryKey(options),
	});
};

export const postApiPatientDraftByEntityIdCommitMutation = (
	options?: Partial<Options<PostApiPatientDraftByEntityIdCommitData>>
) => {
	const mutationOptions: UseMutationOptions<
		PostApiPatientDraftByEntityIdCommitResponse,
		AxiosError<PostApiPatientDraftByEntityIdCommitError>,
		Options<PostApiPatientDraftByEntityIdCommitData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await postApiPatientDraftByEntityIdCommit({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const postApiPatientDraftValidateQueryKey = (
	options?: Options<PostApiPatientDraftValidateData>
) => createQueryKey('postApiPatientDraftValidate', options);

export const postApiPatientDraftValidateOptions = (
	options?: Options<PostApiPatientDraftValidateData>
) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await postApiPatientDraftValidate({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: postApiPatientDraftValidateQueryKey(options),
	});
};

export const postApiPatientDraftValidateMutation = (
	options?: Partial<Options<PostApiPatientDraftValidateData>>
) => {
	const mutationOptions: UseMutationOptions<
		PostApiPatientDraftValidateResponse,
		AxiosError<PostApiPatientDraftValidateError>,
		Options<PostApiPatientDraftValidateData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await postApiPatientDraftValidate({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const postApiPatientDraftByEntityIdValidateQueryKey = (
	options: Options<PostApiPatientDraftByEntityIdValidateData>
) => createQueryKey('postApiPatientDraftByEntityIdValidate', options);

export const postApiPatientDraftByEntityIdValidateOptions = (
	options: Options<PostApiPatientDraftByEntityIdValidateData>
) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await postApiPatientDraftByEntityIdValidate({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: postApiPatientDraftByEntityIdValidateQueryKey(options),
	});
};

export const postApiPatientDraftByEntityIdValidateMutation = (
	options?: Partial<Options<PostApiPatientDraftByEntityIdValidateData>>
) => {
	const mutationOptions: UseMutationOptions<
		PostApiPatientDraftByEntityIdValidateResponse,
		AxiosError<PostApiPatientDraftByEntityIdValidateError>,
		Options<PostApiPatientDraftByEntityIdValidateData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await postApiPatientDraftByEntityIdValidate({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const getApiPatientAppointmentByIdQueryKey = (
	options: Options<GetApiPatientAppointmentByIdData>
) => createQueryKey('getApiPatientAppointmentById', options);

export const getApiPatientAppointmentByIdOptions = (
	options: Options<GetApiPatientAppointmentByIdData>
) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await getApiPatientAppointmentById({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: getApiPatientAppointmentByIdQueryKey(options),
	});
};

export const patchApiPatientAppointmentByIdMutation = (
	options?: Partial<Options<PatchApiPatientAppointmentByIdData>>
) => {
	const mutationOptions: UseMutationOptions<
		PatchApiPatientAppointmentByIdResponse,
		AxiosError<PatchApiPatientAppointmentByIdError>,
		Options<PatchApiPatientAppointmentByIdData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await patchApiPatientAppointmentById({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const putApiPatientAppointmentByIdMutation = (
	options?: Partial<Options<PutApiPatientAppointmentByIdData>>
) => {
	const mutationOptions: UseMutationOptions<
		PutApiPatientAppointmentByIdResponse,
		AxiosError<PutApiPatientAppointmentByIdError>,
		Options<PutApiPatientAppointmentByIdData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await putApiPatientAppointmentById({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const postApiPatientAppointmentSearchQueryKey = (
	options?: Options<PostApiPatientAppointmentSearchData>
) => createQueryKey('postApiPatientAppointmentSearch', options);

export const postApiPatientAppointmentSearchOptions = (
	options?: Options<PostApiPatientAppointmentSearchData>
) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await postApiPatientAppointmentSearch({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: postApiPatientAppointmentSearchQueryKey(options),
	});
};

export const postApiPatientAppointmentSearchInfiniteQueryKey = (
	options?: Options<PostApiPatientAppointmentSearchData>
): QueryKey<Options<PostApiPatientAppointmentSearchData>> =>
	createQueryKey('postApiPatientAppointmentSearch', options, true);

export const postApiPatientAppointmentSearchInfiniteOptions = (
	options?: Options<PostApiPatientAppointmentSearchData>
) => {
	return infiniteQueryOptions<
		PostApiPatientAppointmentSearchResponse,
		AxiosError<DefaultError>,
		InfiniteData<PostApiPatientAppointmentSearchResponse>,
		QueryKey<Options<PostApiPatientAppointmentSearchData>>,
		| number
		| Pick<
				QueryKey<Options<PostApiPatientAppointmentSearchData>>[0],
				'body' | 'headers' | 'path' | 'query'
		  >
	>(
		// @ts-ignore
		{
			queryFn: async ({ pageParam, queryKey, signal }) => {
				// @ts-ignore
				const page: Pick<
					QueryKey<Options<PostApiPatientAppointmentSearchData>>[0],
					'body' | 'headers' | 'path' | 'query'
				> =
					typeof pageParam === 'object'
						? pageParam
						: {
								query: {
									offset: pageParam,
								},
							};
				const params = createInfiniteParams(queryKey, page);
				const { data } = await postApiPatientAppointmentSearch({
					...options,
					...params,
					signal,
					throwOnError: true,
				});
				return data;
			},
			queryKey: postApiPatientAppointmentSearchInfiniteQueryKey(options),
		}
	);
};

export const postApiPatientAppointmentSearchMutation = (
	options?: Partial<Options<PostApiPatientAppointmentSearchData>>
) => {
	const mutationOptions: UseMutationOptions<
		PostApiPatientAppointmentSearchResponse,
		AxiosError<DefaultError>,
		Options<PostApiPatientAppointmentSearchData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await postApiPatientAppointmentSearch({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const getApiPatientAppointmentQueryKey = (options?: Options<GetApiPatientAppointmentData>) =>
	createQueryKey('getApiPatientAppointment', options);

export const getApiPatientAppointmentOptions = (
	options?: Options<GetApiPatientAppointmentData>
) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await getApiPatientAppointment({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: getApiPatientAppointmentQueryKey(options),
	});
};

export const getApiPatientAppointmentInfiniteQueryKey = (
	options?: Options<GetApiPatientAppointmentData>
): QueryKey<Options<GetApiPatientAppointmentData>> =>
	createQueryKey('getApiPatientAppointment', options, true);

export const getApiPatientAppointmentInfiniteOptions = (
	options?: Options<GetApiPatientAppointmentData>
) => {
	return infiniteQueryOptions<
		GetApiPatientAppointmentResponse,
		AxiosError<DefaultError>,
		InfiniteData<GetApiPatientAppointmentResponse>,
		QueryKey<Options<GetApiPatientAppointmentData>>,
		| number
		| Pick<
				QueryKey<Options<GetApiPatientAppointmentData>>[0],
				'body' | 'headers' | 'path' | 'query'
		  >
	>(
		// @ts-ignore
		{
			queryFn: async ({ pageParam, queryKey, signal }) => {
				// @ts-ignore
				const page: Pick<
					QueryKey<Options<GetApiPatientAppointmentData>>[0],
					'body' | 'headers' | 'path' | 'query'
				> =
					typeof pageParam === 'object'
						? pageParam
						: {
								query: {
									offset: pageParam,
								},
							};
				const params = createInfiniteParams(queryKey, page);
				const { data } = await getApiPatientAppointment({
					...options,
					...params,
					signal,
					throwOnError: true,
				});
				return data;
			},
			queryKey: getApiPatientAppointmentInfiniteQueryKey(options),
		}
	);
};

export const postApiPatientAppointmentQueryKey = (
	options?: Options<PostApiPatientAppointmentData>
) => createQueryKey('postApiPatientAppointment', options);

export const postApiPatientAppointmentOptions = (
	options?: Options<PostApiPatientAppointmentData>
) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await postApiPatientAppointment({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: postApiPatientAppointmentQueryKey(options),
	});
};

export const postApiPatientAppointmentMutation = (
	options?: Partial<Options<PostApiPatientAppointmentData>>
) => {
	const mutationOptions: UseMutationOptions<
		PostApiPatientAppointmentResponse,
		AxiosError<PostApiPatientAppointmentError>,
		Options<PostApiPatientAppointmentData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await postApiPatientAppointment({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

// export const postApiPatientAppointmentDraftQueryKey = (
// 	options?: Options<PostApiPatientAppointmentDraftData>
// ) => createQueryKey('postApiPatientAppointmentDraft', options);

// export const postApiPatientAppointmentDraftOptions = (
// 	options?: Options<PostApiPatientAppointmentDraftData>
// ) => {
// 	return queryOptions({
// 		queryFn: async ({ queryKey, signal }) => {
// 			const { data } = await postApiPatientAppointmentDraft({
// 				...options,
// 				...queryKey[0],
// 				signal,
// 				throwOnError: true,
// 			});
// 			return data;
// 		},
// 		queryKey: postApiPatientAppointmentDraftQueryKey(options),
// 	});
// };

// export const postApiPatientAppointmentDraftMutation = (
// 	options?: Partial<Options<PostApiPatientAppointmentDraftData>>
// ) => {
// 	const mutationOptions: UseMutationOptions<
// 		PostApiPatientAppointmentDraftResponse,
// 		AxiosError<PostApiPatientAppointmentDraftError>,
// 		Options<PostApiPatientAppointmentDraftData>
// 	> = {
// 		mutationFn: async (localOptions) => {
// 			const { data } = await postApiPatientAppointmentDraft({
// 				...options,
// 				...localOptions,
// 				throwOnError: true,
// 			});
// 			return data;
// 		},
// 	};
// 	return mutationOptions;
// };

// export const getApiPatientAppointmentDraftByEntityIdQueryKey = (
// 	options: Options<GetApiPatientAppointmentDraftByEntityIdData>
// ) => createQueryKey('getApiPatientAppointmentDraftByEntityId', options);

// export const getApiPatientAppointmentDraftByEntityIdOptions = (
// 	options: Options<GetApiPatientAppointmentDraftByEntityIdData>
// ) => {
// 	return queryOptions({
// 		queryFn: async ({ queryKey, signal }) => {
// 			const { data } = await getApiPatientAppointmentDraftByEntityId({
// 				...options,
// 				...queryKey[0],
// 				signal,
// 				throwOnError: true,
// 			});
// 			return data;
// 		},
// 		queryKey: getApiPatientAppointmentDraftByEntityIdQueryKey(options),
// 	});
// };

// export const putApiPatientAppointmentDraftByEntityIdMutation = (
// 	options?: Partial<Options<PutApiPatientAppointmentDraftByEntityIdData>>
// ) => {
// 	const mutationOptions: UseMutationOptions<
// 		PutApiPatientAppointmentDraftByEntityIdResponse,
// 		AxiosError<PutApiPatientAppointmentDraftByEntityIdError>,
// 		Options<PutApiPatientAppointmentDraftByEntityIdData>
// 	> = {
// 		mutationFn: async (localOptions) => {
// 			const { data } = await putApiPatientAppointmentDraftByEntityId({
// 				...options,
// 				...localOptions,
// 				throwOnError: true,
// 			});
// 			return data;
// 		},
// 	};
// 	return mutationOptions;
// };

// export const postApiPatientAppointmentDraftByEntityIdCommitQueryKey = (
// 	options: Options<PostApiPatientAppointmentDraftByEntityIdCommitData>
// ) => createQueryKey('postApiPatientAppointmentDraftByEntityIdCommit', options);

// export const postApiPatientAppointmentDraftByEntityIdCommitOptions = (
// 	options: Options<PostApiPatientAppointmentDraftByEntityIdCommitData>
// ) => {
// 	return queryOptions({
// 		queryFn: async ({ queryKey, signal }) => {
// 			const { data } = await postApiPatientAppointmentDraftByEntityIdCommit({
// 				...options,
// 				...queryKey[0],
// 				signal,
// 				throwOnError: true,
// 			});
// 			return data;
// 		},
// 		queryKey: postApiPatientAppointmentDraftByEntityIdCommitQueryKey(options),
// 	});
// };

// export const postApiPatientAppointmentDraftByEntityIdCommitMutation = (
// 	options?: Partial<Options<PostApiPatientAppointmentDraftByEntityIdCommitData>>
// ) => {
// 	const mutationOptions: UseMutationOptions<
// 		PostApiPatientAppointmentDraftByEntityIdCommitResponse,
// 		AxiosError<PostApiPatientAppointmentDraftByEntityIdCommitError>,
// 		Options<PostApiPatientAppointmentDraftByEntityIdCommitData>
// 	> = {
// 		mutationFn: async (localOptions) => {
// 			const { data } = await postApiPatientAppointmentDraftByEntityIdCommit({
// 				...options,
// 				...localOptions,
// 				throwOnError: true,
// 			});
// 			return data;
// 		},
// 	};
// 	return mutationOptions;
// };

// export const postApiPatientAppointmentDraftValidateQueryKey = (
// 	options?: Options<PostApiPatientAppointmentDraftValidateData>
// ) => createQueryKey('postApiPatientAppointmentDraftValidate', options);

// export const postApiPatientAppointmentDraftValidateOptions = (
// 	options?: Options<PostApiPatientAppointmentDraftValidateData>
// ) => {
// 	return queryOptions({
// 		queryFn: async ({ queryKey, signal }) => {
// 			const { data } = await postApiPatientAppointmentDraftValidate({
// 				...options,
// 				...queryKey[0],
// 				signal,
// 				throwOnError: true,
// 			});
// 			return data;
// 		},
// 		queryKey: postApiPatientAppointmentDraftValidateQueryKey(options),
// 	});
// };

// export const postApiPatientAppointmentDraftValidateMutation = (
// 	options?: Partial<Options<PostApiPatientAppointmentDraftValidateData>>
// ) => {
// 	const mutationOptions: UseMutationOptions<
// 		PostApiPatientAppointmentDraftValidateResponse,
// 		AxiosError<PostApiPatientAppointmentDraftValidateError>,
// 		Options<PostApiPatientAppointmentDraftValidateData>
// 	> = {
// 		mutationFn: async (localOptions) => {
// 			const { data } = await postApiPatientAppointmentDraftValidate({
// 				...options,
// 				...localOptions,
// 				throwOnError: true,
// 			});
// 			return data;
// 		},
// 	};
// 	return mutationOptions;
// };

// export const postApiPatientAppointmentDraftByEntityIdValidateQueryKey = (
// 	options: Options<PostApiPatientAppointmentDraftByEntityIdValidateData>
// ) => createQueryKey('postApiPatientAppointmentDraftByEntityIdValidate', options);

// export const postApiPatientAppointmentDraftByEntityIdValidateOptions = (
// 	options: Options<PostApiPatientAppointmentDraftByEntityIdValidateData>
// ) => {
// 	return queryOptions({
// 		queryFn: async ({ queryKey, signal }) => {
// 			const { data } = await postApiPatientAppointmentDraftByEntityIdValidate({
// 				...options,
// 				...queryKey[0],
// 				signal,
// 				throwOnError: true,
// 			});
// 			return data;
// 		},
// 		queryKey: postApiPatientAppointmentDraftByEntityIdValidateQueryKey(options),
// 	});
// };

// export const postApiPatientAppointmentDraftByEntityIdValidateMutation = (
// 	options?: Partial<Options<PostApiPatientAppointmentDraftByEntityIdValidateData>>
// ) => {
// 	const mutationOptions: UseMutationOptions<
// 		PostApiPatientAppointmentDraftByEntityIdValidateResponse,
// 		AxiosError<PostApiPatientAppointmentDraftByEntityIdValidateError>,
// 		Options<PostApiPatientAppointmentDraftByEntityIdValidateData>
// 	> = {
// 		mutationFn: async (localOptions) => {
// 			const { data } = await postApiPatientAppointmentDraftByEntityIdValidate({
// 				...options,
// 				...localOptions,
// 				throwOnError: true,
// 			});
// 			return data;
// 		},
// 	};
// 	return mutationOptions;
// };

export const postApiPatientAppointmentDraftQueryKey = (
	options?: Options<PostApiPatientAppointmentDraftData>
) => createQueryKey('postApiPatientAppointmentDraft', options);

export const postApiPatientAppointmentDraftOptions = (
	options?: Options<PostApiPatientAppointmentDraftData>
) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await postApiPatientAppointmentDraft({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: postApiPatientAppointmentDraftQueryKey(options),
	});
};

export const postApiPatientAppointmentDraftMutation = (
	options?: Partial<Options<PostApiPatientAppointmentDraftData>>
) => {
	const mutationOptions: UseMutationOptions<
		PostApiPatientAppointmentDraftResponse,
		AxiosError<PostApiPatientAppointmentDraftError>,
		Options<PostApiPatientAppointmentDraftData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await postApiPatientAppointmentDraft({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const getApiPatientAppointmentDraftByEntityIdQueryKey = (
	options: Options<GetApiPatientAppointmentDraftByEntityIdData>
) => createQueryKey('getApiPatientAppointmentDraftByEntityId', options);

export const getApiPatientAppointmentDraftByEntityIdOptions = (
	options: Options<GetApiPatientAppointmentDraftByEntityIdData>
) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await getApiPatientAppointmentDraftByEntityId({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: getApiPatientAppointmentDraftByEntityIdQueryKey(options),
	});
};

export const putApiPatientAppointmentDraftByEntityIdMutation = (
	options?: Partial<Options<PutApiPatientAppointmentDraftByEntityIdData>>
) => {
	const mutationOptions: UseMutationOptions<
		PutApiPatientAppointmentDraftByEntityIdResponse,
		AxiosError<PutApiPatientAppointmentDraftByEntityIdError>,
		Options<PutApiPatientAppointmentDraftByEntityIdData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await putApiPatientAppointmentDraftByEntityId({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const postApiPatientAppointmentDraftByEntityIdCommitQueryKey = (
	options: Options<PostApiPatientAppointmentDraftByEntityIdCommitData>
) => createQueryKey('postApiPatientAppointmentDraftByEntityIdCommit', options);

export const postApiPatientAppointmentDraftByEntityIdCommitOptions = (
	options: Options<PostApiPatientAppointmentDraftByEntityIdCommitData>
) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await postApiPatientAppointmentDraftByEntityIdCommit({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: postApiPatientAppointmentDraftByEntityIdCommitQueryKey(options),
	});
};

export const postApiPatientAppointmentDraftByEntityIdCommitMutation = (
	options?: Partial<Options<PostApiPatientAppointmentDraftByEntityIdCommitData>>
) => {
	const mutationOptions: UseMutationOptions<
		PostApiPatientAppointmentDraftByEntityIdCommitResponse,
		AxiosError<PostApiPatientAppointmentDraftByEntityIdCommitError>,
		Options<PostApiPatientAppointmentDraftByEntityIdCommitData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await postApiPatientAppointmentDraftByEntityIdCommit({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const postApiPatientAppointmentDraftValidateQueryKey = (
	options?: Options<PostApiPatientAppointmentDraftValidateData>
) => createQueryKey('postApiPatientAppointmentDraftValidate', options);

export const postApiPatientAppointmentDraftValidateOptions = (
	options?: Options<PostApiPatientAppointmentDraftValidateData>
) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await postApiPatientAppointmentDraftValidate({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: postApiPatientAppointmentDraftValidateQueryKey(options),
	});
};

export const postApiPatientAppointmentDraftValidateMutation = (
	options?: Partial<Options<PostApiPatientAppointmentDraftValidateData>>
) => {
	const mutationOptions: UseMutationOptions<
		PostApiPatientAppointmentDraftValidateResponse,
		AxiosError<PostApiPatientAppointmentDraftValidateError>,
		Options<PostApiPatientAppointmentDraftValidateData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await postApiPatientAppointmentDraftValidate({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const postApiPatientAppointmentDraftByEntityIdValidateQueryKey = (
	options: Options<PostApiPatientAppointmentDraftByEntityIdValidateData>
) => createQueryKey('postApiPatientAppointmentDraftByEntityIdValidate', options);

export const postApiPatientAppointmentDraftByEntityIdValidateOptions = (
	options: Options<PostApiPatientAppointmentDraftByEntityIdValidateData>
) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await postApiPatientAppointmentDraftByEntityIdValidate({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: postApiPatientAppointmentDraftByEntityIdValidateQueryKey(options),
	});
};

export const postApiPatientAppointmentDraftByEntityIdValidateMutation = (
	options?: Partial<Options<PostApiPatientAppointmentDraftByEntityIdValidateData>>
) => {
	const mutationOptions: UseMutationOptions<
		PostApiPatientAppointmentDraftByEntityIdValidateResponse,
		AxiosError<PostApiPatientAppointmentDraftByEntityIdValidateError>,
		Options<PostApiPatientAppointmentDraftByEntityIdValidateData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await postApiPatientAppointmentDraftByEntityIdValidate({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const getApiPatientAppointmentConfirmationByIdQueryKey = (
	options: Options<GetApiPatientAppointmentConfirmationByIdData>
) => createQueryKey('getApiPatientAppointmentConfirmationById', options);

export const getApiPatientAppointmentConfirmationByIdOptions = (
	options: Options<GetApiPatientAppointmentConfirmationByIdData>
) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await getApiPatientAppointmentConfirmationById({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: getApiPatientAppointmentConfirmationByIdQueryKey(options),
	});
};

export const patchApiPatientAppointmentConfirmationByIdMutation = (
	options?: Partial<Options<PatchApiPatientAppointmentConfirmationByIdData>>
) => {
	const mutationOptions: UseMutationOptions<
		PatchApiPatientAppointmentConfirmationByIdResponse,
		AxiosError<PatchApiPatientAppointmentConfirmationByIdError>,
		Options<PatchApiPatientAppointmentConfirmationByIdData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await patchApiPatientAppointmentConfirmationById({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const putApiPatientAppointmentConfirmationByIdMutation = (
	options?: Partial<Options<PutApiPatientAppointmentConfirmationByIdData>>
) => {
	const mutationOptions: UseMutationOptions<
		PutApiPatientAppointmentConfirmationByIdResponse,
		AxiosError<PutApiPatientAppointmentConfirmationByIdError>,
		Options<PutApiPatientAppointmentConfirmationByIdData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await putApiPatientAppointmentConfirmationById({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const postApiPatientAppointmentConfirmationSearchQueryKey = (
	options?: Options<PostApiPatientAppointmentConfirmationSearchData>
) => createQueryKey('postApiPatientAppointmentConfirmationSearch', options);

export const postApiPatientAppointmentConfirmationSearchOptions = (
	options?: Options<PostApiPatientAppointmentConfirmationSearchData>
) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await postApiPatientAppointmentConfirmationSearch({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: postApiPatientAppointmentConfirmationSearchQueryKey(options),
	});
};

export const postApiPatientAppointmentConfirmationSearchInfiniteQueryKey = (
	options?: Options<PostApiPatientAppointmentConfirmationSearchData>
): QueryKey<Options<PostApiPatientAppointmentConfirmationSearchData>> =>
	createQueryKey('postApiPatientAppointmentConfirmationSearch', options, true);

export const postApiPatientAppointmentConfirmationSearchInfiniteOptions = (
	options?: Options<PostApiPatientAppointmentConfirmationSearchData>
) => {
	return infiniteQueryOptions<
		PostApiPatientAppointmentConfirmationSearchResponse,
		AxiosError<DefaultError>,
		InfiniteData<PostApiPatientAppointmentConfirmationSearchResponse>,
		QueryKey<Options<PostApiPatientAppointmentConfirmationSearchData>>,
		| number
		| Pick<
				QueryKey<Options<PostApiPatientAppointmentConfirmationSearchData>>[0],
				'body' | 'headers' | 'path' | 'query'
		  >
	>(
		// @ts-ignore
		{
			queryFn: async ({ pageParam, queryKey, signal }) => {
				// @ts-ignore
				const page: Pick<
					QueryKey<Options<PostApiPatientAppointmentConfirmationSearchData>>[0],
					'body' | 'headers' | 'path' | 'query'
				> =
					typeof pageParam === 'object'
						? pageParam
						: {
								query: {
									offset: pageParam,
								},
							};
				const params = createInfiniteParams(queryKey, page);
				const { data } = await postApiPatientAppointmentConfirmationSearch({
					...options,
					...params,
					signal,
					throwOnError: true,
				});
				return data;
			},
			queryKey: postApiPatientAppointmentConfirmationSearchInfiniteQueryKey(options),
		}
	);
};

export const postApiPatientAppointmentConfirmationSearchMutation = (
	options?: Partial<Options<PostApiPatientAppointmentConfirmationSearchData>>
) => {
	const mutationOptions: UseMutationOptions<
		PostApiPatientAppointmentConfirmationSearchResponse,
		AxiosError<DefaultError>,
		Options<PostApiPatientAppointmentConfirmationSearchData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await postApiPatientAppointmentConfirmationSearch({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const getApiPatientAppointmentConfirmationQueryKey = (
	options?: Options<GetApiPatientAppointmentConfirmationData>
) => createQueryKey('getApiPatientAppointmentConfirmation', options);

export const getApiPatientAppointmentConfirmationOptions = (
	options?: Options<GetApiPatientAppointmentConfirmationData>
) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await getApiPatientAppointmentConfirmation({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: getApiPatientAppointmentConfirmationQueryKey(options),
	});
};

export const getApiPatientAppointmentConfirmationInfiniteQueryKey = (
	options?: Options<GetApiPatientAppointmentConfirmationData>
): QueryKey<Options<GetApiPatientAppointmentConfirmationData>> =>
	createQueryKey('getApiPatientAppointmentConfirmation', options, true);

export const getApiPatientAppointmentConfirmationInfiniteOptions = (
	options?: Options<GetApiPatientAppointmentConfirmationData>
) => {
	return infiniteQueryOptions<
		GetApiPatientAppointmentConfirmationResponse,
		AxiosError<DefaultError>,
		InfiniteData<GetApiPatientAppointmentConfirmationResponse>,
		QueryKey<Options<GetApiPatientAppointmentConfirmationData>>,
		| number
		| Pick<
				QueryKey<Options<GetApiPatientAppointmentConfirmationData>>[0],
				'body' | 'headers' | 'path' | 'query'
		  >
	>(
		// @ts-ignore
		{
			queryFn: async ({ pageParam, queryKey, signal }) => {
				// @ts-ignore
				const page: Pick<
					QueryKey<Options<GetApiPatientAppointmentConfirmationData>>[0],
					'body' | 'headers' | 'path' | 'query'
				> =
					typeof pageParam === 'object'
						? pageParam
						: {
								query: {
									offset: pageParam,
								},
							};
				const params = createInfiniteParams(queryKey, page);
				const { data } = await getApiPatientAppointmentConfirmation({
					...options,
					...params,
					signal,
					throwOnError: true,
				});
				return data;
			},
			queryKey: getApiPatientAppointmentConfirmationInfiniteQueryKey(options),
		}
	);
};

export const postApiPatientAppointmentConfirmationQueryKey = (
	options?: Options<PostApiPatientAppointmentConfirmationData>
) => createQueryKey('postApiPatientAppointmentConfirmation', options);

export const postApiPatientAppointmentConfirmationOptions = (
	options?: Options<PostApiPatientAppointmentConfirmationData>
) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await postApiPatientAppointmentConfirmation({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: postApiPatientAppointmentConfirmationQueryKey(options),
	});
};

export const postApiPatientAppointmentConfirmationMutation = (
	options?: Partial<Options<PostApiPatientAppointmentConfirmationData>>
) => {
	const mutationOptions: UseMutationOptions<
		PostApiPatientAppointmentConfirmationResponse,
		AxiosError<PostApiPatientAppointmentConfirmationError>,
		Options<PostApiPatientAppointmentConfirmationData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await postApiPatientAppointmentConfirmation({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

// export const postApiPatientAppointmentConfirmationDraftQueryKey = (
// 	options?: Options<PostApiPatientAppointmentConfirmationDraftData>
// ) => createQueryKey('postApiPatientAppointmentConfirmationDraft', options);

// export const postApiPatientAppointmentConfirmationDraftOptions = (
// 	options?: Options<PostApiPatientAppointmentConfirmationDraftData>
// ) => {
// 	return queryOptions({
// 		queryFn: async ({ queryKey, signal }) => {
// 			const { data } = await postApiPatientAppointmentConfirmationDraft({
// 				...options,
// 				...queryKey[0],
// 				signal,
// 				throwOnError: true,
// 			});
// 			return data;
// 		},
// 		queryKey: postApiPatientAppointmentConfirmationDraftQueryKey(options),
// 	});
// };

// export const postApiPatientAppointmentConfirmationDraftMutation = (
// 	options?: Partial<Options<PostApiPatientAppointmentConfirmationDraftData>>
// ) => {
// 	const mutationOptions: UseMutationOptions<
// 		PostApiPatientAppointmentConfirmationDraftResponse,
// 		AxiosError<PostApiPatientAppointmentConfirmationDraftError>,
// 		Options<PostApiPatientAppointmentConfirmationDraftData>
// 	> = {
// 		mutationFn: async (localOptions) => {
// 			const { data } = await postApiPatientAppointmentConfirmationDraft({
// 				...options,
// 				...localOptions,
// 				throwOnError: true,
// 			});
// 			return data;
// 		},
// 	};
// 	return mutationOptions;
// };

// export const getApiPatientAppointmentConfirmationDraftByEntityIdQueryKey = (
// 	options: Options<GetApiPatientAppointmentConfirmationDraftByEntityIdData>
// ) => createQueryKey('getApiPatientAppointmentConfirmationDraftByEntityId', options);

// export const getApiPatientAppointmentConfirmationDraftByEntityIdOptions = (
// 	options: Options<GetApiPatientAppointmentConfirmationDraftByEntityIdData>
// ) => {
// 	return queryOptions({
// 		queryFn: async ({ queryKey, signal }) => {
// 			const { data } = await getApiPatientAppointmentConfirmationDraftByEntityId({
// 				...options,
// 				...queryKey[0],
// 				signal,
// 				throwOnError: true,
// 			});
// 			return data;
// 		},
// 		queryKey: getApiPatientAppointmentConfirmationDraftByEntityIdQueryKey(options),
// 	});
// };

// export const putApiPatientAppointmentConfirmationDraftByEntityIdMutation = (
// 	options?: Partial<Options<PutApiPatientAppointmentConfirmationDraftByEntityIdData>>
// ) => {
// 	const mutationOptions: UseMutationOptions<
// 		PutApiPatientAppointmentConfirmationDraftByEntityIdResponse,
// 		AxiosError<PutApiPatientAppointmentConfirmationDraftByEntityIdError>,
// 		Options<PutApiPatientAppointmentConfirmationDraftByEntityIdData>
// 	> = {
// 		mutationFn: async (localOptions) => {
// 			const { data } = await putApiPatientAppointmentConfirmationDraftByEntityId({
// 				...options,
// 				...localOptions,
// 				throwOnError: true,
// 			});
// 			return data;
// 		},
// 	};
// 	return mutationOptions;
// };

// export const postApiPatientAppointmentConfirmationDraftByEntityIdCommitQueryKey = (
// 	options: Options<PostApiPatientAppointmentConfirmationDraftByEntityIdCommitData>
// ) => createQueryKey('postApiPatientAppointmentConfirmationDraftByEntityIdCommit', options);

// export const postApiPatientAppointmentConfirmationDraftByEntityIdCommitOptions = (
// 	options: Options<PostApiPatientAppointmentConfirmationDraftByEntityIdCommitData>
// ) => {
// 	return queryOptions({
// 		queryFn: async ({ queryKey, signal }) => {
// 			const { data } = await postApiPatientAppointmentConfirmationDraftByEntityIdCommit({
// 				...options,
// 				...queryKey[0],
// 				signal,
// 				throwOnError: true,
// 			});
// 			return data;
// 		},
// 		queryKey: postApiPatientAppointmentConfirmationDraftByEntityIdCommitQueryKey(options),
// 	});
// };

// export const postApiPatientAppointmentConfirmationDraftByEntityIdCommitMutation = (
// 	options?: Partial<Options<PostApiPatientAppointmentConfirmationDraftByEntityIdCommitData>>
// ) => {
// 	const mutationOptions: UseMutationOptions<
// 		PostApiPatientAppointmentConfirmationDraftByEntityIdCommitResponse,
// 		AxiosError<PostApiPatientAppointmentConfirmationDraftByEntityIdCommitError>,
// 		Options<PostApiPatientAppointmentConfirmationDraftByEntityIdCommitData>
// 	> = {
// 		mutationFn: async (localOptions) => {
// 			const { data } = await postApiPatientAppointmentConfirmationDraftByEntityIdCommit({
// 				...options,
// 				...localOptions,
// 				throwOnError: true,
// 			});
// 			return data;
// 		},
// 	};
// 	return mutationOptions;
// };

// export const postApiPatientAppointmentConfirmationDraftValidateQueryKey = (
// 	options?: Options<PostApiPatientAppointmentConfirmationDraftValidateData>
// ) => createQueryKey('postApiPatientAppointmentConfirmationDraftValidate', options);

// export const postApiPatientAppointmentConfirmationDraftValidateOptions = (
// 	options?: Options<PostApiPatientAppointmentConfirmationDraftValidateData>
// ) => {
// 	return queryOptions({
// 		queryFn: async ({ queryKey, signal }) => {
// 			const { data } = await postApiPatientAppointmentConfirmationDraftValidate({
// 				...options,
// 				...queryKey[0],
// 				signal,
// 				throwOnError: true,
// 			});
// 			return data;
// 		},
// 		queryKey: postApiPatientAppointmentConfirmationDraftValidateQueryKey(options),
// 	});
// };

// export const postApiPatientAppointmentConfirmationDraftValidateMutation = (
// 	options?: Partial<Options<PostApiPatientAppointmentConfirmationDraftValidateData>>
// ) => {
// 	const mutationOptions: UseMutationOptions<
// 		PostApiPatientAppointmentConfirmationDraftValidateResponse,
// 		AxiosError<PostApiPatientAppointmentConfirmationDraftValidateError>,
// 		Options<PostApiPatientAppointmentConfirmationDraftValidateData>
// 	> = {
// 		mutationFn: async (localOptions) => {
// 			const { data } = await postApiPatientAppointmentConfirmationDraftValidate({
// 				...options,
// 				...localOptions,
// 				throwOnError: true,
// 			});
// 			return data;
// 		},
// 	};
// 	return mutationOptions;
// };

// export const postApiPatientAppointmentConfirmationDraftByEntityIdValidateQueryKey = (
// 	options: Options<PostApiPatientAppointmentConfirmationDraftByEntityIdValidateData>
// ) => createQueryKey('postApiPatientAppointmentConfirmationDraftByEntityIdValidate', options);

// export const postApiPatientAppointmentConfirmationDraftByEntityIdValidateOptions = (
// 	options: Options<PostApiPatientAppointmentConfirmationDraftByEntityIdValidateData>
// ) => {
// 	return queryOptions({
// 		queryFn: async ({ queryKey, signal }) => {
// 			const { data } = await postApiPatientAppointmentConfirmationDraftByEntityIdValidate({
// 				...options,
// 				...queryKey[0],
// 				signal,
// 				throwOnError: true,
// 			});
// 			return data;
// 		},
// 		queryKey: postApiPatientAppointmentConfirmationDraftByEntityIdValidateQueryKey(options),
// 	});
// };

// export const postApiPatientAppointmentConfirmationDraftByEntityIdValidateMutation = (
// 	options?: Partial<Options<PostApiPatientAppointmentConfirmationDraftByEntityIdValidateData>>
// ) => {
// 	const mutationOptions: UseMutationOptions<
// 		PostApiPatientAppointmentConfirmationDraftByEntityIdValidateResponse,
// 		AxiosError<PostApiPatientAppointmentConfirmationDraftByEntityIdValidateError>,
// 		Options<PostApiPatientAppointmentConfirmationDraftByEntityIdValidateData>
// 	> = {
// 		mutationFn: async (localOptions) => {
// 			const { data } = await postApiPatientAppointmentConfirmationDraftByEntityIdValidate({
// 				...options,
// 				...localOptions,
// 				throwOnError: true,
// 			});
// 			return data;
// 		},
// 	};
// 	return mutationOptions;
// };

export const postApiPatientAppointmentConfirmationDraftQueryKey = (
	options?: Options<PostApiPatientAppointmentConfirmationDraftData>
) => createQueryKey('postApiPatientAppointmentConfirmationDraft', options);

export const postApiPatientAppointmentConfirmationDraftOptions = (
	options?: Options<PostApiPatientAppointmentConfirmationDraftData>
) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await postApiPatientAppointmentConfirmationDraft({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: postApiPatientAppointmentConfirmationDraftQueryKey(options),
	});
};

export const postApiPatientAppointmentConfirmationDraftMutation = (
	options?: Partial<Options<PostApiPatientAppointmentConfirmationDraftData>>
) => {
	const mutationOptions: UseMutationOptions<
		PostApiPatientAppointmentConfirmationDraftResponse,
		AxiosError<PostApiPatientAppointmentConfirmationDraftError>,
		Options<PostApiPatientAppointmentConfirmationDraftData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await postApiPatientAppointmentConfirmationDraft({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const getApiPatientAppointmentConfirmationDraftByEntityIdQueryKey = (
	options: Options<GetApiPatientAppointmentConfirmationDraftByEntityIdData>
) => createQueryKey('getApiPatientAppointmentConfirmationDraftByEntityId', options);

export const getApiPatientAppointmentConfirmationDraftByEntityIdOptions = (
	options: Options<GetApiPatientAppointmentConfirmationDraftByEntityIdData>
) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await getApiPatientAppointmentConfirmationDraftByEntityId({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: getApiPatientAppointmentConfirmationDraftByEntityIdQueryKey(options),
	});
};

export const putApiPatientAppointmentConfirmationDraftByEntityIdMutation = (
	options?: Partial<Options<PutApiPatientAppointmentConfirmationDraftByEntityIdData>>
) => {
	const mutationOptions: UseMutationOptions<
		PutApiPatientAppointmentConfirmationDraftByEntityIdResponse,
		AxiosError<PutApiPatientAppointmentConfirmationDraftByEntityIdError>,
		Options<PutApiPatientAppointmentConfirmationDraftByEntityIdData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await putApiPatientAppointmentConfirmationDraftByEntityId({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const postApiPatientAppointmentConfirmationDraftByEntityIdCommitQueryKey = (
	options: Options<PostApiPatientAppointmentConfirmationDraftByEntityIdCommitData>
) => createQueryKey('postApiPatientAppointmentConfirmationDraftByEntityIdCommit', options);

export const postApiPatientAppointmentConfirmationDraftByEntityIdCommitOptions = (
	options: Options<PostApiPatientAppointmentConfirmationDraftByEntityIdCommitData>
) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await postApiPatientAppointmentConfirmationDraftByEntityIdCommit({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: postApiPatientAppointmentConfirmationDraftByEntityIdCommitQueryKey(options),
	});
};

export const postApiPatientAppointmentConfirmationDraftByEntityIdCommitMutation = (
	options?: Partial<Options<PostApiPatientAppointmentConfirmationDraftByEntityIdCommitData>>
) => {
	const mutationOptions: UseMutationOptions<
		PostApiPatientAppointmentConfirmationDraftByEntityIdCommitResponse,
		AxiosError<PostApiPatientAppointmentConfirmationDraftByEntityIdCommitError>,
		Options<PostApiPatientAppointmentConfirmationDraftByEntityIdCommitData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await postApiPatientAppointmentConfirmationDraftByEntityIdCommit({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const postApiPatientAppointmentConfirmationDraftValidateQueryKey = (
	options?: Options<PostApiPatientAppointmentConfirmationDraftValidateData>
) => createQueryKey('postApiPatientAppointmentConfirmationDraftValidate', options);

export const postApiPatientAppointmentConfirmationDraftValidateOptions = (
	options?: Options<PostApiPatientAppointmentConfirmationDraftValidateData>
) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await postApiPatientAppointmentConfirmationDraftValidate({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: postApiPatientAppointmentConfirmationDraftValidateQueryKey(options),
	});
};

export const postApiPatientAppointmentConfirmationDraftValidateMutation = (
	options?: Partial<Options<PostApiPatientAppointmentConfirmationDraftValidateData>>
) => {
	const mutationOptions: UseMutationOptions<
		PostApiPatientAppointmentConfirmationDraftValidateResponse,
		AxiosError<PostApiPatientAppointmentConfirmationDraftValidateError>,
		Options<PostApiPatientAppointmentConfirmationDraftValidateData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await postApiPatientAppointmentConfirmationDraftValidate({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const postApiPatientAppointmentConfirmationDraftByEntityIdValidateQueryKey = (
	options: Options<PostApiPatientAppointmentConfirmationDraftByEntityIdValidateData>
) => createQueryKey('postApiPatientAppointmentConfirmationDraftByEntityIdValidate', options);

export const postApiPatientAppointmentConfirmationDraftByEntityIdValidateOptions = (
	options: Options<PostApiPatientAppointmentConfirmationDraftByEntityIdValidateData>
) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await postApiPatientAppointmentConfirmationDraftByEntityIdValidate({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: postApiPatientAppointmentConfirmationDraftByEntityIdValidateQueryKey(options),
	});
};

export const postApiPatientAppointmentConfirmationDraftByEntityIdValidateMutation = (
	options?: Partial<Options<PostApiPatientAppointmentConfirmationDraftByEntityIdValidateData>>
) => {
	const mutationOptions: UseMutationOptions<
		PostApiPatientAppointmentConfirmationDraftByEntityIdValidateResponse,
		AxiosError<PostApiPatientAppointmentConfirmationDraftByEntityIdValidateError>,
		Options<PostApiPatientAppointmentConfirmationDraftByEntityIdValidateData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await postApiPatientAppointmentConfirmationDraftByEntityIdValidate({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const getApiPhysicianByIdQueryKey = (options: Options<GetApiPhysicianByIdData>) =>
	createQueryKey('getApiPhysicianById', options);

export const getApiPhysicianByIdOptions = (options: Options<GetApiPhysicianByIdData>) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await getApiPhysicianById({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: getApiPhysicianByIdQueryKey(options),
	});
};

export const patchApiPhysicianByIdMutation = (
	options?: Partial<Options<PatchApiPhysicianByIdData>>
) => {
	const mutationOptions: UseMutationOptions<
		PatchApiPhysicianByIdResponse,
		AxiosError<PatchApiPhysicianByIdError>,
		Options<PatchApiPhysicianByIdData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await patchApiPhysicianById({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const putApiPhysicianByIdMutation = (
	options?: Partial<Options<PutApiPhysicianByIdData>>
) => {
	const mutationOptions: UseMutationOptions<
		PutApiPhysicianByIdResponse,
		AxiosError<PutApiPhysicianByIdError>,
		Options<PutApiPhysicianByIdData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await putApiPhysicianById({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const postApiPhysicianSearchQueryKey = (options?: Options<PostApiPhysicianSearchData>) =>
	createQueryKey('postApiPhysicianSearch', options);

export const postApiPhysicianSearchOptions = (options?: Options<PostApiPhysicianSearchData>) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await postApiPhysicianSearch({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: postApiPhysicianSearchQueryKey(options),
	});
};

export const postApiPhysicianSearchInfiniteQueryKey = (
	options?: Options<PostApiPhysicianSearchData>
): QueryKey<Options<PostApiPhysicianSearchData>> =>
	createQueryKey('postApiPhysicianSearch', options, true);

export const postApiPhysicianSearchInfiniteOptions = (
	options?: Options<PostApiPhysicianSearchData>
) => {
	return infiniteQueryOptions<
		PostApiPhysicianSearchResponse,
		AxiosError<DefaultError>,
		InfiniteData<PostApiPhysicianSearchResponse>,
		QueryKey<Options<PostApiPhysicianSearchData>>,
		| number
		| Pick<QueryKey<Options<PostApiPhysicianSearchData>>[0], 'body' | 'headers' | 'path' | 'query'>
	>(
		// @ts-ignore
		{
			queryFn: async ({ pageParam, queryKey, signal }) => {
				// @ts-ignore
				const page: Pick<
					QueryKey<Options<PostApiPhysicianSearchData>>[0],
					'body' | 'headers' | 'path' | 'query'
				> =
					typeof pageParam === 'object'
						? pageParam
						: {
								query: {
									offset: pageParam,
								},
							};
				const params = createInfiniteParams(queryKey, page);
				const { data } = await postApiPhysicianSearch({
					...options,
					...params,
					signal,
					throwOnError: true,
				});
				return data;
			},
			queryKey: postApiPhysicianSearchInfiniteQueryKey(options),
		}
	);
};

export const postApiPhysicianSearchMutation = (
	options?: Partial<Options<PostApiPhysicianSearchData>>
) => {
	const mutationOptions: UseMutationOptions<
		PostApiPhysicianSearchResponse,
		AxiosError<DefaultError>,
		Options<PostApiPhysicianSearchData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await postApiPhysicianSearch({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const getApiPhysicianQueryKey = (options?: Options<GetApiPhysicianData>) =>
	createQueryKey('getApiPhysician', options);

export const getApiPhysicianOptions = (options?: Options<GetApiPhysicianData>) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await getApiPhysician({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: getApiPhysicianQueryKey(options),
	});
};

export const getApiPhysicianInfiniteQueryKey = (
	options?: Options<GetApiPhysicianData>
): QueryKey<Options<GetApiPhysicianData>> => createQueryKey('getApiPhysician', options, true);

export const getApiPhysicianInfiniteOptions = (options?: Options<GetApiPhysicianData>) => {
	return infiniteQueryOptions<
		GetApiPhysicianResponse,
		AxiosError<DefaultError>,
		InfiniteData<GetApiPhysicianResponse>,
		QueryKey<Options<GetApiPhysicianData>>,
		number | Pick<QueryKey<Options<GetApiPhysicianData>>[0], 'body' | 'headers' | 'path' | 'query'>
	>(
		// @ts-ignore
		{
			queryFn: async ({ pageParam, queryKey, signal }) => {
				// @ts-ignore
				const page: Pick<
					QueryKey<Options<GetApiPhysicianData>>[0],
					'body' | 'headers' | 'path' | 'query'
				> =
					typeof pageParam === 'object'
						? pageParam
						: {
								query: {
									offset: pageParam,
								},
							};
				const params = createInfiniteParams(queryKey, page);
				const { data } = await getApiPhysician({
					...options,
					...params,
					signal,
					throwOnError: true,
				});
				return data;
			},
			queryKey: getApiPhysicianInfiniteQueryKey(options),
		}
	);
};

export const postApiPhysicianQueryKey = (options?: Options<PostApiPhysicianData>) =>
	createQueryKey('postApiPhysician', options);

export const postApiPhysicianOptions = (options?: Options<PostApiPhysicianData>) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await postApiPhysician({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: postApiPhysicianQueryKey(options),
	});
};

export const postApiPhysicianMutation = (options?: Partial<Options<PostApiPhysicianData>>) => {
	const mutationOptions: UseMutationOptions<
		PostApiPhysicianResponse,
		AxiosError<PostApiPhysicianError>,
		Options<PostApiPhysicianData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await postApiPhysician({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

// export const postApiPhysicianDraftQueryKey = (options?: Options<PostApiPhysicianDraftData>) =>
// 	createQueryKey('postApiPhysicianDraft', options);

// export const postApiPhysicianDraftOptions = (options?: Options<PostApiPhysicianDraftData>) => {
// 	return queryOptions({
// 		queryFn: async ({ queryKey, signal }) => {
// 			const { data } = await postApiPhysicianDraft({
// 				...options,
// 				...queryKey[0],
// 				signal,
// 				throwOnError: true,
// 			});
// 			return data;
// 		},
// 		queryKey: postApiPhysicianDraftQueryKey(options),
// 	});
// };

// export const postApiPhysicianDraftMutation = (
// 	options?: Partial<Options<PostApiPhysicianDraftData>>
// ) => {
// 	const mutationOptions: UseMutationOptions<
// 		PostApiPhysicianDraftResponse,
// 		AxiosError<PostApiPhysicianDraftError>,
// 		Options<PostApiPhysicianDraftData>
// 	> = {
// 		mutationFn: async (localOptions) => {
// 			const { data } = await postApiPhysicianDraft({
// 				...options,
// 				...localOptions,
// 				throwOnError: true,
// 			});
// 			return data;
// 		},
// 	};
// 	return mutationOptions;
// };

// export const getApiPhysicianDraftByEntityIdQueryKey = (
// 	options: Options<GetApiPhysicianDraftByEntityIdData>
// ) => createQueryKey('getApiPhysicianDraftByEntityId', options);

// export const getApiPhysicianDraftByEntityIdOptions = (
// 	options: Options<GetApiPhysicianDraftByEntityIdData>
// ) => {
// 	return queryOptions({
// 		queryFn: async ({ queryKey, signal }) => {
// 			const { data } = await getApiPhysicianDraftByEntityId({
// 				...options,
// 				...queryKey[0],
// 				signal,
// 				throwOnError: true,
// 			});
// 			return data;
// 		},
// 		queryKey: getApiPhysicianDraftByEntityIdQueryKey(options),
// 	});
// };

// export const putApiPhysicianDraftByEntityIdMutation = (
// 	options?: Partial<Options<PutApiPhysicianDraftByEntityIdData>>
// ) => {
// 	const mutationOptions: UseMutationOptions<
// 		PutApiPhysicianDraftByEntityIdResponse,
// 		AxiosError<PutApiPhysicianDraftByEntityIdError>,
// 		Options<PutApiPhysicianDraftByEntityIdData>
// 	> = {
// 		mutationFn: async (localOptions) => {
// 			const { data } = await putApiPhysicianDraftByEntityId({
// 				...options,
// 				...localOptions,
// 				throwOnError: true,
// 			});
// 			return data;
// 		},
// 	};
// 	return mutationOptions;
// };

// export const postApiPhysicianDraftByEntityIdCommitQueryKey = (
// 	options: Options<PostApiPhysicianDraftByEntityIdCommitData>
// ) => createQueryKey('postApiPhysicianDraftByEntityIdCommit', options);

// export const postApiPhysicianDraftByEntityIdCommitOptions = (
// 	options: Options<PostApiPhysicianDraftByEntityIdCommitData>
// ) => {
// 	return queryOptions({
// 		queryFn: async ({ queryKey, signal }) => {
// 			const { data } = await postApiPhysicianDraftByEntityIdCommit({
// 				...options,
// 				...queryKey[0],
// 				signal,
// 				throwOnError: true,
// 			});
// 			return data;
// 		},
// 		queryKey: postApiPhysicianDraftByEntityIdCommitQueryKey(options),
// 	});
// };

// export const postApiPhysicianDraftByEntityIdCommitMutation = (
// 	options?: Partial<Options<PostApiPhysicianDraftByEntityIdCommitData>>
// ) => {
// 	const mutationOptions: UseMutationOptions<
// 		PostApiPhysicianDraftByEntityIdCommitResponse,
// 		AxiosError<PostApiPhysicianDraftByEntityIdCommitError>,
// 		Options<PostApiPhysicianDraftByEntityIdCommitData>
// 	> = {
// 		mutationFn: async (localOptions) => {
// 			const { data } = await postApiPhysicianDraftByEntityIdCommit({
// 				...options,
// 				...localOptions,
// 				throwOnError: true,
// 			});
// 			return data;
// 		},
// 	};
// 	return mutationOptions;
// };

// export const postApiPhysicianDraftValidateQueryKey = (
// 	options?: Options<PostApiPhysicianDraftValidateData>
// ) => createQueryKey('postApiPhysicianDraftValidate', options);

// export const postApiPhysicianDraftValidateOptions = (
// 	options?: Options<PostApiPhysicianDraftValidateData>
// ) => {
// 	return queryOptions({
// 		queryFn: async ({ queryKey, signal }) => {
// 			const { data } = await postApiPhysicianDraftValidate({
// 				...options,
// 				...queryKey[0],
// 				signal,
// 				throwOnError: true,
// 			});
// 			return data;
// 		},
// 		queryKey: postApiPhysicianDraftValidateQueryKey(options),
// 	});
// };

// export const postApiPhysicianDraftValidateMutation = (
// 	options?: Partial<Options<PostApiPhysicianDraftValidateData>>
// ) => {
// 	const mutationOptions: UseMutationOptions<
// 		PostApiPhysicianDraftValidateResponse,
// 		AxiosError<PostApiPhysicianDraftValidateError>,
// 		Options<PostApiPhysicianDraftValidateData>
// 	> = {
// 		mutationFn: async (localOptions) => {
// 			const { data } = await postApiPhysicianDraftValidate({
// 				...options,
// 				...localOptions,
// 				throwOnError: true,
// 			});
// 			return data;
// 		},
// 	};
// 	return mutationOptions;
// };

// export const postApiPhysicianDraftByEntityIdValidateQueryKey = (
// 	options: Options<PostApiPhysicianDraftByEntityIdValidateData>
// ) => createQueryKey('postApiPhysicianDraftByEntityIdValidate', options);

// export const postApiPhysicianDraftByEntityIdValidateOptions = (
// 	options: Options<PostApiPhysicianDraftByEntityIdValidateData>
// ) => {
// 	return queryOptions({
// 		queryFn: async ({ queryKey, signal }) => {
// 			const { data } = await postApiPhysicianDraftByEntityIdValidate({
// 				...options,
// 				...queryKey[0],
// 				signal,
// 				throwOnError: true,
// 			});
// 			return data;
// 		},
// 		queryKey: postApiPhysicianDraftByEntityIdValidateQueryKey(options),
// 	});
// };

// export const postApiPhysicianDraftByEntityIdValidateMutation = (
// 	options?: Partial<Options<PostApiPhysicianDraftByEntityIdValidateData>>
// ) => {
// 	const mutationOptions: UseMutationOptions<
// 		PostApiPhysicianDraftByEntityIdValidateResponse,
// 		AxiosError<PostApiPhysicianDraftByEntityIdValidateError>,
// 		Options<PostApiPhysicianDraftByEntityIdValidateData>
// 	> = {
// 		mutationFn: async (localOptions) => {
// 			const { data } = await postApiPhysicianDraftByEntityIdValidate({
// 				...options,
// 				...localOptions,
// 				throwOnError: true,
// 			});
// 			return data;
// 		},
// 	};
// 	return mutationOptions;
// };

export const postApiPhysicianDraftQueryKey = (options?: Options<PostApiPhysicianDraftData>) =>
	createQueryKey('postApiPhysicianDraft', options);

export const postApiPhysicianDraftOptions = (options?: Options<PostApiPhysicianDraftData>) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await postApiPhysicianDraft({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: postApiPhysicianDraftQueryKey(options),
	});
};

export const postApiPhysicianDraftMutation = (
	options?: Partial<Options<PostApiPhysicianDraftData>>
) => {
	const mutationOptions: UseMutationOptions<
		PostApiPhysicianDraftResponse,
		AxiosError<PostApiPhysicianDraftError>,
		Options<PostApiPhysicianDraftData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await postApiPhysicianDraft({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const getApiPhysicianDraftByEntityIdQueryKey = (
	options: Options<GetApiPhysicianDraftByEntityIdData>
) => createQueryKey('getApiPhysicianDraftByEntityId', options);

export const getApiPhysicianDraftByEntityIdOptions = (
	options: Options<GetApiPhysicianDraftByEntityIdData>
) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await getApiPhysicianDraftByEntityId({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: getApiPhysicianDraftByEntityIdQueryKey(options),
	});
};

export const putApiPhysicianDraftByEntityIdMutation = (
	options?: Partial<Options<PutApiPhysicianDraftByEntityIdData>>
) => {
	const mutationOptions: UseMutationOptions<
		PutApiPhysicianDraftByEntityIdResponse,
		AxiosError<PutApiPhysicianDraftByEntityIdError>,
		Options<PutApiPhysicianDraftByEntityIdData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await putApiPhysicianDraftByEntityId({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const postApiPhysicianDraftByEntityIdCommitQueryKey = (
	options: Options<PostApiPhysicianDraftByEntityIdCommitData>
) => createQueryKey('postApiPhysicianDraftByEntityIdCommit', options);

export const postApiPhysicianDraftByEntityIdCommitOptions = (
	options: Options<PostApiPhysicianDraftByEntityIdCommitData>
) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await postApiPhysicianDraftByEntityIdCommit({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: postApiPhysicianDraftByEntityIdCommitQueryKey(options),
	});
};

export const postApiPhysicianDraftByEntityIdCommitMutation = (
	options?: Partial<Options<PostApiPhysicianDraftByEntityIdCommitData>>
) => {
	const mutationOptions: UseMutationOptions<
		PostApiPhysicianDraftByEntityIdCommitResponse,
		AxiosError<PostApiPhysicianDraftByEntityIdCommitError>,
		Options<PostApiPhysicianDraftByEntityIdCommitData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await postApiPhysicianDraftByEntityIdCommit({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const postApiPhysicianDraftValidateQueryKey = (
	options?: Options<PostApiPhysicianDraftValidateData>
) => createQueryKey('postApiPhysicianDraftValidate', options);

export const postApiPhysicianDraftValidateOptions = (
	options?: Options<PostApiPhysicianDraftValidateData>
) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await postApiPhysicianDraftValidate({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: postApiPhysicianDraftValidateQueryKey(options),
	});
};

export const postApiPhysicianDraftValidateMutation = (
	options?: Partial<Options<PostApiPhysicianDraftValidateData>>
) => {
	const mutationOptions: UseMutationOptions<
		PostApiPhysicianDraftValidateResponse,
		AxiosError<PostApiPhysicianDraftValidateError>,
		Options<PostApiPhysicianDraftValidateData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await postApiPhysicianDraftValidate({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const postApiPhysicianDraftByEntityIdValidateQueryKey = (
	options: Options<PostApiPhysicianDraftByEntityIdValidateData>
) => createQueryKey('postApiPhysicianDraftByEntityIdValidate', options);

export const postApiPhysicianDraftByEntityIdValidateOptions = (
	options: Options<PostApiPhysicianDraftByEntityIdValidateData>
) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await postApiPhysicianDraftByEntityIdValidate({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: postApiPhysicianDraftByEntityIdValidateQueryKey(options),
	});
};

export const postApiPhysicianDraftByEntityIdValidateMutation = (
	options?: Partial<Options<PostApiPhysicianDraftByEntityIdValidateData>>
) => {
	const mutationOptions: UseMutationOptions<
		PostApiPhysicianDraftByEntityIdValidateResponse,
		AxiosError<PostApiPhysicianDraftByEntityIdValidateError>,
		Options<PostApiPhysicianDraftByEntityIdValidateData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await postApiPhysicianDraftByEntityIdValidate({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const getApiProviderByIdQueryKey = (options: Options<GetApiProviderByIdData>) =>
	createQueryKey('getApiProviderById', options);

export const getApiProviderByIdOptions = (options: Options<GetApiProviderByIdData>) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await getApiProviderById({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: getApiProviderByIdQueryKey(options),
	});
};

export const patchApiProviderByIdMutation = (
	options?: Partial<Options<PatchApiProviderByIdData>>
) => {
	const mutationOptions: UseMutationOptions<
		PatchApiProviderByIdResponse,
		AxiosError<PatchApiProviderByIdError>,
		Options<PatchApiProviderByIdData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await patchApiProviderById({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const putApiProviderByIdMutation = (options?: Partial<Options<PutApiProviderByIdData>>) => {
	const mutationOptions: UseMutationOptions<
		PutApiProviderByIdResponse,
		AxiosError<PutApiProviderByIdError>,
		Options<PutApiProviderByIdData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await putApiProviderById({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const postApiProviderSearchQueryKey = (options?: Options<PostApiProviderSearchData>) =>
	createQueryKey('postApiProviderSearch', options);

export const postApiProviderSearchOptions = (options?: Options<PostApiProviderSearchData>) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await postApiProviderSearch({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: postApiProviderSearchQueryKey(options),
	});
};

export const postApiProviderSearchInfiniteQueryKey = (
	options?: Options<PostApiProviderSearchData>
): QueryKey<Options<PostApiProviderSearchData>> =>
	createQueryKey('postApiProviderSearch', options, true);

export const postApiProviderSearchInfiniteOptions = (
	options?: Options<PostApiProviderSearchData>
) => {
	return infiniteQueryOptions<
		PostApiProviderSearchResponse,
		AxiosError<DefaultError>,
		InfiniteData<PostApiProviderSearchResponse>,
		QueryKey<Options<PostApiProviderSearchData>>,
		| number
		| Pick<QueryKey<Options<PostApiProviderSearchData>>[0], 'body' | 'headers' | 'path' | 'query'>
	>(
		// @ts-ignore
		{
			queryFn: async ({ pageParam, queryKey, signal }) => {
				// @ts-ignore
				const page: Pick<
					QueryKey<Options<PostApiProviderSearchData>>[0],
					'body' | 'headers' | 'path' | 'query'
				> =
					typeof pageParam === 'object'
						? pageParam
						: {
								query: {
									offset: pageParam,
								},
							};
				const params = createInfiniteParams(queryKey, page);
				const { data } = await postApiProviderSearch({
					...options,
					...params,
					signal,
					throwOnError: true,
				});
				return data;
			},
			queryKey: postApiProviderSearchInfiniteQueryKey(options),
		}
	);
};

export const postApiProviderSearchMutation = (
	options?: Partial<Options<PostApiProviderSearchData>>
) => {
	const mutationOptions: UseMutationOptions<
		PostApiProviderSearchResponse,
		AxiosError<DefaultError>,
		Options<PostApiProviderSearchData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await postApiProviderSearch({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const getApiProviderQueryKey = (options?: Options<GetApiProviderData>) =>
	createQueryKey('getApiProvider', options);

export const getApiProviderOptions = (options?: Options<GetApiProviderData>) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await getApiProvider({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: getApiProviderQueryKey(options),
	});
};

export const getApiProviderInfiniteQueryKey = (
	options?: Options<GetApiProviderData>
): QueryKey<Options<GetApiProviderData>> => createQueryKey('getApiProvider', options, true);

export const getApiProviderInfiniteOptions = (options?: Options<GetApiProviderData>) => {
	return infiniteQueryOptions<
		GetApiProviderResponse,
		AxiosError<DefaultError>,
		InfiniteData<GetApiProviderResponse>,
		QueryKey<Options<GetApiProviderData>>,
		number | Pick<QueryKey<Options<GetApiProviderData>>[0], 'body' | 'headers' | 'path' | 'query'>
	>(
		// @ts-ignore
		{
			queryFn: async ({ pageParam, queryKey, signal }) => {
				// @ts-ignore
				const page: Pick<
					QueryKey<Options<GetApiProviderData>>[0],
					'body' | 'headers' | 'path' | 'query'
				> =
					typeof pageParam === 'object'
						? pageParam
						: {
								query: {
									offset: pageParam,
								},
							};
				const params = createInfiniteParams(queryKey, page);
				const { data } = await getApiProvider({
					...options,
					...params,
					signal,
					throwOnError: true,
				});
				return data;
			},
			queryKey: getApiProviderInfiniteQueryKey(options),
		}
	);
};

export const postApiProviderQueryKey = (options?: Options<PostApiProviderData>) =>
	createQueryKey('postApiProvider', options);

export const postApiProviderOptions = (options?: Options<PostApiProviderData>) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await postApiProvider({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: postApiProviderQueryKey(options),
	});
};

export const postApiProviderMutation = (options?: Partial<Options<PostApiProviderData>>) => {
	const mutationOptions: UseMutationOptions<
		PostApiProviderResponse,
		AxiosError<PostApiProviderError>,
		Options<PostApiProviderData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await postApiProvider({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

// export const postApiProviderDraftQueryKey = (options?: Options<PostApiProviderDraftData>) =>
// 	createQueryKey('postApiProviderDraft', options);

// export const postApiProviderDraftOptions = (options?: Options<PostApiProviderDraftData>) => {
// 	return queryOptions({
// 		queryFn: async ({ queryKey, signal }) => {
// 			const { data } = await postApiProviderDraft({
// 				...options,
// 				...queryKey[0],
// 				signal,
// 				throwOnError: true,
// 			});
// 			return data;
// 		},
// 		queryKey: postApiProviderDraftQueryKey(options),
// 	});
// };

// export const postApiProviderDraftMutation = (
// 	options?: Partial<Options<PostApiProviderDraftData>>
// ) => {
// 	const mutationOptions: UseMutationOptions<
// 		PostApiProviderDraftResponse,
// 		AxiosError<PostApiProviderDraftError>,
// 		Options<PostApiProviderDraftData>
// 	> = {
// 		mutationFn: async (localOptions) => {
// 			const { data } = await postApiProviderDraft({
// 				...options,
// 				...localOptions,
// 				throwOnError: true,
// 			});
// 			return data;
// 		},
// 	};
// 	return mutationOptions;
// };

// export const getApiProviderDraftByEntityIdQueryKey = (
// 	options: Options<GetApiProviderDraftByEntityIdData>
// ) => createQueryKey('getApiProviderDraftByEntityId', options);

// export const getApiProviderDraftByEntityIdOptions = (
// 	options: Options<GetApiProviderDraftByEntityIdData>
// ) => {
// 	return queryOptions({
// 		queryFn: async ({ queryKey, signal }) => {
// 			const { data } = await getApiProviderDraftByEntityId({
// 				...options,
// 				...queryKey[0],
// 				signal,
// 				throwOnError: true,
// 			});
// 			return data;
// 		},
// 		queryKey: getApiProviderDraftByEntityIdQueryKey(options),
// 	});
// };

// export const putApiProviderDraftByEntityIdMutation = (
// 	options?: Partial<Options<PutApiProviderDraftByEntityIdData>>
// ) => {
// 	const mutationOptions: UseMutationOptions<
// 		PutApiProviderDraftByEntityIdResponse,
// 		AxiosError<PutApiProviderDraftByEntityIdError>,
// 		Options<PutApiProviderDraftByEntityIdData>
// 	> = {
// 		mutationFn: async (localOptions) => {
// 			const { data } = await putApiProviderDraftByEntityId({
// 				...options,
// 				...localOptions,
// 				throwOnError: true,
// 			});
// 			return data;
// 		},
// 	};
// 	return mutationOptions;
// };

// export const postApiProviderDraftByEntityIdCommitQueryKey = (
// 	options: Options<PostApiProviderDraftByEntityIdCommitData>
// ) => createQueryKey('postApiProviderDraftByEntityIdCommit', options);

// export const postApiProviderDraftByEntityIdCommitOptions = (
// 	options: Options<PostApiProviderDraftByEntityIdCommitData>
// ) => {
// 	return queryOptions({
// 		queryFn: async ({ queryKey, signal }) => {
// 			const { data } = await postApiProviderDraftByEntityIdCommit({
// 				...options,
// 				...queryKey[0],
// 				signal,
// 				throwOnError: true,
// 			});
// 			return data;
// 		},
// 		queryKey: postApiProviderDraftByEntityIdCommitQueryKey(options),
// 	});
// };

// export const postApiProviderDraftByEntityIdCommitMutation = (
// 	options?: Partial<Options<PostApiProviderDraftByEntityIdCommitData>>
// ) => {
// 	const mutationOptions: UseMutationOptions<
// 		PostApiProviderDraftByEntityIdCommitResponse,
// 		AxiosError<PostApiProviderDraftByEntityIdCommitError>,
// 		Options<PostApiProviderDraftByEntityIdCommitData>
// 	> = {
// 		mutationFn: async (localOptions) => {
// 			const { data } = await postApiProviderDraftByEntityIdCommit({
// 				...options,
// 				...localOptions,
// 				throwOnError: true,
// 			});
// 			return data;
// 		},
// 	};
// 	return mutationOptions;
// };

// export const postApiProviderDraftValidateQueryKey = (
// 	options?: Options<PostApiProviderDraftValidateData>
// ) => createQueryKey('postApiProviderDraftValidate', options);

// export const postApiProviderDraftValidateOptions = (
// 	options?: Options<PostApiProviderDraftValidateData>
// ) => {
// 	return queryOptions({
// 		queryFn: async ({ queryKey, signal }) => {
// 			const { data } = await postApiProviderDraftValidate({
// 				...options,
// 				...queryKey[0],
// 				signal,
// 				throwOnError: true,
// 			});
// 			return data;
// 		},
// 		queryKey: postApiProviderDraftValidateQueryKey(options),
// 	});
// };

// export const postApiProviderDraftValidateMutation = (
// 	options?: Partial<Options<PostApiProviderDraftValidateData>>
// ) => {
// 	const mutationOptions: UseMutationOptions<
// 		PostApiProviderDraftValidateResponse,
// 		AxiosError<PostApiProviderDraftValidateError>,
// 		Options<PostApiProviderDraftValidateData>
// 	> = {
// 		mutationFn: async (localOptions) => {
// 			const { data } = await postApiProviderDraftValidate({
// 				...options,
// 				...localOptions,
// 				throwOnError: true,
// 			});
// 			return data;
// 		},
// 	};
// 	return mutationOptions;
// };

// export const postApiProviderDraftByEntityIdValidateQueryKey = (
// 	options: Options<PostApiProviderDraftByEntityIdValidateData>
// ) => createQueryKey('postApiProviderDraftByEntityIdValidate', options);

// export const postApiProviderDraftByEntityIdValidateOptions = (
// 	options: Options<PostApiProviderDraftByEntityIdValidateData>
// ) => {
// 	return queryOptions({
// 		queryFn: async ({ queryKey, signal }) => {
// 			const { data } = await postApiProviderDraftByEntityIdValidate({
// 				...options,
// 				...queryKey[0],
// 				signal,
// 				throwOnError: true,
// 			});
// 			return data;
// 		},
// 		queryKey: postApiProviderDraftByEntityIdValidateQueryKey(options),
// 	});
// };

// export const postApiProviderDraftByEntityIdValidateMutation = (
// 	options?: Partial<Options<PostApiProviderDraftByEntityIdValidateData>>
// ) => {
// 	const mutationOptions: UseMutationOptions<
// 		PostApiProviderDraftByEntityIdValidateResponse,
// 		AxiosError<PostApiProviderDraftByEntityIdValidateError>,
// 		Options<PostApiProviderDraftByEntityIdValidateData>
// 	> = {
// 		mutationFn: async (localOptions) => {
// 			const { data } = await postApiProviderDraftByEntityIdValidate({
// 				...options,
// 				...localOptions,
// 				throwOnError: true,
// 			});
// 			return data;
// 		},
// 	};
// 	return mutationOptions;
// };

export const postApiProviderDraftQueryKey = (options?: Options<PostApiProviderDraftData>) =>
	createQueryKey('postApiProviderDraft', options);

export const postApiProviderDraftOptions = (options?: Options<PostApiProviderDraftData>) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await postApiProviderDraft({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: postApiProviderDraftQueryKey(options),
	});
};

export const postApiProviderDraftMutation = (
	options?: Partial<Options<PostApiProviderDraftData>>
) => {
	const mutationOptions: UseMutationOptions<
		PostApiProviderDraftResponse,
		AxiosError<PostApiProviderDraftError>,
		Options<PostApiProviderDraftData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await postApiProviderDraft({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const getApiProviderDraftByEntityIdQueryKey = (
	options: Options<GetApiProviderDraftByEntityIdData>
) => createQueryKey('getApiProviderDraftByEntityId', options);

export const getApiProviderDraftByEntityIdOptions = (
	options: Options<GetApiProviderDraftByEntityIdData>
) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await getApiProviderDraftByEntityId({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: getApiProviderDraftByEntityIdQueryKey(options),
	});
};

export const putApiProviderDraftByEntityIdMutation = (
	options?: Partial<Options<PutApiProviderDraftByEntityIdData>>
) => {
	const mutationOptions: UseMutationOptions<
		PutApiProviderDraftByEntityIdResponse,
		AxiosError<PutApiProviderDraftByEntityIdError>,
		Options<PutApiProviderDraftByEntityIdData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await putApiProviderDraftByEntityId({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const postApiProviderDraftByEntityIdCommitQueryKey = (
	options: Options<PostApiProviderDraftByEntityIdCommitData>
) => createQueryKey('postApiProviderDraftByEntityIdCommit', options);

export const postApiProviderDraftByEntityIdCommitOptions = (
	options: Options<PostApiProviderDraftByEntityIdCommitData>
) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await postApiProviderDraftByEntityIdCommit({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: postApiProviderDraftByEntityIdCommitQueryKey(options),
	});
};

export const postApiProviderDraftByEntityIdCommitMutation = (
	options?: Partial<Options<PostApiProviderDraftByEntityIdCommitData>>
) => {
	const mutationOptions: UseMutationOptions<
		PostApiProviderDraftByEntityIdCommitResponse,
		AxiosError<PostApiProviderDraftByEntityIdCommitError>,
		Options<PostApiProviderDraftByEntityIdCommitData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await postApiProviderDraftByEntityIdCommit({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const postApiProviderDraftValidateQueryKey = (
	options?: Options<PostApiProviderDraftValidateData>
) => createQueryKey('postApiProviderDraftValidate', options);

export const postApiProviderDraftValidateOptions = (
	options?: Options<PostApiProviderDraftValidateData>
) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await postApiProviderDraftValidate({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: postApiProviderDraftValidateQueryKey(options),
	});
};

export const postApiProviderDraftValidateMutation = (
	options?: Partial<Options<PostApiProviderDraftValidateData>>
) => {
	const mutationOptions: UseMutationOptions<
		PostApiProviderDraftValidateResponse,
		AxiosError<PostApiProviderDraftValidateError>,
		Options<PostApiProviderDraftValidateData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await postApiProviderDraftValidate({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const postApiProviderDraftByEntityIdValidateQueryKey = (
	options: Options<PostApiProviderDraftByEntityIdValidateData>
) => createQueryKey('postApiProviderDraftByEntityIdValidate', options);

export const postApiProviderDraftByEntityIdValidateOptions = (
	options: Options<PostApiProviderDraftByEntityIdValidateData>
) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await postApiProviderDraftByEntityIdValidate({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: postApiProviderDraftByEntityIdValidateQueryKey(options),
	});
};

export const postApiProviderDraftByEntityIdValidateMutation = (
	options?: Partial<Options<PostApiProviderDraftByEntityIdValidateData>>
) => {
	const mutationOptions: UseMutationOptions<
		PostApiProviderDraftByEntityIdValidateResponse,
		AxiosError<PostApiProviderDraftByEntityIdValidateError>,
		Options<PostApiProviderDraftByEntityIdValidateData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await postApiProviderDraftByEntityIdValidate({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const getPyapiZipQueryKey = (options?: Options<GetPyapiZipData>) =>
	createQueryKey('getPyapiZip', options);

export const getPyapiZipOptions = (options?: Options<GetPyapiZipData>) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await getPyapiZip({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: getPyapiZipQueryKey(options),
	});
};

// export const getTsapiZipQueryKey = (options?: Options<GetTsapiZipData>) =>
// 	createQueryKey('getTsapiZip', options);

// export const getTsapiZipOptions = (options?: Options<GetTsapiZipData>) => {
// 	return queryOptions({
// 		queryFn: async ({ queryKey, signal }) => {
// 			const { data } = await getTsapiZip({
// 				...options,
// 				...queryKey[0],
// 				signal,
// 				throwOnError: true,
// 			});
// 			return data;
// 		},
// 		queryKey: getTsapiZipQueryKey(options),
// 	});
// };

export const getTsapiZipQueryKey = (options?: Options<GetTsapiZipData>) =>
	createQueryKey('getTsapiZip', options);

export const getTsapiZipOptions = (options?: Options<GetTsapiZipData>) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await getTsapiZip({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: getTsapiZipQueryKey(options),
	});
};

export const getApiRoomByIdQueryKey = (options: Options<GetApiRoomByIdData>) =>
	createQueryKey('getApiRoomById', options);

export const getApiRoomByIdOptions = (options: Options<GetApiRoomByIdData>) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await getApiRoomById({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: getApiRoomByIdQueryKey(options),
	});
};

export const patchApiRoomByIdMutation = (options?: Partial<Options<PatchApiRoomByIdData>>) => {
	const mutationOptions: UseMutationOptions<
		PatchApiRoomByIdResponse,
		AxiosError<PatchApiRoomByIdError>,
		Options<PatchApiRoomByIdData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await patchApiRoomById({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const putApiRoomByIdMutation = (options?: Partial<Options<PutApiRoomByIdData>>) => {
	const mutationOptions: UseMutationOptions<
		PutApiRoomByIdResponse,
		AxiosError<PutApiRoomByIdError>,
		Options<PutApiRoomByIdData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await putApiRoomById({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const postApiRoomSearchQueryKey = (options?: Options<PostApiRoomSearchData>) =>
	createQueryKey('postApiRoomSearch', options);

export const postApiRoomSearchOptions = (options?: Options<PostApiRoomSearchData>) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await postApiRoomSearch({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: postApiRoomSearchQueryKey(options),
	});
};

export const postApiRoomSearchInfiniteQueryKey = (
	options?: Options<PostApiRoomSearchData>
): QueryKey<Options<PostApiRoomSearchData>> => createQueryKey('postApiRoomSearch', options, true);

export const postApiRoomSearchInfiniteOptions = (options?: Options<PostApiRoomSearchData>) => {
	return infiniteQueryOptions<
		PostApiRoomSearchResponse,
		AxiosError<DefaultError>,
		InfiniteData<PostApiRoomSearchResponse>,
		QueryKey<Options<PostApiRoomSearchData>>,
		| number
		| Pick<QueryKey<Options<PostApiRoomSearchData>>[0], 'body' | 'headers' | 'path' | 'query'>
	>(
		// @ts-ignore
		{
			queryFn: async ({ pageParam, queryKey, signal }) => {
				// @ts-ignore
				const page: Pick<
					QueryKey<Options<PostApiRoomSearchData>>[0],
					'body' | 'headers' | 'path' | 'query'
				> =
					typeof pageParam === 'object'
						? pageParam
						: {
								query: {
									offset: pageParam,
								},
							};
				const params = createInfiniteParams(queryKey, page);
				const { data } = await postApiRoomSearch({
					...options,
					...params,
					signal,
					throwOnError: true,
				});
				return data;
			},
			queryKey: postApiRoomSearchInfiniteQueryKey(options),
		}
	);
};

export const postApiRoomSearchMutation = (options?: Partial<Options<PostApiRoomSearchData>>) => {
	const mutationOptions: UseMutationOptions<
		PostApiRoomSearchResponse,
		AxiosError<DefaultError>,
		Options<PostApiRoomSearchData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await postApiRoomSearch({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const getApiRoomQueryKey = (options?: Options<GetApiRoomData>) =>
	createQueryKey('getApiRoom', options);

export const getApiRoomOptions = (options?: Options<GetApiRoomData>) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await getApiRoom({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: getApiRoomQueryKey(options),
	});
};

export const getApiRoomInfiniteQueryKey = (
	options?: Options<GetApiRoomData>
): QueryKey<Options<GetApiRoomData>> => createQueryKey('getApiRoom', options, true);

export const getApiRoomInfiniteOptions = (options?: Options<GetApiRoomData>) => {
	return infiniteQueryOptions<
		GetApiRoomResponse,
		AxiosError<DefaultError>,
		InfiniteData<GetApiRoomResponse>,
		QueryKey<Options<GetApiRoomData>>,
		number | Pick<QueryKey<Options<GetApiRoomData>>[0], 'body' | 'headers' | 'path' | 'query'>
	>(
		// @ts-ignore
		{
			queryFn: async ({ pageParam, queryKey, signal }) => {
				// @ts-ignore
				const page: Pick<
					QueryKey<Options<GetApiRoomData>>[0],
					'body' | 'headers' | 'path' | 'query'
				> =
					typeof pageParam === 'object'
						? pageParam
						: {
								query: {
									offset: pageParam,
								},
							};
				const params = createInfiniteParams(queryKey, page);
				const { data } = await getApiRoom({
					...options,
					...params,
					signal,
					throwOnError: true,
				});
				return data;
			},
			queryKey: getApiRoomInfiniteQueryKey(options),
		}
	);
};

export const postApiRoomQueryKey = (options?: Options<PostApiRoomData>) =>
	createQueryKey('postApiRoom', options);

export const postApiRoomOptions = (options?: Options<PostApiRoomData>) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await postApiRoom({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: postApiRoomQueryKey(options),
	});
};

export const postApiRoomMutation = (options?: Partial<Options<PostApiRoomData>>) => {
	const mutationOptions: UseMutationOptions<
		PostApiRoomResponse,
		AxiosError<PostApiRoomError>,
		Options<PostApiRoomData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await postApiRoom({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

// export const postApiRoomDraftQueryKey = (options?: Options<PostApiRoomDraftData>) =>
// 	createQueryKey('postApiRoomDraft', options);

// export const postApiRoomDraftOptions = (options?: Options<PostApiRoomDraftData>) => {
// 	return queryOptions({
// 		queryFn: async ({ queryKey, signal }) => {
// 			const { data } = await postApiRoomDraft({
// 				...options,
// 				...queryKey[0],
// 				signal,
// 				throwOnError: true,
// 			});
// 			return data;
// 		},
// 		queryKey: postApiRoomDraftQueryKey(options),
// 	});
// };

// export const postApiRoomDraftMutation = (options?: Partial<Options<PostApiRoomDraftData>>) => {
// 	const mutationOptions: UseMutationOptions<
// 		PostApiRoomDraftResponse,
// 		AxiosError<PostApiRoomDraftError>,
// 		Options<PostApiRoomDraftData>
// 	> = {
// 		mutationFn: async (localOptions) => {
// 			const { data } = await postApiRoomDraft({
// 				...options,
// 				...localOptions,
// 				throwOnError: true,
// 			});
// 			return data;
// 		},
// 	};
// 	return mutationOptions;
// };

// export const getApiRoomDraftByEntityIdQueryKey = (
// 	options: Options<GetApiRoomDraftByEntityIdData>
// ) => createQueryKey('getApiRoomDraftByEntityId', options);

// export const getApiRoomDraftByEntityIdOptions = (
// 	options: Options<GetApiRoomDraftByEntityIdData>
// ) => {
// 	return queryOptions({
// 		queryFn: async ({ queryKey, signal }) => {
// 			const { data } = await getApiRoomDraftByEntityId({
// 				...options,
// 				...queryKey[0],
// 				signal,
// 				throwOnError: true,
// 			});
// 			return data;
// 		},
// 		queryKey: getApiRoomDraftByEntityIdQueryKey(options),
// 	});
// };

// export const putApiRoomDraftByEntityIdMutation = (
// 	options?: Partial<Options<PutApiRoomDraftByEntityIdData>>
// ) => {
// 	const mutationOptions: UseMutationOptions<
// 		PutApiRoomDraftByEntityIdResponse,
// 		AxiosError<PutApiRoomDraftByEntityIdError>,
// 		Options<PutApiRoomDraftByEntityIdData>
// 	> = {
// 		mutationFn: async (localOptions) => {
// 			const { data } = await putApiRoomDraftByEntityId({
// 				...options,
// 				...localOptions,
// 				throwOnError: true,
// 			});
// 			return data;
// 		},
// 	};
// 	return mutationOptions;
// };

// export const postApiRoomDraftByEntityIdCommitQueryKey = (
// 	options: Options<PostApiRoomDraftByEntityIdCommitData>
// ) => createQueryKey('postApiRoomDraftByEntityIdCommit', options);

// export const postApiRoomDraftByEntityIdCommitOptions = (
// 	options: Options<PostApiRoomDraftByEntityIdCommitData>
// ) => {
// 	return queryOptions({
// 		queryFn: async ({ queryKey, signal }) => {
// 			const { data } = await postApiRoomDraftByEntityIdCommit({
// 				...options,
// 				...queryKey[0],
// 				signal,
// 				throwOnError: true,
// 			});
// 			return data;
// 		},
// 		queryKey: postApiRoomDraftByEntityIdCommitQueryKey(options),
// 	});
// };

// export const postApiRoomDraftByEntityIdCommitMutation = (
// 	options?: Partial<Options<PostApiRoomDraftByEntityIdCommitData>>
// ) => {
// 	const mutationOptions: UseMutationOptions<
// 		PostApiRoomDraftByEntityIdCommitResponse,
// 		AxiosError<PostApiRoomDraftByEntityIdCommitError>,
// 		Options<PostApiRoomDraftByEntityIdCommitData>
// 	> = {
// 		mutationFn: async (localOptions) => {
// 			const { data } = await postApiRoomDraftByEntityIdCommit({
// 				...options,
// 				...localOptions,
// 				throwOnError: true,
// 			});
// 			return data;
// 		},
// 	};
// 	return mutationOptions;
// };

// export const postApiRoomDraftValidateQueryKey = (options?: Options<PostApiRoomDraftValidateData>) =>
// 	createQueryKey('postApiRoomDraftValidate', options);

// export const postApiRoomDraftValidateOptions = (
// 	options?: Options<PostApiRoomDraftValidateData>
// ) => {
// 	return queryOptions({
// 		queryFn: async ({ queryKey, signal }) => {
// 			const { data } = await postApiRoomDraftValidate({
// 				...options,
// 				...queryKey[0],
// 				signal,
// 				throwOnError: true,
// 			});
// 			return data;
// 		},
// 		queryKey: postApiRoomDraftValidateQueryKey(options),
// 	});
// };

// export const postApiRoomDraftValidateMutation = (
// 	options?: Partial<Options<PostApiRoomDraftValidateData>>
// ) => {
// 	const mutationOptions: UseMutationOptions<
// 		PostApiRoomDraftValidateResponse,
// 		AxiosError<PostApiRoomDraftValidateError>,
// 		Options<PostApiRoomDraftValidateData>
// 	> = {
// 		mutationFn: async (localOptions) => {
// 			const { data } = await postApiRoomDraftValidate({
// 				...options,
// 				...localOptions,
// 				throwOnError: true,
// 			});
// 			return data;
// 		},
// 	};
// 	return mutationOptions;
// };

// export const postApiRoomDraftByEntityIdValidateQueryKey = (
// 	options: Options<PostApiRoomDraftByEntityIdValidateData>
// ) => createQueryKey('postApiRoomDraftByEntityIdValidate', options);

// export const postApiRoomDraftByEntityIdValidateOptions = (
// 	options: Options<PostApiRoomDraftByEntityIdValidateData>
// ) => {
// 	return queryOptions({
// 		queryFn: async ({ queryKey, signal }) => {
// 			const { data } = await postApiRoomDraftByEntityIdValidate({
// 				...options,
// 				...queryKey[0],
// 				signal,
// 				throwOnError: true,
// 			});
// 			return data;
// 		},
// 		queryKey: postApiRoomDraftByEntityIdValidateQueryKey(options),
// 	});
// };

// export const postApiRoomDraftByEntityIdValidateMutation = (
// 	options?: Partial<Options<PostApiRoomDraftByEntityIdValidateData>>
// ) => {
// 	const mutationOptions: UseMutationOptions<
// 		PostApiRoomDraftByEntityIdValidateResponse,
// 		AxiosError<PostApiRoomDraftByEntityIdValidateError>,
// 		Options<PostApiRoomDraftByEntityIdValidateData>
// 	> = {
// 		mutationFn: async (localOptions) => {
// 			const { data } = await postApiRoomDraftByEntityIdValidate({
// 				...options,
// 				...localOptions,
// 				throwOnError: true,
// 			});
// 			return data;
// 		},
// 	};
// 	return mutationOptions;
// };

export const postApiRoomDraftQueryKey = (options?: Options<PostApiRoomDraftData>) =>
	createQueryKey('postApiRoomDraft', options);

export const postApiRoomDraftOptions = (options?: Options<PostApiRoomDraftData>) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await postApiRoomDraft({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: postApiRoomDraftQueryKey(options),
	});
};

export const postApiRoomDraftMutation = (options?: Partial<Options<PostApiRoomDraftData>>) => {
	const mutationOptions: UseMutationOptions<
		PostApiRoomDraftResponse,
		AxiosError<PostApiRoomDraftError>,
		Options<PostApiRoomDraftData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await postApiRoomDraft({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const getApiRoomDraftByEntityIdQueryKey = (
	options: Options<GetApiRoomDraftByEntityIdData>
) => createQueryKey('getApiRoomDraftByEntityId', options);

export const getApiRoomDraftByEntityIdOptions = (
	options: Options<GetApiRoomDraftByEntityIdData>
) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await getApiRoomDraftByEntityId({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: getApiRoomDraftByEntityIdQueryKey(options),
	});
};

export const putApiRoomDraftByEntityIdMutation = (
	options?: Partial<Options<PutApiRoomDraftByEntityIdData>>
) => {
	const mutationOptions: UseMutationOptions<
		PutApiRoomDraftByEntityIdResponse,
		AxiosError<PutApiRoomDraftByEntityIdError>,
		Options<PutApiRoomDraftByEntityIdData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await putApiRoomDraftByEntityId({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const postApiRoomDraftByEntityIdCommitQueryKey = (
	options: Options<PostApiRoomDraftByEntityIdCommitData>
) => createQueryKey('postApiRoomDraftByEntityIdCommit', options);

export const postApiRoomDraftByEntityIdCommitOptions = (
	options: Options<PostApiRoomDraftByEntityIdCommitData>
) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await postApiRoomDraftByEntityIdCommit({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: postApiRoomDraftByEntityIdCommitQueryKey(options),
	});
};

export const postApiRoomDraftByEntityIdCommitMutation = (
	options?: Partial<Options<PostApiRoomDraftByEntityIdCommitData>>
) => {
	const mutationOptions: UseMutationOptions<
		PostApiRoomDraftByEntityIdCommitResponse,
		AxiosError<PostApiRoomDraftByEntityIdCommitError>,
		Options<PostApiRoomDraftByEntityIdCommitData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await postApiRoomDraftByEntityIdCommit({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const postApiRoomDraftValidateQueryKey = (options?: Options<PostApiRoomDraftValidateData>) =>
	createQueryKey('postApiRoomDraftValidate', options);

export const postApiRoomDraftValidateOptions = (
	options?: Options<PostApiRoomDraftValidateData>
) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await postApiRoomDraftValidate({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: postApiRoomDraftValidateQueryKey(options),
	});
};

export const postApiRoomDraftValidateMutation = (
	options?: Partial<Options<PostApiRoomDraftValidateData>>
) => {
	const mutationOptions: UseMutationOptions<
		PostApiRoomDraftValidateResponse,
		AxiosError<PostApiRoomDraftValidateError>,
		Options<PostApiRoomDraftValidateData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await postApiRoomDraftValidate({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const postApiRoomDraftByEntityIdValidateQueryKey = (
	options: Options<PostApiRoomDraftByEntityIdValidateData>
) => createQueryKey('postApiRoomDraftByEntityIdValidate', options);

export const postApiRoomDraftByEntityIdValidateOptions = (
	options: Options<PostApiRoomDraftByEntityIdValidateData>
) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await postApiRoomDraftByEntityIdValidate({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: postApiRoomDraftByEntityIdValidateQueryKey(options),
	});
};

export const postApiRoomDraftByEntityIdValidateMutation = (
	options?: Partial<Options<PostApiRoomDraftByEntityIdValidateData>>
) => {
	const mutationOptions: UseMutationOptions<
		PostApiRoomDraftByEntityIdValidateResponse,
		AxiosError<PostApiRoomDraftByEntityIdValidateError>,
		Options<PostApiRoomDraftByEntityIdValidateData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await postApiRoomDraftByEntityIdValidate({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const postApiSchedulingFindSlotsQueryKey = (
	options?: Options<PostApiSchedulingFindSlotsData>
) => createQueryKey('postApiSchedulingFindSlots', options);

export const postApiSchedulingFindSlotsOptions = (
	options?: Options<PostApiSchedulingFindSlotsData>
) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await postApiSchedulingFindSlots({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: postApiSchedulingFindSlotsQueryKey(options),
	});
};

export const postApiSchedulingFindSlotsMutation = (
	options?: Partial<Options<PostApiSchedulingFindSlotsData>>
) => {
	const mutationOptions: UseMutationOptions<
		PostApiSchedulingFindSlotsResponse,
		AxiosError<DefaultError>,
		Options<PostApiSchedulingFindSlotsData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await postApiSchedulingFindSlots({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const getApiSchedulingConstraintByIdQueryKey = (
	options: Options<GetApiSchedulingConstraintByIdData>
) => createQueryKey('getApiSchedulingConstraintById', options);

export const getApiSchedulingConstraintByIdOptions = (
	options: Options<GetApiSchedulingConstraintByIdData>
) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await getApiSchedulingConstraintById({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: getApiSchedulingConstraintByIdQueryKey(options),
	});
};

export const patchApiSchedulingConstraintByIdMutation = (
	options?: Partial<Options<PatchApiSchedulingConstraintByIdData>>
) => {
	const mutationOptions: UseMutationOptions<
		PatchApiSchedulingConstraintByIdResponse,
		AxiosError<PatchApiSchedulingConstraintByIdError>,
		Options<PatchApiSchedulingConstraintByIdData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await patchApiSchedulingConstraintById({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const putApiSchedulingConstraintByIdMutation = (
	options?: Partial<Options<PutApiSchedulingConstraintByIdData>>
) => {
	const mutationOptions: UseMutationOptions<
		PutApiSchedulingConstraintByIdResponse,
		AxiosError<PutApiSchedulingConstraintByIdError>,
		Options<PutApiSchedulingConstraintByIdData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await putApiSchedulingConstraintById({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const postApiSchedulingConstraintSearchQueryKey = (
	options?: Options<PostApiSchedulingConstraintSearchData>
) => createQueryKey('postApiSchedulingConstraintSearch', options);

export const postApiSchedulingConstraintSearchOptions = (
	options?: Options<PostApiSchedulingConstraintSearchData>
) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await postApiSchedulingConstraintSearch({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: postApiSchedulingConstraintSearchQueryKey(options),
	});
};

export const postApiSchedulingConstraintSearchInfiniteQueryKey = (
	options?: Options<PostApiSchedulingConstraintSearchData>
): QueryKey<Options<PostApiSchedulingConstraintSearchData>> =>
	createQueryKey('postApiSchedulingConstraintSearch', options, true);

export const postApiSchedulingConstraintSearchInfiniteOptions = (
	options?: Options<PostApiSchedulingConstraintSearchData>
) => {
	return infiniteQueryOptions<
		PostApiSchedulingConstraintSearchResponse,
		AxiosError<DefaultError>,
		InfiniteData<PostApiSchedulingConstraintSearchResponse>,
		QueryKey<Options<PostApiSchedulingConstraintSearchData>>,
		| number
		| Pick<
				QueryKey<Options<PostApiSchedulingConstraintSearchData>>[0],
				'body' | 'headers' | 'path' | 'query'
		  >
	>(
		// @ts-ignore
		{
			queryFn: async ({ pageParam, queryKey, signal }) => {
				// @ts-ignore
				const page: Pick<
					QueryKey<Options<PostApiSchedulingConstraintSearchData>>[0],
					'body' | 'headers' | 'path' | 'query'
				> =
					typeof pageParam === 'object'
						? pageParam
						: {
								query: {
									offset: pageParam,
								},
							};
				const params = createInfiniteParams(queryKey, page);
				const { data } = await postApiSchedulingConstraintSearch({
					...options,
					...params,
					signal,
					throwOnError: true,
				});
				return data;
			},
			queryKey: postApiSchedulingConstraintSearchInfiniteQueryKey(options),
		}
	);
};

export const postApiSchedulingConstraintSearchMutation = (
	options?: Partial<Options<PostApiSchedulingConstraintSearchData>>
) => {
	const mutationOptions: UseMutationOptions<
		PostApiSchedulingConstraintSearchResponse,
		AxiosError<DefaultError>,
		Options<PostApiSchedulingConstraintSearchData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await postApiSchedulingConstraintSearch({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const getApiSchedulingConstraintQueryKey = (
	options?: Options<GetApiSchedulingConstraintData>
) => createQueryKey('getApiSchedulingConstraint', options);

export const getApiSchedulingConstraintOptions = (
	options?: Options<GetApiSchedulingConstraintData>
) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await getApiSchedulingConstraint({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: getApiSchedulingConstraintQueryKey(options),
	});
};

export const getApiSchedulingConstraintInfiniteQueryKey = (
	options?: Options<GetApiSchedulingConstraintData>
): QueryKey<Options<GetApiSchedulingConstraintData>> =>
	createQueryKey('getApiSchedulingConstraint', options, true);

export const getApiSchedulingConstraintInfiniteOptions = (
	options?: Options<GetApiSchedulingConstraintData>
) => {
	return infiniteQueryOptions<
		GetApiSchedulingConstraintResponse,
		AxiosError<DefaultError>,
		InfiniteData<GetApiSchedulingConstraintResponse>,
		QueryKey<Options<GetApiSchedulingConstraintData>>,
		| number
		| Pick<
				QueryKey<Options<GetApiSchedulingConstraintData>>[0],
				'body' | 'headers' | 'path' | 'query'
		  >
	>(
		// @ts-ignore
		{
			queryFn: async ({ pageParam, queryKey, signal }) => {
				// @ts-ignore
				const page: Pick<
					QueryKey<Options<GetApiSchedulingConstraintData>>[0],
					'body' | 'headers' | 'path' | 'query'
				> =
					typeof pageParam === 'object'
						? pageParam
						: {
								query: {
									offset: pageParam,
								},
							};
				const params = createInfiniteParams(queryKey, page);
				const { data } = await getApiSchedulingConstraint({
					...options,
					...params,
					signal,
					throwOnError: true,
				});
				return data;
			},
			queryKey: getApiSchedulingConstraintInfiniteQueryKey(options),
		}
	);
};

export const postApiSchedulingConstraintQueryKey = (
	options?: Options<PostApiSchedulingConstraintData>
) => createQueryKey('postApiSchedulingConstraint', options);

export const postApiSchedulingConstraintOptions = (
	options?: Options<PostApiSchedulingConstraintData>
) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await postApiSchedulingConstraint({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: postApiSchedulingConstraintQueryKey(options),
	});
};

export const postApiSchedulingConstraintMutation = (
	options?: Partial<Options<PostApiSchedulingConstraintData>>
) => {
	const mutationOptions: UseMutationOptions<
		PostApiSchedulingConstraintResponse,
		AxiosError<PostApiSchedulingConstraintError>,
		Options<PostApiSchedulingConstraintData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await postApiSchedulingConstraint({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

// export const postApiSchedulingConstraintDraftQueryKey = (
// 	options?: Options<PostApiSchedulingConstraintDraftData>
// ) => createQueryKey('postApiSchedulingConstraintDraft', options);

// export const postApiSchedulingConstraintDraftOptions = (
// 	options?: Options<PostApiSchedulingConstraintDraftData>
// ) => {
// 	return queryOptions({
// 		queryFn: async ({ queryKey, signal }) => {
// 			const { data } = await postApiSchedulingConstraintDraft({
// 				...options,
// 				...queryKey[0],
// 				signal,
// 				throwOnError: true,
// 			});
// 			return data;
// 		},
// 		queryKey: postApiSchedulingConstraintDraftQueryKey(options),
// 	});
// };

// export const postApiSchedulingConstraintDraftMutation = (
// 	options?: Partial<Options<PostApiSchedulingConstraintDraftData>>
// ) => {
// 	const mutationOptions: UseMutationOptions<
// 		PostApiSchedulingConstraintDraftResponse,
// 		AxiosError<PostApiSchedulingConstraintDraftError>,
// 		Options<PostApiSchedulingConstraintDraftData>
// 	> = {
// 		mutationFn: async (localOptions) => {
// 			const { data } = await postApiSchedulingConstraintDraft({
// 				...options,
// 				...localOptions,
// 				throwOnError: true,
// 			});
// 			return data;
// 		},
// 	};
// 	return mutationOptions;
// };

// export const getApiSchedulingConstraintDraftByEntityIdQueryKey = (
// 	options: Options<GetApiSchedulingConstraintDraftByEntityIdData>
// ) => createQueryKey('getApiSchedulingConstraintDraftByEntityId', options);

// export const getApiSchedulingConstraintDraftByEntityIdOptions = (
// 	options: Options<GetApiSchedulingConstraintDraftByEntityIdData>
// ) => {
// 	return queryOptions({
// 		queryFn: async ({ queryKey, signal }) => {
// 			const { data } = await getApiSchedulingConstraintDraftByEntityId({
// 				...options,
// 				...queryKey[0],
// 				signal,
// 				throwOnError: true,
// 			});
// 			return data;
// 		},
// 		queryKey: getApiSchedulingConstraintDraftByEntityIdQueryKey(options),
// 	});
// };

// export const putApiSchedulingConstraintDraftByEntityIdMutation = (
// 	options?: Partial<Options<PutApiSchedulingConstraintDraftByEntityIdData>>
// ) => {
// 	const mutationOptions: UseMutationOptions<
// 		PutApiSchedulingConstraintDraftByEntityIdResponse,
// 		AxiosError<PutApiSchedulingConstraintDraftByEntityIdError>,
// 		Options<PutApiSchedulingConstraintDraftByEntityIdData>
// 	> = {
// 		mutationFn: async (localOptions) => {
// 			const { data } = await putApiSchedulingConstraintDraftByEntityId({
// 				...options,
// 				...localOptions,
// 				throwOnError: true,
// 			});
// 			return data;
// 		},
// 	};
// 	return mutationOptions;
// };

// export const postApiSchedulingConstraintDraftByEntityIdCommitQueryKey = (
// 	options: Options<PostApiSchedulingConstraintDraftByEntityIdCommitData>
// ) => createQueryKey('postApiSchedulingConstraintDraftByEntityIdCommit', options);

// export const postApiSchedulingConstraintDraftByEntityIdCommitOptions = (
// 	options: Options<PostApiSchedulingConstraintDraftByEntityIdCommitData>
// ) => {
// 	return queryOptions({
// 		queryFn: async ({ queryKey, signal }) => {
// 			const { data } = await postApiSchedulingConstraintDraftByEntityIdCommit({
// 				...options,
// 				...queryKey[0],
// 				signal,
// 				throwOnError: true,
// 			});
// 			return data;
// 		},
// 		queryKey: postApiSchedulingConstraintDraftByEntityIdCommitQueryKey(options),
// 	});
// };

// export const postApiSchedulingConstraintDraftByEntityIdCommitMutation = (
// 	options?: Partial<Options<PostApiSchedulingConstraintDraftByEntityIdCommitData>>
// ) => {
// 	const mutationOptions: UseMutationOptions<
// 		PostApiSchedulingConstraintDraftByEntityIdCommitResponse,
// 		AxiosError<PostApiSchedulingConstraintDraftByEntityIdCommitError>,
// 		Options<PostApiSchedulingConstraintDraftByEntityIdCommitData>
// 	> = {
// 		mutationFn: async (localOptions) => {
// 			const { data } = await postApiSchedulingConstraintDraftByEntityIdCommit({
// 				...options,
// 				...localOptions,
// 				throwOnError: true,
// 			});
// 			return data;
// 		},
// 	};
// 	return mutationOptions;
// };

// export const postApiSchedulingConstraintDraftValidateQueryKey = (
// 	options?: Options<PostApiSchedulingConstraintDraftValidateData>
// ) => createQueryKey('postApiSchedulingConstraintDraftValidate', options);

// export const postApiSchedulingConstraintDraftValidateOptions = (
// 	options?: Options<PostApiSchedulingConstraintDraftValidateData>
// ) => {
// 	return queryOptions({
// 		queryFn: async ({ queryKey, signal }) => {
// 			const { data } = await postApiSchedulingConstraintDraftValidate({
// 				...options,
// 				...queryKey[0],
// 				signal,
// 				throwOnError: true,
// 			});
// 			return data;
// 		},
// 		queryKey: postApiSchedulingConstraintDraftValidateQueryKey(options),
// 	});
// };

// export const postApiSchedulingConstraintDraftValidateMutation = (
// 	options?: Partial<Options<PostApiSchedulingConstraintDraftValidateData>>
// ) => {
// 	const mutationOptions: UseMutationOptions<
// 		PostApiSchedulingConstraintDraftValidateResponse,
// 		AxiosError<PostApiSchedulingConstraintDraftValidateError>,
// 		Options<PostApiSchedulingConstraintDraftValidateData>
// 	> = {
// 		mutationFn: async (localOptions) => {
// 			const { data } = await postApiSchedulingConstraintDraftValidate({
// 				...options,
// 				...localOptions,
// 				throwOnError: true,
// 			});
// 			return data;
// 		},
// 	};
// 	return mutationOptions;
// };

// export const postApiSchedulingConstraintDraftByEntityIdValidateQueryKey = (
// 	options: Options<PostApiSchedulingConstraintDraftByEntityIdValidateData>
// ) => createQueryKey('postApiSchedulingConstraintDraftByEntityIdValidate', options);

// export const postApiSchedulingConstraintDraftByEntityIdValidateOptions = (
// 	options: Options<PostApiSchedulingConstraintDraftByEntityIdValidateData>
// ) => {
// 	return queryOptions({
// 		queryFn: async ({ queryKey, signal }) => {
// 			const { data } = await postApiSchedulingConstraintDraftByEntityIdValidate({
// 				...options,
// 				...queryKey[0],
// 				signal,
// 				throwOnError: true,
// 			});
// 			return data;
// 		},
// 		queryKey: postApiSchedulingConstraintDraftByEntityIdValidateQueryKey(options),
// 	});
// };

// export const postApiSchedulingConstraintDraftByEntityIdValidateMutation = (
// 	options?: Partial<Options<PostApiSchedulingConstraintDraftByEntityIdValidateData>>
// ) => {
// 	const mutationOptions: UseMutationOptions<
// 		PostApiSchedulingConstraintDraftByEntityIdValidateResponse,
// 		AxiosError<PostApiSchedulingConstraintDraftByEntityIdValidateError>,
// 		Options<PostApiSchedulingConstraintDraftByEntityIdValidateData>
// 	> = {
// 		mutationFn: async (localOptions) => {
// 			const { data } = await postApiSchedulingConstraintDraftByEntityIdValidate({
// 				...options,
// 				...localOptions,
// 				throwOnError: true,
// 			});
// 			return data;
// 		},
// 	};
// 	return mutationOptions;
// };

export const postApiSchedulingConstraintDraftQueryKey = (
	options?: Options<PostApiSchedulingConstraintDraftData>
) => createQueryKey('postApiSchedulingConstraintDraft', options);

export const postApiSchedulingConstraintDraftOptions = (
	options?: Options<PostApiSchedulingConstraintDraftData>
) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await postApiSchedulingConstraintDraft({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: postApiSchedulingConstraintDraftQueryKey(options),
	});
};

export const postApiSchedulingConstraintDraftMutation = (
	options?: Partial<Options<PostApiSchedulingConstraintDraftData>>
) => {
	const mutationOptions: UseMutationOptions<
		PostApiSchedulingConstraintDraftResponse,
		AxiosError<PostApiSchedulingConstraintDraftError>,
		Options<PostApiSchedulingConstraintDraftData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await postApiSchedulingConstraintDraft({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const getApiSchedulingConstraintDraftByEntityIdQueryKey = (
	options: Options<GetApiSchedulingConstraintDraftByEntityIdData>
) => createQueryKey('getApiSchedulingConstraintDraftByEntityId', options);

export const getApiSchedulingConstraintDraftByEntityIdOptions = (
	options: Options<GetApiSchedulingConstraintDraftByEntityIdData>
) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await getApiSchedulingConstraintDraftByEntityId({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: getApiSchedulingConstraintDraftByEntityIdQueryKey(options),
	});
};

export const putApiSchedulingConstraintDraftByEntityIdMutation = (
	options?: Partial<Options<PutApiSchedulingConstraintDraftByEntityIdData>>
) => {
	const mutationOptions: UseMutationOptions<
		PutApiSchedulingConstraintDraftByEntityIdResponse,
		AxiosError<PutApiSchedulingConstraintDraftByEntityIdError>,
		Options<PutApiSchedulingConstraintDraftByEntityIdData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await putApiSchedulingConstraintDraftByEntityId({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const postApiSchedulingConstraintDraftByEntityIdCommitQueryKey = (
	options: Options<PostApiSchedulingConstraintDraftByEntityIdCommitData>
) => createQueryKey('postApiSchedulingConstraintDraftByEntityIdCommit', options);

export const postApiSchedulingConstraintDraftByEntityIdCommitOptions = (
	options: Options<PostApiSchedulingConstraintDraftByEntityIdCommitData>
) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await postApiSchedulingConstraintDraftByEntityIdCommit({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: postApiSchedulingConstraintDraftByEntityIdCommitQueryKey(options),
	});
};

export const postApiSchedulingConstraintDraftByEntityIdCommitMutation = (
	options?: Partial<Options<PostApiSchedulingConstraintDraftByEntityIdCommitData>>
) => {
	const mutationOptions: UseMutationOptions<
		PostApiSchedulingConstraintDraftByEntityIdCommitResponse,
		AxiosError<PostApiSchedulingConstraintDraftByEntityIdCommitError>,
		Options<PostApiSchedulingConstraintDraftByEntityIdCommitData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await postApiSchedulingConstraintDraftByEntityIdCommit({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const postApiSchedulingConstraintDraftValidateQueryKey = (
	options?: Options<PostApiSchedulingConstraintDraftValidateData>
) => createQueryKey('postApiSchedulingConstraintDraftValidate', options);

export const postApiSchedulingConstraintDraftValidateOptions = (
	options?: Options<PostApiSchedulingConstraintDraftValidateData>
) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await postApiSchedulingConstraintDraftValidate({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: postApiSchedulingConstraintDraftValidateQueryKey(options),
	});
};

export const postApiSchedulingConstraintDraftValidateMutation = (
	options?: Partial<Options<PostApiSchedulingConstraintDraftValidateData>>
) => {
	const mutationOptions: UseMutationOptions<
		PostApiSchedulingConstraintDraftValidateResponse,
		AxiosError<PostApiSchedulingConstraintDraftValidateError>,
		Options<PostApiSchedulingConstraintDraftValidateData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await postApiSchedulingConstraintDraftValidate({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const postApiSchedulingConstraintDraftByEntityIdValidateQueryKey = (
	options: Options<PostApiSchedulingConstraintDraftByEntityIdValidateData>
) => createQueryKey('postApiSchedulingConstraintDraftByEntityIdValidate', options);

export const postApiSchedulingConstraintDraftByEntityIdValidateOptions = (
	options: Options<PostApiSchedulingConstraintDraftByEntityIdValidateData>
) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await postApiSchedulingConstraintDraftByEntityIdValidate({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: postApiSchedulingConstraintDraftByEntityIdValidateQueryKey(options),
	});
};

export const postApiSchedulingConstraintDraftByEntityIdValidateMutation = (
	options?: Partial<Options<PostApiSchedulingConstraintDraftByEntityIdValidateData>>
) => {
	const mutationOptions: UseMutationOptions<
		PostApiSchedulingConstraintDraftByEntityIdValidateResponse,
		AxiosError<PostApiSchedulingConstraintDraftByEntityIdValidateError>,
		Options<PostApiSchedulingConstraintDraftByEntityIdValidateData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await postApiSchedulingConstraintDraftByEntityIdValidate({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const getApiStudyByIdQueryKey = (options: Options<GetApiStudyByIdData>) =>
	createQueryKey('getApiStudyById', options);

export const getApiStudyByIdOptions = (options: Options<GetApiStudyByIdData>) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await getApiStudyById({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: getApiStudyByIdQueryKey(options),
	});
};

export const patchApiStudyByIdMutation = (options?: Partial<Options<PatchApiStudyByIdData>>) => {
	const mutationOptions: UseMutationOptions<
		PatchApiStudyByIdResponse,
		AxiosError<PatchApiStudyByIdError>,
		Options<PatchApiStudyByIdData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await patchApiStudyById({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const putApiStudyByIdMutation = (options?: Partial<Options<PutApiStudyByIdData>>) => {
	const mutationOptions: UseMutationOptions<
		PutApiStudyByIdResponse,
		AxiosError<PutApiStudyByIdError>,
		Options<PutApiStudyByIdData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await putApiStudyById({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const postApiStudySearchQueryKey = (options?: Options<PostApiStudySearchData>) =>
	createQueryKey('postApiStudySearch', options);

export const postApiStudySearchOptions = (options?: Options<PostApiStudySearchData>) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await postApiStudySearch({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: postApiStudySearchQueryKey(options),
	});
};

export const postApiStudySearchInfiniteQueryKey = (
	options?: Options<PostApiStudySearchData>
): QueryKey<Options<PostApiStudySearchData>> => createQueryKey('postApiStudySearch', options, true);

export const postApiStudySearchInfiniteOptions = (options?: Options<PostApiStudySearchData>) => {
	return infiniteQueryOptions<
		PostApiStudySearchResponse,
		AxiosError<DefaultError>,
		InfiniteData<PostApiStudySearchResponse>,
		QueryKey<Options<PostApiStudySearchData>>,
		| number
		| Pick<QueryKey<Options<PostApiStudySearchData>>[0], 'body' | 'headers' | 'path' | 'query'>
	>(
		// @ts-ignore
		{
			queryFn: async ({ pageParam, queryKey, signal }) => {
				// @ts-ignore
				const page: Pick<
					QueryKey<Options<PostApiStudySearchData>>[0],
					'body' | 'headers' | 'path' | 'query'
				> =
					typeof pageParam === 'object'
						? pageParam
						: {
								query: {
									offset: pageParam,
								},
							};
				const params = createInfiniteParams(queryKey, page);
				const { data } = await postApiStudySearch({
					...options,
					...params,
					signal,
					throwOnError: true,
				});
				return data;
			},
			queryKey: postApiStudySearchInfiniteQueryKey(options),
		}
	);
};

export const postApiStudySearchMutation = (options?: Partial<Options<PostApiStudySearchData>>) => {
	const mutationOptions: UseMutationOptions<
		PostApiStudySearchResponse,
		AxiosError<DefaultError>,
		Options<PostApiStudySearchData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await postApiStudySearch({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const getApiStudyQueryKey = (options?: Options<GetApiStudyData>) =>
	createQueryKey('getApiStudy', options);

export const getApiStudyOptions = (options?: Options<GetApiStudyData>) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await getApiStudy({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: getApiStudyQueryKey(options),
	});
};

export const getApiStudyInfiniteQueryKey = (
	options?: Options<GetApiStudyData>
): QueryKey<Options<GetApiStudyData>> => createQueryKey('getApiStudy', options, true);

export const getApiStudyInfiniteOptions = (options?: Options<GetApiStudyData>) => {
	return infiniteQueryOptions<
		GetApiStudyResponse,
		AxiosError<DefaultError>,
		InfiniteData<GetApiStudyResponse>,
		QueryKey<Options<GetApiStudyData>>,
		number | Pick<QueryKey<Options<GetApiStudyData>>[0], 'body' | 'headers' | 'path' | 'query'>
	>(
		// @ts-ignore
		{
			queryFn: async ({ pageParam, queryKey, signal }) => {
				// @ts-ignore
				const page: Pick<
					QueryKey<Options<GetApiStudyData>>[0],
					'body' | 'headers' | 'path' | 'query'
				> =
					typeof pageParam === 'object'
						? pageParam
						: {
								query: {
									offset: pageParam,
								},
							};
				const params = createInfiniteParams(queryKey, page);
				const { data } = await getApiStudy({
					...options,
					...params,
					signal,
					throwOnError: true,
				});
				return data;
			},
			queryKey: getApiStudyInfiniteQueryKey(options),
		}
	);
};

export const postApiStudyQueryKey = (options?: Options<PostApiStudyData>) =>
	createQueryKey('postApiStudy', options);

export const postApiStudyOptions = (options?: Options<PostApiStudyData>) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await postApiStudy({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: postApiStudyQueryKey(options),
	});
};

export const postApiStudyMutation = (options?: Partial<Options<PostApiStudyData>>) => {
	const mutationOptions: UseMutationOptions<
		PostApiStudyResponse,
		AxiosError<PostApiStudyError>,
		Options<PostApiStudyData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await postApiStudy({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

// export const postApiStudyDraftQueryKey = (options?: Options<PostApiStudyDraftData>) =>
// 	createQueryKey('postApiStudyDraft', options);

// export const postApiStudyDraftOptions = (options?: Options<PostApiStudyDraftData>) => {
// 	return queryOptions({
// 		queryFn: async ({ queryKey, signal }) => {
// 			const { data } = await postApiStudyDraft({
// 				...options,
// 				...queryKey[0],
// 				signal,
// 				throwOnError: true,
// 			});
// 			return data;
// 		},
// 		queryKey: postApiStudyDraftQueryKey(options),
// 	});
// };

// export const postApiStudyDraftMutation = (options?: Partial<Options<PostApiStudyDraftData>>) => {
// 	const mutationOptions: UseMutationOptions<
// 		PostApiStudyDraftResponse,
// 		AxiosError<PostApiStudyDraftError>,
// 		Options<PostApiStudyDraftData>
// 	> = {
// 		mutationFn: async (localOptions) => {
// 			const { data } = await postApiStudyDraft({
// 				...options,
// 				...localOptions,
// 				throwOnError: true,
// 			});
// 			return data;
// 		},
// 	};
// 	return mutationOptions;
// };

// export const getApiStudyDraftByEntityIdQueryKey = (
// 	options: Options<GetApiStudyDraftByEntityIdData>
// ) => createQueryKey('getApiStudyDraftByEntityId', options);

// export const getApiStudyDraftByEntityIdOptions = (
// 	options: Options<GetApiStudyDraftByEntityIdData>
// ) => {
// 	return queryOptions({
// 		queryFn: async ({ queryKey, signal }) => {
// 			const { data } = await getApiStudyDraftByEntityId({
// 				...options,
// 				...queryKey[0],
// 				signal,
// 				throwOnError: true,
// 			});
// 			return data;
// 		},
// 		queryKey: getApiStudyDraftByEntityIdQueryKey(options),
// 	});
// };

// export const putApiStudyDraftByEntityIdMutation = (
// 	options?: Partial<Options<PutApiStudyDraftByEntityIdData>>
// ) => {
// 	const mutationOptions: UseMutationOptions<
// 		PutApiStudyDraftByEntityIdResponse,
// 		AxiosError<PutApiStudyDraftByEntityIdError>,
// 		Options<PutApiStudyDraftByEntityIdData>
// 	> = {
// 		mutationFn: async (localOptions) => {
// 			const { data } = await putApiStudyDraftByEntityId({
// 				...options,
// 				...localOptions,
// 				throwOnError: true,
// 			});
// 			return data;
// 		},
// 	};
// 	return mutationOptions;
// };

// export const postApiStudyDraftByEntityIdCommitQueryKey = (
// 	options: Options<PostApiStudyDraftByEntityIdCommitData>
// ) => createQueryKey('postApiStudyDraftByEntityIdCommit', options);

// export const postApiStudyDraftByEntityIdCommitOptions = (
// 	options: Options<PostApiStudyDraftByEntityIdCommitData>
// ) => {
// 	return queryOptions({
// 		queryFn: async ({ queryKey, signal }) => {
// 			const { data } = await postApiStudyDraftByEntityIdCommit({
// 				...options,
// 				...queryKey[0],
// 				signal,
// 				throwOnError: true,
// 			});
// 			return data;
// 		},
// 		queryKey: postApiStudyDraftByEntityIdCommitQueryKey(options),
// 	});
// };

// export const postApiStudyDraftByEntityIdCommitMutation = (
// 	options?: Partial<Options<PostApiStudyDraftByEntityIdCommitData>>
// ) => {
// 	const mutationOptions: UseMutationOptions<
// 		PostApiStudyDraftByEntityIdCommitResponse,
// 		AxiosError<PostApiStudyDraftByEntityIdCommitError>,
// 		Options<PostApiStudyDraftByEntityIdCommitData>
// 	> = {
// 		mutationFn: async (localOptions) => {
// 			const { data } = await postApiStudyDraftByEntityIdCommit({
// 				...options,
// 				...localOptions,
// 				throwOnError: true,
// 			});
// 			return data;
// 		},
// 	};
// 	return mutationOptions;
// };

// export const postApiStudyDraftValidateQueryKey = (
// 	options?: Options<PostApiStudyDraftValidateData>
// ) => createQueryKey('postApiStudyDraftValidate', options);

// export const postApiStudyDraftValidateOptions = (
// 	options?: Options<PostApiStudyDraftValidateData>
// ) => {
// 	return queryOptions({
// 		queryFn: async ({ queryKey, signal }) => {
// 			const { data } = await postApiStudyDraftValidate({
// 				...options,
// 				...queryKey[0],
// 				signal,
// 				throwOnError: true,
// 			});
// 			return data;
// 		},
// 		queryKey: postApiStudyDraftValidateQueryKey(options),
// 	});
// };

// export const postApiStudyDraftValidateMutation = (
// 	options?: Partial<Options<PostApiStudyDraftValidateData>>
// ) => {
// 	const mutationOptions: UseMutationOptions<
// 		PostApiStudyDraftValidateResponse,
// 		AxiosError<PostApiStudyDraftValidateError>,
// 		Options<PostApiStudyDraftValidateData>
// 	> = {
// 		mutationFn: async (localOptions) => {
// 			const { data } = await postApiStudyDraftValidate({
// 				...options,
// 				...localOptions,
// 				throwOnError: true,
// 			});
// 			return data;
// 		},
// 	};
// 	return mutationOptions;
// };

// export const postApiStudyDraftByEntityIdValidateQueryKey = (
// 	options: Options<PostApiStudyDraftByEntityIdValidateData>
// ) => createQueryKey('postApiStudyDraftByEntityIdValidate', options);

// export const postApiStudyDraftByEntityIdValidateOptions = (
// 	options: Options<PostApiStudyDraftByEntityIdValidateData>
// ) => {
// 	return queryOptions({
// 		queryFn: async ({ queryKey, signal }) => {
// 			const { data } = await postApiStudyDraftByEntityIdValidate({
// 				...options,
// 				...queryKey[0],
// 				signal,
// 				throwOnError: true,
// 			});
// 			return data;
// 		},
// 		queryKey: postApiStudyDraftByEntityIdValidateQueryKey(options),
// 	});
// };

// export const postApiStudyDraftByEntityIdValidateMutation = (
// 	options?: Partial<Options<PostApiStudyDraftByEntityIdValidateData>>
// ) => {
// 	const mutationOptions: UseMutationOptions<
// 		PostApiStudyDraftByEntityIdValidateResponse,
// 		AxiosError<PostApiStudyDraftByEntityIdValidateError>,
// 		Options<PostApiStudyDraftByEntityIdValidateData>
// 	> = {
// 		mutationFn: async (localOptions) => {
// 			const { data } = await postApiStudyDraftByEntityIdValidate({
// 				...options,
// 				...localOptions,
// 				throwOnError: true,
// 			});
// 			return data;
// 		},
// 	};
// 	return mutationOptions;
// };

export const postApiStudyDraftQueryKey = (options?: Options<PostApiStudyDraftData>) =>
	createQueryKey('postApiStudyDraft', options);

export const postApiStudyDraftOptions = (options?: Options<PostApiStudyDraftData>) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await postApiStudyDraft({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: postApiStudyDraftQueryKey(options),
	});
};

export const postApiStudyDraftMutation = (options?: Partial<Options<PostApiStudyDraftData>>) => {
	const mutationOptions: UseMutationOptions<
		PostApiStudyDraftResponse,
		AxiosError<PostApiStudyDraftError>,
		Options<PostApiStudyDraftData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await postApiStudyDraft({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const getApiStudyDraftByEntityIdQueryKey = (
	options: Options<GetApiStudyDraftByEntityIdData>
) => createQueryKey('getApiStudyDraftByEntityId', options);

export const getApiStudyDraftByEntityIdOptions = (
	options: Options<GetApiStudyDraftByEntityIdData>
) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await getApiStudyDraftByEntityId({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: getApiStudyDraftByEntityIdQueryKey(options),
	});
};

export const putApiStudyDraftByEntityIdMutation = (
	options?: Partial<Options<PutApiStudyDraftByEntityIdData>>
) => {
	const mutationOptions: UseMutationOptions<
		PutApiStudyDraftByEntityIdResponse,
		AxiosError<PutApiStudyDraftByEntityIdError>,
		Options<PutApiStudyDraftByEntityIdData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await putApiStudyDraftByEntityId({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const postApiStudyDraftByEntityIdCommitQueryKey = (
	options: Options<PostApiStudyDraftByEntityIdCommitData>
) => createQueryKey('postApiStudyDraftByEntityIdCommit', options);

export const postApiStudyDraftByEntityIdCommitOptions = (
	options: Options<PostApiStudyDraftByEntityIdCommitData>
) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await postApiStudyDraftByEntityIdCommit({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: postApiStudyDraftByEntityIdCommitQueryKey(options),
	});
};

export const postApiStudyDraftByEntityIdCommitMutation = (
	options?: Partial<Options<PostApiStudyDraftByEntityIdCommitData>>
) => {
	const mutationOptions: UseMutationOptions<
		PostApiStudyDraftByEntityIdCommitResponse,
		AxiosError<PostApiStudyDraftByEntityIdCommitError>,
		Options<PostApiStudyDraftByEntityIdCommitData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await postApiStudyDraftByEntityIdCommit({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const postApiStudyDraftValidateQueryKey = (
	options?: Options<PostApiStudyDraftValidateData>
) => createQueryKey('postApiStudyDraftValidate', options);

export const postApiStudyDraftValidateOptions = (
	options?: Options<PostApiStudyDraftValidateData>
) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await postApiStudyDraftValidate({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: postApiStudyDraftValidateQueryKey(options),
	});
};

export const postApiStudyDraftValidateMutation = (
	options?: Partial<Options<PostApiStudyDraftValidateData>>
) => {
	const mutationOptions: UseMutationOptions<
		PostApiStudyDraftValidateResponse,
		AxiosError<PostApiStudyDraftValidateError>,
		Options<PostApiStudyDraftValidateData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await postApiStudyDraftValidate({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const postApiStudyDraftByEntityIdValidateQueryKey = (
	options: Options<PostApiStudyDraftByEntityIdValidateData>
) => createQueryKey('postApiStudyDraftByEntityIdValidate', options);

export const postApiStudyDraftByEntityIdValidateOptions = (
	options: Options<PostApiStudyDraftByEntityIdValidateData>
) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await postApiStudyDraftByEntityIdValidate({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: postApiStudyDraftByEntityIdValidateQueryKey(options),
	});
};

export const postApiStudyDraftByEntityIdValidateMutation = (
	options?: Partial<Options<PostApiStudyDraftByEntityIdValidateData>>
) => {
	const mutationOptions: UseMutationOptions<
		PostApiStudyDraftByEntityIdValidateResponse,
		AxiosError<PostApiStudyDraftByEntityIdValidateError>,
		Options<PostApiStudyDraftByEntityIdValidateData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await postApiStudyDraftByEntityIdValidate({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const getApiTechnicianByIdQueryKey = (options: Options<GetApiTechnicianByIdData>) =>
	createQueryKey('getApiTechnicianById', options);

export const getApiTechnicianByIdOptions = (options: Options<GetApiTechnicianByIdData>) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await getApiTechnicianById({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: getApiTechnicianByIdQueryKey(options),
	});
};

export const patchApiTechnicianByIdMutation = (
	options?: Partial<Options<PatchApiTechnicianByIdData>>
) => {
	const mutationOptions: UseMutationOptions<
		PatchApiTechnicianByIdResponse,
		AxiosError<PatchApiTechnicianByIdError>,
		Options<PatchApiTechnicianByIdData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await patchApiTechnicianById({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const putApiTechnicianByIdMutation = (
	options?: Partial<Options<PutApiTechnicianByIdData>>
) => {
	const mutationOptions: UseMutationOptions<
		PutApiTechnicianByIdResponse,
		AxiosError<PutApiTechnicianByIdError>,
		Options<PutApiTechnicianByIdData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await putApiTechnicianById({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const postApiTechnicianSearchQueryKey = (options?: Options<PostApiTechnicianSearchData>) =>
	createQueryKey('postApiTechnicianSearch', options);

export const postApiTechnicianSearchOptions = (options?: Options<PostApiTechnicianSearchData>) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await postApiTechnicianSearch({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: postApiTechnicianSearchQueryKey(options),
	});
};

export const postApiTechnicianSearchInfiniteQueryKey = (
	options?: Options<PostApiTechnicianSearchData>
): QueryKey<Options<PostApiTechnicianSearchData>> =>
	createQueryKey('postApiTechnicianSearch', options, true);

export const postApiTechnicianSearchInfiniteOptions = (
	options?: Options<PostApiTechnicianSearchData>
) => {
	return infiniteQueryOptions<
		PostApiTechnicianSearchResponse,
		AxiosError<DefaultError>,
		InfiniteData<PostApiTechnicianSearchResponse>,
		QueryKey<Options<PostApiTechnicianSearchData>>,
		| number
		| Pick<QueryKey<Options<PostApiTechnicianSearchData>>[0], 'body' | 'headers' | 'path' | 'query'>
	>(
		// @ts-ignore
		{
			queryFn: async ({ pageParam, queryKey, signal }) => {
				// @ts-ignore
				const page: Pick<
					QueryKey<Options<PostApiTechnicianSearchData>>[0],
					'body' | 'headers' | 'path' | 'query'
				> =
					typeof pageParam === 'object'
						? pageParam
						: {
								query: {
									offset: pageParam,
								},
							};
				const params = createInfiniteParams(queryKey, page);
				const { data } = await postApiTechnicianSearch({
					...options,
					...params,
					signal,
					throwOnError: true,
				});
				return data;
			},
			queryKey: postApiTechnicianSearchInfiniteQueryKey(options),
		}
	);
};

export const postApiTechnicianSearchMutation = (
	options?: Partial<Options<PostApiTechnicianSearchData>>
) => {
	const mutationOptions: UseMutationOptions<
		PostApiTechnicianSearchResponse,
		AxiosError<DefaultError>,
		Options<PostApiTechnicianSearchData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await postApiTechnicianSearch({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const getApiTechnicianQueryKey = (options?: Options<GetApiTechnicianData>) =>
	createQueryKey('getApiTechnician', options);

export const getApiTechnicianOptions = (options?: Options<GetApiTechnicianData>) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await getApiTechnician({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: getApiTechnicianQueryKey(options),
	});
};

export const getApiTechnicianInfiniteQueryKey = (
	options?: Options<GetApiTechnicianData>
): QueryKey<Options<GetApiTechnicianData>> => createQueryKey('getApiTechnician', options, true);

export const getApiTechnicianInfiniteOptions = (options?: Options<GetApiTechnicianData>) => {
	return infiniteQueryOptions<
		GetApiTechnicianResponse,
		AxiosError<DefaultError>,
		InfiniteData<GetApiTechnicianResponse>,
		QueryKey<Options<GetApiTechnicianData>>,
		number | Pick<QueryKey<Options<GetApiTechnicianData>>[0], 'body' | 'headers' | 'path' | 'query'>
	>(
		// @ts-ignore
		{
			queryFn: async ({ pageParam, queryKey, signal }) => {
				// @ts-ignore
				const page: Pick<
					QueryKey<Options<GetApiTechnicianData>>[0],
					'body' | 'headers' | 'path' | 'query'
				> =
					typeof pageParam === 'object'
						? pageParam
						: {
								query: {
									offset: pageParam,
								},
							};
				const params = createInfiniteParams(queryKey, page);
				const { data } = await getApiTechnician({
					...options,
					...params,
					signal,
					throwOnError: true,
				});
				return data;
			},
			queryKey: getApiTechnicianInfiniteQueryKey(options),
		}
	);
};

export const postApiTechnicianQueryKey = (options?: Options<PostApiTechnicianData>) =>
	createQueryKey('postApiTechnician', options);

export const postApiTechnicianOptions = (options?: Options<PostApiTechnicianData>) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await postApiTechnician({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: postApiTechnicianQueryKey(options),
	});
};

export const postApiTechnicianMutation = (options?: Partial<Options<PostApiTechnicianData>>) => {
	const mutationOptions: UseMutationOptions<
		PostApiTechnicianResponse,
		AxiosError<PostApiTechnicianError>,
		Options<PostApiTechnicianData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await postApiTechnician({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

// export const postApiTechnicianDraftQueryKey = (options?: Options<PostApiTechnicianDraftData>) =>
// 	createQueryKey('postApiTechnicianDraft', options);

// export const postApiTechnicianDraftOptions = (options?: Options<PostApiTechnicianDraftData>) => {
// 	return queryOptions({
// 		queryFn: async ({ queryKey, signal }) => {
// 			const { data } = await postApiTechnicianDraft({
// 				...options,
// 				...queryKey[0],
// 				signal,
// 				throwOnError: true,
// 			});
// 			return data;
// 		},
// 		queryKey: postApiTechnicianDraftQueryKey(options),
// 	});
// };

// export const postApiTechnicianDraftMutation = (
// 	options?: Partial<Options<PostApiTechnicianDraftData>>
// ) => {
// 	const mutationOptions: UseMutationOptions<
// 		PostApiTechnicianDraftResponse,
// 		AxiosError<PostApiTechnicianDraftError>,
// 		Options<PostApiTechnicianDraftData>
// 	> = {
// 		mutationFn: async (localOptions) => {
// 			const { data } = await postApiTechnicianDraft({
// 				...options,
// 				...localOptions,
// 				throwOnError: true,
// 			});
// 			return data;
// 		},
// 	};
// 	return mutationOptions;
// };

// export const getApiTechnicianDraftByEntityIdQueryKey = (
// 	options: Options<GetApiTechnicianDraftByEntityIdData>
// ) => createQueryKey('getApiTechnicianDraftByEntityId', options);

// export const getApiTechnicianDraftByEntityIdOptions = (
// 	options: Options<GetApiTechnicianDraftByEntityIdData>
// ) => {
// 	return queryOptions({
// 		queryFn: async ({ queryKey, signal }) => {
// 			const { data } = await getApiTechnicianDraftByEntityId({
// 				...options,
// 				...queryKey[0],
// 				signal,
// 				throwOnError: true,
// 			});
// 			return data;
// 		},
// 		queryKey: getApiTechnicianDraftByEntityIdQueryKey(options),
// 	});
// };

// export const putApiTechnicianDraftByEntityIdMutation = (
// 	options?: Partial<Options<PutApiTechnicianDraftByEntityIdData>>
// ) => {
// 	const mutationOptions: UseMutationOptions<
// 		PutApiTechnicianDraftByEntityIdResponse,
// 		AxiosError<PutApiTechnicianDraftByEntityIdError>,
// 		Options<PutApiTechnicianDraftByEntityIdData>
// 	> = {
// 		mutationFn: async (localOptions) => {
// 			const { data } = await putApiTechnicianDraftByEntityId({
// 				...options,
// 				...localOptions,
// 				throwOnError: true,
// 			});
// 			return data;
// 		},
// 	};
// 	return mutationOptions;
// };

// export const postApiTechnicianDraftByEntityIdCommitQueryKey = (
// 	options: Options<PostApiTechnicianDraftByEntityIdCommitData>
// ) => createQueryKey('postApiTechnicianDraftByEntityIdCommit', options);

// export const postApiTechnicianDraftByEntityIdCommitOptions = (
// 	options: Options<PostApiTechnicianDraftByEntityIdCommitData>
// ) => {
// 	return queryOptions({
// 		queryFn: async ({ queryKey, signal }) => {
// 			const { data } = await postApiTechnicianDraftByEntityIdCommit({
// 				...options,
// 				...queryKey[0],
// 				signal,
// 				throwOnError: true,
// 			});
// 			return data;
// 		},
// 		queryKey: postApiTechnicianDraftByEntityIdCommitQueryKey(options),
// 	});
// };

// export const postApiTechnicianDraftByEntityIdCommitMutation = (
// 	options?: Partial<Options<PostApiTechnicianDraftByEntityIdCommitData>>
// ) => {
// 	const mutationOptions: UseMutationOptions<
// 		PostApiTechnicianDraftByEntityIdCommitResponse,
// 		AxiosError<PostApiTechnicianDraftByEntityIdCommitError>,
// 		Options<PostApiTechnicianDraftByEntityIdCommitData>
// 	> = {
// 		mutationFn: async (localOptions) => {
// 			const { data } = await postApiTechnicianDraftByEntityIdCommit({
// 				...options,
// 				...localOptions,
// 				throwOnError: true,
// 			});
// 			return data;
// 		},
// 	};
// 	return mutationOptions;
// };

// export const postApiTechnicianDraftValidateQueryKey = (
// 	options?: Options<PostApiTechnicianDraftValidateData>
// ) => createQueryKey('postApiTechnicianDraftValidate', options);

// export const postApiTechnicianDraftValidateOptions = (
// 	options?: Options<PostApiTechnicianDraftValidateData>
// ) => {
// 	return queryOptions({
// 		queryFn: async ({ queryKey, signal }) => {
// 			const { data } = await postApiTechnicianDraftValidate({
// 				...options,
// 				...queryKey[0],
// 				signal,
// 				throwOnError: true,
// 			});
// 			return data;
// 		},
// 		queryKey: postApiTechnicianDraftValidateQueryKey(options),
// 	});
// };

// export const postApiTechnicianDraftValidateMutation = (
// 	options?: Partial<Options<PostApiTechnicianDraftValidateData>>
// ) => {
// 	const mutationOptions: UseMutationOptions<
// 		PostApiTechnicianDraftValidateResponse,
// 		AxiosError<PostApiTechnicianDraftValidateError>,
// 		Options<PostApiTechnicianDraftValidateData>
// 	> = {
// 		mutationFn: async (localOptions) => {
// 			const { data } = await postApiTechnicianDraftValidate({
// 				...options,
// 				...localOptions,
// 				throwOnError: true,
// 			});
// 			return data;
// 		},
// 	};
// 	return mutationOptions;
// };

// export const postApiTechnicianDraftByEntityIdValidateQueryKey = (
// 	options: Options<PostApiTechnicianDraftByEntityIdValidateData>
// ) => createQueryKey('postApiTechnicianDraftByEntityIdValidate', options);

// export const postApiTechnicianDraftByEntityIdValidateOptions = (
// 	options: Options<PostApiTechnicianDraftByEntityIdValidateData>
// ) => {
// 	return queryOptions({
// 		queryFn: async ({ queryKey, signal }) => {
// 			const { data } = await postApiTechnicianDraftByEntityIdValidate({
// 				...options,
// 				...queryKey[0],
// 				signal,
// 				throwOnError: true,
// 			});
// 			return data;
// 		},
// 		queryKey: postApiTechnicianDraftByEntityIdValidateQueryKey(options),
// 	});
// };

// export const postApiTechnicianDraftByEntityIdValidateMutation = (
// 	options?: Partial<Options<PostApiTechnicianDraftByEntityIdValidateData>>
// ) => {
// 	const mutationOptions: UseMutationOptions<
// 		PostApiTechnicianDraftByEntityIdValidateResponse,
// 		AxiosError<PostApiTechnicianDraftByEntityIdValidateError>,
// 		Options<PostApiTechnicianDraftByEntityIdValidateData>
// 	> = {
// 		mutationFn: async (localOptions) => {
// 			const { data } = await postApiTechnicianDraftByEntityIdValidate({
// 				...options,
// 				...localOptions,
// 				throwOnError: true,
// 			});
// 			return data;
// 		},
// 	};
// 	return mutationOptions;
// };

export const postApiTechnicianDraftQueryKey = (options?: Options<PostApiTechnicianDraftData>) =>
	createQueryKey('postApiTechnicianDraft', options);

export const postApiTechnicianDraftOptions = (options?: Options<PostApiTechnicianDraftData>) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await postApiTechnicianDraft({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: postApiTechnicianDraftQueryKey(options),
	});
};

export const postApiTechnicianDraftMutation = (
	options?: Partial<Options<PostApiTechnicianDraftData>>
) => {
	const mutationOptions: UseMutationOptions<
		PostApiTechnicianDraftResponse,
		AxiosError<PostApiTechnicianDraftError>,
		Options<PostApiTechnicianDraftData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await postApiTechnicianDraft({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const getApiTechnicianDraftByEntityIdQueryKey = (
	options: Options<GetApiTechnicianDraftByEntityIdData>
) => createQueryKey('getApiTechnicianDraftByEntityId', options);

export const getApiTechnicianDraftByEntityIdOptions = (
	options: Options<GetApiTechnicianDraftByEntityIdData>
) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await getApiTechnicianDraftByEntityId({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: getApiTechnicianDraftByEntityIdQueryKey(options),
	});
};

export const putApiTechnicianDraftByEntityIdMutation = (
	options?: Partial<Options<PutApiTechnicianDraftByEntityIdData>>
) => {
	const mutationOptions: UseMutationOptions<
		PutApiTechnicianDraftByEntityIdResponse,
		AxiosError<PutApiTechnicianDraftByEntityIdError>,
		Options<PutApiTechnicianDraftByEntityIdData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await putApiTechnicianDraftByEntityId({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const postApiTechnicianDraftByEntityIdCommitQueryKey = (
	options: Options<PostApiTechnicianDraftByEntityIdCommitData>
) => createQueryKey('postApiTechnicianDraftByEntityIdCommit', options);

export const postApiTechnicianDraftByEntityIdCommitOptions = (
	options: Options<PostApiTechnicianDraftByEntityIdCommitData>
) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await postApiTechnicianDraftByEntityIdCommit({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: postApiTechnicianDraftByEntityIdCommitQueryKey(options),
	});
};

export const postApiTechnicianDraftByEntityIdCommitMutation = (
	options?: Partial<Options<PostApiTechnicianDraftByEntityIdCommitData>>
) => {
	const mutationOptions: UseMutationOptions<
		PostApiTechnicianDraftByEntityIdCommitResponse,
		AxiosError<PostApiTechnicianDraftByEntityIdCommitError>,
		Options<PostApiTechnicianDraftByEntityIdCommitData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await postApiTechnicianDraftByEntityIdCommit({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const postApiTechnicianDraftValidateQueryKey = (
	options?: Options<PostApiTechnicianDraftValidateData>
) => createQueryKey('postApiTechnicianDraftValidate', options);

export const postApiTechnicianDraftValidateOptions = (
	options?: Options<PostApiTechnicianDraftValidateData>
) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await postApiTechnicianDraftValidate({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: postApiTechnicianDraftValidateQueryKey(options),
	});
};

export const postApiTechnicianDraftValidateMutation = (
	options?: Partial<Options<PostApiTechnicianDraftValidateData>>
) => {
	const mutationOptions: UseMutationOptions<
		PostApiTechnicianDraftValidateResponse,
		AxiosError<PostApiTechnicianDraftValidateError>,
		Options<PostApiTechnicianDraftValidateData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await postApiTechnicianDraftValidate({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const postApiTechnicianDraftByEntityIdValidateQueryKey = (
	options: Options<PostApiTechnicianDraftByEntityIdValidateData>
) => createQueryKey('postApiTechnicianDraftByEntityIdValidate', options);

export const postApiTechnicianDraftByEntityIdValidateOptions = (
	options: Options<PostApiTechnicianDraftByEntityIdValidateData>
) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await postApiTechnicianDraftByEntityIdValidate({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: postApiTechnicianDraftByEntityIdValidateQueryKey(options),
	});
};

export const postApiTechnicianDraftByEntityIdValidateMutation = (
	options?: Partial<Options<PostApiTechnicianDraftByEntityIdValidateData>>
) => {
	const mutationOptions: UseMutationOptions<
		PostApiTechnicianDraftByEntityIdValidateResponse,
		AxiosError<PostApiTechnicianDraftByEntityIdValidateError>,
		Options<PostApiTechnicianDraftByEntityIdValidateData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await postApiTechnicianDraftByEntityIdValidate({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const getApiTechnicianAppointmentByIdQueryKey = (
	options: Options<GetApiTechnicianAppointmentByIdData>
) => createQueryKey('getApiTechnicianAppointmentById', options);

export const getApiTechnicianAppointmentByIdOptions = (
	options: Options<GetApiTechnicianAppointmentByIdData>
) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await getApiTechnicianAppointmentById({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: getApiTechnicianAppointmentByIdQueryKey(options),
	});
};

export const patchApiTechnicianAppointmentByIdMutation = (
	options?: Partial<Options<PatchApiTechnicianAppointmentByIdData>>
) => {
	const mutationOptions: UseMutationOptions<
		PatchApiTechnicianAppointmentByIdResponse,
		AxiosError<PatchApiTechnicianAppointmentByIdError>,
		Options<PatchApiTechnicianAppointmentByIdData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await patchApiTechnicianAppointmentById({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const putApiTechnicianAppointmentByIdMutation = (
	options?: Partial<Options<PutApiTechnicianAppointmentByIdData>>
) => {
	const mutationOptions: UseMutationOptions<
		PutApiTechnicianAppointmentByIdResponse,
		AxiosError<PutApiTechnicianAppointmentByIdError>,
		Options<PutApiTechnicianAppointmentByIdData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await putApiTechnicianAppointmentById({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const postApiTechnicianAppointmentSearchQueryKey = (
	options?: Options<PostApiTechnicianAppointmentSearchData>
) => createQueryKey('postApiTechnicianAppointmentSearch', options);

export const postApiTechnicianAppointmentSearchOptions = (
	options?: Options<PostApiTechnicianAppointmentSearchData>
) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await postApiTechnicianAppointmentSearch({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: postApiTechnicianAppointmentSearchQueryKey(options),
	});
};

export const postApiTechnicianAppointmentSearchInfiniteQueryKey = (
	options?: Options<PostApiTechnicianAppointmentSearchData>
): QueryKey<Options<PostApiTechnicianAppointmentSearchData>> =>
	createQueryKey('postApiTechnicianAppointmentSearch', options, true);

export const postApiTechnicianAppointmentSearchInfiniteOptions = (
	options?: Options<PostApiTechnicianAppointmentSearchData>
) => {
	return infiniteQueryOptions<
		PostApiTechnicianAppointmentSearchResponse,
		AxiosError<DefaultError>,
		InfiniteData<PostApiTechnicianAppointmentSearchResponse>,
		QueryKey<Options<PostApiTechnicianAppointmentSearchData>>,
		| number
		| Pick<
				QueryKey<Options<PostApiTechnicianAppointmentSearchData>>[0],
				'body' | 'headers' | 'path' | 'query'
		  >
	>(
		// @ts-ignore
		{
			queryFn: async ({ pageParam, queryKey, signal }) => {
				// @ts-ignore
				const page: Pick<
					QueryKey<Options<PostApiTechnicianAppointmentSearchData>>[0],
					'body' | 'headers' | 'path' | 'query'
				> =
					typeof pageParam === 'object'
						? pageParam
						: {
								query: {
									offset: pageParam,
								},
							};
				const params = createInfiniteParams(queryKey, page);
				const { data } = await postApiTechnicianAppointmentSearch({
					...options,
					...params,
					signal,
					throwOnError: true,
				});
				return data;
			},
			queryKey: postApiTechnicianAppointmentSearchInfiniteQueryKey(options),
		}
	);
};

export const postApiTechnicianAppointmentSearchMutation = (
	options?: Partial<Options<PostApiTechnicianAppointmentSearchData>>
) => {
	const mutationOptions: UseMutationOptions<
		PostApiTechnicianAppointmentSearchResponse,
		AxiosError<DefaultError>,
		Options<PostApiTechnicianAppointmentSearchData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await postApiTechnicianAppointmentSearch({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const getApiTechnicianAppointmentQueryKey = (
	options?: Options<GetApiTechnicianAppointmentData>
) => createQueryKey('getApiTechnicianAppointment', options);

export const getApiTechnicianAppointmentOptions = (
	options?: Options<GetApiTechnicianAppointmentData>
) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await getApiTechnicianAppointment({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: getApiTechnicianAppointmentQueryKey(options),
	});
};

export const getApiTechnicianAppointmentInfiniteQueryKey = (
	options?: Options<GetApiTechnicianAppointmentData>
): QueryKey<Options<GetApiTechnicianAppointmentData>> =>
	createQueryKey('getApiTechnicianAppointment', options, true);

export const getApiTechnicianAppointmentInfiniteOptions = (
	options?: Options<GetApiTechnicianAppointmentData>
) => {
	return infiniteQueryOptions<
		GetApiTechnicianAppointmentResponse,
		AxiosError<DefaultError>,
		InfiniteData<GetApiTechnicianAppointmentResponse>,
		QueryKey<Options<GetApiTechnicianAppointmentData>>,
		| number
		| Pick<
				QueryKey<Options<GetApiTechnicianAppointmentData>>[0],
				'body' | 'headers' | 'path' | 'query'
		  >
	>(
		// @ts-ignore
		{
			queryFn: async ({ pageParam, queryKey, signal }) => {
				// @ts-ignore
				const page: Pick<
					QueryKey<Options<GetApiTechnicianAppointmentData>>[0],
					'body' | 'headers' | 'path' | 'query'
				> =
					typeof pageParam === 'object'
						? pageParam
						: {
								query: {
									offset: pageParam,
								},
							};
				const params = createInfiniteParams(queryKey, page);
				const { data } = await getApiTechnicianAppointment({
					...options,
					...params,
					signal,
					throwOnError: true,
				});
				return data;
			},
			queryKey: getApiTechnicianAppointmentInfiniteQueryKey(options),
		}
	);
};

export const postApiTechnicianAppointmentQueryKey = (
	options?: Options<PostApiTechnicianAppointmentData>
) => createQueryKey('postApiTechnicianAppointment', options);

export const postApiTechnicianAppointmentOptions = (
	options?: Options<PostApiTechnicianAppointmentData>
) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await postApiTechnicianAppointment({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: postApiTechnicianAppointmentQueryKey(options),
	});
};

export const postApiTechnicianAppointmentMutation = (
	options?: Partial<Options<PostApiTechnicianAppointmentData>>
) => {
	const mutationOptions: UseMutationOptions<
		PostApiTechnicianAppointmentResponse,
		AxiosError<PostApiTechnicianAppointmentError>,
		Options<PostApiTechnicianAppointmentData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await postApiTechnicianAppointment({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

// export const postApiTechnicianAppointmentDraftQueryKey = (
// 	options?: Options<PostApiTechnicianAppointmentDraftData>
// ) => createQueryKey('postApiTechnicianAppointmentDraft', options);

// export const postApiTechnicianAppointmentDraftOptions = (
// 	options?: Options<PostApiTechnicianAppointmentDraftData>
// ) => {
// 	return queryOptions({
// 		queryFn: async ({ queryKey, signal }) => {
// 			const { data } = await postApiTechnicianAppointmentDraft({
// 				...options,
// 				...queryKey[0],
// 				signal,
// 				throwOnError: true,
// 			});
// 			return data;
// 		},
// 		queryKey: postApiTechnicianAppointmentDraftQueryKey(options),
// 	});
// };

// export const postApiTechnicianAppointmentDraftMutation = (
// 	options?: Partial<Options<PostApiTechnicianAppointmentDraftData>>
// ) => {
// 	const mutationOptions: UseMutationOptions<
// 		PostApiTechnicianAppointmentDraftResponse,
// 		AxiosError<PostApiTechnicianAppointmentDraftError>,
// 		Options<PostApiTechnicianAppointmentDraftData>
// 	> = {
// 		mutationFn: async (localOptions) => {
// 			const { data } = await postApiTechnicianAppointmentDraft({
// 				...options,
// 				...localOptions,
// 				throwOnError: true,
// 			});
// 			return data;
// 		},
// 	};
// 	return mutationOptions;
// };

// export const getApiTechnicianAppointmentDraftByEntityIdQueryKey = (
// 	options: Options<GetApiTechnicianAppointmentDraftByEntityIdData>
// ) => createQueryKey('getApiTechnicianAppointmentDraftByEntityId', options);

// export const getApiTechnicianAppointmentDraftByEntityIdOptions = (
// 	options: Options<GetApiTechnicianAppointmentDraftByEntityIdData>
// ) => {
// 	return queryOptions({
// 		queryFn: async ({ queryKey, signal }) => {
// 			const { data } = await getApiTechnicianAppointmentDraftByEntityId({
// 				...options,
// 				...queryKey[0],
// 				signal,
// 				throwOnError: true,
// 			});
// 			return data;
// 		},
// 		queryKey: getApiTechnicianAppointmentDraftByEntityIdQueryKey(options),
// 	});
// };

// export const putApiTechnicianAppointmentDraftByEntityIdMutation = (
// 	options?: Partial<Options<PutApiTechnicianAppointmentDraftByEntityIdData>>
// ) => {
// 	const mutationOptions: UseMutationOptions<
// 		PutApiTechnicianAppointmentDraftByEntityIdResponse,
// 		AxiosError<PutApiTechnicianAppointmentDraftByEntityIdError>,
// 		Options<PutApiTechnicianAppointmentDraftByEntityIdData>
// 	> = {
// 		mutationFn: async (localOptions) => {
// 			const { data } = await putApiTechnicianAppointmentDraftByEntityId({
// 				...options,
// 				...localOptions,
// 				throwOnError: true,
// 			});
// 			return data;
// 		},
// 	};
// 	return mutationOptions;
// };

// export const postApiTechnicianAppointmentDraftByEntityIdCommitQueryKey = (
// 	options: Options<PostApiTechnicianAppointmentDraftByEntityIdCommitData>
// ) => createQueryKey('postApiTechnicianAppointmentDraftByEntityIdCommit', options);

// export const postApiTechnicianAppointmentDraftByEntityIdCommitOptions = (
// 	options: Options<PostApiTechnicianAppointmentDraftByEntityIdCommitData>
// ) => {
// 	return queryOptions({
// 		queryFn: async ({ queryKey, signal }) => {
// 			const { data } = await postApiTechnicianAppointmentDraftByEntityIdCommit({
// 				...options,
// 				...queryKey[0],
// 				signal,
// 				throwOnError: true,
// 			});
// 			return data;
// 		},
// 		queryKey: postApiTechnicianAppointmentDraftByEntityIdCommitQueryKey(options),
// 	});
// };

// export const postApiTechnicianAppointmentDraftByEntityIdCommitMutation = (
// 	options?: Partial<Options<PostApiTechnicianAppointmentDraftByEntityIdCommitData>>
// ) => {
// 	const mutationOptions: UseMutationOptions<
// 		PostApiTechnicianAppointmentDraftByEntityIdCommitResponse,
// 		AxiosError<PostApiTechnicianAppointmentDraftByEntityIdCommitError>,
// 		Options<PostApiTechnicianAppointmentDraftByEntityIdCommitData>
// 	> = {
// 		mutationFn: async (localOptions) => {
// 			const { data } = await postApiTechnicianAppointmentDraftByEntityIdCommit({
// 				...options,
// 				...localOptions,
// 				throwOnError: true,
// 			});
// 			return data;
// 		},
// 	};
// 	return mutationOptions;
// };

// export const postApiTechnicianAppointmentDraftValidateQueryKey = (
// 	options?: Options<PostApiTechnicianAppointmentDraftValidateData>
// ) => createQueryKey('postApiTechnicianAppointmentDraftValidate', options);

// export const postApiTechnicianAppointmentDraftValidateOptions = (
// 	options?: Options<PostApiTechnicianAppointmentDraftValidateData>
// ) => {
// 	return queryOptions({
// 		queryFn: async ({ queryKey, signal }) => {
// 			const { data } = await postApiTechnicianAppointmentDraftValidate({
// 				...options,
// 				...queryKey[0],
// 				signal,
// 				throwOnError: true,
// 			});
// 			return data;
// 		},
// 		queryKey: postApiTechnicianAppointmentDraftValidateQueryKey(options),
// 	});
// };

// export const postApiTechnicianAppointmentDraftValidateMutation = (
// 	options?: Partial<Options<PostApiTechnicianAppointmentDraftValidateData>>
// ) => {
// 	const mutationOptions: UseMutationOptions<
// 		PostApiTechnicianAppointmentDraftValidateResponse,
// 		AxiosError<PostApiTechnicianAppointmentDraftValidateError>,
// 		Options<PostApiTechnicianAppointmentDraftValidateData>
// 	> = {
// 		mutationFn: async (localOptions) => {
// 			const { data } = await postApiTechnicianAppointmentDraftValidate({
// 				...options,
// 				...localOptions,
// 				throwOnError: true,
// 			});
// 			return data;
// 		},
// 	};
// 	return mutationOptions;
// };

// export const postApiTechnicianAppointmentDraftByEntityIdValidateQueryKey = (
// 	options: Options<PostApiTechnicianAppointmentDraftByEntityIdValidateData>
// ) => createQueryKey('postApiTechnicianAppointmentDraftByEntityIdValidate', options);

// export const postApiTechnicianAppointmentDraftByEntityIdValidateOptions = (
// 	options: Options<PostApiTechnicianAppointmentDraftByEntityIdValidateData>
// ) => {
// 	return queryOptions({
// 		queryFn: async ({ queryKey, signal }) => {
// 			const { data } = await postApiTechnicianAppointmentDraftByEntityIdValidate({
// 				...options,
// 				...queryKey[0],
// 				signal,
// 				throwOnError: true,
// 			});
// 			return data;
// 		},
// 		queryKey: postApiTechnicianAppointmentDraftByEntityIdValidateQueryKey(options),
// 	});
// };

// export const postApiTechnicianAppointmentDraftByEntityIdValidateMutation = (
// 	options?: Partial<Options<PostApiTechnicianAppointmentDraftByEntityIdValidateData>>
// ) => {
// 	const mutationOptions: UseMutationOptions<
// 		PostApiTechnicianAppointmentDraftByEntityIdValidateResponse,
// 		AxiosError<PostApiTechnicianAppointmentDraftByEntityIdValidateError>,
// 		Options<PostApiTechnicianAppointmentDraftByEntityIdValidateData>
// 	> = {
// 		mutationFn: async (localOptions) => {
// 			const { data } = await postApiTechnicianAppointmentDraftByEntityIdValidate({
// 				...options,
// 				...localOptions,
// 				throwOnError: true,
// 			});
// 			return data;
// 		},
// 	};
// 	return mutationOptions;
// };

export const postApiTechnicianAppointmentDraftQueryKey = (
	options?: Options<PostApiTechnicianAppointmentDraftData>
) => createQueryKey('postApiTechnicianAppointmentDraft', options);

export const postApiTechnicianAppointmentDraftOptions = (
	options?: Options<PostApiTechnicianAppointmentDraftData>
) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await postApiTechnicianAppointmentDraft({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: postApiTechnicianAppointmentDraftQueryKey(options),
	});
};

export const postApiTechnicianAppointmentDraftMutation = (
	options?: Partial<Options<PostApiTechnicianAppointmentDraftData>>
) => {
	const mutationOptions: UseMutationOptions<
		PostApiTechnicianAppointmentDraftResponse,
		AxiosError<PostApiTechnicianAppointmentDraftError>,
		Options<PostApiTechnicianAppointmentDraftData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await postApiTechnicianAppointmentDraft({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const getApiTechnicianAppointmentDraftByEntityIdQueryKey = (
	options: Options<GetApiTechnicianAppointmentDraftByEntityIdData>
) => createQueryKey('getApiTechnicianAppointmentDraftByEntityId', options);

export const getApiTechnicianAppointmentDraftByEntityIdOptions = (
	options: Options<GetApiTechnicianAppointmentDraftByEntityIdData>
) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await getApiTechnicianAppointmentDraftByEntityId({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: getApiTechnicianAppointmentDraftByEntityIdQueryKey(options),
	});
};

export const putApiTechnicianAppointmentDraftByEntityIdMutation = (
	options?: Partial<Options<PutApiTechnicianAppointmentDraftByEntityIdData>>
) => {
	const mutationOptions: UseMutationOptions<
		PutApiTechnicianAppointmentDraftByEntityIdResponse,
		AxiosError<PutApiTechnicianAppointmentDraftByEntityIdError>,
		Options<PutApiTechnicianAppointmentDraftByEntityIdData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await putApiTechnicianAppointmentDraftByEntityId({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const postApiTechnicianAppointmentDraftByEntityIdCommitQueryKey = (
	options: Options<PostApiTechnicianAppointmentDraftByEntityIdCommitData>
) => createQueryKey('postApiTechnicianAppointmentDraftByEntityIdCommit', options);

export const postApiTechnicianAppointmentDraftByEntityIdCommitOptions = (
	options: Options<PostApiTechnicianAppointmentDraftByEntityIdCommitData>
) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await postApiTechnicianAppointmentDraftByEntityIdCommit({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: postApiTechnicianAppointmentDraftByEntityIdCommitQueryKey(options),
	});
};

export const postApiTechnicianAppointmentDraftByEntityIdCommitMutation = (
	options?: Partial<Options<PostApiTechnicianAppointmentDraftByEntityIdCommitData>>
) => {
	const mutationOptions: UseMutationOptions<
		PostApiTechnicianAppointmentDraftByEntityIdCommitResponse,
		AxiosError<PostApiTechnicianAppointmentDraftByEntityIdCommitError>,
		Options<PostApiTechnicianAppointmentDraftByEntityIdCommitData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await postApiTechnicianAppointmentDraftByEntityIdCommit({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const postApiTechnicianAppointmentDraftValidateQueryKey = (
	options?: Options<PostApiTechnicianAppointmentDraftValidateData>
) => createQueryKey('postApiTechnicianAppointmentDraftValidate', options);

export const postApiTechnicianAppointmentDraftValidateOptions = (
	options?: Options<PostApiTechnicianAppointmentDraftValidateData>
) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await postApiTechnicianAppointmentDraftValidate({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: postApiTechnicianAppointmentDraftValidateQueryKey(options),
	});
};

export const postApiTechnicianAppointmentDraftValidateMutation = (
	options?: Partial<Options<PostApiTechnicianAppointmentDraftValidateData>>
) => {
	const mutationOptions: UseMutationOptions<
		PostApiTechnicianAppointmentDraftValidateResponse,
		AxiosError<PostApiTechnicianAppointmentDraftValidateError>,
		Options<PostApiTechnicianAppointmentDraftValidateData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await postApiTechnicianAppointmentDraftValidate({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const postApiTechnicianAppointmentDraftByEntityIdValidateQueryKey = (
	options: Options<PostApiTechnicianAppointmentDraftByEntityIdValidateData>
) => createQueryKey('postApiTechnicianAppointmentDraftByEntityIdValidate', options);

export const postApiTechnicianAppointmentDraftByEntityIdValidateOptions = (
	options: Options<PostApiTechnicianAppointmentDraftByEntityIdValidateData>
) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await postApiTechnicianAppointmentDraftByEntityIdValidate({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: postApiTechnicianAppointmentDraftByEntityIdValidateQueryKey(options),
	});
};

export const postApiTechnicianAppointmentDraftByEntityIdValidateMutation = (
	options?: Partial<Options<PostApiTechnicianAppointmentDraftByEntityIdValidateData>>
) => {
	const mutationOptions: UseMutationOptions<
		PostApiTechnicianAppointmentDraftByEntityIdValidateResponse,
		AxiosError<PostApiTechnicianAppointmentDraftByEntityIdValidateError>,
		Options<PostApiTechnicianAppointmentDraftByEntityIdValidateData>
	> = {
		mutationFn: async (localOptions) => {
			const { data } = await postApiTechnicianAppointmentDraftByEntityIdValidate({
				...options,
				...localOptions,
				throwOnError: true,
			});
			return data;
		},
	};
	return mutationOptions;
};

export const getApiWorkflowFlowsQueryKey = (options?: Options<GetApiWorkflowFlowsData>) =>
	createQueryKey('getApiWorkflowFlows', options);

export const getApiWorkflowFlowsOptions = (options?: Options<GetApiWorkflowFlowsData>) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await getApiWorkflowFlows({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: getApiWorkflowFlowsQueryKey(options),
	});
};

export const getApiWorkflowMyTasksQueryKey = (options?: Options<GetApiWorkflowMyTasksData>) =>
	createQueryKey('getApiWorkflowMyTasks', options);

export const getApiWorkflowMyTasksOptions = (options?: Options<GetApiWorkflowMyTasksData>) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await getApiWorkflowMyTasks({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: getApiWorkflowMyTasksQueryKey(options),
	});
};

export const getApiWorkflowProfileQueryKey = (options?: Options<GetApiWorkflowProfileData>) =>
	createQueryKey('getApiWorkflowProfile', options);

export const getApiWorkflowProfileOptions = (options?: Options<GetApiWorkflowProfileData>) => {
	return queryOptions({
		queryFn: async ({ queryKey, signal }) => {
			const { data } = await getApiWorkflowProfile({
				...options,
				...queryKey[0],
				signal,
				throwOnError: true,
			});
			return data;
		},
		queryKey: getApiWorkflowProfileQueryKey(options),
	});
};
