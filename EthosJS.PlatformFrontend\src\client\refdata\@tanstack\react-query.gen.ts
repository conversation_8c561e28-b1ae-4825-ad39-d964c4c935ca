// This file is auto-generated by @hey-api/openapi-ts

import { type Options, postApiReferenceBootstrap, deleteApiReferenceSetsBySetId, getApiReferenceSetsBySetId, deleteApiReferenceSets, getApiReferenceSets, postApiReferenceSets, getApiReferenceJobsByJobId, getApiReferenceListTypes, getApiReferenceListsByListId, postApiReferenceLists, getApiReferenceListsByListIdValues, postApiReferenceListsByListIdValues, putApiReferenceListsByListIdValues, getApiReferenceSetsBySetIdValues, postApiReferenceSetsBySetIdValues, getApiReferenceSetsBySetIdAlternates, postApiReferenceSetsBySetIdAlternates, postApiReferenceSetsBySetIdAlternatesProcess, getApiReferenceSetsBySetIdValuesByValueId, getApiReferenceSetsKeysById, getApiReferenceSetsKeys, getApiReferenceSetsValues, postApiReferenceValidate, getApiReferenceSearchKeys, getApiReferenceSearchValues, postApiReferenceInternalImportCsv, postApiReferenceInternalIntake } from '../sdk.gen';
import { queryOptions, type UseMutationOptions, type DefaultError, infiniteQueryOptions, type InfiniteData } from '@tanstack/react-query';
import type { PostApiReferenceBootstrapData, DeleteApiReferenceSetsBySetIdData, GetApiReferenceSetsBySetIdData, DeleteApiReferenceSetsData, GetApiReferenceSetsData, GetApiReferenceSetsResponse, PostApiReferenceSetsData, GetApiReferenceJobsByJobIdData, GetApiReferenceListTypesData, GetApiReferenceListTypesResponse, GetApiReferenceListsByListIdData, PostApiReferenceListsData, GetApiReferenceListsByListIdValuesData, PostApiReferenceListsByListIdValuesData, PutApiReferenceListsByListIdValuesData, GetApiReferenceSetsBySetIdValuesData, PostApiReferenceSetsBySetIdValuesData, GetApiReferenceSetsBySetIdAlternatesData, PostApiReferenceSetsBySetIdAlternatesData, PostApiReferenceSetsBySetIdAlternatesProcessData, GetApiReferenceSetsBySetIdValuesByValueIdData, GetApiReferenceSetsKeysByIdData, GetApiReferenceSetsKeysData, GetApiReferenceSetsValuesData, PostApiReferenceValidateData, GetApiReferenceSearchKeysData, GetApiReferenceSearchValuesData, PostApiReferenceInternalImportCsvData, PostApiReferenceInternalImportCsvResponse, PostApiReferenceInternalIntakeData, PostApiReferenceInternalIntakeResponse } from '../types.gen';
import type { AxiosError } from 'axios';
import { client as _heyApiClient } from '../client.gen';

export type QueryKey<TOptions extends Options> = [
    Pick<TOptions, 'baseURL' | 'body' | 'headers' | 'path' | 'query'> & {
        _id: string;
        _infinite?: boolean;
    }
];

const createQueryKey = <TOptions extends Options>(id: string, options?: TOptions, infinite?: boolean): [
    QueryKey<TOptions>[0]
] => {
    const params: QueryKey<TOptions>[0] = { _id: id, baseURL: (options?.client ?? _heyApiClient).getConfig().baseURL } as QueryKey<TOptions>[0];
    if (infinite) {
        params._infinite = infinite;
    }
    if (options?.body) {
        params.body = options.body;
    }
    if (options?.headers) {
        params.headers = options.headers;
    }
    if (options?.path) {
        params.path = options.path;
    }
    if (options?.query) {
        params.query = options.query;
    }
    return [
        params
    ];
};

export const postApiReferenceBootstrapQueryKey = (options?: Options<PostApiReferenceBootstrapData>) => createQueryKey('postApiReferenceBootstrap', options);

export const postApiReferenceBootstrapOptions = (options?: Options<PostApiReferenceBootstrapData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await postApiReferenceBootstrap({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: postApiReferenceBootstrapQueryKey(options)
    });
};

export const postApiReferenceBootstrapMutation = (options?: Partial<Options<PostApiReferenceBootstrapData>>) => {
    const mutationOptions: UseMutationOptions<unknown, AxiosError<DefaultError>, Options<PostApiReferenceBootstrapData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await postApiReferenceBootstrap({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const deleteApiReferenceSetsBySetIdMutation = (options?: Partial<Options<DeleteApiReferenceSetsBySetIdData>>) => {
    const mutationOptions: UseMutationOptions<unknown, AxiosError<DefaultError>, Options<DeleteApiReferenceSetsBySetIdData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await deleteApiReferenceSetsBySetId({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const getApiReferenceSetsBySetIdQueryKey = (options: Options<GetApiReferenceSetsBySetIdData>) => createQueryKey('getApiReferenceSetsBySetId', options);

export const getApiReferenceSetsBySetIdOptions = (options: Options<GetApiReferenceSetsBySetIdData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getApiReferenceSetsBySetId({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getApiReferenceSetsBySetIdQueryKey(options)
    });
};

export const deleteApiReferenceSetsMutation = (options?: Partial<Options<DeleteApiReferenceSetsData>>) => {
    const mutationOptions: UseMutationOptions<unknown, AxiosError<DefaultError>, Options<DeleteApiReferenceSetsData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await deleteApiReferenceSets({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const getApiReferenceSetsQueryKey = (options?: Options<GetApiReferenceSetsData>) => createQueryKey('getApiReferenceSets', options);

export const getApiReferenceSetsOptions = (options?: Options<GetApiReferenceSetsData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getApiReferenceSets({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getApiReferenceSetsQueryKey(options)
    });
};

const createInfiniteParams = <K extends Pick<QueryKey<Options>[0], 'body' | 'headers' | 'path' | 'query'>>(queryKey: QueryKey<Options>, page: K) => {
    const params = queryKey[0];
    if (page.body) {
        params.body = {
            ...queryKey[0].body as any,
            ...page.body as any
        };
    }
    if (page.headers) {
        params.headers = {
            ...queryKey[0].headers,
            ...page.headers
        };
    }
    if (page.path) {
        params.path = {
            ...queryKey[0].path as any,
            ...page.path as any
        };
    }
    if (page.query) {
        params.query = {
            ...queryKey[0].query as any,
            ...page.query as any
        };
    }
    return params as unknown as typeof page;
};

export const getApiReferenceSetsInfiniteQueryKey = (options?: Options<GetApiReferenceSetsData>): QueryKey<Options<GetApiReferenceSetsData>> => createQueryKey('getApiReferenceSets', options, true);

export const getApiReferenceSetsInfiniteOptions = (options?: Options<GetApiReferenceSetsData>) => {
    return infiniteQueryOptions<GetApiReferenceSetsResponse, AxiosError<DefaultError>, InfiniteData<GetApiReferenceSetsResponse>, QueryKey<Options<GetApiReferenceSetsData>>, number | Pick<QueryKey<Options<GetApiReferenceSetsData>>[0], 'body' | 'headers' | 'path' | 'query'>>(
    // @ts-ignore
    {
        queryFn: async ({ pageParam, queryKey, signal }) => {
            // @ts-ignore
            const page: Pick<QueryKey<Options<GetApiReferenceSetsData>>[0], 'body' | 'headers' | 'path' | 'query'> = typeof pageParam === 'object' ? pageParam : {
                query: {
                    offset: pageParam
                }
            };
            const params = createInfiniteParams(queryKey, page);
            const { data } = await getApiReferenceSets({
                ...options,
                ...params,
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getApiReferenceSetsInfiniteQueryKey(options)
    });
};

export const postApiReferenceSetsQueryKey = (options?: Options<PostApiReferenceSetsData>) => createQueryKey('postApiReferenceSets', options);

export const postApiReferenceSetsOptions = (options?: Options<PostApiReferenceSetsData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await postApiReferenceSets({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: postApiReferenceSetsQueryKey(options)
    });
};

export const postApiReferenceSetsMutation = (options?: Partial<Options<PostApiReferenceSetsData>>) => {
    const mutationOptions: UseMutationOptions<unknown, AxiosError<DefaultError>, Options<PostApiReferenceSetsData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await postApiReferenceSets({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const getApiReferenceJobsByJobIdQueryKey = (options: Options<GetApiReferenceJobsByJobIdData>) => createQueryKey('getApiReferenceJobsByJobId', options);

export const getApiReferenceJobsByJobIdOptions = (options: Options<GetApiReferenceJobsByJobIdData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getApiReferenceJobsByJobId({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getApiReferenceJobsByJobIdQueryKey(options)
    });
};

export const getApiReferenceListTypesQueryKey = (options?: Options<GetApiReferenceListTypesData>) => createQueryKey('getApiReferenceListTypes', options);

export const getApiReferenceListTypesOptions = (options?: Options<GetApiReferenceListTypesData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getApiReferenceListTypes({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getApiReferenceListTypesQueryKey(options)
    });
};

export const getApiReferenceListTypesInfiniteQueryKey = (options?: Options<GetApiReferenceListTypesData>): QueryKey<Options<GetApiReferenceListTypesData>> => createQueryKey('getApiReferenceListTypes', options, true);

export const getApiReferenceListTypesInfiniteOptions = (options?: Options<GetApiReferenceListTypesData>) => {
    return infiniteQueryOptions<GetApiReferenceListTypesResponse, AxiosError<DefaultError>, InfiniteData<GetApiReferenceListTypesResponse>, QueryKey<Options<GetApiReferenceListTypesData>>, number | Pick<QueryKey<Options<GetApiReferenceListTypesData>>[0], 'body' | 'headers' | 'path' | 'query'>>(
    // @ts-ignore
    {
        queryFn: async ({ pageParam, queryKey, signal }) => {
            // @ts-ignore
            const page: Pick<QueryKey<Options<GetApiReferenceListTypesData>>[0], 'body' | 'headers' | 'path' | 'query'> = typeof pageParam === 'object' ? pageParam : {
                query: {
                    offset: pageParam
                }
            };
            const params = createInfiniteParams(queryKey, page);
            const { data } = await getApiReferenceListTypes({
                ...options,
                ...params,
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getApiReferenceListTypesInfiniteQueryKey(options)
    });
};

export const getApiReferenceListsByListIdQueryKey = (options: Options<GetApiReferenceListsByListIdData>) => createQueryKey('getApiReferenceListsByListId', options);

export const getApiReferenceListsByListIdOptions = (options: Options<GetApiReferenceListsByListIdData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getApiReferenceListsByListId({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getApiReferenceListsByListIdQueryKey(options)
    });
};

export const postApiReferenceListsQueryKey = (options?: Options<PostApiReferenceListsData>) => createQueryKey('postApiReferenceLists', options);

export const postApiReferenceListsOptions = (options?: Options<PostApiReferenceListsData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await postApiReferenceLists({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: postApiReferenceListsQueryKey(options)
    });
};

export const postApiReferenceListsMutation = (options?: Partial<Options<PostApiReferenceListsData>>) => {
    const mutationOptions: UseMutationOptions<unknown, AxiosError<DefaultError>, Options<PostApiReferenceListsData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await postApiReferenceLists({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const getApiReferenceListsByListIdValuesQueryKey = (options: Options<GetApiReferenceListsByListIdValuesData>) => createQueryKey('getApiReferenceListsByListIdValues', options);

export const getApiReferenceListsByListIdValuesOptions = (options: Options<GetApiReferenceListsByListIdValuesData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getApiReferenceListsByListIdValues({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getApiReferenceListsByListIdValuesQueryKey(options)
    });
};

export const getApiReferenceListsByListIdValuesInfiniteQueryKey = (options: Options<GetApiReferenceListsByListIdValuesData>): QueryKey<Options<GetApiReferenceListsByListIdValuesData>> => createQueryKey('getApiReferenceListsByListIdValues', options, true);

export const getApiReferenceListsByListIdValuesInfiniteOptions = (options: Options<GetApiReferenceListsByListIdValuesData>) => {
    return infiniteQueryOptions<unknown, AxiosError<DefaultError>, InfiniteData<unknown>, QueryKey<Options<GetApiReferenceListsByListIdValuesData>>, number | Pick<QueryKey<Options<GetApiReferenceListsByListIdValuesData>>[0], 'body' | 'headers' | 'path' | 'query'>>(
    // @ts-ignore
    {
        queryFn: async ({ pageParam, queryKey, signal }) => {
            // @ts-ignore
            const page: Pick<QueryKey<Options<GetApiReferenceListsByListIdValuesData>>[0], 'body' | 'headers' | 'path' | 'query'> = typeof pageParam === 'object' ? pageParam : {
                query: {
                    offset: pageParam
                }
            };
            const params = createInfiniteParams(queryKey, page);
            const { data } = await getApiReferenceListsByListIdValues({
                ...options,
                ...params,
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getApiReferenceListsByListIdValuesInfiniteQueryKey(options)
    });
};

export const postApiReferenceListsByListIdValuesQueryKey = (options: Options<PostApiReferenceListsByListIdValuesData>) => createQueryKey('postApiReferenceListsByListIdValues', options);

export const postApiReferenceListsByListIdValuesOptions = (options: Options<PostApiReferenceListsByListIdValuesData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await postApiReferenceListsByListIdValues({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: postApiReferenceListsByListIdValuesQueryKey(options)
    });
};

export const postApiReferenceListsByListIdValuesMutation = (options?: Partial<Options<PostApiReferenceListsByListIdValuesData>>) => {
    const mutationOptions: UseMutationOptions<unknown, AxiosError<DefaultError>, Options<PostApiReferenceListsByListIdValuesData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await postApiReferenceListsByListIdValues({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const putApiReferenceListsByListIdValuesMutation = (options?: Partial<Options<PutApiReferenceListsByListIdValuesData>>) => {
    const mutationOptions: UseMutationOptions<unknown, AxiosError<DefaultError>, Options<PutApiReferenceListsByListIdValuesData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await putApiReferenceListsByListIdValues({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const getApiReferenceSetsBySetIdValuesQueryKey = (options: Options<GetApiReferenceSetsBySetIdValuesData>) => createQueryKey('getApiReferenceSetsBySetIdValues', options);

export const getApiReferenceSetsBySetIdValuesOptions = (options: Options<GetApiReferenceSetsBySetIdValuesData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getApiReferenceSetsBySetIdValues({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getApiReferenceSetsBySetIdValuesQueryKey(options)
    });
};

export const getApiReferenceSetsBySetIdValuesInfiniteQueryKey = (options: Options<GetApiReferenceSetsBySetIdValuesData>): QueryKey<Options<GetApiReferenceSetsBySetIdValuesData>> => createQueryKey('getApiReferenceSetsBySetIdValues', options, true);

export const getApiReferenceSetsBySetIdValuesInfiniteOptions = (options: Options<GetApiReferenceSetsBySetIdValuesData>) => {
    return infiniteQueryOptions<unknown, AxiosError<DefaultError>, InfiniteData<unknown>, QueryKey<Options<GetApiReferenceSetsBySetIdValuesData>>, number | Pick<QueryKey<Options<GetApiReferenceSetsBySetIdValuesData>>[0], 'body' | 'headers' | 'path' | 'query'>>(
    // @ts-ignore
    {
        queryFn: async ({ pageParam, queryKey, signal }) => {
            // @ts-ignore
            const page: Pick<QueryKey<Options<GetApiReferenceSetsBySetIdValuesData>>[0], 'body' | 'headers' | 'path' | 'query'> = typeof pageParam === 'object' ? pageParam : {
                query: {
                    offset: pageParam
                }
            };
            const params = createInfiniteParams(queryKey, page);
            const { data } = await getApiReferenceSetsBySetIdValues({
                ...options,
                ...params,
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getApiReferenceSetsBySetIdValuesInfiniteQueryKey(options)
    });
};

export const postApiReferenceSetsBySetIdValuesQueryKey = (options: Options<PostApiReferenceSetsBySetIdValuesData>) => createQueryKey('postApiReferenceSetsBySetIdValues', options);

export const postApiReferenceSetsBySetIdValuesOptions = (options: Options<PostApiReferenceSetsBySetIdValuesData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await postApiReferenceSetsBySetIdValues({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: postApiReferenceSetsBySetIdValuesQueryKey(options)
    });
};

export const postApiReferenceSetsBySetIdValuesMutation = (options?: Partial<Options<PostApiReferenceSetsBySetIdValuesData>>) => {
    const mutationOptions: UseMutationOptions<unknown, AxiosError<DefaultError>, Options<PostApiReferenceSetsBySetIdValuesData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await postApiReferenceSetsBySetIdValues({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const getApiReferenceSetsBySetIdAlternatesQueryKey = (options: Options<GetApiReferenceSetsBySetIdAlternatesData>) => createQueryKey('getApiReferenceSetsBySetIdAlternates', options);

export const getApiReferenceSetsBySetIdAlternatesOptions = (options: Options<GetApiReferenceSetsBySetIdAlternatesData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getApiReferenceSetsBySetIdAlternates({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getApiReferenceSetsBySetIdAlternatesQueryKey(options)
    });
};

export const getApiReferenceSetsBySetIdAlternatesInfiniteQueryKey = (options: Options<GetApiReferenceSetsBySetIdAlternatesData>): QueryKey<Options<GetApiReferenceSetsBySetIdAlternatesData>> => createQueryKey('getApiReferenceSetsBySetIdAlternates', options, true);

export const getApiReferenceSetsBySetIdAlternatesInfiniteOptions = (options: Options<GetApiReferenceSetsBySetIdAlternatesData>) => {
    return infiniteQueryOptions<unknown, AxiosError<DefaultError>, InfiniteData<unknown>, QueryKey<Options<GetApiReferenceSetsBySetIdAlternatesData>>, number | Pick<QueryKey<Options<GetApiReferenceSetsBySetIdAlternatesData>>[0], 'body' | 'headers' | 'path' | 'query'>>(
    // @ts-ignore
    {
        queryFn: async ({ pageParam, queryKey, signal }) => {
            // @ts-ignore
            const page: Pick<QueryKey<Options<GetApiReferenceSetsBySetIdAlternatesData>>[0], 'body' | 'headers' | 'path' | 'query'> = typeof pageParam === 'object' ? pageParam : {
                query: {
                    offset: pageParam
                }
            };
            const params = createInfiniteParams(queryKey, page);
            const { data } = await getApiReferenceSetsBySetIdAlternates({
                ...options,
                ...params,
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getApiReferenceSetsBySetIdAlternatesInfiniteQueryKey(options)
    });
};

export const postApiReferenceSetsBySetIdAlternatesQueryKey = (options: Options<PostApiReferenceSetsBySetIdAlternatesData>) => createQueryKey('postApiReferenceSetsBySetIdAlternates', options);

export const postApiReferenceSetsBySetIdAlternatesOptions = (options: Options<PostApiReferenceSetsBySetIdAlternatesData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await postApiReferenceSetsBySetIdAlternates({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: postApiReferenceSetsBySetIdAlternatesQueryKey(options)
    });
};

export const postApiReferenceSetsBySetIdAlternatesMutation = (options?: Partial<Options<PostApiReferenceSetsBySetIdAlternatesData>>) => {
    const mutationOptions: UseMutationOptions<unknown, AxiosError<DefaultError>, Options<PostApiReferenceSetsBySetIdAlternatesData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await postApiReferenceSetsBySetIdAlternates({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const postApiReferenceSetsBySetIdAlternatesProcessQueryKey = (options: Options<PostApiReferenceSetsBySetIdAlternatesProcessData>) => createQueryKey('postApiReferenceSetsBySetIdAlternatesProcess', options);

export const postApiReferenceSetsBySetIdAlternatesProcessOptions = (options: Options<PostApiReferenceSetsBySetIdAlternatesProcessData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await postApiReferenceSetsBySetIdAlternatesProcess({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: postApiReferenceSetsBySetIdAlternatesProcessQueryKey(options)
    });
};

export const postApiReferenceSetsBySetIdAlternatesProcessMutation = (options?: Partial<Options<PostApiReferenceSetsBySetIdAlternatesProcessData>>) => {
    const mutationOptions: UseMutationOptions<unknown, AxiosError<DefaultError>, Options<PostApiReferenceSetsBySetIdAlternatesProcessData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await postApiReferenceSetsBySetIdAlternatesProcess({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const getApiReferenceSetsBySetIdValuesByValueIdQueryKey = (options: Options<GetApiReferenceSetsBySetIdValuesByValueIdData>) => createQueryKey('getApiReferenceSetsBySetIdValuesByValueId', options);

export const getApiReferenceSetsBySetIdValuesByValueIdOptions = (options: Options<GetApiReferenceSetsBySetIdValuesByValueIdData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getApiReferenceSetsBySetIdValuesByValueId({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getApiReferenceSetsBySetIdValuesByValueIdQueryKey(options)
    });
};

export const getApiReferenceSetsKeysByIdQueryKey = (options: Options<GetApiReferenceSetsKeysByIdData>) => createQueryKey('getApiReferenceSetsKeysById', options);

export const getApiReferenceSetsKeysByIdOptions = (options: Options<GetApiReferenceSetsKeysByIdData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getApiReferenceSetsKeysById({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getApiReferenceSetsKeysByIdQueryKey(options)
    });
};

export const getApiReferenceSetsKeysQueryKey = (options?: Options<GetApiReferenceSetsKeysData>) => createQueryKey('getApiReferenceSetsKeys', options);

export const getApiReferenceSetsKeysOptions = (options?: Options<GetApiReferenceSetsKeysData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getApiReferenceSetsKeys({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getApiReferenceSetsKeysQueryKey(options)
    });
};

export const getApiReferenceSetsKeysInfiniteQueryKey = (options?: Options<GetApiReferenceSetsKeysData>): QueryKey<Options<GetApiReferenceSetsKeysData>> => createQueryKey('getApiReferenceSetsKeys', options, true);

export const getApiReferenceSetsKeysInfiniteOptions = (options?: Options<GetApiReferenceSetsKeysData>) => {
    return infiniteQueryOptions<unknown, AxiosError<DefaultError>, InfiniteData<unknown>, QueryKey<Options<GetApiReferenceSetsKeysData>>, number | Pick<QueryKey<Options<GetApiReferenceSetsKeysData>>[0], 'body' | 'headers' | 'path' | 'query'>>(
    // @ts-ignore
    {
        queryFn: async ({ pageParam, queryKey, signal }) => {
            // @ts-ignore
            const page: Pick<QueryKey<Options<GetApiReferenceSetsKeysData>>[0], 'body' | 'headers' | 'path' | 'query'> = typeof pageParam === 'object' ? pageParam : {
                query: {
                    offset: pageParam
                }
            };
            const params = createInfiniteParams(queryKey, page);
            const { data } = await getApiReferenceSetsKeys({
                ...options,
                ...params,
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getApiReferenceSetsKeysInfiniteQueryKey(options)
    });
};

export const getApiReferenceSetsValuesQueryKey = (options?: Options<GetApiReferenceSetsValuesData>) => createQueryKey('getApiReferenceSetsValues', options);

export const getApiReferenceSetsValuesOptions = (options?: Options<GetApiReferenceSetsValuesData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getApiReferenceSetsValues({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getApiReferenceSetsValuesQueryKey(options)
    });
};

export const getApiReferenceSetsValuesInfiniteQueryKey = (options?: Options<GetApiReferenceSetsValuesData>): QueryKey<Options<GetApiReferenceSetsValuesData>> => createQueryKey('getApiReferenceSetsValues', options, true);

export const getApiReferenceSetsValuesInfiniteOptions = (options?: Options<GetApiReferenceSetsValuesData>) => {
    return infiniteQueryOptions<unknown, AxiosError<DefaultError>, InfiniteData<unknown>, QueryKey<Options<GetApiReferenceSetsValuesData>>, number | Pick<QueryKey<Options<GetApiReferenceSetsValuesData>>[0], 'body' | 'headers' | 'path' | 'query'>>(
    // @ts-ignore
    {
        queryFn: async ({ pageParam, queryKey, signal }) => {
            // @ts-ignore
            const page: Pick<QueryKey<Options<GetApiReferenceSetsValuesData>>[0], 'body' | 'headers' | 'path' | 'query'> = typeof pageParam === 'object' ? pageParam : {
                query: {
                    offset: pageParam
                }
            };
            const params = createInfiniteParams(queryKey, page);
            const { data } = await getApiReferenceSetsValues({
                ...options,
                ...params,
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getApiReferenceSetsValuesInfiniteQueryKey(options)
    });
};

export const postApiReferenceValidateQueryKey = (options?: Options<PostApiReferenceValidateData>) => createQueryKey('postApiReferenceValidate', options);

export const postApiReferenceValidateOptions = (options?: Options<PostApiReferenceValidateData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await postApiReferenceValidate({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: postApiReferenceValidateQueryKey(options)
    });
};

export const postApiReferenceValidateMutation = (options?: Partial<Options<PostApiReferenceValidateData>>) => {
    const mutationOptions: UseMutationOptions<unknown, AxiosError<DefaultError>, Options<PostApiReferenceValidateData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await postApiReferenceValidate({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const getApiReferenceSearchKeysQueryKey = (options?: Options<GetApiReferenceSearchKeysData>) => createQueryKey('getApiReferenceSearchKeys', options);

export const getApiReferenceSearchKeysOptions = (options?: Options<GetApiReferenceSearchKeysData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getApiReferenceSearchKeys({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getApiReferenceSearchKeysQueryKey(options)
    });
};

export const getApiReferenceSearchKeysInfiniteQueryKey = (options?: Options<GetApiReferenceSearchKeysData>): QueryKey<Options<GetApiReferenceSearchKeysData>> => createQueryKey('getApiReferenceSearchKeys', options, true);

export const getApiReferenceSearchKeysInfiniteOptions = (options?: Options<GetApiReferenceSearchKeysData>) => {
    return infiniteQueryOptions<unknown, AxiosError<DefaultError>, InfiniteData<unknown>, QueryKey<Options<GetApiReferenceSearchKeysData>>, number | Pick<QueryKey<Options<GetApiReferenceSearchKeysData>>[0], 'body' | 'headers' | 'path' | 'query'>>(
    // @ts-ignore
    {
        queryFn: async ({ pageParam, queryKey, signal }) => {
            // @ts-ignore
            const page: Pick<QueryKey<Options<GetApiReferenceSearchKeysData>>[0], 'body' | 'headers' | 'path' | 'query'> = typeof pageParam === 'object' ? pageParam : {
                query: {
                    offset: pageParam
                }
            };
            const params = createInfiniteParams(queryKey, page);
            const { data } = await getApiReferenceSearchKeys({
                ...options,
                ...params,
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getApiReferenceSearchKeysInfiniteQueryKey(options)
    });
};

export const getApiReferenceSearchValuesQueryKey = (options?: Options<GetApiReferenceSearchValuesData>) => createQueryKey('getApiReferenceSearchValues', options);

export const getApiReferenceSearchValuesOptions = (options?: Options<GetApiReferenceSearchValuesData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getApiReferenceSearchValues({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getApiReferenceSearchValuesQueryKey(options)
    });
};

export const getApiReferenceSearchValuesInfiniteQueryKey = (options?: Options<GetApiReferenceSearchValuesData>): QueryKey<Options<GetApiReferenceSearchValuesData>> => createQueryKey('getApiReferenceSearchValues', options, true);

export const getApiReferenceSearchValuesInfiniteOptions = (options?: Options<GetApiReferenceSearchValuesData>) => {
    return infiniteQueryOptions<unknown, AxiosError<DefaultError>, InfiniteData<unknown>, QueryKey<Options<GetApiReferenceSearchValuesData>>, number | Pick<QueryKey<Options<GetApiReferenceSearchValuesData>>[0], 'body' | 'headers' | 'path' | 'query'>>(
    // @ts-ignore
    {
        queryFn: async ({ pageParam, queryKey, signal }) => {
            // @ts-ignore
            const page: Pick<QueryKey<Options<GetApiReferenceSearchValuesData>>[0], 'body' | 'headers' | 'path' | 'query'> = typeof pageParam === 'object' ? pageParam : {
                query: {
                    offset: pageParam
                }
            };
            const params = createInfiniteParams(queryKey, page);
            const { data } = await getApiReferenceSearchValues({
                ...options,
                ...params,
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getApiReferenceSearchValuesInfiniteQueryKey(options)
    });
};

export const postApiReferenceInternalImportCsvQueryKey = (options?: Options<PostApiReferenceInternalImportCsvData>) => createQueryKey('postApiReferenceInternalImportCsv', options);

export const postApiReferenceInternalImportCsvOptions = (options?: Options<PostApiReferenceInternalImportCsvData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await postApiReferenceInternalImportCsv({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: postApiReferenceInternalImportCsvQueryKey(options)
    });
};

export const postApiReferenceInternalImportCsvMutation = (options?: Partial<Options<PostApiReferenceInternalImportCsvData>>) => {
    const mutationOptions: UseMutationOptions<PostApiReferenceInternalImportCsvResponse, AxiosError<DefaultError>, Options<PostApiReferenceInternalImportCsvData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await postApiReferenceInternalImportCsv({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const postApiReferenceInternalIntakeQueryKey = (options?: Options<PostApiReferenceInternalIntakeData>) => createQueryKey('postApiReferenceInternalIntake', options);

export const postApiReferenceInternalIntakeOptions = (options?: Options<PostApiReferenceInternalIntakeData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await postApiReferenceInternalIntake({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: postApiReferenceInternalIntakeQueryKey(options)
    });
};

export const postApiReferenceInternalIntakeMutation = (options?: Partial<Options<PostApiReferenceInternalIntakeData>>) => {
    const mutationOptions: UseMutationOptions<PostApiReferenceInternalIntakeResponse, AxiosError<DefaultError>, Options<PostApiReferenceInternalIntakeData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await postApiReferenceInternalIntake({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};