import { AppFormType, useAppForm } from '@hooks/app-form';
import { Edit, Emergency } from '@mui/icons-material';
import { Grid2 as Grid, Divider, CardHeader, IconButton } from '@mui/material';
import { useStore } from '@tanstack/react-form';
import MainCardContainer from '@components/main-container/main-card-container';
import { useMemo, useState } from 'react';
import { EthosWorkflowsApiPersonalEmergencyContactDto } from '@client/workflows';
import { ValidationErrors } from '@app-types/validation';
import { defaultValues, emergencyContactFormOptions, emergencyContactTransformer } from './utils';
import { formHasErrors } from '@utils/forms';

export type EmergencyContactFormData = {
	relationship: string | null;
	prefix: string;
	firstName: string;
	lastName: string;
	middleName: string;
	suffix: string;
	contactInformation: string;
};
interface EmergencyContactFormProps {
	onAdd?: (values: EthosWorkflowsApiPersonalEmergencyContactDto) => void;
	onCancel?: (shouldRemove: boolean) => void;
	onDelete?: () => void;
	formValues?: EthosWorkflowsApiPersonalEmergencyContactDto;
	onValidate: (
		data: EthosWorkflowsApiPersonalEmergencyContactDto
	) => Promise<ValidationErrors | undefined>;
	isUpdate?: boolean;
}

export default function EmergencyContactForm({
	onAdd,
	onCancel,
	formValues,
	onValidate,
	isUpdate,
}: EmergencyContactFormProps) {
	const options = useMemo(() => emergencyContactFormOptions(formValues), [formValues]);
	const hasValues = JSON.stringify(formValues) !== JSON.stringify(defaultValues);

	const form = useAppForm({
		...options,
		validators: {
			onChangeAsyncDebounceMs: 500,
			onChangeAsync: async ({ value }) => {
				const res = await onValidate(emergencyContactTransformer(value));
				return res;
			},
		},
		defaultState: {
			isDirty: hasValues,
			isPristine: !hasValues,
		},
	});

	const { values, canSubmit, isDirty } = useStore(form.store, ({ values, canSubmit, isDirty }) => ({
		values,
		canSubmit,
		isDirty,
	}));

	const [edit, setEdit] = useState(() => JSON.stringify(values) === JSON.stringify(defaultValues));
	const hasErrors = formHasErrors(form as AppFormType);

	console.log('values', hasValues);

	if (!edit) {
		if (!hasValues) {
			return null;
		}
		return (
			<CardHeader
				title={`${values.firstName} ${values.lastName}`}
				subheader={values.relationship || undefined}
				sx={{ borderRadius: 1 }}
				action={
					<IconButton onClick={() => setEdit(true)}>
						<Edit />
					</IconButton>
				}
			/>
		);
	}

	return (
		<MainCardContainer
			title="Add Emergency Contact"
			icon={<Emergency fontSize="large" />}
			color={hasErrors ? 'error' : 'primary'}
			emphasis="low"
			descriptionSubheader="* Indicates a required field"
			descriptionText="Add an emergency contact for the patient."
			footerProps={{
				primaryButton1: {
					label: hasValues || isUpdate ? 'Update' : 'Add',
					onClick: () => {
						onAdd?.(emergencyContactTransformer(values));
						setEdit(false);
					},
					disabled: !canSubmit || !isDirty,
					'data-testid': 'emergencyContact.submitButton',
				},
				primaryButton2: {
					label: 'Cancel',
					onClick: () => {
						form.reset();
						setEdit(false);
						onCancel?.(!hasValues);
					},
					'data-testid': 'emergencyContact.cancelButton',
				},
			}}>
			<Grid
				container
				columnSpacing={2}
				rowSpacing={2}>
				<Grid size={12}>
					<form.AppField name="relationship">
						{(field) => (
							<field.AppSelectField
								label="Relationship"
								required
								referenceDataSetName="relationship"
								referenceDataFilter="relationshipType eq Emergency Contact"
								name="emergencyContact.relationship"
								dataTestId="emergencyContact.relationship"
							/>
						)}
					</form.AppField>
				</Grid>
				<Grid size={12}>
					<Divider />
				</Grid>
				<Grid size={12}>
					<form.AppField name="prefix">
						{(field) => (
							<field.AppAutocompleteField
								label="Prefix"
								referenceDataSetName="namePrefix"
								sx={{ width: { xs: '100%', sm: '50%', md: '25%' } }}
								name="emergencyContact.prefix"
								dataTestId="emergencyContact.prefix"
							/>
						)}
					</form.AppField>
				</Grid>
				<Grid size={4}>
					<form.AppField name="firstName">
						{(field) => (
							<field.AppTextField
								label="First Name"
								required
								name="emergencyContact.firstName"
								dataTestId="emergencyContact.firstName"
							/>
						)}
					</form.AppField>
				</Grid>
				<Grid size={4}>
					<form.AppField name="middleName">
						{(field) => (
							<field.AppTextField
								label="Middle Name"
								name="emergencyContact.middleName"
								dataTestId="emergencyContact.middleName"
							/>
						)}
					</form.AppField>
				</Grid>
				<Grid size={4}>
					<form.AppField name="lastName">
						{(field) => (
							<field.AppTextField
								label="Last Name"
								required
								name="emergencyContact.lastName"
								dataTestId="emergencyContact.lastName"
							/>
						)}
					</form.AppField>
				</Grid>
				<Grid size={12}>
					<form.AppField name="suffix">
						{(field) => (
							<field.AppAutocompleteField
								label="Suffix"
								referenceDataSetName="nameSuffix"
								sx={{ width: { xs: '100%', sm: '50%', md: '25%' } }}
								name="emergencyContact.suffix"
								dataTestId="emergencyContact.suffix"
							/>
						)}
					</form.AppField>
				</Grid>
				<Grid size={12}>
					<Divider />
				</Grid>
				<Grid size={12}>
					<form.AppField name="contactInformation">
						{({ AppPhoneNumberField }) => (
							<AppPhoneNumberField
								label="Phone Number"
								required
								name="emergencyContact.contactInformation"
								dataTestId="emergencyContact.contactInformation"
							/>
						)}
					</form.AppField>
				</Grid>
			</Grid>
		</MainCardContainer>
	);
}
