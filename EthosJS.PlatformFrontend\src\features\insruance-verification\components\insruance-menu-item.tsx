import MenuItem from '@components/menu/menu-item';
import { Status } from '@config/status';
import { ShieldCheck } from 'lucide-react';
import useInsuranceVerification from '../hooks/use-insurance-verification';

export default function InsuranceMenuItem({
	studyId,
	selected,
	onClick,
}: {
	studyId: string;
	selected: boolean;
	onClick: () => void;
}) {
	const { verificationStatus } = useInsuranceVerification({
		studyId,
	});

	const getStatus = () => {
		switch (verificationStatus?.currentCoarseState) {
			case 'Processing':
				return Status.Process;
			case 'Pending':
				return Status.Process;
			case 'Error':
				return Status.Error;
			case 'Completed': {
				console.log({ verificationStatus });
				return verificationStatus.coarseStateSucceeded ? Status.Success : Status.Error;
			}
			default:
				return Status.NotStarted;
		}
	};

	return (
		<MenuItem
			title="Insurance Verification"
			value="insurance-verification"
			icon={ShieldCheck}
			size="large"
			selected={selected}
			onClick={onClick}
			status={getStatus()}
		/>
	);
}
