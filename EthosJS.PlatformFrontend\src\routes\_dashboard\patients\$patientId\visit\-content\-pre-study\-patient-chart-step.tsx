import React, { useImperative<PERSON>andle, useState } from 'react';
import { Box, Checkbox, Stack, Typography } from '@mui/material';
import { useForm } from '@tanstack/react-form';
import usePreStudyStore from '@hooks/use-pre-study-store';
import { useStore } from '@tanstack/react-store';
import { NoteAdd } from '@mui/icons-material';
import NotesContainer, { Note, NoteItem } from '@components/notes-container';

import { PatientChart, StudyDetails } from '../../-content/pre-study';
import { NotebookPen, Signature, StickyNote } from 'lucide-react';
import MainCardContainer from '@components/main-container/main-card-container';
import { SingleNote } from '@components/notes-container/single-note';

interface PatientChartStepProps {
  formRef: React.RefObject<ReturnType<typeof useForm> | null>;
  studyId: string;
  patientId: string;
}

const PatientChartStep = ({ formRef, studyId, patientId }: PatientChartStepProps) => {
  const { store, actions } = usePreStudyStore();
  const { values } = useStore(store, (state) => state);

  const [additionalPatientNotes, setAdditionalPatientNotes] = useState<Note>({
    id: '1',
    header: 'Additional Patient Notes',
    subHeader: 'Added by John Scott, May 7, 2025 at 9:15 AM',
    content: `- Patient has a history of mild anxiety in medical settings; prefers detailed explanations of procedures
- Hard of hearing in left ear - speak clearly and face patient when communicating
- Allergic to latex gloves - use nitrile alternatives
- Prefers morning appointments (8-10 AM) due to afternoon fatigue
- Uses reading glasses but often forgets to bring them - keep large print materials available
- Has difficulty with stairs; requires ground floor exam rooms when possible
- Previous adverse reaction to morphine - see allergy list for details
- Patient requests female providers when possible for personal care
- Speaks primarily Spanish; may need interpreter for complex medical discussions`
  });
  const [caregiverInformation, setCaregiverInformation] = useState<Note>({
    id: '1',
    header: 'Caregiver Information',
    subHeader: 'Added by John Scott, May 7, 2025 at 9:15 AM',
    content: `Primary Caregiver: Maria Bishop (Daughter)
- Relationship: Daughter, Medical POA
- Phone: (************* (cell - preferred contact)
- Email: <EMAIL>
- Availability: Mon-Fri after 3 PM, weekends anytime
- Lives with patient, manages medications and appointments
- Authorized to receive all medical information

Secondary Contact: Peter Bishop (Son)
- Relationship: Son
- Phone: (************* (work)
- Email: <EMAIL>
- Lives out of state (Boston, MA)
- Contact for emergencies only if Maria unavailable
- Visits monthly, helps with financial decisions

Additional Notes:
- Family prefers to be present for all major medical decisions
- Daughter Maria handles day-to-day care and medication management
- Son Peter manages insurance and billing matters`
  });
  const [notes, setNotes] = useState<Note[]>([]);

  const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const isChecked = event.target.checked;
    actions.setValues({ step1Completed: isChecked });
  };

  const form = useForm({
    defaultValues: {
      step1Completed: values.step1Completed
    },
    onSubmit({ value }) {
      actions.setValues(value);
      actions.moveNext();
    }
  });

  useImperativeHandle(formRef, () => form as unknown as ReturnType<typeof useForm>);

  const handleAddNote = (content: string) => {
    const newNote: Note = {
      id: Date.now().toString(),
      header: 'Added by John Scott, May 7, 2025 at 9:15 AM',
      content
    };
    setNotes((prev) => [...prev, newNote]);
  };

  const handleEditNote = (id: string, updatedNote: Omit<Note, 'id'>) => {
    setNotes((prev) => prev.map((note) => (note.id === id ? { ...updatedNote, id } : note)));
  };

  const handleDeleteNote = (id: string) => {
    setNotes((prev) => prev.filter((note) => note.id !== id));
  };

  return (
    <Stack gap="12px">
      <PatientChart />
      <StudyDetails studyId={studyId} patientId={patientId} />
      <SingleNote
        note={additionalPatientNotes}
        allowEdit={true}
        onEdit={(updatedNote) => {
          setAdditionalPatientNotes({
            ...updatedNote,
            id: additionalPatientNotes.id
          });
        }}
      />
      <SingleNote
        note={caregiverInformation}
        allowEdit={true}
        onEdit={(updatedNote) => {
          setCaregiverInformation({
            ...updatedNote,
            id: caregiverInformation.id
          });
        }}
      />
      <NotesContainer
        title="Add Chart Review Notes"
        icon={<NoteAdd />}
        onAddNote={handleAddNote}
        onDeleteNote={handleDeleteNote}
        allowAdd={true}
        allowDelete={true}
        descriptionSubheader="Add any notes about chart review, document discrepancies, or important patient information...">
        {notes.map((note) => (
          <NoteItem
            icon={<StickyNote />}
            key={note.id}
            note={note}
            allowEdit={true}
            onEdit={(updatedNote) => handleEditNote(note.id, updatedNote)}
          />
        ))}
      </NotesContainer>
      <MainCardContainer
        title="Confirmation Checklist"
        color="primary"
        emphasis="high"
        icon={<NotebookPen />}>
        <Box>
          <Stack direction="row" alignItems="center" justifyContent="space-between">
            <Stack direction="row" alignItems="center" spacing={1.5} sx={{ flexGrow: 1 }}>
              <Signature style={{ fontSize: '22.5px', color: '#422F7D' }} />
              <Typography sx={{ textAlign: 'left', flexGrow: 1 }}>
                I have reviewed the patient information and confirmed it is correct.
              </Typography>
            </Stack>
            <Checkbox checked={values.step1Completed} onChange={handleChange} sx={{ ml: 1 }} />
          </Stack>
        </Box>
      </MainCardContainer>
    </Stack>
  );
};

export default PatientChartStep;
