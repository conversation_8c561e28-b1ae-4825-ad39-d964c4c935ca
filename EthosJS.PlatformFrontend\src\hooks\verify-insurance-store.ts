import { Status } from '@components/left-menu';

import { Store } from '@tanstack/react-store';
import { verifyInsuranceSteps } from '../routes/_dashboard/patients/$patientId/-old.insurance';

type VerifyInsuranceState = {
	steps: typeof verifyInsuranceSteps;
	currentStep: (typeof verifyInsuranceSteps)[number] | null;
	selectedStep: (typeof verifyInsuranceSteps)[number] | null;
	status: 'idle' | 'loading' | 'success' | 'error';
};

export function useVerifyInsuranceStore() {
	const store = new Store<VerifyInsuranceState>({
		steps: verifyInsuranceSteps,
		currentStep: verifyInsuranceSteps[0],
		selectedStep: null,
		status: 'idle',
	});

	const actions = {
		updateState: (state: Partial<VerifyInsuranceState>) => {
			store.setState((prev) => ({
				...prev,
				...state,
			}));
		},
		updateStepStatus: (key: string, status: Status) => {
			store.setState((prev) => ({
				...prev,
				steps: prev.steps.map((step) => {
					if (step.key === key) {
						return {
							...step,
							status,
						};
					}
					return step;
				}),
			}));
		},
		moveToNextStep: () => {
			store.setState((prev) => {
				const currentIndex = prev.steps.findIndex((step) => step.key === prev.currentStep?.key);
				if (currentIndex < prev.steps.length - 1) {
					return {
						...prev,
						currentStep: prev.steps[currentIndex + 1],
						selectedStep: null,
					};
				}
				return prev;
			});
		},
	};

	return { store, actions };
}
